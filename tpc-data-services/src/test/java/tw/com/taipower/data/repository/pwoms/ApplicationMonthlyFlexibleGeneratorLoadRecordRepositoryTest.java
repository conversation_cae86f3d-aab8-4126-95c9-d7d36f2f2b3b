package tw.com.taipower.data.repository.pwoms;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.data.AbstractRepositoryTest;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @class: ApplicationMonthlyFlexibleGeneratorLoadRecordRepositoryTest
 * @author: ting
 * @version:
 * @since: 2024-09-16 15:30
 * @see:
 **/
@Log4j2
@SpringBootTest
@ActiveProfiles("mte-t1-test-s1")
public class ApplicationMonthlyFlexibleGeneratorLoadRecordRepositoryTest extends AbstractRepositoryTest {

    @Autowired
    private ApplicationMonthlyFlexibleGeneratorLoadRecordRepository repository;

    @Autowired
    ApplicationMonthlyRematchGeneratorLoadRecordRepository applicationMonthlyRematchGeneratorLoadRecordRepository;

    @Autowired
    EnergyChargeRepository energyChargeRepository;

    Date serviceDate = new GregorianCalendar(2024, Calendar.JUNE, 1).getTime();

    @Test
    void findApplicationIdByDate() {
        //log.info(repository.findApplicationIdByDate(serviceDate));
    }

    @Test
    public void findLoadMatchedRmTimeSlotByDateRangeTest() { // #11
        Date billStart = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date billEnd = new Date();
        List<Map<String, Object>> res = applicationMonthlyRematchGeneratorLoadRecordRepository
                .findLoadMatchedRmTimeSlotByDateRange(billStart, billEnd);
        // assertion
        assertEquals(748, res.size());
    }

    @Test
    public void findLoadsMatchedRmByApplicationLoadIdsTest() { // #11
        List<Long> applicationLoadIds = //List.of(19L, 20L, 21L, 22L, 23L, 24L, 25L, 41L, 42L, 43L, 44L, 45L, 35262L,35263L,35264L,35265L,35266L,35267L,35268L,35269L,35270L); //60L,61L,62L,63L,64L,65L,66L,67L,68L
                    List.of(1L,2L,3L,5L,6L,7L,8L,9L,10L,11L,12L,13L,14L,15L,16L,17L,30L,31L,32L,33L,44L,45L,46L,47L,48L,49L,50L,51L,52L,53L,88L,89L,90L,91L,92L,93L,94L,95L,96L,97L,98L,101L,102L,107L,109L,110L,111L,112L,113L,114L,116L,117L,118L,119L,120L,121L,122L,123L,124L,125L,126L,128L,129L);
        Date billStart = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date billEnd = new Date();
        List<Map<String, Object>> res = repository.findLoadsMatchedRmByApplicationLoadIds(billStart, billEnd);
        //System.out.println("res size:"+res.size());

        // assertion
        assertEquals(1140, res.size());
        int count = 0;
        for (int i =0; i< res.size(); i++) {
            Map<String, Object> m = res.get(i);
            if (m.get("serviceDate").toString().equals("2024-07-01")) count++;
        }
        assertEquals(applicationLoadIds.size()*4, count); // 缺 65,66,67,68
        //assertEquals(12, res.size()); // 只有 62, 63,64
    }
}