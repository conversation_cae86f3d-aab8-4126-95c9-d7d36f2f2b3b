package tw.com.taipower.data.repository.pwoms;

import lombok.extern.log4j.Log4j2;
import org.antlr.v4.runtime.misc.Array2DHashSet;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.data.AbstractRepositoryTest;
import tw.com.taipower.data.entity.pwoms.ApplicationTimelyGeneratorRecord;
import tw.com.taipower.data.vo.powms.IMatchedRmEcChangeId;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @class: ApplicationTimelyGeneratorRecordRepositoryTest
 * @author: ting
 * @version:
 * @since: 2024-05-29 17:26
 * @see:
 **/

@Log4j2
@SpringBootTest
@ActiveProfiles("mte-t1-test-s1")
//@ActiveProfiles("mte-t2-tpc")
public class ApplicationTimelyGeneratorRecordRepositoryTest extends AbstractRepositoryTest {

    @Autowired
    private ResourceLoader resourceLoader = null;

    @Autowired
    private ApplicationTimelyGeneratorRecordRepository repository;

    boolean tpcMark = false;

    @BeforeEach
    public void setUp() {
        tpcMark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t2-tpc");
    }

    @Test
    public void findByGeneratorIdIn() {
        List<ApplicationTimelyGeneratorRecord> generatorRecordList = repository.findByGeneratorIdIn(List.of(3L));
        //log.info(generatorRecordList);
        assertEquals(0, generatorRecordList.size());
    }

    @Test
    public void findByDatetimeBetweenOrderByDatetimeAsc() {
        Date startTime = new GregorianCalendar(2024, Calendar.MAY, 1, 0, 0, 0).getTime();
        Date endTime = new GregorianCalendar(2024, Calendar.MAY, 1, 23, 45, 0).getTime();

        List<ApplicationTimelyGeneratorRecord> generatorRecordList = repository.findByDatetimeBetweenOrderByDatetimeAsc(startTime, endTime);
        //log.info(generatorRecordList);
        assertEquals(0, generatorRecordList.size());
    }

    @Test
    public void findGen15MinutesMatchedRmByDateTest() {
        List<Long> settlementId = tpcMark? List.of(136L,137L,138L):List.of(367L,368L,372L); // 2025-05 mte-t1

        if (tpcMark) {
            for (int i =0 ;i< settlementId.size(); i++) {
                List<Map<String, Object>> resS = repository.findGen15MinutesMatchedRmByDate(settlementId.get(i));
                //log.info("i:"+i+", res size:"+resS.size());
                if (0 == i) {
                    assertEquals(133920, resS.size()); // 45 * 96 * 31 = 133,920
                    assertEquals(0, resS.size()%96);
                    assertEquals("2025-03-01", resS.get(0).get("time").toString().split(" ")[0]);
                    assertEquals("2025-03-31", resS.get(resS.size()-1).get("time").toString().split(" ")[0]);
                } else if (1 == i) {
                    assertEquals(132480, resS.size());
                    assertEquals(0, resS.size()%96);
                    assertEquals("2025-04-01", resS.get(0).get("time").toString().split(" ")[0]);
                    assertEquals("2025-04-30", resS.get(resS.size()-1).get("time").toString().split(" ")[0]);
                } else {
                    assertEquals(133920, resS.size());
                    assertEquals(0, resS.size()%96);
                    assertEquals("2025-05-01", resS.get(0).get("time").toString().split(" ")[0]);
                    assertEquals("2025-05-31", resS.get(resS.size()-1).get("time").toString().split(" ")[0]);
                }
            }
        } else {
            for (int i =0 ;i< settlementId.size(); i++) {
                List<Map<String, Object>> resS = repository.findGen15MinutesMatchedRmByDate(settlementId.get(i));
                //log.info("i:"+i+", res size:"+resS.size());
                if (0 == i) {
                    assertEquals(136896, resS.size()); // 46 * 96 * 31 = 136,896
                    assertEquals(0, resS.size()%96);
                    assertEquals("2024-07-01", resS.get(0).get("time").toString().split(" ")[0]);
                    assertEquals("2024-07-31", resS.get(resS.size()-1).get("time").toString().split(" ")[0]);
                } else if (1 == i) {
                    assertEquals(139872, resS.size());
                    assertEquals(0, resS.size()%96);
                    assertEquals("2024-08-01", resS.get(0).get("time").toString().split(" ")[0]);
                    assertEquals("2024-08-31", resS.get(resS.size()-1).get("time").toString().split(" ")[0]);
                } else {
                    assertEquals(141120, resS.size());
                    assertEquals(0, resS.size()%96);
                    assertEquals("2024-09-01", resS.get(0).get("time").toString().split(" ")[0]);
                    assertEquals("2024-09-30", resS.get(resS.size()-1).get("time").toString().split(" ")[0]);
                }
            }
            // 2025-04
            settlementId = List.of(361L,362L,365L); // 2025-05 mte-t1
            for (int i =0 ;i< settlementId.size(); i++) {
                List<Map<String, Object>> resS = repository.findGen15MinutesMatchedRmByDate(settlementId.get(i));
                //log.info("i:"+i+", res size:"+resS.size());
                if (0 == i) {
                    assertEquals(136896, resS.size()); // 46 * 96 * 31 = 136,896 // 361 2024-08
                    assertEquals(0, resS.size()%96);
                    assertEquals("2024-08-01", resS.get(0).get("time").toString().split(" ")[0]);
                    assertEquals("2024-08-31", resS.get(resS.size()-1).get("time").toString().split(" ")[0]);
                } else if (1 == i) {
                    assertEquals(138240, resS.size());
                    assertEquals(0, resS.size()%96);
                    assertEquals("2024-09-01", resS.get(0).get("time").toString().split(" ")[0]);
                    assertEquals("2024-09-30", resS.get(resS.size()-1).get("time").toString().split(" ")[0]);
                } else {
                    assertEquals(0, resS.size());
                    assertEquals(0, resS.size()%96);
                }
            }
        }
    }

    @Test
    public void sumMatchedRm13911ByApplicationGeneratorIdBetweenDateRangeTest() {
        List<Long> settlementIds = List.of(367L, 368L, 372L); // 2025-05 mte-t1
        Long applicationGeneratorId = 52L;
        Date from = new GregorianCalendar(2024, Calendar.SEPTEMBER, 1).getTime();
        Date to = new GregorianCalendar(2024, Calendar.SEPTEMBER, 2, 12, 0).getTime();
        List<IMatchedRmEcChangeId> sums = repository.sumMatchedRm13911ByApplicationGeneratorIdBetweenDateRange(
                settlementIds, applicationGeneratorId, from, to);

        // assertion
        assertEquals(2, sums.size());
        assertEquals("1", sums.get(0).getEcChangeId().toString());
        assertEquals("3", sums.get(1).getEcChangeId().toString());
        assertEquals("0.0000", sums.get(0).getMatchedRm().toString());
        assertNull(sums.get(0).getDirMatched());
        assertEquals("0.0000", sums.get(1).getMatchedRm().toString());
        assertNull(sums.get(1).getDirMatched());
        // 2025-04
        settlementIds = List.of(360L, 361L, 362L, 375L, 376L); // 2025-04 mte-t1
        sums = repository.sumMatchedRm13911ByApplicationGeneratorIdBetweenDateRange(
                settlementIds, applicationGeneratorId, from, to);
        assertEquals(2, sums.size());
        assertEquals("1", sums.get(0).getEcChangeId().toString());
        assertEquals("3", sums.get(1).getEcChangeId().toString());
        assertEquals("0.0000", sums.get(0).getMatchedRm().toString());
        assertNull(sums.get(0).getDirMatched());
        assertEquals("0.0000", sums.get(1).getMatchedRm().toString());
        assertNull(sums.get(1).getDirMatched());
    }
}