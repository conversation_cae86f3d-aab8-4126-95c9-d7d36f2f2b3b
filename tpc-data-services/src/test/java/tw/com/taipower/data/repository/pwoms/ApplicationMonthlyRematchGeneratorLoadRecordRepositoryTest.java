package tw.com.taipower.data.repository.pwoms;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.data.AbstractRepositoryTest;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @class: ApplicationMonthlyRematchGeneratorLoadRecordRepositoryTest
 * @author: jingfungchen
 * @version:
 * @since: 2024-09-12 11:31
 * @see:
 **/
@Log4j2
@SpringBootTest
//@ActiveProfiles("ae-dev")
//@ActiveProfiles("mte-t1-test-s1")
@ActiveProfiles("mte-t2-tpc")
class ApplicationMonthlyRematchGeneratorLoadRecordRepositoryTest extends AbstractRepositoryTest {

    @Autowired
    private ResourceLoader resourceLoader = null;

    @Autowired
    private ApplicationMonthlyRematchGeneratorLoadRecordRepository repository;

    boolean tpcMark = false;
    boolean mteT1Mark = false;

    @BeforeEach
    public void setUp() throws Exception {
        tpcMark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t2-tpc");
        mteT1Mark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t1-test-s1");
    }

    @Test
    public void sumUnMatchedRmByGeneratorEntityNamesYearTest() {
        Date serviceStart = new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> info = repository.sumUnMatchedRmByGeneratorEntityNamesYear(serviceStart, serviceEnd);
        // assertion
        if (mteT1Mark) {
            assertEquals(45, info.size()); // 10
            Map<String, Object> first = info.get(0);
            assertEquals(3, first.size());
            assertEquals("台灣電力股份有限公司", first.get("gEntityNAME").toString()); // 寶玄測試發電端虛擬集團
            assertEquals("9182205.0000", first.get("matchedKw").toString()); // 3064726.0000 -> 9082205.0000
            assertNull(first.get("serviceDate"));
            Map<String, Object> ms = info.get(1);
            assertEquals("恩富資本太陽能股份有限公司", ms.get("gEntityNAME").toString()); // 京承能源股份有限公司
            assertEquals("2913852.0000", ms.get("matchedKw").toString()); // 7753.0000 -> 2913852.0000
            assertNull(first.get("serviceDate"));
            ms = info.get(15);
            assertEquals("泰陽光電股份有限公司", ms.get("gEntityNAME").toString());
            assertEquals("234808.0000", ms.get("matchedKw").toString()); // 234808.0000
            assertEquals("2024-07-01", ms.get("serviceDate").toString());
            ms = info.get(25);
            assertEquals("協勝發鋼鐵廠股份有限公司", ms.get("gEntityNAME").toString());
            assertEquals("14590.0000", ms.get("matchedKw").toString()); // 14604.0000
            assertEquals("2024-08-01", ms.get("serviceDate").toString());
            Map<String, Object> end = info.get(37);
            assertEquals("得禾能源股份有限公司", end.get("gEntityNAME").toString()); // 發電端8
            assertEquals("3757.0000", end.get("matchedKw").toString()); // 423.0000 -> 3757.0000
            assertEquals("2024-09-01", end.get("serviceDate").toString()); // 2024-06-01
        } else {
            assertEquals(0, info.size());
            /*assertEquals("寶玄測試發電端虛擬集團", first.get("gEntityNAME").toString());
            assertEquals("3064726.0000", first.get("matchedKw").toString());
            assertNull(first.get("serviceDate"));
            Map<String, Object> ms = info.get(1);
            assertEquals("京承能源股份有限公司", ms.get("gEntityNAME").toString());
            assertEquals("7753.0000", ms.get("matchedKw").toString());
            assertNull(first.get("serviceDate"));
            Map<String, Object> end = info.get(info.size()-1);
            assertEquals("發電端8", end.get("gEntityNAME").toString());
            assertEquals("423.0000", end.get("matchedKw").toString());
            assertEquals("2024-06-01", end.get("serviceDate").toString());*/
        }
    }
}