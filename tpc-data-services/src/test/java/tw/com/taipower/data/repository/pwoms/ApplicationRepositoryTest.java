package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.data.AbstractRepositoryTest;
import tw.com.taipower.data.entity.pwoms.Application;
import tw.com.taipower.data.entity.pwoms.ApplicationChangeRecord;
import tw.com.taipower.data.entity.pwoms.OfficialApplicationQuantity;

import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static tw.com.taipower.data.utils.CommonUtil.randomInt;

@Log4j2
@SpringBootTest
@ActiveProfiles("ae-dev")
//@ActiveProfiles("ae-s1")
//@ActiveProfiles("mte-t1-test-s1")
//@ActiveProfiles("mte-t2-tpc")
public class ApplicationRepositoryTest extends AbstractRepositoryTest {

    @Autowired
    private ResourceLoader resourceLoader = null;

    @Autowired
    ApplicationRepository repository;

    @Autowired
    OfficialApplicationQuantityRepository officialApplicationQuantityRepository;

    Date startTime = new GregorianCalendar(2024, Calendar.JUNE, 1).getTime();
    Date endTime = new GregorianCalendar(2024, Calendar.JULY, 1).getTime();
    boolean devMark = false;
    boolean tpcMark = false; // for 台電

    @BeforeEach
    public void setUp() throws Exception {
        devMark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals(
                "ae-dev");
        tpcMark =
                ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals(
                        "mte-t2-tpc");
    }

    @Transactional
    @Test
    public void findAllTest() {
        var applications = repository.findAll(PageRequest.of(0, 10));
    }

    @Test
    public void findByContractedStartAfterAndContractedEndBefore() {
        Date startTime = new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime();
        YearMonth yearMonth = YearMonth.of(2024, 1);
        int lastDay = yearMonth.atEndOfMonth().getDayOfMonth();
        Date endTime = new GregorianCalendar(2024, Calendar.JANUARY, lastDay).getTime();

        List<Application> applicationList =
                repository.findByContractedStartLessThanEqualAndContractedEndGreaterThanEqual(startTime, endTime);
        //System.out.println(applicationList);
    }

    @Test
    public void findIdAndContractNoByIdIn() {
        List<?> applicationList = repository.findIdAndContractNoByIdIn(Arrays.asList(75L, 76L, 77L, 78L));
        log.info(applicationList);
    }

    @Test
    public void findIdAndTypeByIdInOrderByIdAsc() {
        List<?> applicationList = repository.findIdAndTypeByIdInOrderByIdAsc(Arrays.asList(75L, 76L, 77L, 78L));
        log.info(applicationList);
    }

    @Test
    public void findAllByContractNo1() {
        var fisrt = repository.findAllByContractNo("1%", new Date());
        log.info(fisrt);

    }

    @Test
    public void findAllByContractNo() {
        var fisrt = repository.findAllByContractNo(List.of(4839L), new Date());
        log.info(fisrt);
        var second = repository.findAllByContractNo(fisrt, new Date());
        log.info(second);
    }

    @Test
    public void findAllByContractNo2() {
        var fisrt = repository.findAllByContractNo(List.of(165L), new Date(), 5);
        log.info(fisrt);
        var second = repository.findAllByContractNo(fisrt, new Date(), 5);
        log.info(second);
    }

    @Test
    public void changeRecordTest() {
        if (devMark) {
            var application = repository.findById(11L).orElseThrow();
            var cr = ApplicationChangeRecord.builder().addGen(List.of(1L, 2L, 3L)).build();
            application.setChangeRecord(cr);
            repository.save(application);
        }
    }

    @Test
    public void findFatherContractListByContractNosTest() {
        List<String> contracts = List.of("1-11206-15-002");
        List<Map<String, Object>> res = repository.findFatherContractListByContractNos(contracts);
        //System.out.println(res);

        // assertion
        if (devMark) {
            assertEquals(8, res.size());
            assertEquals(4, res.get(0).size());
            assertNotNull(res.get(0).get("FATHER_CONTRACT_TERMINATE_DATE"));
            assertNotEquals("00", res.get(0).get("VERSION"));
        } else {
            assertTrue(res.isEmpty());
        }
    }

    @Test
    public void findGeneratorContractInfoTest() { // #16 發電月報
        /* 不考慮 ami 狀況
        List<String> el =
                List.of("21297046029", "21040974063", "20902063127", "21301329101", "21201441219", "18442600035",
                        "23999999999", "20671959016", "23000000005", "20733704225", "21294694937", "20865770000", "23000000002",
                        "21061740086", "21273343108", "21050763092", "20727002703", "18442600115", "21297046201", "23999999998",
                        "21306604992", "23999999996", "21261949105", "21277864632", "21297046405", "21125488430", "21302242951",
                        "21274248912", "20894366102", "20800185231", "21151675157", "21151479251", "20662554768", "21291057985",
                        "23999999997", "21155369996", "21015135704", "21201074271", "20651602912", "21115084909", "21016505305",
                        "21304746976", "21103615040", "20903010067", "23000000004", "21262077001", "20800397033", "23000000001",
                        "21166577968", "20726944608", "21272326979", "21191034918", "20790723068", "20891225606", "20656186010",
                        "21030403118", "21297046109", "20663509003", "20716844127", "20727017937", "21104079986", "21297046303",
                        "23000000003", "20720345928", "20790723057", "21304553985", "21191902726"); //67個
        List<String> el = List.of("21297046029","21040974063","20902063127","21301329101","21201441219","18442600035"
                ,"23999999999","20671959016","23000000005","20733704225","21294694937","20865770000","23000000002","21061740086"
                ,"21273343108","21050763092","20727002703","21297046201","23999999998","21306604992","23999999996","21261949105"
                ,"21277864632","21297046405","21302242951","21274248912","20894366102","20800185231","21151675157","21151479251"
                ,"21291057985","23999999997","21155369996","21015135704","21201074271","20651602912","21115084909","21016505305"
                ,"21304746976","21103615040","20903010067","23000000004","21262077001","20800397033","23000000001","21166577968"
                ,"20726944608","21191034918","20790723068","20891225606","21030403118","21297046109","20663509003","20716844127"
                ,"20727017937","21104079986","21297046303","23000000003","20720345928","20790723057","21304553985","21191902726");//62個
         */
        List<Map<String, Object>> res = repository.findGeneratorContractInfo(startTime, endTime);
        //System.out.println("res size:" + res.size());

        // assertion
        if (devMark) {
            assertEquals(740,
                    res.size()); // ap.TRIAL_RUN_CAPACITY >= 0 and (ap.LICENSE_CAPACITY + ap.TRIAL_RUN_CAPACITY) > 0
            // 741 -> 734 考慮全部 ami 電號 有 17個 發電電號 有 12個
        } else if (tpcMark) {
            assertEquals(26, res.size()); // 25
        } else {
            assertEquals(46, res.size());
        }
        assertEquals(11, res.get(0).size()); // GEN_ELEC_NO, SERVICE_ID, PWDS, GEN_NAME, DEVICE_ADDRESS
        // , EQUIP_VOLT_LEVEL[發電設備電壓層級] DEVICE_CAPACITY[裝置容量=APPLICATION_GENERATOR.LICENSE_CAPACITY] ENERGY_TYPE[能源別]
        // CONTRACT_EFFE_DATE[契約生效日期] TERMINATE_DATE[契約終止日期]
        // , generatorEntityId
    }

    @Test
    public void findGeneratorEntityNbsCustomerNumbersTest() {
        List<String> elecNos =
                List.of("21297046029", "21040974063", "20902063127", "21301329101", "21201441219", "18442600035"
                        , "23999999999", "20671959016", "23000000005", "20733704225", "21294694937", "20865770000",
                        "23000000002", "21061740086"
                        , "21273343108", "21050763092", "20727002703", "21297046201", "23999999998", "21306604992",
                        "23999999996", "21261949105"
                        , "21277864632", "21297046405", "21302242951", "21274248912", "20894366102", "20800185231",
                        "21151675157", "21151479251"
                        , "21291057985", "23999999997", "21155369996", "21015135704", "21201074271", "20651602912",
                        "21115084909", "21016505305"
                        , "21304746976", "21103615040", "20903010067", "23000000004", "21262077001", "20800397033",
                        "23000000001", "21166577968"
                        , "20726944608", "21191034918", "20790723068", "20891225606", "21030403118", "21297046109",
                        "20663509003", "20716844127"
                        , "20727017937", "21104079986", "21297046303", "23000000003", "20720345928", "20790723057",
                        "21304553985", "21191902726");//62個
        List<String> res = repository.findGeneratorEntityNbsCustomerNumbers(elecNos, startTime, endTime);
        //System.out.println(res + ", size:" + res.size());

        // assertion
        if (devMark) assertEquals(12, res.size()); // 12
    }

    @Test
    public void findLoadEntityNbsCustomerNumbersTest() {
        List<String> elecNos =
                List.of("18442600035", "23999999999", "20671959016", "21273343108", "20727002703", "18442600115"
                        , "23999999998", "23999999996", "21125488430", "21274248912", "20800185231", "20662554768",
                        "23999999997", "20651602912"
                        , "21115084909", "20800397033", "21272326979", "21191034918", "20790723068", "20656186010",
                        "20790723057");//21個
        List<String> res = repository.findLoadEntityNbsCustomerNumbers(elecNos, startTime, endTime);
        //System.out.println(res + ",size:" + res.size());

        // assertion
        if (devMark) assertEquals(6, res.size()); // 6
    }

    @Test
    public void findApplicationApplicantTpcCompanyTest() {
        Date startDate = new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime();
        Date endDate = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> info = repository.findApplicationApplicantTpcCompany(startDate, endDate);
        //System.out.println("info size:" + info.size());

        // assertion
        if (devMark) {
            assertTrue(info.size() >= 1067);
            assertEquals(1866, info.size()); // 1852 -> 1853 -> 1857 -> 1861 -> 1864
            Set<Long> appIds = new HashSet<>();
            for (Map<String, Object> m : info) {
                Long ids = (long) m.get("appId");
                appIds.add(ids);
            }
            //System.out.println("appIds:" + appIds);
            assertEquals(info.size(), appIds.size());
        } else if (tpcMark) {
            assertEquals(30, info.size()); // 27
        } else {
            assertEquals(35, info.size());
        }
        assertEquals(27, info.get(0).size());
    }

    @Test
    public void findApplicationGeneratorLoadElecNosTest() {
        Date startDate = new GregorianCalendar(2024, Calendar.JUNE, 1).getTime();
        Date endDate = new GregorianCalendar(2024, Calendar.JULY, 1).getTime();
        List<Map<String, Object>> res = repository.findApplicationGeneratorLoadElecNos(startDate, endDate);
        //System.out.println("res size:" + res.size());

        // assertion
        if (devMark) assertEquals(28550, res.size()); //28425->28547
    }

    @Test
    public void findGeneratorElecNoComApplicationIdTest() {
        Date startDate = new GregorianCalendar(2024, Calendar.AUGUST, 1).getTime();
        Date endDate = new GregorianCalendar(2024, Calendar.SEPTEMBER, 1).getTime();
        List<String> elecNo = List.of("04946986547", "08376175049", "08377070922", "10518602032", "10540664987"
                , "10623888006", "12400473286", "12407527081", "18442092110", "18442092154", "18442092165",
                "18442092278"
                , "18442092289", "18442092381", "20524573952", "20657023914", "20657023925");
        List<Map<String, Object>> info = repository.findGeneratorElecNoComApplicationId(elecNo, startDate, endDate);
        //System.out.println("info size:" + info.size());

        // assertion
        if (devMark) assertEquals(32, info.size());
    }

    @Test
    public void findLoadElecNoComApplicationIdTest() {
        Date startDate = new GregorianCalendar(2024, Calendar.AUGUST, 1).getTime();
        Date endDate = new GregorianCalendar(2024, Calendar.SEPTEMBER, 1).getTime();
        List<String> elecNo =
                List.of("00253041208", "00447580112", "00566630115", "00566652112", "00608165112", "00608270110"
                        , "00608280112", "00957000112", "00987304021", "00987304101", "01193510118", "04074055254",
                        "04074055356", "04193120119"
                        , "04231070133", "04414947152", "04553195158", "04946961150", "05052815112", "05461010116",
                        "05785219115", "06250022158"
                        , "06250999137", "06256225112", "06425470119", "06467028110", "06467077110", "06467077212",
                        "06485319110", "06485610114"
                        , "06485610216", "06710610113", "06710610135", "06816425113", "07329379604", "07614621135",
                        "07615680005", "07615680050"
                        , "07687878004", "10391807914", "10518601907", "11083498008", "11120889001", "12106582003",
                        "16040559109", "16056183906"
                        , "16277602115", "16455276118", "16460155511", "16463595303", "16484349400", "16485445514",
                        "16581921935", "16588071909"
                        , "16605801114", "16644008114", "18442092030", "18442134007", "20522460907", "21450409001");
        List<Map<String, Object>> info = repository.findLoadElecNoComApplicationId(elecNo, startDate, endDate);
        //System.out.println("info size:" + info.size());

        // assertion
        if (devMark) assertEquals(174, info.size());
    }

    @Test
    public void findParentIdByContractNosOnlineAtTest_s1() {
        // s1 36 -> 33個 DISTINCT contract_no - 2個沒有結帳資料 注意 settleContract 需要排序
        List<String> settleContractNos =
                List.of("1-10902-04-001", "1-10902-10-001", "1-11105-08-007", "1-11106-12-003", "1-11110-20-002",
                        "1-11110-20-003", "1-11110-20-004", "1-11110-20-005", "1-11110-20-007", "1-11112-21-002",
                        "1-11202-18-004", "1-11204-08-002", "1-11204-08-004", "1-11205-04-001", "1-11206-08-003",
                        "1-11206-09-004", "1-11206-10-003", "1-11206-20-004", "1-11208-08-003", "1-11208-08-006",
                        "1-11211-10-004", "1-11212-08-010", "1-11212-09-002", "1-11212-09-003", "1-11212-09-004",
                        "1-11301-08-004", "1-11307-10-002", "1-11307-18-002", "2-11112-07-001", "2-11112-18-001",
                        "4-11208-04-002");
        // 使用 11307-BSMI 起始時間 11101 結束 11306 contractNo 146 個
        //Date endDate = new GregorianCalendar(2024, Calendar.JULY, 1).getTime();
        List<String> contractNos =
                List.of("1-11011-07-000", "1-11009-08-000", "1-10904-14-000", "1-11104-08-001", "1-11105-21-001",
                        "1-10907-19-000", "1-11010-08-000", "1-11101-10-000", "1-11003-11-000", "1-11105-07-000",
                        "1-11103-08-000", "1-11106-17-001", "1-11103-09-001", "1-11009-18-000", "1-11110-14-001",
                        "1-11108-07-001", "1-11108-20-001", "1-11106-12-003", "1-11011-08-000", "1-11108-08-001",
                        "1-11012-19-000", "1-11109-12-002", "1-11106-07-001", "1-11109-10-004", "1-11210-12-003",
                        "1-11105-08-000", "1-11106-12-001", "1-11110-20-001", "1-11108-20-005", "1-11108-12-001",
                        "1-11107-12-005", "1-11106-19-001", "1-11109-10-003", "1-11104-11-000", "1-11112-19-003",
                        "1-11203-19-003", "1-11203-19-001", "1-11204-11-002", "1-11203-19-002", "1-11202-00-001",
                        "1-11111-10-002", "1-11202-20-001", "1-11111-10-005", "1-11108-08-002", "1-11112-19-001",
                        "1-11112-19-002", "1-11112-20-003", "1-11111-10-006", "1-11204-07-001", "1-11204-11-001",
                        "1-11205-20-002", "1-11111-08-001", "1-11109-05-002", "1-11204-01-001", "1-11111-10-003",
                        "1-11206-20-001", "1-11205-08-002", "1-11103-09-000", "1-11207-08-001", "1-11206-08-002",
                        "1-11206-20-005", "1-11208-08-005", "1-11207-08-006", "1-11206-16-002", "1-11205-10-004",
                        "1-11207-08-009", "1-11112-03-002", "1-11205-12-002", "1-11205-19-003", "1-11205-18-003",
                        "1-11206-19-006", "1-11110-18-003", "1-11109-10-002", "1-11208-12-004", "1-11208-12-003",
                        "1-11204-12-001", "1-11204-12-002", "1-11203-12-001", "1-11110-12-001", "1-11206-20-004",
                        "1-11112-10-003", "1-11205-18-001", "1-11203-12-002", "1-11201-18-001", "1-11109-20-002",
                        "1-11201-11-001", "1-11208-12-002", "1-11205-10-001", "1-11205-18-004", "1-11112-21-001",
                        "1-11112-20-001", "1-11206-07-004", "1-11208-12-005", "1-11208-08-007", "1-11206-07-005",
                        "4-11208-21-007", "4-11208-21-004", "4-11208-19-001", "4-11208-06-001", "1-11206-19-007",
                        "1-11203-08-006", "1-11208-09-001", "1-11109-18-002", "1-11207-12-001", "1-11207-20-002",
                        "1-11204-11-003", "1-11104-11-001", "1-11206-18-002", "1-11204-04-002", "1-11206-16-001",
                        "1-11303-04-001", "1-11302-20-001", "4-11211-19-001", "1-11211-07-002", "1-11302-08-002",
                        "1-11206-07-001", "1-11105-07-002", "1-11207-12-002", "1-11207-20-005", "1-11206-19-008",
                        "1-11111-10-001", "4-11208-21-005", "4-11208-08-001", "4-11208-21-002", "1-11206-20-003",
                        "1-11211-07-001", "1-11206-07-002", "1-11211-09-002", "1-11203-07-001", "4-11208-08-002",
                        "1-10907-08-000", "1-11206-12-001", "1-11206-10-012", "1-11207-20-007", "1-11111-08-002",
                        "1-11206-08-001", "1-11111-08-003", "1-11207-20-006", "1-11207-09-004", "1-11208-12-001",
                        "1-11206-08-009", "1-11207-12-003", "1-11210-12-002", "1-11110-07-001", "1-11208-19-002");
        Set<String> contractSet = new HashSet<>(contractNos);
        List<Map<String, Object>> info = repository.findParentIdByContractNos(settleContractNos, contractNos);
        //System.out.println("info size:" + info.size());

        // assertion
        if (devMark) {
            List<String> serviceIdInfo = new ArrayList<>();
            List<String> settleFatherInfo = new ArrayList<>();
            Map<String, Integer> contractCount = new HashMap<>();
            Map<Long, String> idFather = new HashMap<>();
            int count = 0;
            int idx = 0;
            String keepFa = "";
            for (Map<String, Object> m : info) {
                Long appId = (Long) m.get("appId");
                String contractNo = m.get("CONTRACT_NO").toString();
                String serviceId = (String) m.get("SERVICE_ID");
                String parentId = null == m.get("PARENT_ID") ? "" : m.get("PARENT_ID").toString();
                Long pIds = (Long) m.get("PId5");
                String fatherContract =
                        null == m.get("PARENT_ID") ? "" : null == m.get("PId2") ? m.get("fatherContract").toString()
                                : null == m.get("PId3") ? m.get("pContractNo3").toString() : null == m.get("PId4")
                                ? m.get("pContractNo4").toString() : null == m.get("PId5") ? m.get(
                                "pContractNo5").toString() : "";
                if (null == fatherContract) fatherContract = "";
                idFather.put(appId, fatherContract);
                if (fatherContract.isEmpty() && idFather.size() > 0) {
                    if (null != pIds) fatherContract = idFather.get(pIds);
                    if (null == fatherContract || fatherContract.isEmpty()) fatherContract = serviceId;
                    idFather.put(appId, fatherContract);
                }
                if (contractSet.contains(contractNo)) contractCount.put(contractNo, count);
                if (!fatherContract.isEmpty() && contractSet.contains(
                        fatherContract.substring(0, fatherContract.length() - 3))) {
                    keepFa = fatherContract;
                }
                if (idx < settleContractNos.size() && settleContractNos.get(idx).equals(contractNo)) {
                    settleFatherInfo.add(
                            appId + "~" + serviceId + "~" + contractNo + "~fa~" + fatherContract + "~kBs~" + keepFa + "~count~" + count);
                    idx++;
                }
                serviceIdInfo.add(appId + "~" + serviceId + "~" + contractNo + "~" + parentId + "~" + fatherContract);
                if (idx == settleContractNos.size()) break;
                //idServiceId.put(count+"~"+appId
                //        , serviceId+"~"+parentId+"~"+submitAt+"~"+contractStart+"~"+contractEnd+"~"+onlineAt+"~"+contractStatus+"~"+fatherContract);
                //log.info(count+"~"+appId+"~"+serviceId+"~"+parentId+"~"+submitAt+"~"+contractStart+"~"+contractEnd+"~"+onlineAt+"~"+contractStatus+"~"+fatherContract);
                count++;
                keepFa = "";
            }
            //System.out.println("serviceIdInfo:" + serviceIdInfo + ",size:" + serviceIdInfo.size() + ",settleContractNos size:" + settleContractNos.size());
            //System.out.println("settleFatherInfo:" + settleFatherInfo + ",size:" + settleContractNos.size() + ", BsmicontractNos size:" + contractNos.size());
            //System.out.println("idFather:" + idFather + ",size:" + idFather.size());
            //System.out.println("contractCount:" + contractCount + ",size:" + contractCount.size());
            assertEquals(settleContractNos.size(), settleFatherInfo.size());
            String[] settleInfo = settleFatherInfo.get(1).split("~");
            assertEquals("1-10902-10-001", settleInfo[2]); // in settleContract
            assertEquals("1-10902-10-000-01", settleInfo[4]); // fatherContract
            assertEquals("", settleInfo[6]); // in applyContract
            settleInfo = settleFatherInfo.get(2).split("~");
            assertEquals("1-11105-08-007", settleInfo[2]); // in settleContract
            assertEquals("1-10907-08-000-01", settleInfo[6]); // in applyContract
            assertNotEquals(settleInfo[2], settleInfo[6].substring(0, settleInfo[6].length() - 3));
            settleInfo = settleFatherInfo.get(3).split("~");
            assertEquals("1-11106-12-003", settleInfo[2]); // in settleContract
            assertEquals("1-11106-12-003-00", settleInfo[6]); // in applyContract
            settleInfo = settleFatherInfo.get(17).split("~");
            assertEquals("1-11206-20-004", settleInfo[2]); // in settleContract
            assertEquals("1-11206-20-004-00", settleInfo[6]); // in applyContract
            settleInfo = settleFatherInfo.get(settleContractNos.size() - 1).split("~");
            assertEquals("4-11208-04-002", settleInfo[2]); // in settleContract
            assertEquals("4-11208-04-002-00", settleInfo[4]); // fatherContract
            assertEquals("", settleInfo[6]); // in applyContract
        } else if (tpcMark) {
            assertEquals(20, info.size());
        } else assertEquals(33, info.size());

    }

    @Test
    public void findParentIdByContractNosOnlineAtTest_pwoms() {
        // pwoms 12 個結帳契約 注意 settleContract 需要排序
        List<String> settleContractNos =
                List.of("1-11206-15-002", "1-11307-21-001", "1-11307-21-002", "2-11111-18-001", "2-11311-23-001",
                        "3-11308-23-002", "4-44401-08-003", "4-44401-09-003", "4-44401-19-002", "4-44401-19-004",
                        "4-44401-21-007", "4-44401-21-008");
        // 使用 11307-BSMI 起始時間 11101 結束 11306 contractNo 146 個
        //Date endDate = new GregorianCalendar(2024, Calendar.JULY, 1).getTime();
        List<String> contractNos =
                List.of("1-11011-07-000", "1-11009-08-000", "1-10904-14-000", "1-11104-08-001", "1-11105-21-001",
                        "1-10907-19-000", "1-11010-08-000", "1-11101-10-000", "1-11003-11-000", "1-11105-07-000",
                        "1-11103-08-000", "1-11106-17-001", "1-11103-09-001", "1-11009-18-000", "1-11110-14-001",
                        "1-11108-07-001", "1-11108-20-001", "1-11106-12-003", "1-11011-08-000", "1-11108-08-001",
                        "1-11012-19-000", "1-11109-12-002", "1-11106-07-001", "1-11109-10-004", "1-11210-12-003",
                        "1-11105-08-000", "1-11106-12-001", "1-11110-20-001", "1-11108-20-005", "1-11108-12-001",
                        "1-11107-12-005", "1-11106-19-001", "1-11109-10-003", "1-11104-11-000", "1-11112-19-003",
                        "1-11203-19-003", "1-11203-19-001", "1-11204-11-002", "1-11203-19-002", "1-11202-00-001",
                        "1-11111-10-002", "1-11202-20-001", "1-11111-10-005", "1-11108-08-002", "1-11112-19-001",
                        "1-11112-19-002", "1-11112-20-003", "1-11111-10-006", "1-11204-07-001", "1-11204-11-001",
                        "1-11205-20-002", "1-11111-08-001", "1-11109-05-002", "1-11204-01-001", "1-11111-10-003",
                        "1-11206-20-001", "1-11205-08-002", "1-11103-09-000", "1-11207-08-001", "1-11206-08-002",
                        "1-11206-20-005", "1-11208-08-005", "1-11207-08-006", "1-11206-16-002", "1-11205-10-004",
                        "1-11207-08-009", "1-11112-03-002", "1-11205-12-002", "1-11205-19-003", "1-11205-18-003",
                        "1-11206-19-006", "1-11110-18-003", "1-11109-10-002", "1-11208-12-004", "1-11208-12-003",
                        "1-11204-12-001", "1-11204-12-002", "1-11203-12-001", "1-11110-12-001", "1-11206-20-004",
                        "1-11112-10-003", "1-11205-18-001", "1-11203-12-002", "1-11201-18-001", "1-11109-20-002",
                        "1-11201-11-001", "1-11208-12-002", "1-11205-10-001", "1-11205-18-004", "1-11112-21-001",
                        "1-11112-20-001", "1-11206-07-004", "1-11208-12-005", "1-11208-08-007", "1-11206-07-005",
                        "4-11208-21-007", "4-11208-21-004", "4-11208-19-001", "4-11208-06-001", "1-11206-19-007",
                        "1-11203-08-006", "1-11208-09-001", "1-11109-18-002", "1-11207-12-001", "1-11207-20-002",
                        "1-11204-11-003", "1-11104-11-001", "1-11206-18-002", "1-11204-04-002", "1-11206-16-001",
                        "1-11303-04-001", "1-11302-20-001", "4-11211-19-001", "1-11211-07-002", "1-11302-08-002",
                        "1-11206-07-001", "1-11105-07-002", "1-11207-12-002", "1-11207-20-005", "1-11206-19-008",
                        "1-11111-10-001", "4-11208-21-005", "4-11208-08-001", "4-11208-21-002", "1-11206-20-003",
                        "1-11211-07-001", "1-11206-07-002", "1-11211-09-002", "1-11203-07-001", "4-11208-08-002",
                        "1-10907-08-000", "1-11206-12-001", "1-11206-10-012", "1-11207-20-007", "1-11111-08-002",
                        "1-11206-08-001", "1-11111-08-003", "1-11207-20-006", "1-11207-09-004", "1-11208-12-001",
                        "1-11206-08-009", "1-11207-12-003", "1-11210-12-002", "1-11110-07-001", "1-11208-19-002");
        Set<String> contractSet = new HashSet<>(contractNos);
        List<Map<String, Object>> info = repository.findParentIdByContractNos(settleContractNos, contractNos);
        //System.out.println("info size:" + info.size());

        // assertion
        if (devMark) {
            List<String> serviceIdInfo = new ArrayList<>();
            List<String> settleFatherInfo = new ArrayList<>();
            Map<String, Integer> contractCount = new HashMap<>();
            Map<Long, String> idFather = new HashMap<>();
            int count = 0;
            int idx = 0;
            String keepFa = "";
            for (Map<String, Object> m : info) {
                Long appId = (Long) m.get("appId");
                String contractNo = m.get("CONTRACT_NO").toString();
                String serviceId = (String) m.get("SERVICE_ID");
                String parentId = null == m.get("PARENT_ID") ? "" : m.get("PARENT_ID").toString();
                Long pIds = (Long) m.get("PId5");
                String fatherContract =
                        null == m.get("PARENT_ID") ? "" : null == m.get("PId2") ? m.get("fatherContract").toString()
                                : null == m.get("PId3") ? m.get("pContractNo3").toString() : null == m.get("PId4")
                                ? m.get("pContractNo4").toString() : null == m.get("PId5") ? m.get(
                                "pContractNo5").toString() : "";
                if (null == fatherContract) fatherContract = "";
                idFather.put(appId, fatherContract);
                if (fatherContract.isEmpty() && idFather.size() > 0) {
                    if (null != pIds) fatherContract = idFather.get(pIds);
                    if (null == fatherContract || fatherContract.isEmpty()) fatherContract = serviceId;
                    idFather.put(appId, fatherContract);
                }
                if (contractSet.contains(contractNo)) contractCount.put(contractNo, count);
                if (!fatherContract.isEmpty() && contractSet.contains(
                        fatherContract.substring(0, fatherContract.length() - 3))) {
                    keepFa = fatherContract;
                }
                if (idx < settleContractNos.size() && settleContractNos.get(idx).equals(contractNo)) {
                    settleFatherInfo.add(
                            appId + "~" + serviceId + "~" + contractNo + "~fa~" + fatherContract + "~kBs~" + keepFa + "~count~" + count);
                    idx++;
                }
                serviceIdInfo.add(appId + "~" + serviceId + "~" + contractNo + "~" + parentId + "~" + fatherContract);
                if (idx == settleContractNos.size()) break;
                count++;
                keepFa = "";
            }
            //System.out.println("serviceIdInfo:" + serviceIdInfo + ",size:" + serviceIdInfo.size() + ",settleContractNos size:" + settleContractNos.size());
            //System.out.println("settleFatherInfo:" + settleFatherInfo + ",size:" + settleContractNos.size() + ", BsmicontractNos size:" + contractNos.size());
            //System.out.println("idFather:" + idFather + ",size:" + idFather.size());
            //System.out.println("contractCount:" + contractCount + ",size:" + contractCount.size());
            assertEquals(settleContractNos.size(), settleFatherInfo.size());
            String[] settleInfo = settleFatherInfo.get(0).split("~");
            assertEquals("1-11206-15-002", settleInfo[2]); // in settleContract
            assertEquals("1-11206-15-002-00", settleInfo[4]); // fatherContract
            assertEquals("", settleInfo[6]); // in applyContract
            settleInfo = settleFatherInfo.get(5).split("~");
            assertEquals("3-11308-23-002", settleInfo[2]); // in settleContract
            assertEquals("3-11308-23-002-00", settleInfo[4]); // fatherContract
            assertEquals("", settleInfo[6]); // in applyContract
            settleInfo = settleFatherInfo.get(settleContractNos.size() - 1).split("~");
            assertEquals("4-44401-21-008", settleInfo[2]); // in settleContract
            assertEquals("4-44401-21-008-00", settleInfo[4]); // fatherContract
            assertEquals("", settleInfo[6]); // in applyContract
        } else if (tpcMark) assertEquals(0, info.size());
        else assertEquals(2, info.size());
    }

    @Test
    public void countApplicantEntityByTypeTest() {
        //Date start = new GregorianCalendar(2023, Calendar.DECEMBER, 1).getTime();
        Date date = new GregorianCalendar(2024, Calendar.DECEMBER, 1).getTime();
        List<Map<String, Object>> counts = repository.countApplicantEntityByType(date, 11311);
        // assertion
        assertEquals(3, counts.size());
        //Map<String, Object> ms = counts.get(0);
        //assertEquals(0, ms.get("office"));  // 大於1個的虛擬集團數量
        //assertEquals(1, ms.get("sale"));
        //assertEquals(1, ms.get("gen"));
        //assertNull(ms.get("device"));
        Map<String, Object> ms = counts.get(0);
        assertEquals(1, ms.get("office"));  // 公報
        assertEquals(36, ms.get("sale"));
        assertEquals(62, ms.get("gen"));
        assertEquals(59, ms.get("device"));
        ms = counts.get(1);
        if (devMark) {
            assertEquals(2, ms.get("office"));  // DB directly
            assertEquals(37, ms.get("sale")); //sale: APPLICANT_TYPE.ID = 2 - 再生能源售電業
            assertEquals(63, ms.get("gen")); // gen: APPLICANT_TYPE.ID = 1 - 再生能源發電業
            assertEquals(54, ms.get("device")); // device: APPLICANT_TYPE.ID = 3 - 自用發電設備設置者
            ms = counts.get(2);
            assertEquals(3, ms.get("office"));  // DB virtual
            assertEquals(36, ms.get("sale")); //sale: APPLICANT_TYPE.ID = 2 - 再生能源售電業
            assertEquals(62, ms.get("gen")); // gen: APPLICANT_TYPE.ID = 1 - 再生能源發電業
            assertEquals(54, ms.get("device")); // device: APPLICANT_TYPE.ID = 3 - 自用發電設備設置者
        } else if (tpcMark) {
            assertEquals(2, ms.get("office"));  // DB directly
            assertEquals(4, ms.get("sale")); //sale: APPLICANT_TYPE.ID = 2 - 再生能源售電業
            assertEquals(2, ms.get("gen")); // gen: APPLICANT_TYPE.ID = 1 - 再生能源發電業
            assertEquals(3, ms.get("device")); // device: APPLICANT_TYPE.ID = 3 - 自用發電設備設置者
            ms = counts.get(2);
            assertEquals(3, ms.get("office"));  // DB virtual
            assertEquals(4, ms.get("sale")); //sale: APPLICANT_TYPE.ID = 2 - 再生能源售電業
            assertEquals(2, ms.get("gen")); // gen: APPLICANT_TYPE.ID = 1 - 再生能源發電業
            assertEquals(3, ms.get("device")); // device: APPLICANT_TYPE.ID = 3 - 自用發電設備設置者
        } else {
            assertEquals(2, ms.get("office"));  // DB directly
            assertEquals(8, ms.get("sale")); //sale: APPLICANT_TYPE.ID = 2 - 再生能源售電業
            assertEquals(4, ms.get("gen")); // gen: APPLICANT_TYPE.ID = 1 - 再生能源發電業
            assertEquals(4, ms.get("device")); // device: APPLICANT_TYPE.ID = 3 - 自用發電設備設置者
            ms = counts.get(2);
            assertEquals(3, ms.get("office"));  // DB virtual
            assertEquals(8, ms.get("sale")); //sale: APPLICANT_TYPE.ID = 2 - 再生能源售電業
            assertEquals(4, ms.get("gen")); // gen: APPLICANT_TYPE.ID = 1 - 再生能源發電業
            assertEquals(4, ms.get("device")); // device: APPLICANT_TYPE.ID = 3 - 自用發電設備設置者
        }
    }

    @Test
    public void countApplicantEntityByTypeTest_not_bulletin() {
        //Date start = new GregorianCalendar(2023, Calendar.DECEMBER, 1).getTime();
        Date date = new GregorianCalendar(2021, Calendar.DECEMBER, 1).getTime();
        List<Map<String, Object>> counts = repository.countApplicantEntityByType(date, 11011);
        // assertion
        assertEquals(2, counts.size());
        //Map<String, Object> ms = counts.get(0);
        //assertEquals(0, ms.get("office")); //
        //assertEquals(1, ms.get("sale"));
        //assertEquals(1, ms.get("gen"));
        //assertNull(ms.get("device"));
        Map<String, Object> ms = counts.get(0);
        assertEquals(2, ms.get("office"));  // DB directly
        if (devMark) {
            assertEquals(5, ms.get("sale"));
            assertEquals(10, ms.get("gen"));
            assertEquals(1, ms.get("device"));
            ms = counts.get(1);
            assertEquals(3, ms.get("office"));  // DB virtual
            assertEquals(5, ms.get("sale")); //sale: APPLICANT_TYPE.ID = 2 - 再生能源售電業
            assertEquals(10, ms.get("gen")); // gen: APPLICANT_TYPE.ID = 1 - 再生能源發電業
            assertEquals(1, ms.get("device")); // device: APPLICANT_TYPE.ID = 3 - 自用發電設備設置者
        } else {
            assertEquals(0, ms.get("sale"));
            assertEquals(0, ms.get("gen"));
            assertEquals(0, ms.get("device"));
            ms = counts.get(1);
            assertEquals(3, ms.get("office"));  // DB virtual
            assertEquals(0, ms.get("sale")); //sale: APPLICANT_TYPE.ID = 2 - 再生能源售電業
            assertEquals(0, ms.get("gen")); // gen: APPLICANT_TYPE.ID = 1 - 再生能源發電業
            assertEquals(0, ms.get("device")); // device: APPLICANT_TYPE.ID = 3 - 自用發電設備設置者
        }
    }

    @Test
    public void insertServiceDateTest() throws Exception {
        Date ss = officialApplicationQuantityRepository.findMaxServiceDate();
        String the = dateStr(getMonthFirstDate(new Date()).getTime());
        String[] sc = ss.toString().split("-");
        int sm = Integer.parseInt(sc[1]);
        int sy = Integer.parseInt(sc[0]);
        if (12 == sm) {
            sm = 0;
            sy += 1;
        }
        Date saf = new GregorianCalendar(sy, sm, 1).getTime();
        String ssStr = dateStr(getMonthFirstDate(saf).getTime());
        //System.out.println("ss:" + ss + ", after start:" + ssStr + ", the:" + the + ",same:" + ss.toString().equals(the));
        List<Date> dd = !ss.toString().equals(the) ? getMonthRange(ssStr, the) : new ArrayList<>();

        if (3 == dd.size()) {
            Integer aa = officialApplicationQuantityRepository.insertServiceDate3(dd.get(0), dd.get(1), dd.get(2));
            //System.out.println("aa3:" + aa);
        } else if (2 == dd.size()) {
            Integer aa = officialApplicationQuantityRepository.insertServiceDate2(dd.get(0), dd.get(1));
            //System.out.println("aa2:" + aa);
        } else if (!dd.isEmpty()) { // 只加一條
            Integer aa = officialApplicationQuantityRepository.insertServiceDate(dd.get(0));
            //System.out.println("aa1:" + aa);
        } else log.info("no month can add");
        // assertion
        for (int i = 0; i < dd.size(); i++) {
            OfficialApplicationQuantity oo = officialApplicationQuantityRepository.findById(dd.get(i)).orElse(null);
            assertNotNull(oo);
        }
    }

    @Test
    public void OfficialApplicationQuantityRepositoryTest() throws Exception {
        Date theDate = new GregorianCalendar(2025, Calendar.MARCH, 1).getTime();
        OfficialApplicationQuantity dd = officialApplicationQuantityRepository.findById(theDate).orElse(null);
        assertEquals("2025-03-01", dd.getServiceDate().toString().split(" ")[0]);
        theDate = new GregorianCalendar(2024, Calendar.DECEMBER, 1).getTime();
        Calendar date = getMonthFirstDate(theDate);
        dd = officialApplicationQuantityRepository.findById(date.getTime()).orElse(null);
        assertEquals(theDate, dd.getServiceDate());
        theDate = new GregorianCalendar(2025, Calendar.FEBRUARY, 1).getTime();
        date = getMonthFirstDate(theDate);
        dd = officialApplicationQuantityRepository.findById(date.getTime()).orElse(null);
        if (null == dd) {
            OfficialApplicationQuantity uu = OfficialApplicationQuantity.builder().serviceDate(date.getTime()).build();
            officialApplicationQuantityRepository.save(uu);
        }
        //if (null != dd) System.out.println("[have exist date]" + dd);
    }

    @Test
    public void countApplicationByMonthRangeTest() throws Exception {
        Date startDate = new GregorianCalendar(2024, Calendar.MARCH, 1).getTime();
        Date endDate = getMonthFirstDate(new Date()).getTime();
        List<Map<String, Object>> res = repository.countApplicationByMonthRange(startDate, endDate);
        // assertion
        assertEquals(14, res.size()); // 13
        assertEquals(6, res.get(0).size());
        Map<String, Object> m = res.get(4);
        assertEquals("629", m.get("TOTAL").toString());
        if (devMark) {
            for (int i = 0; i < res.size(); i++) {
                if (m.get("SERVICE_DATE").toString().equals("2025-03-01")) {
                    assertNull(m.get("TOTAL"));
                    assertEquals("7", m.get("new").toString());
                    assertEquals("2", m.get("exist").toString());
                    assertEquals("0", m.get("change").toString());
                }
            }
        } else if (tpcMark) {
            for (int i = 0; i < res.size(); i++) {
                if (m.get("SERVICE_DATE").toString().equals("2025-03-01")) {
                    assertNull(m.get("TOTAL"));
                    assertEquals("2", m.get("new").toString());
                    assertEquals("1", m.get("exist").toString());
                    assertEquals("0", m.get("change").toString());
                }
            }
        } else {
            for (int i = 0; i < res.size(); i++) {
                if (m.get("SERVICE_DATE").toString().equals("2025-03-01")) {
                    assertNull(m.get("TOTAL"));
                    assertEquals("0", m.get("new").toString());
                    assertEquals("0", m.get("exist").toString());
                    assertEquals("0", m.get("change").toString());
                }
            }
        }
    }

    @Test
    public void countApplicationStatusTest() {
        Date tt = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> res = repository.countApplicationStatus(tt);

        // assertion
        assertEquals(5, res.size());
        if (devMark) {
            assertEquals("482", res.get(0).get("review").toString()); // 473 -> 475 -> 476 -> 480
            assertEquals("18", res.get(1).get("back").toString()); // 19
            assertEquals("467", res.get(2).get("apply").toString()); // 457 -> 462 -> 465
            assertEquals("4", res.get(3).get("sign").toString());
            assertEquals("576", res.get(4).get("operator").toString());
        } else if (tpcMark) {
            assertEquals("0", res.get(0).get("review").toString()); // 1
            assertEquals("1", res.get(1).get("back").toString()); // 0
            assertEquals("0", res.get(2).get("apply").toString());
            assertEquals("5", res.get(3).get("sign").toString()); // 4
            assertEquals("25", res.get(4).get("operator").toString()); // 22
        } else {
            assertEquals("1", res.get(0).get("review").toString());
            assertEquals("0", res.get(1).get("back").toString());
            assertEquals("0", res.get(2).get("apply").toString());
            assertEquals("0", res.get(3).get("sign").toString());
            assertEquals("30", res.get(4).get("operator").toString());
        }
    }

    @Test
    public void countContractStatusTest() {
        Date tt = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> out = repository.countContractStatus(tt);
        // assertion
        assertEquals(3, out.size());
        if (devMark) {
            assertEquals("4", out.get(0).get("sign").toString());
            assertEquals("0", out.get(1).get("change").toString());
            assertEquals("0", out.get(out.size() - 1).get("finish").toString());
        } else if (tpcMark) {
            assertEquals("5", out.get(0).get("sign").toString()); // 4
            assertEquals("0", out.get(1).get("change").toString());
            assertEquals("2", out.get(out.size() - 1).get("finish").toString());
        } else {
            assertEquals("0", out.get(0).get("sign").toString());
            assertEquals("0", out.get(1).get("change").toString());
            assertEquals("0", out.get(out.size() - 1).get("finish").toString());
        }
    }

    @Test // #23 各區處 轉供中 契約數量
    public void countMainTpcApplicationTest() {
        List<Map<String, Object>> res = repository.countMainTpcApplication();
        // assertion
        int count = 0;
        for (int i = 0; i < res.size(); i++)
            count += (Integer) res.get(i).get("operator");
        if (devMark) {
            assertEquals(23, res.size());
            assertEquals(576, count);
        } else if (tpcMark) {
            assertEquals(10, res.size());
            assertEquals(25, count);
        } else {
            assertEquals(9, res.size());
            assertEquals(30, count);
        }
    }

    @Test
    public void findAllByContractNoWithoutAgGrow() {
        repository.findAllByContractNoWithoutAgGrow(List.of(13L), new Date());
    }

    @Test
    void findTypeById() {
        String type = repository.findTypeById(104L);
        log.info(type);
    }

    @Test
    public void computeMonthRangeTest() throws Exception {
        // same year
        String theS = dateStr(getMonthFirstDate(new GregorianCalendar(2024, 0, 1).getTime()).getTime());
        int randomM = randomInt() % 10 + 2; // 2~ 12 月 = 1 ~ 11
        String theE = dateStr(getMonthFirstDate(new GregorianCalendar(2024, randomM, 1).getTime()).getTime());
        List<Date> dd = getMonthRange(theS, theE);
        //System.out.println("date list:" + dd + ", endM:" + randomM);
        assertEquals(randomM + 1, dd.size());
        // over one Year
        //randomM = (int) (Math.random()*9); // 0 ~ 8 1月 ~ 9月
        theS = dateStr(getMonthFirstDate(new GregorianCalendar(2024, randomM, 1).getTime()).getTime());
        int randomE = randomInt() % 2; // 0 ~ 2 1月 ~ 3月
        theE = dateStr(getMonthFirstDate(new GregorianCalendar(2025, randomE, 1).getTime()).getTime());
        dd = getMonthRange(theS, theE);
        //System.out.println("date list:" + dd + ", s:" + randomM + ", e:" + randomE);
        assertEquals(12 - randomM + randomE + 1, dd.size());
        // over 2 year
        theS = dateStr(getMonthFirstDate(new GregorianCalendar(2023, randomM, 1).getTime()).getTime());
        randomE = randomInt() % 2; // 0 ~ 2 1月 ~ 3月
        theE = dateStr(getMonthFirstDate(new GregorianCalendar(2025, randomE, 1).getTime()).getTime());
        dd = getMonthRange(theS, theE);
        //System.out.println("date list:" + dd + ", s:" + randomM + ", e:" + randomE);
        assertEquals(12 - randomM + 12 + randomE + 1, dd.size());
        // over 5 year
        theS = dateStr(getMonthFirstDate(new GregorianCalendar(2020, randomM, 1).getTime()).getTime());
        randomE = randomInt() % 2; // 0 ~ 2 1月 ~ 3月
        theE = dateStr(getMonthFirstDate(new GregorianCalendar(2025, randomE, 1).getTime()).getTime());
        dd = getMonthRange(theS, theE);
        //System.out.println("date list:" + dd + ", s:" + randomM + ", e:" + randomE);
        assertEquals(12 - randomM + 12 * 4 + randomE + 1, dd.size());
    }

    private List<Date> getMonthRange(String dateS, String dateE) {
        String[] sd = dateS.split("-");
        Integer sy = Integer.parseInt(sd[0]);
        Integer sm = Integer.parseInt(sd[1]);
        String[] td = dateE.split("-");
        Integer ty = Integer.parseInt(td[0]);
        Integer tm = Integer.parseInt(td[1]);
        List<Date> dd = new ArrayList<>();
        if (sy.equals(ty)) {
            for (int i = sm; i <= tm; i++) {
                dd.add(new GregorianCalendar(sy, i - 1, 1).getTime());
            }
        } else {
            for (int j = sy; j <= ty; j++) {
                if (j == sy) {
                    for (int i = sm - 1; i < 12; i++)
                        dd.add(new GregorianCalendar(j, i, 1).getTime());
                } else if (j == ty) {
                    for (int i = 0; i < tm; i++)
                        dd.add(new GregorianCalendar(j, i, 1).getTime());
                } else {
                    for (int i = 0; i < 12; i++)
                        dd.add(new GregorianCalendar(j, i, 1).getTime());
                }
            }
        }
        return dd;
    }

    private String dateStr(Date date) throws Exception {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(date);
    }

    private Calendar getMonthFirstDate(Date theDate) throws Exception {
        Calendar date = Calendar.getInstance();
        date.setTime(theDate);

        date.set(Calendar.DAY_OF_MONTH, 1);
        date.set(Calendar.HOUR_OF_DAY, 0);
        date.set(Calendar.MINUTE, 0);
        date.set(Calendar.SECOND, 0);
        date.set(Calendar.MILLISECOND, 0);
        return date;
    }


    @Test
    public void findApplicationIdByApplicantIdAndAccountIdTest() {
        var count = repository.findApplicationIdByApplicantIdAndAccountId(11L, 31L);
        log.info(count);
    }

    @Test
    public void findContractRelation() {
        var result = repository.findContractRelation(List.of(78L), null);
        log.info(result);
        var result2 = repository.findContractRelation(List.of(78L), 5);
        log.info(result2);
    }

    @Test
    public void findMaxVersion() {
        var number = repository.findMaxUseVersionWithContractNo("1-11403-00-008");
        log.info(number);
    }
}
