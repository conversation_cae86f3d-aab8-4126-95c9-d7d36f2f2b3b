package tw.com.taipower.data.repository.pwoms;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.data.AbstractRepositoryTest;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@Log4j2
//@ActiveProfiles("mte-test-s1")
@ActiveProfiles("mte-t1-test-s1")
//@ActiveProfiles("ae-dev")
//@ActiveProfiles("ae-s1")
//@ActiveProfiles("mte-t2-tpc")
class ApplicationMonthlyCapacitySettlementRepositoryTest extends AbstractRepositoryTest {

    @Autowired
    private ResourceLoader resourceLoader = null;

    @Autowired
    ApplicationMonthlyCapacitySettlementRepository repository;

    boolean devMark = false;
    boolean mteT1Mark = false;
    boolean tpcMark = false;
    Date billDate = null;

    @BeforeEach
    public void setUp() throws Exception {
        devMark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("ae-dev");
        mteT1Mark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t1-test-s1");
        tpcMark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t2-tpc");

        billDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
    }

    @Test
    void countCurrentErpModeTest() {
        Date nextMon = tpcMark? new GregorianCalendar(2025, Calendar.JULY, 1).getTime()
                : new GregorianCalendar(2025, Calendar.JUNE, 1).getTime();
        Integer cc = repository.countCurrentErpMode(billDate, nextMon);
        // assertion
        if (mteT1Mark) assertEquals(79, cc);
        else assertEquals(0, cc);
    }

    @Test
    void sumKwhADSTByBillingDateTest() { // #2 會計室
        List<Map<String, Object>> info = repository.sumKwhADSTByBillingDate(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(4, info.size());
            Map<String, Object> m = info.get(0);
            assertEquals(2, m.size());
            assertEquals("9697358", m.get("kwh").toString()); // 3月+4月+5月
            assertEquals("12", m.get("powerType").toString());
            m = info.get(1);
            assertEquals("17065693", m.get("kwh").toString()); // 3月+4月+5月
            assertEquals("3", m.get("powerType").toString());
            m = info.get(2);
            assertNull(m.get("kwh"));
            assertEquals("2", m.get("powerType").toString());
            m = info.get(info.size()-1);
            assertEquals("1806076", m.get("kwh").toString());
            assertEquals("1", m.get("powerType").toString());
        } else if (mteT1Mark) {
            assertEquals(4, info.size());
            Map<String, Object> m = info.get(0);
            assertEquals(2, m.size());
            assertEquals("19568141", m.get("kwh").toString()); // 6841730 -> 6568006 + 250959(noErp) -> 7月+8月+9月
            assertEquals("12", m.get("powerType").toString());
            m = info.get(1);
            assertEquals("42715629", m.get("kwh").toString()); // 11500386 -> 11315933 + 250959(noErp) -> 7月+8月+9月
            assertEquals("3", m.get("powerType").toString());
            m = info.get(2);
            assertEquals("18151", m.get("kwh").toString());
            assertEquals("2", m.get("powerType").toString());
            m = info.get(info.size()-1);
            assertEquals("16836674", m.get("kwh").toString());
            assertEquals("1", m.get("powerType").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void sumMatchedKwByVoltageClassFuelLabelApplicationTypeTest() { // #23
        List<Map<String, Object>> info = repository.sumMatchedKwByVoltageClassFuelLabelApplicationType(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(12, info.size());
            assertEquals(5, info.get(0).size());
            //FUEL_LABEL[發電類型]  FUEL_FORM[能源來源] PWDS[直供 DS 轉供 PW 小額綠電 Q] VOLT_LEVEL_CLASS[發電端電壓別] matchedKw[電量(度)]
            Map<String, Object> m = info.get(0);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("水面型", m.get("FUEL_FORM").toString());
            assertEquals("DS", m.get("PWDS").toString());
            assertEquals("211973", m.get("matchedKw").toString());
            m = info.get(1);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("屋頂型", m.get("FUEL_FORM").toString());
            assertEquals("DS", m.get("PWDS").toString());
            assertEquals("5350286", m.get("matchedKw").toString());
            m = info.get(2);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("屋頂型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("725780", m.get("matchedKw").toString());
            m = info.get(3);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("陸域", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("4499644", m.get("matchedKw").toString());
            m = info.get(4);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertNull(m.get("FUEL_FORM"));
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("429286", m.get("matchedKw").toString()); // 2173746
            m = info.get(5);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("86256", m.get("matchedKw").toString());
            m = info.get(6);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("屋頂型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("173046", m.get("matchedKw").toString()); // 830485
            m = info.get(7);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertNull(m.get("FUEL_FORM"));
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("423766", m.get("matchedKw").toString());
            m = info.get(8);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("518310", m.get("matchedKw").toString()); // 2173746
            m = info.get(9);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("屋頂型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("3633346", m.get("matchedKw").toString()); // 9331430
            m = info.get(10);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("陸域", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("864000", m.get("matchedKw").toString());
            m = info.get(info.size()-1);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("Q", m.get("PWDS").toString());
            assertEquals("150000", m.get("matchedKw").toString());
        } else if (mteT1Mark) {
            assertEquals(12, info.size());
            assertEquals(5, info.get(0).size());
            //FUEL_LABEL[發電類型]  FUEL_FORM[能源來源] PWDS[直供 DS 轉供 PW 小額綠電 Q] VOLT_LEVEL_CLASS[發電端電壓別] matchedKw[電量(度)]
            Map<String, Object> m = info.get(0);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("其他", m.get("FUEL_FORM").toString());
            assertEquals("DS", m.get("PWDS").toString());
            assertEquals("5480129", m.get("matchedKw").toString());
            m = info.get(1);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("其他", m.get("FUEL_FORM").toString());
            assertEquals("DS", m.get("PWDS").toString());
            assertEquals("812534", m.get("matchedKw").toString());
            m = info.get(2);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("1905851", m.get("matchedKw").toString());
            m = info.get(3);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("陸域", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("1101212", m.get("matchedKw").toString());
            m = info.get(4);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertNull(m.get("FUEL_FORM"));
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("37382", m.get("matchedKw").toString()); // 2173746
            m = info.get(5);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("18151", m.get("matchedKw").toString());
            m = info.get(6);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("556530", m.get("matchedKw").toString()); // 830485
            m = info.get(7);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("屋頂型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("137211", m.get("matchedKw").toString());
            m = info.get(8);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertNull(m.get("FUEL_FORM"));
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("2136364", m.get("matchedKw").toString()); // 2173746
            m = info.get(9);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("22102412", m.get("matchedKw").toString()); // 9331430
            m = info.get(10);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("陸域", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("7679978", m.get("matchedKw").toString());
            m = info.get(info.size()-1);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("Q", m.get("PWDS").toString());
            assertEquals("747875", m.get("matchedKw").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void sumMatchedKwByApplicationIdTest() { // #22
        List<Map<String, Object>> res = repository.sumMatchedKwByApplicationId(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(68, res.size());
            for (int i =0;i<res.size();i++)
                assertEquals("2025-06-01", res.get(i).get("billDate").toString());
            assertEquals(4, res.get(0).size());
            Map<String, Object> m = res.get(0);
            assertEquals("73", m.get("ID").toString());
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("987193", m.get("matchedKw").toString());
            m = res.get(1);
            assertEquals("73", m.get("ID").toString());
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("1070816", m.get("matchedKw").toString());
            m = res.get(2);
            assertEquals("73", m.get("ID").toString());
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("1201791", m.get("matchedKw").toString());
            m = res.get(3);
            assertEquals("74", m.get("ID").toString());
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("717839", m.get("matchedKw").toString());
            m = res.get(4);
            assertEquals("74", m.get("ID").toString());
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("718689", m.get("matchedKw").toString());
            m = res.get(7);
            assertEquals("78", m.get("ID").toString());
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("144273", m.get("matchedKw").toString());
            m = res.get(13);
            assertEquals("80", m.get("ID").toString());
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("10796", m.get("matchedKw").toString());
            m = res.get(30);
            assertEquals("86", m.get("ID").toString()); // 小額綠電
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("50000", m.get("matchedKw").toString());
            m = res.get(31);
            assertEquals("86", m.get("ID").toString()); // 小額綠電
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("50000", m.get("matchedKw").toString());
            m = res.get(32);
            assertEquals("86", m.get("ID").toString()); // 小額綠電
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("50000", m.get("matchedKw").toString());
            m = res.get(34);
            assertEquals("88", m.get("ID").toString());
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("23797", m.get("matchedKw").toString());
            m = res.get(36);
            assertEquals("89", m.get("ID").toString()); // 彈性分配
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("864000", m.get("matchedKw").toString());
            m = res.get(62);
            assertEquals("102", m.get("ID").toString());
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("775977", m.get("matchedKw").toString());
            m = res.get(res.size() - 3);
            assertEquals("103", m.get("ID").toString());
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("99", m.get("matchedKw").toString());
            m = res.get(res.size() - 2);
            assertEquals("103", m.get("ID").toString());
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("98", m.get("matchedKw").toString());
            m = res.get(res.size() - 1);
            assertEquals("103", m.get("ID").toString());
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("98", m.get("matchedKw").toString());
        } else if (mteT1Mark) {
            assertEquals(82, res.size());
            for (int i =0;i<res.size();i++)
                assertEquals("2025-05-01", res.get(i).get("billDate").toString());
            assertEquals(4, res.get(0).size());
            Map<String, Object> m = res.get(0);
            assertEquals("1", m.get("ID").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("861172", m.get("matchedKw").toString());
            m = res.get(1);
            assertEquals("1", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("741419", m.get("matchedKw").toString());
            m = res.get(2);
            assertEquals("1", m.get("ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("680821", m.get("matchedKw").toString());
            m = res.get(4);
            assertEquals("2", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("6360", m.get("matchedKw").toString());
            m = res.get(13);
            assertEquals("6", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("5611142", m.get("matchedKw").toString());
            m = res.get(33);
            assertEquals("16", m.get("ID").toString()); // 小額綠電
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("250222", m.get("matchedKw").toString());
            m = res.get(36);
            assertEquals("17", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("13668", m.get("matchedKw").toString());
            m = res.get(39);
            assertEquals("19", m.get("ID").toString()); // 彈性分配
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("1391010", m.get("matchedKw").toString());
            m = res.get(76);
            assertEquals("34", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("171707", m.get("matchedKw").toString());
            m = res.get(res.size() - 3);
            assertEquals("35", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("13095", m.get("matchedKw").toString());
            m = res.get(res.size() - 2);
            assertEquals("35", m.get("ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("10859", m.get("matchedKw").toString());
            m = res.get(res.size() - 1);
            assertEquals("36", m.get("ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("34483", m.get("matchedKw").toString());
        } else assertEquals(0, res.size());
    }

    @Test
    public void sumMatchedKmByEachApplicationGeneratorIdTest() { // #13
        List<Map<String, Object>> res = repository.sumMatchedKmByEachApplicationGeneratorId(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(6, res.size());
            assertEquals(4, res.get(0).size());
            Map<String, Object> m = res.get(0);
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("2025-06-01", m.get("billDate").toString());
            assertEquals("4641560", m.get("matchedKw").toString()); // 9204521 -> 9020068(bill 5/2) + 250959(noErp 5/2)
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(1);
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("2025-06-01", m.get("billDate").toString());
            assertEquals("1705032", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(2);
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("2025-06-01", m.get("billDate").toString());
            assertEquals("2931544", m.get("matchedKw").toString());
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(3);
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("2025-06-01", m.get("billDate").toString());
            assertEquals("1789505", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(4);
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("2025-06-01", m.get("billDate").toString());
            assertEquals("3066330", m.get("matchedKw").toString()); // 11240598 -> 11275164(bill 5/3) + 250222(noErp 5/2)
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(res.size()-1);
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("2025-06-01", m.get("billDate").toString());
            assertEquals("2067722", m.get("matchedKw").toString()); // 11240598
            assertEquals("DS", m.get("PWDS").toString());
        } else if (mteT1Mark) {
            assertEquals(12, res.size()); // 10月-6月轉直 9月-6,5月轉直 8月-5月轉直 = 8個
            assertEquals(4, res.get(0).size());
            //serviceDate(Date), billDate(Date), MATCHED_KW[計費度數] PWDS[轉供直供 2,3 直供; 其他轉供]
            Map<String, Object> m = res.get(0);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("9271027", m.get("matchedKw").toString()); // 9204521 -> 9020068(bill 5/2) + 250959(noErp 5/2)
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(1);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("2295865", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(2);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("8941958", m.get("matchedKw").toString());
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(3);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("2295865", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(4);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("11525386", m.get("matchedKw").toString()); // 11240598 -> 11275164(bill 5/3) + 250222(noErp 5/2)
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(5);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("2132429", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(6);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("11233724", m.get("matchedKw").toString()); // 11240598
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(7);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("2132429", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(8);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("15626553", m.get("matchedKw").toString()); // 15409652 -> 15379859(bill 5/3) + 246694(noErp 5/2)
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(9);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("1864369", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(res.size()-2);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("15313153", m.get("matchedKw").toString());
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(res.size()-1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("1864369", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
        } else assertEquals(0, res.size());
    }

    @Test
    void sumMatchedKmGeneratorEndInfoTest() { // #14
        List<Map<String, Object>> res = repository.sumMatchedKmGeneratorEndInfo(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(45, res.size());
            Map<String, Object> m = res.get(0);
            assertNull(m.get("applicationGeneratorId"));
            assertNull(m.get("matchedKw"));
            assertNull(m.get("PWDS"));
            assertNull(m.get("PWDS_CONTRACT_TYPE"));
            assertNull(m.get("GEN_NAME"));
            assertNull(m.get("GEN_ELEC_NO"));
            assertNull(m.get("gEntityId"));
            assertEquals("2025-03-01", m.get("serviceStart").toString()); // 服務起始月初
            assertEquals("2025-06-01", m.get("serviceEndNext").toString()); // 服務結束隔月月初
            for (int i = 0; i < res.size(); i++)
                assertEquals("2025-06-01", res.get(i).get("billDate").toString());
            for (int i = 1; i < res.size(); i++) {
                m = res.get(i);
                assertNull(m.get("serviceStart"));
                assertNull(m.get("serviceEndNext"));
            }
            res.remove(0);
            assertEquals(44, res.size()); // app.TYPE != 4 // 51(2025-05) + 48(2025-04)= 99
            assertEquals(10, res.get(0).size());
            m = res.get(0);
            assertEquals("62", m.get("applicationGeneratorId").toString());
            assertEquals("55463", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供(一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("07729897768", m.get("GEN_ELEC_NO").toString());
            assertEquals("5815", m.get("gEntityId").toString());
            m = res.get(4);
            assertEquals("66", m.get("applicationGeneratorId").toString());
            assertEquals("65541", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供(一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("07729897815", m.get("GEN_ELEC_NO").toString());
            assertEquals("5819", m.get("gEntityId").toString());
            m = res.get(5);
            assertEquals("67", m.get("applicationGeneratorId").toString());
            assertEquals("38824", m.get("matchedKw").toString()); // 2024-07-01
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供(一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("07729897848", m.get("GEN_ELEC_NO").toString());
            assertEquals("5820", m.get("gEntityId").toString());
            m = res.get(19);
            assertEquals("81", m.get("applicationGeneratorId").toString());
            assertEquals("273415", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供(一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("18442092381", m.get("GEN_ELEC_NO").toString());
            assertEquals("5859", m.get("gEntityId").toString());
            m = res.get(20);
            assertEquals("85", m.get("applicationGeneratorId").toString());
            assertEquals("99720", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供(非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("存量能源股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("07338966170", m.get("GEN_ELEC_NO").toString());
            assertEquals("7122", m.get("gEntityId").toString());
            m = res.get(29);
            assertEquals("94", m.get("applicationGeneratorId").toString());
            assertEquals("150000", m.get("matchedKw").toString());
            assertEquals("Q", m.get("PWDS").toString());
            assertEquals("轉供(小額綠電）", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("台灣電力股份有限公司再生能源處", m.get("GEN_NAME").toString());
            assertEquals("08377135111", m.get("GEN_ELEC_NO").toString());
            assertEquals("7992", m.get("gEntityId").toString());
            m = res.get(33);
            assertEquals("102", m.get("applicationGeneratorId").toString());
            assertEquals("423766", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供(非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("星曄綠能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("20524573952", m.get("GEN_ELEC_NO").toString());
            assertEquals("7379", m.get("gEntityId").toString());
            m = res.get(res.size()-1);
            assertEquals("113", m.get("applicationGeneratorId").toString());
            assertEquals("295", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供(非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("同陽能源股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("01843330041", m.get("GEN_ELEC_NO").toString());
            assertEquals("6660", m.get("gEntityId").toString());
        } else if (mteT1Mark) {
            assertEquals(100, res.size());
            Map<String, Object> m = res.get(0);
            assertNull(m.get("applicationGeneratorId"));
            assertNull(m.get("matchedKw"));
            assertNull(m.get("PWDS"));
            assertNull(m.get("PWDS_CONTRACT_TYPE"));
            assertNull(m.get("GEN_NAME"));
            assertNull(m.get("GEN_ELEC_NO"));
            assertNull(m.get("gEntityId"));
            assertEquals("2024-07-01", m.get("serviceStart").toString()); // 服務起始月初
            assertEquals("2024-10-01", m.get("serviceEndNext").toString()); // 服務結束隔月月初
            for (int i = 0; i < 52; i++)
                assertEquals("2025-05-01", res.get(i).get("billDate").toString());
            for (int i = 52; i < res.size(); i++) {
                m = res.get(i);
                assertEquals("2025-04-01", m.get("billDate").toString());
                assertNull(m.get("PWDS"));
                assertNull(m.get("PWDS_CONTRACT_TYPE"));
                assertNull(m.get("GEN_NAME"));
                assertNull(m.get("GEN_ELEC_NO"));
                assertNull(m.get("gEntityId"));
            }
            for (int i = 1; i < res.size(); i++) {
                m = res.get(i);
                assertNull(m.get("serviceStart"));
                assertNull(m.get("serviceEndNext"));
            }
            res.remove(0);
            assertEquals(99, res.size()); // app.TYPE != 4 // 51(2025-05) + 48(2025-04)= 99
            assertEquals(10, res.get(0).size());
            // serviceDate(Date), billDate(Date), applicationGeneratorId(long) gEntityId(long)
            // -- MATCHED_KW[計費度數]  PWDS_CONTRACT_TYPE[說明: 契約類型] GEN_NAME[發電端名稱] GEN_ELEC_NO[發電端電號] PWDS[轉供直供 2,3 直供 其他轉供]
            m = res.get(0);
            assertEquals("1", m.get("applicationGeneratorId").toString());
            assertEquals("1295584", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供 (一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("18442092110", m.get("GEN_ELEC_NO").toString());
            assertEquals("26", m.get("gEntityId").toString());
            m = res.get(5);
            assertEquals("6", m.get("applicationGeneratorId").toString());
            assertEquals("277094", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供 (一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("18442092381", m.get("GEN_ELEC_NO").toString());
            assertEquals("46", m.get("gEntityId").toString());
            m = res.get(6);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("18151", m.get("matchedKw").toString()); // 2024-07-01
            assertEquals("5", m.get("PWDS").toString());
            assertEquals("轉供 (自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("信通交通器材股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("04946986547", m.get("GEN_ELEC_NO").toString());
            assertEquals("450", m.get("gEntityId").toString());
            m = res.get(18);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("747875", m.get("matchedKw").toString());
            assertEquals("Q", m.get("PWDS").toString());
            assertEquals("轉供 (小額綠電)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("台電公司南鹽光", m.get("GEN_NAME").toString());
            assertEquals("10623888006", m.get("GEN_ELEC_NO").toString());
            assertEquals("1282", m.get("gEntityId").toString());
            m = res.get(35);
            assertEquals("41", m.get("applicationGeneratorId").toString());
            assertEquals("186972", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供 (非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("台泥綠能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("08376141004", m.get("GEN_ELEC_NO").toString());
            assertEquals("487", m.get("gEntityId").toString());
            m = res.get(49);
            assertEquals("55", m.get("applicationGeneratorId").toString());
            assertEquals("37382", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供 (非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("存量能源股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("08369525025", m.get("GEN_ELEC_NO").toString());
            assertEquals("1048", m.get("gEntityId").toString());
            m = res.get(50);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("34483", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供 (非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("普雷嘉工程有限公司\t", m.get("GEN_NAME").toString());
            assertEquals("10641031703", m.get("GEN_ELEC_NO").toString());
            assertEquals("297", m.get("gEntityId").toString());
            m = res.get(51);
            assertEquals("1", m.get("applicationGeneratorId").toString());
            assertEquals("1295584", m.get("matchedKw").toString());
            m = res.get(56);
            assertEquals("6", m.get("applicationGeneratorId").toString());
            assertEquals("277094", m.get("matchedKw").toString());
            m = res.get(57);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("18151", m.get("matchedKw").toString()); // 2024-07-01
            m = res.get(70);
            assertEquals("25", m.get("applicationGeneratorId").toString()); // 2025-04 沒有 小額綠電 22
            assertEquals("72420", m.get("matchedKw").toString());
            m = res.get(86);
            assertEquals("44", m.get("applicationGeneratorId").toString()); // APPLICATION_ID = 22 APPLICATION_GENERATOR_ID = 41 2025-04 沒有結帳
            assertEquals("549934", m.get("matchedKw").toString());
            m = res.get(res.size()-2);
            assertEquals("55", m.get("applicationGeneratorId").toString());
            assertEquals("37382", m.get("matchedKw").toString());
            m = res.get(res.size()-1);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("34483", m.get("matchedKw").toString());
        } else assertEquals(0, res.size());
    }

    @Test // #14 調度處 發轉餘報表(單月查詢) 統計 - 再媒合表 餘電總和(一階餘電減去二階轉供)[再媒合未媒合電量]
    void sumGenMatchedRmUnmatchedRmByDateTest() {
        List<Map<String, Object>> res = repository.sumGenMatchedRmUnmatchedRmByDate(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(19, res.size());
            assertEquals(3, res.get(0).size()); // applicationGeneratorId, time, matchedRm, unmatchedRm
            Map<String, Object> m = res.get(0);
            assertEquals("85", m.get("applicationGeneratorId").toString());
            assertEquals("9218.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(1);
            assertEquals("86", m.get("applicationGeneratorId").toString());
            assertEquals("33031.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(2);
            assertEquals("87", m.get("applicationGeneratorId").toString());
            assertEquals("252.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(8);
            assertEquals("96", m.get("applicationGeneratorId").toString());
            assertEquals("26346.0000", m.get("matchedRm").toString());
            assertEquals("459.0000", m.get("unmatchedRm").toString());
            m = res.get(13);
            assertEquals("107", m.get("applicationGeneratorId").toString());
            assertEquals("20725.0000", m.get("matchedRm").toString());
            assertEquals("22487.0000", m.get("unmatchedRm").toString());
            m = res.get(15);
            assertEquals("109", m.get("applicationGeneratorId").toString());
            assertEquals("14153.0000", m.get("matchedRm").toString());
            assertEquals("2969.0000", m.get("unmatchedRm").toString());
            m = res.get(16);
            assertEquals("110", m.get("applicationGeneratorId").toString());
            assertEquals("20459.0000", m.get("matchedRm").toString());
            assertEquals("3907.0000", m.get("unmatchedRm").toString());
            m = res.get(res.size()-1);
            assertEquals("112", m.get("applicationGeneratorId").toString());
            assertEquals("58878.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
        } else if (mteT1Mark) {
            assertEquals(26, res.size());
            assertEquals(3, res.get(0).size()); // applicationGeneratorId, time, matchedRm, unmatchedRm
            Map<String, Object> m = res.get(0);
            assertEquals("8", m.get("applicationGeneratorId").toString());
            assertEquals("0.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(1);
            assertEquals("10", m.get("applicationGeneratorId").toString());
            assertEquals("14523.0000", m.get("matchedRm").toString());
            assertEquals("32021.0000", m.get("unmatchedRm").toString());
            m = res.get(2);
            assertEquals("11", m.get("applicationGeneratorId").toString());
            assertEquals("6.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(9);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("26173.0000", m.get("matchedRm").toString());
            assertEquals("5624539.0000", m.get("unmatchedRm").toString());
            m = res.get(12);
            assertEquals("41", m.get("applicationGeneratorId").toString());
            assertEquals("1.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(21);
            assertEquals("52", m.get("applicationGeneratorId").toString());
            assertEquals("6927.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(res.size()-1);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("5249.0000", m.get("matchedRm").toString());
            assertEquals("8316.0000", m.get("unmatchedRm").toString());
        } else assertEquals(0, res.size());
    }

    @Test
    void findLoadFuelTypeTpcCompanyVoltLevelTest() { // #17 用電端
        List<Map<String, Object>> info = repository.findLoadFuelTypeTpcCompanyVoltLevel(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(122, info.size());
            for (int i =0;i<info.size(); i++) {
                assertEquals("2025-06-01", info.get(i).get("billDate").toString());
                if (info.get(i).get("applicationGeneratorId").toString().equals("97") // 彈性分配 除了 applicationLoadId = 204 之外 KWH = 0
                        && !info.get(i).get("applicationLoadId").toString().equals("204"))
                    assertEquals("0", info.get(i).get("A_KWH").toString());
            }
            Map<String, Object> m = info.get(0);
            assertEquals(7, m.size());
            assertEquals("85", m.get("applicationGeneratorId").toString());
            assertEquals("186", m.get("applicationLoadId").toString());
            assertEquals("99720", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("00", m.get("TPC_CODE").toString());
            m = info.get(1);
            assertEquals("86", m.get("applicationGeneratorId").toString());
            assertEquals("186", m.get("applicationLoadId").toString());
            assertEquals("342700", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("00", m.get("TPC_CODE").toString());
            m = info.get(4);
            assertEquals("100", m.get("applicationGeneratorId").toString());
            assertEquals("231", m.get("applicationLoadId").toString());
            assertEquals("298", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("00", m.get("TPC_CODE").toString());
            m = info.get(13);
            assertEquals("89", m.get("applicationGeneratorId").toString());
            assertEquals("189", m.get("applicationLoadId").toString());
            assertEquals("22902", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("06", m.get("TPC_CODE").toString());
            m = info.get(14);
            assertEquals("97", m.get("applicationGeneratorId").toString()); // 彈性分配 TYPE = 4 appId =89 applicationGeneratorId = 97
            assertEquals("203", m.get("applicationLoadId").toString()); // applicationLoadId in (203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226)
            assertEquals("0", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("06", m.get("TPC_CODE").toString());
            m = info.get(15);
            assertEquals("97", m.get("applicationGeneratorId").toString()); // 彈性分配 TYPE = 4 appId =89 applicationGeneratorId = 97
            assertEquals("204", m.get("applicationLoadId").toString()); // applicationLoadId in (203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226)
            assertEquals("864000", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("06", m.get("TPC_CODE").toString());
            m = info.get(17);
            assertEquals("97", m.get("applicationGeneratorId").toString()); // 彈性分配 TYPE = 4 appId =89 applicationGeneratorId = 97
            assertEquals("206", m.get("applicationLoadId").toString()); // applicationLoadId in (203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226)
            assertEquals("0", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("06", m.get("TPC_CODE").toString());
            m = info.get(36);
            assertEquals("97", m.get("applicationGeneratorId").toString()); // 彈性分配 TYPE = 4 appId =89 applicationGeneratorId = 97
            assertEquals("211", m.get("applicationLoadId").toString()); // applicationLoadId in (203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226)
            assertEquals("0", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("07", m.get("TPC_CODE").toString());
            m = info.get(55);
            assertEquals("97", m.get("applicationGeneratorId").toString()); // 彈性分配 TYPE = 4 appId =89 applicationGeneratorId = 97
            assertEquals("224", m.get("applicationLoadId").toString()); // applicationLoadId in (203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226)
            assertEquals("0", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("10", m.get("TPC_CODE").toString());
            m = info.get(116);
            assertEquals("94", m.get("applicationGeneratorId").toString()); // 小額綠電 appId = 86 applicationGeneratorId = 94 TYPE = Q
            assertEquals("200", m.get("applicationLoadId").toString()); // applicationLoadId = 200
            assertEquals("150000", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("19", m.get("TPC_CODE").toString());
            m = info.get(info.size()-3);
            assertEquals("105", m.get("applicationGeneratorId").toString());
            assertEquals("236", m.get("applicationLoadId").toString());
            assertEquals("128550", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("19", m.get("TPC_CODE").toString());
            m = info.get(info.size()-1);
            assertEquals("97", m.get("applicationGeneratorId").toString()); // 彈性分配 TYPE = 4 appId =89 applicationGeneratorId = 97
            assertEquals("226", m.get("applicationLoadId").toString()); // applicationLoadId in (203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226)
            assertEquals("0", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("21", m.get("TPC_CODE").toString());
        } else if (mteT1Mark) {
            assertEquals(128, info.size()); // matchedKw 1294
            for (int i =0;i<info.size(); i++)
                assertEquals("2025-05-01", info.get(i).get("billDate").toString());
            Map<String, Object> m = info.get(0);
            assertEquals(7, m.size());
            //assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("10", m.get("applicationGeneratorId").toString());
            assertEquals("88", m.get("applicationLoadId").toString());
            assertEquals("10729", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("00", m.get("TPC_CODE").toString());
            m = info.get(1);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("10", m.get("applicationGeneratorId").toString());
            assertEquals("89", m.get("applicationLoadId").toString());
            assertEquals("7929", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("00", m.get("TPC_CODE").toString());
            m = info.get(4);
            //assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("11", m.get("applicationGeneratorId").toString());
            assertEquals("92", m.get("applicationLoadId").toString());
            assertEquals("427361", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("00", m.get("TPC_CODE").toString());
            m = info.get(33);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("44", m.get("applicationGeneratorId").toString());
            assertEquals("116", m.get("applicationLoadId").toString());
            assertEquals("549934", m.get("A_KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("04", m.get("TPC_CODE").toString());
            m = info.get(60);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("41", m.get("applicationGeneratorId").toString());
            assertEquals("110", m.get("applicationLoadId").toString());
            assertEquals("9021", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("06", m.get("TPC_CODE").toString());
            m = info.get(90);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("11", m.get("applicationGeneratorId").toString());
            assertEquals("15", m.get("applicationLoadId").toString());
            assertEquals("180163", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("11", m.get("TPC_CODE").toString());
            m = info.get(118);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("6", m.get("applicationGeneratorId").toString());
            assertEquals("1", m.get("applicationLoadId").toString());
            assertEquals("277094", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("18", m.get("TPC_CODE").toString());
            m = info.get(info.size()-1);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("23", m.get("applicationGeneratorId").toString());
            assertEquals("53", m.get("applicationLoadId").toString());
            assertEquals("56347", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("21", m.get("TPC_CODE").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    void findGeneratorFuelTypeTpcCompanyVoltLevelTest() { // #17 發電端
        List<Map<String, Object>> info = repository.findGeneratorFuelTypeTpcCompanyVoltLevel(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(122, info.size());
            for (int i =0;i<info.size(); i++) {
                assertEquals("2025-06-01", info.get(i).get("billDate").toString());
                if (info.get(i).get("applicationGeneratorId").toString().equals("97") // 彈性分配 除了 applicationLoadId = 204 之外 KWH = 0
                        && !info.get(i).get("applicationLoadId").toString().equals("204"))
                    assertEquals("0", info.get(i).get("A_KWH").toString());
            }
            Map<String, Object> m = info.get(0);
            assertEquals(7, m.size());
            assertEquals("98", m.get("applicationGeneratorId").toString());
            assertEquals("229", m.get("applicationLoadId").toString());
            assertEquals("198", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("01", m.get("TPC_CODE").toString());
            m = info.get(1);
            assertEquals("100", m.get("applicationGeneratorId").toString());
            assertEquals("231", m.get("applicationLoadId").toString());
            assertEquals("298", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("01", m.get("TPC_CODE").toString());
            m = info.get(4);
            assertEquals("97", m.get("applicationGeneratorId").toString()); // 彈性分配 TYPE = 4 appId =89 applicationGeneratorId = 97
            assertEquals("204", m.get("applicationLoadId").toString());// applicationLoadId in (203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226)
            assertEquals("864000", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("04", m.get("TPC_CODE").toString());
            m = info.get(26);
            assertEquals("97", m.get("applicationGeneratorId").toString()); // 彈性分配 TYPE = 4 appId =89 applicationGeneratorId = 97
            assertEquals("226", m.get("applicationLoadId").toString());// applicationLoadId in (203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226)
            assertEquals("0", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("04", m.get("TPC_CODE").toString());
            m = info.get(35);
            assertEquals("68", m.get("applicationGeneratorId").toString());
            assertEquals("227", m.get("applicationLoadId").toString());
            assertEquals("667903", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("07", m.get("TPC_CODE").toString());
            m = info.get(45);
            assertEquals("94", m.get("applicationGeneratorId").toString()); // 小額綠電 appId = 86 TYPE = Q applicationGeneratorId = 94
            assertEquals("200", m.get("applicationLoadId").toString());// applicationLoadId = 200
            assertEquals("150000", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("08", m.get("TPC_CODE").toString());
            m = info.get(79);
            assertEquals("112", m.get("applicationGeneratorId").toString());
            assertEquals("241", m.get("applicationLoadId").toString());
            assertEquals("450704", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("08", m.get("TPC_CODE").toString());
            m = info.get(103);
            assertEquals("112", m.get("applicationGeneratorId").toString());
            assertEquals("265", m.get("applicationLoadId").toString());
            assertEquals("6630", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("08", m.get("TPC_CODE").toString());
            m = info.get(107);
            assertEquals("103", m.get("applicationGeneratorId").toString());
            assertEquals("234", m.get("applicationLoadId").toString());
            assertEquals("112102", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("09", m.get("TPC_CODE").toString());
            m = info.get(info.size()-3);
            assertEquals("108", m.get("applicationGeneratorId").toString());
            assertEquals("239", m.get("applicationLoadId").toString());
            assertEquals("518310", m.get("A_KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("19", m.get("TPC_CODE").toString());
            m = info.get(info.size()-1);
            assertEquals("86", m.get("applicationGeneratorId").toString());
            assertEquals("186", m.get("applicationLoadId").toString());
            assertEquals("342700", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("21", m.get("TPC_CODE").toString());
        } else if (mteT1Mark) {
            assertEquals(128, info.size()); // matchedKw 1294
            for (int i =0;i<info.size(); i++)
                assertEquals("2025-05-01", info.get(i).get("billDate").toString());
            Map<String, Object> m = info.get(0);
            assertEquals(7, m.size());
            //assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("2", m.get("applicationLoadId").toString());
            assertEquals("18151", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("04", m.get("TPC_CODE").toString());
            m = info.get(1);
            //assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("42", m.get("applicationGeneratorId").toString());
            assertEquals("114", m.get("applicationLoadId").toString());
            assertEquals("37927", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("04", m.get("TPC_CODE").toString());
            m = info.get(25);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("48", m.get("applicationGeneratorId").toString());
            assertEquals("122", m.get("applicationLoadId").toString());
            assertEquals("3383", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("08", m.get("TPC_CODE").toString());
            m = info.get(36);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("47", m.get("applicationGeneratorId").toString());
            assertEquals("121", m.get("applicationLoadId").toString());
            assertEquals("30737", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("08", m.get("TPC_CODE").toString());
            m = info.get(40);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("50", m.get("applicationGeneratorId").toString());
            assertEquals("125", m.get("applicationLoadId").toString());
            assertEquals("128034", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("09", m.get("TPC_CODE").toString());
            m = info.get(94);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("40", m.get("applicationGeneratorId").toString());
            assertEquals("109", m.get("applicationLoadId").toString());
            assertEquals("701379", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("18", m.get("TPC_CODE").toString());
            m = info.get(99);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("18", m.get("applicationGeneratorId").toString());
            assertEquals("32", m.get("applicationLoadId").toString());
            assertEquals("381300", m.get("A_KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("20", m.get("TPC_CODE").toString());
            m = info.get(info.size()-1);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("54", m.get("applicationGeneratorId").toString());
            assertEquals("128", m.get("applicationLoadId").toString());
            assertEquals("382857", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("21", m.get("TPC_CODE").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    void findSettleServiceDatesTest() { // #16 #18 #19 調度處 服務起始 結束隔月
        List<Map<String, Object>> info = repository.findSettleServiceDates(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(3, info.size());
            assertEquals("2025-03-01", info.get(0).get("serviceDate").toString());
            assertEquals("2025-04-01", info.get(1).get("serviceDate").toString());
            assertEquals("2025-05-01", info.get(info.size()-1).get("serviceDate").toString());
        } else if (mteT1Mark) {
            assertEquals(3, info.size());
            assertEquals("2024-07-01", info.get(0).get("serviceDate").toString());
            assertEquals("2024-08-01", info.get(1).get("serviceDate").toString());
            assertEquals("2024-09-01", info.get(info.size()-1).get("serviceDate").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    void sumExpsByServiceDateBillDateTest() { // #15
        List<Map<String, Object>> info = repository.sumExpsByServiceDateBillDate(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(5, info.size());
            for (int i =0;i <info.size();i++) {
                assertEquals("2025-06-01", info.get(i).get("billDate").toString());
            }
            assertEquals("1806076", info.get(0).get("matchedKw").toString());
            assertEquals("1", info.get(0).get("powerType").toString());
            assertEquals("9547358", info.get(1).get("matchedKw").toString());
            assertEquals("12", info.get(1).get("powerType").toString());
            assertNull(info.get(2).get("matchedKw"));
            assertEquals("2", info.get(2).get("powerType").toString());
            assertEquals("3", info.get(3).get("powerType").toString());
            assertEquals("16915693", info.get(3).get("matchedKw").toString());
            assertEquals("150000", info.get(info.size()-1).get("matchedKw").toString());
            assertEquals("Q", info.get(info.size()-1).get("powerType").toString());
        } else if (mteT1Mark) {
            assertEquals(5, info.size());
            for (int i =0;i <info.size();i++) {
                assertEquals("2025-05-01", info.get(i).get("billDate").toString());
            }
            assertEquals("16836674", info.get(0).get("matchedKw").toString());
            assertEquals("1", info.get(0).get("powerType").toString());
            assertEquals("18820266", info.get(1).get("matchedKw").toString());
            assertEquals("12", info.get(1).get("powerType").toString());
            assertEquals("18151", info.get(2).get("matchedKw").toString());
            assertEquals("2", info.get(2).get("powerType").toString());
            assertEquals("41967754", info.get(3).get("matchedKw").toString());
            assertEquals("3", info.get(3).get("powerType").toString());
            assertEquals("747875", info.get(info.size()-1).get("matchedKw").toString());
            assertEquals("Q", info.get(info.size()-1).get("powerType").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void findExpVoltInfoByServiceDatesTest() {
        List<Map<String, Object>> info = repository.findExpVoltInfoByServiceDates(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(15, info.size()); // 1572
            Map<String, Object> m = info.get(0);
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("3773285", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(1);
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("357596", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(2);
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("2636354", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(3);
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("222466", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(4);
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("220891", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
            m = info.get(5);
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("2994013", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(6);
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("139525", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(7);
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("1119708", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(8);
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("233670", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(9);
            assertEquals("2025-04-01", m.get("serviceDate").toString());
            assertEquals("234133", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
            m = info.get(10);
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("3442307", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(11);
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("444955", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(12);
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("743582", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(13);
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("269644", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(info.size()-1);
            assertEquals("2025-05-01", m.get("serviceDate").toString());
            assertEquals("233564", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
        } else if (mteT1Mark) {
            assertEquals(15, info.size()); // 1572
            Map<String, Object> m = info.get(0);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("16427", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(1);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("108363", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(2);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("10984", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(3);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("38615", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(4);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("8377", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
            m = info.get(5);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("15496", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(6);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("148797", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(7);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("7173", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(8);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("35995", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(9);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("8331", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
            m = info.get(10);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("14491", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(11);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("212556", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(12);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("9634", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(13);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("31702", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(info.size()-1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("7972", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
        } else assertEquals(0, info.size());
    }

    @Test // #7 #14 15分鐘發電端媒合度數 ami15
    void find15MinutesGeneratorSettleInfoByServiceDateTest() {
        List<Map<String, Object>> res = repository.find15MinutesGeneratorSettleInfoByServiceDate(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(130, res.size());
            for (int i =0;i<res.size();i++) {
                //assertEquals("2025-06-01", res.get(i).get("billDate").toString());
                assertNull(res.get(i).get("genMeterReplaceDate"));
                assertNull(res.get(i).get("oldGenMeterNo"));
                assertNull(res.get(i).get("oGMeterNext"));
            }
            for (int i =0 ;i <43;i++) {
                Map<String, Object> m = res.get(i);
                assertEquals("136", m.get("SETTLEMENT_ID").toString());
                assertEquals("2025-03-01", m.get("serviceDate").toString());
            }
            for (int i = 43; i<87; i++) {
                Map<String, Object> m = res.get(i);
                assertEquals("137", m.get("SETTLEMENT_ID").toString());
                assertEquals("2025-04-01", m.get("serviceDate").toString());
            }
            for (int i = 87;i< res.size();i++) {
                Map<String, Object> m = res.get(i);
                assertEquals("138", m.get("SETTLEMENT_ID").toString());
                assertEquals("2025-05-01", m.get("serviceDate").toString());
            }
            Map<String, Object> m = res.get(0);
            assertEquals("62", m.get("applicationGeneratorId").toString());
            assertEquals("WB20202189", m.get("GEN_METER_NO").toString());
            assertEquals("07729897768", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-07-001-02", m.get("SERVICE_ID").toString());
            m = res.get(6);
            assertEquals("68", m.get("applicationGeneratorId").toString());
            assertEquals("TI22850708", m.get("GEN_METER_NO").toString());
            assertEquals("07729897859", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-07-001-02", m.get("SERVICE_ID").toString());
            m = res.get(16);
            assertEquals("78", m.get("applicationGeneratorId").toString());
            assertEquals("WB15003002", m.get("GEN_METER_NO").toString());
            assertEquals("18442092165", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-18-001-04", m.get("SERVICE_ID").toString());
            m = res.get(29);
            assertEquals("94", m.get("applicationGeneratorId").toString());
            assertEquals("XG87135111", m.get("GEN_METER_NO").toString());
            assertEquals("08377135111", m.get("GEN_ELEC_NO").toString());
            assertEquals("Q", m.get("appType").toString());
            assertEquals("1-11212-08-010-02", m.get("SERVICE_ID").toString());
            m = res.get(42);
            assertEquals("113", m.get("applicationGeneratorId").toString());
            assertEquals("WB20202185", m.get("GEN_METER_NO").toString());
            assertEquals("01843330041", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11405-01-001-00", m.get("SERVICE_ID").toString());
            m = res.get(44);
            assertEquals("63", m.get("applicationGeneratorId").toString()); // 2025-04-01
            assertEquals("WB20202240", m.get("GEN_METER_NO").toString());
            assertEquals("07729897780", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-07-001-02", m.get("SERVICE_ID").toString());
            m = res.get(52);
            assertEquals("71", m.get("applicationGeneratorId").toString());
            assertEquals("TI19702988", m.get("GEN_METER_NO").toString());
            assertEquals("07729897928", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-07-001-02", m.get("SERVICE_ID").toString());
            m = res.get(63);
            assertEquals("85", m.get("applicationGeneratorId").toString());
            assertEquals("TU19601716", m.get("GEN_METER_NO").toString());
            assertEquals("07338966170", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11112-21-002-01", m.get("SERVICE_ID").toString());
            m = res.get(72);
            assertEquals("94", m.get("applicationGeneratorId").toString());
            assertEquals("XG87135111", m.get("GEN_METER_NO").toString());
            assertEquals("08377135111", m.get("GEN_ELEC_NO").toString());
            assertEquals("Q", m.get("appType").toString());
            assertEquals("1-11212-08-010-02", m.get("SERVICE_ID").toString());
            m = res.get(85);
            assertEquals("112", m.get("applicationGeneratorId").toString());
            assertEquals("WB20201007", m.get("GEN_METER_NO").toString());
            assertEquals("08376175049", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11311-08-005-00", m.get("SERVICE_ID").toString());
            m = res.get(87);
            assertEquals("62", m.get("applicationGeneratorId").toString()); // 2024-09-01
            assertEquals("WB20202189", m.get("GEN_METER_NO").toString());
            assertEquals("07729897768", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-07-001-02", m.get("SERVICE_ID").toString());
            m = res.get(99);
            assertEquals("74", m.get("applicationGeneratorId").toString());
            assertEquals("XG89897951", m.get("GEN_METER_NO").toString());
            assertEquals("07729897951", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-07-001-02", m.get("SERVICE_ID").toString());
            m = res.get(110);
            assertEquals("88", m.get("applicationGeneratorId").toString());
            assertEquals("KU21318071", m.get("GEN_METER_NO").toString());
            assertEquals("08369525025", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11204-08-002-00", m.get("SERVICE_ID").toString());
            m = res.get(116);
            assertEquals("94", m.get("applicationGeneratorId").toString());
            assertEquals("XG87135111", m.get("GEN_METER_NO").toString());
            assertEquals("08377135111", m.get("GEN_ELEC_NO").toString());
            assertEquals("Q", m.get("appType").toString());
            assertEquals("1-11212-08-010-02", m.get("SERVICE_ID").toString());
            m = res.get(res.size()-1);
            assertEquals("113", m.get("applicationGeneratorId").toString());
            assertEquals("WB20202185", m.get("GEN_METER_NO").toString());
            assertEquals("01843330041", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11405-01-001-00", m.get("SERVICE_ID").toString());
        } else if (mteT1Mark) {
            assertEquals(142, res.size());
            //for (int i =0;i<res.size();i++)
            //  assertEquals("2025-05-01", res.get(i).get("billDate").toString());
            for (int i =0;i<45;i++) {
                Map<String, Object> m = res.get(i);
                assertEquals("367", m.get("SETTLEMENT_ID").toString());
                assertEquals("2024-07-01", m.get("serviceDate").toString());
                assertNull(m.get("genMeterReplaceDate"));
                assertNull(m.get("oldGenMeterNo"));
                assertNull(m.get("oGMeterNext"));
            }
            for (int i =46;i<93;i++) {
                Map<String, Object> m = res.get(i);
                assertEquals("368", m.get("SETTLEMENT_ID").toString());
                assertEquals("2024-08-01", m.get("serviceDate").toString());
                assertNull(m.get("genMeterReplaceDate"));
                assertNull(m.get("oldGenMeterNo"));
                assertNull(m.get("oGMeterNext"));
            }
            for (int i =93;i<res.size();i++) {
                Map<String, Object> m = res.get(i);
                assertEquals("372", m.get("SETTLEMENT_ID").toString());
                assertEquals("2024-09-01", m.get("serviceDate").toString());
                if (null != m.get("genMeterReplaceDate") && m.get("genMeterReplaceDate").toString().equals("2024-09-02 12:00:00.0")) {
                    assertEquals("TU19619813", m.get("oldGenMeterNo").toString());
                } else {
                    assertNull(m.get("genMeterReplaceDate"));
                    assertNull(m.get("oldGenMeterNo"));
                    assertNull(m.get("oGMeterNext"));
                }
            }
            Map<String, Object> m = res.get(0);
            assertEquals("1", m.get("applicationGeneratorId").toString());
            assertEquals("WB15001444", m.get("GEN_METER_NO").toString());
            assertEquals("18442092110", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-18-001-01", m.get("SERVICE_ID").toString());
            m = res.get(6);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("RU19014595", m.get("GEN_METER_NO").toString());
            assertEquals("04946986547", m.get("GEN_ELEC_NO").toString());
            assertEquals("5", m.get("appType").toString());
            assertEquals("1-10902-04-001-02", m.get("SERVICE_ID").toString());
            m = res.get(16);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("XG83888006", m.get("GEN_METER_NO").toString());
            assertEquals("10623888006", m.get("GEN_ELEC_NO").toString());
            assertEquals("Q", m.get("appType").toString());
            assertEquals("1-11211-10-004-00", m.get("SERVICE_ID").toString()); // 18
            m = res.get(45);
            assertEquals("55", m.get("applicationGeneratorId").toString());
            assertEquals("KU21318071", m.get("GEN_METER_NO").toString());
            assertEquals("08369525025", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11204-08-002-00", m.get("SERVICE_ID").toString());
            m = res.get(51);
            assertEquals("6", m.get("applicationGeneratorId").toString()); // 2024-08-01
            assertEquals("WB20202681", m.get("GEN_METER_NO").toString());
            assertEquals("18442092381", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-18-001-01", m.get("SERVICE_ID").toString());
            m = res.get(52);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("RU19014595", m.get("GEN_METER_NO").toString());
            assertEquals("04946986547", m.get("GEN_ELEC_NO").toString());
            assertEquals("5", m.get("appType").toString());
            assertEquals("1-10902-04-001-02", m.get("SERVICE_ID").toString());
            m = res.get(63);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("XG83888006", m.get("GEN_METER_NO").toString());
            assertEquals("10623888006", m.get("GEN_ELEC_NO").toString());
            assertEquals("Q", m.get("appType").toString());
            assertEquals("1-11211-10-004-00", m.get("SERVICE_ID").toString()); // 18
            m = res.get(91);
            assertEquals("54", m.get("applicationGeneratorId").toString());
            assertEquals("WB20202967", m.get("GEN_METER_NO").toString());
            assertEquals("21954994920", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11112-21-002-01", m.get("SERVICE_ID").toString());
            m = res.get(94);
            assertEquals("2", m.get("applicationGeneratorId").toString()); // 2024-09-01
            assertEquals("WB18081624", m.get("GEN_METER_NO").toString());
            assertEquals("18442092154", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-18-001-01", m.get("SERVICE_ID").toString());
            m = res.get(99);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("RU19014595", m.get("GEN_METER_NO").toString());
            assertEquals("04946986547", m.get("GEN_ELEC_NO").toString());
            assertEquals("5", m.get("appType").toString());
            assertEquals("1-10902-04-001-02", m.get("SERVICE_ID").toString());
            m = res.get(110);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("XG83888006", m.get("GEN_METER_NO").toString());
            assertEquals("10623888006", m.get("GEN_ELEC_NO").toString());
            assertEquals("Q", m.get("appType").toString());
            assertEquals("1-11211-10-004-00", m.get("SERVICE_ID").toString()); // 18
            m = res.get(137);
            assertEquals("52", m.get("applicationGeneratorId").toString());
            assertEquals("KU23702412", m.get("GEN_METER_NO").toString());
            assertEquals("18857150021", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11307-18-002-00", m.get("SERVICE_ID").toString());
            m = res.get(res.size()-1);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("TU21725289", m.get("GEN_METER_NO").toString());
            assertEquals("10641031703", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11307-10-002-00", m.get("SERVICE_ID").toString());
        } else assertEquals(0, res.size());
    }

    @Test
    public void findApplicationMonthlyGeneratorsReMatchInfoByBillDateTest() { // #8
        List<Map<String, Object>> res = repository.findApplicationMonthlyGeneratorsReMatchInfoByBillDate(billDate);
        // assertion
        if (tpcMark) assertEquals(179, res.size());
        else if (mteT1Mark) {
            assertEquals(14, res.get(0).size());
            assertEquals(232, res.size());
        } else assertEquals(0, res.size());
    }

    @Test
    public void findMonthlyApplicationLoadByBillDateRangeTest() {
        Date billStart = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date billEnd = new GregorianCalendar(2026, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> res = repository.findMonthlyApplicationLoadByBillDateRange(billStart, billEnd);
        // assertion
        if (tpcMark) assertEquals(619, res.size());
        else if (mteT1Mark) assertEquals(1140, res.size()); // 1140
        else assertEquals(0, res.size());
        if (!res.isEmpty()) {
            Map<String, Object> m = res.get(0);
            String keepKey = m.get("serviceDate").toString()+"~"+m.get("applicationLoadId").toString()+"~"+m.get("ENERGY_CHARGE_SECTION_ID").toString();
            String keepSettleId = m.get("SETTLEMENT_ID").toString();
            for (int i = 1; i < res.size()-1; i++) {
                m = res.get(i);
                String thisKey = m.get("serviceDate").toString()+"~"+m.get("applicationLoadId").toString()+"~"+m.get("ENERGY_CHARGE_SECTION_ID").toString();
                if (keepKey.equals(thisKey))
                    assertEquals(keepSettleId, m.get("SETTLEMENT_ID").toString());
                assertNotEquals(keepKey, thisKey);
                keepKey = thisKey;
            }
        }
    }

    @Test
    public void findApplicationGeneratorSettleInfoTest() { // #3 #4
        List<Map<String, Object>> res = repository.findApplicationGeneratorSettleInfo(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(437, res.size());
            Map<String, Object> m = res.get(0);
            assertEquals("2025-03-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", res.get(res.size()-1).get("serviceDate").toString());
            for (int i =0; i< res.size(); i++) {
                m = res.get(i);
                assertNull(m.get("GEN_METER_CHANGE_DATE"));
                assertNull(m.get("oldGenMeterNo"));
            }
        } else if (mteT1Mark) {
            assertEquals(568, res.size());
            Map<String, Object> m = res.get(0);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2024-09-01", res.get(res.size()-1).get("serviceDate").toString());
            for (int i =0; i< res.size(); i++) {
                m = res.get(i);
                if (m.get("GEN_ELEC_NO").toString().equals("18857150021") && m.get("serviceDate").toString().equals("2024-09-01")) {
                    assertEquals("2024-09-02 12:00:00.0", m.get("GEN_METER_CHANGE_DATE").toString());
                    assertEquals("KU23702412", m.get("METER_NO").toString());
                    assertEquals("TU19619813", m.get("oldGenMeterNo").toString());
                } else {
                    assertNull(m.get("GEN_METER_CHANGE_DATE"));
                    assertNull(m.get("oldGenMeterNo"));
                }
            }
        } else assertEquals(0, res.size());
    }

    @Test
    public void findAllSettlementIdTest() {
        List<Long> settleIds = repository.findAllSettlementId(billDate);
        // assertion
        if (tpcMark) {
            assertEquals(4, settleIds.size());
            assertEquals("136", settleIds.get(0).toString());
            assertEquals("137", settleIds.get(1).toString());
            assertEquals("138", settleIds.get(2).toString());
            assertEquals("139", settleIds.get(settleIds.size() - 1).toString());
        } else if (mteT1Mark) {
            assertEquals(4, settleIds.size());
            assertEquals("366", settleIds.get(0).toString()); // <- 比從 VIEW_SETTLEMENT_ALL_BILL_AND_NO_ERP 多 366 ˇˊˊ
            assertEquals("367", settleIds.get(1).toString());
            assertEquals("368", settleIds.get(2).toString());
            assertEquals("372", settleIds.get(settleIds.size() - 1).toString());
        } else assertEquals(0, settleIds.size());
    }
}