package tw.com.taipower.data.repository.pwoms;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.data.AbstractRepositoryTest;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@Log4j2
@SpringBootTest
//@ActiveProfiles("mte-test-s1")
@ActiveProfiles("mte-t1-test-s1")
//@ActiveProfiles("ae-dev")
//@ActiveProfiles("ae-s1")
//@ActiveProfiles("mte-t2-tpc")
class ApplicationMonthlyCapacityRecordRepositoryTest extends AbstractRepositoryTest {

    @Autowired
    private ResourceLoader resourceLoader = null;

    @Autowired
    ApplicationMonthlySettlementRepository applicationMonthlySettlementRepository;

    @Autowired
    ApplicationGeneratorRepository applicationGeneratorRepository;

    @Autowired
    ApplicationMonthlyCapacityRecordRepository repository;

    boolean devMark = false;
    boolean s1Mark = false;
    boolean mteT1Mark = false;
    boolean tpcMark = false;

    @BeforeEach
    public void setUp() throws Exception {
        devMark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("ae-dev");
        mteT1Mark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t1-test-s1");
        tpcMark = ((AnnotationConfigApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t2-tpc");
    }

    @Test
    public void findAllSettleMeterYearsTest_dev12mon_t1_5mon() {
        String billYM = devMark? "2024-12":"2025-05";
        List<Map<String, Object>> info = devMark? applicationMonthlySettlementRepository.findAllSettleMeterYears(billYM)
                : repository.findAllSettleMeterYears(billYM);
        //log.info("info size:" + info.size());
        // assertion
        if (tpcMark) {
            assertEquals(87, info.size());
            int count = 0;
            Map<String, Object> m = info.get(0);
            for (int i =0;i<info.size();i++) {
                m = info.get(i);
                assertEquals("2025-03-01", m.get("serviceDate").toString());
                assertEquals("0", m.get("flexible").toString());
                if (null == m.get("VOLTAGE_CLASS")) {
                    count++;
                    assertEquals("L", m.get("gLMark").toString());
                } else {
                    assertEquals("G", m.get("gLMark").toString());
                }
            }
            assertEquals(58, count);
            assertEquals(5, info.get(0).size());
            m = info.get(0);
            assertEquals("GB12931337", m.get("METER_NO").toString());
            assertNull(m.get("VOLTAGE_CLASS"));
            assertEquals("L", m.get("gLMark").toString());
            m = info.get(47);
            assertEquals("TI20850636", m.get("METER_NO").toString());
            assertEquals("lv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("G", m.get("gLMark").toString());
            m = info.get(info.size()-1);
            assertEquals("XL89897917", m.get("METER_NO").toString());
            assertNull(m.get("VOLTAGE_CLASS"));
            assertEquals("L", m.get("gLMark").toString());
        } else if (mteT1Mark || devMark) {
            assertEquals(326, info.size());
            Map<String, Object> m = info.get(0);
            assertEquals(5, m.size());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("GB11932004", m.get("METER_NO").toString());
            assertEquals("hv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("0", m.get("flexible").toString());
            m = info.get(95);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("XL86652112", m.get("METER_NO").toString());
            assertEquals("hv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("0", m.get("flexible").toString());
            m = info.get(141);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("TB16800012", m.get("METER_NO").toString());
            assertEquals("hv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("0", m.get("flexible").toString());
            m = info.get(209);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("XT87023925", m.get("METER_NO").toString());
            assertEquals("uhv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("G", m.get("gLMark").toString());
            assertEquals("0", m.get("flexible").toString());
            m = info.get(233);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("RU19014595", m.get("METER_NO").toString());
            assertEquals("hv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("G", m.get("gLMark").toString());
            assertEquals("0", m.get("flexible").toString());
            m = info.get(info.size()-1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("XL89897917", m.get("METER_NO").toString());
            assertEquals("uhv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("0", m.get("flexible").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void findAllSettleMeterYearsTest() {
        String billYM = tpcMark? "2025-05": devMark? "2024-11":"2025-04";
        List<Map<String, Object>> info = devMark? applicationMonthlySettlementRepository.findAllSettleMeterYears(billYM)
                : repository.findAllSettleMeterYears(billYM);
        //log.info("info size:"+info.size());
        // assertion
        if (tpcMark) {
            assertEquals(87, info.size());
            int count = 0;
            for (int i =0;i<info.size();i++) {
                Map<String, Object> m = info.get(i);
                assertEquals("2025-03-01", m.get("serviceDate").toString());
                assertEquals("0", m.get("flexible").toString());
                if (null == m.get("VOLTAGE_CLASS")) {
                    count++;
                    assertEquals("L", m.get("gLMark").toString());
                } else {
                    assertEquals("G", m.get("gLMark").toString());
                }
            }
            assertEquals(58, count);
            Map<String, Object> m = info.get(0);
            assertEquals(5, m.size());
            assertEquals("GB12931337", m.get("METER_NO").toString());
            assertNull(m.get("VOLTAGE_CLASS"));
            assertEquals("L", m.get("gLMark").toString());
            m = info.get(3);
            assertEquals("GE12941072", m.get("METER_NO").toString());
            assertEquals("uhv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("G", m.get("gLMark").toString());
            m = info.get(35);
            assertEquals("TC18743786", m.get("METER_NO").toString());
            assertNull(m.get("VOLTAGE_CLASS"));
            assertEquals("L", m.get("gLMark").toString());
            m = info.get(54);
            assertEquals("TU21725289", m.get("METER_NO").toString());
            assertEquals("lv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("G", m.get("gLMark").toString());
            m = info.get(57);
            assertEquals("TU22778876", m.get("METER_NO").toString());
            assertNull(m.get("VOLTAGE_CLASS"));
            assertEquals("L", m.get("gLMark").toString());
            m = info.get(64);
            assertEquals("WB18081630", m.get("METER_NO").toString());
            assertEquals("lv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("G", m.get("gLMark").toString());
            m = info.get(79);
            assertEquals("XG86895017", m.get("METER_NO").toString());
            assertEquals("lv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("G", m.get("gLMark").toString());
            m = info.get(80);
            assertEquals("XG87135111", m.get("METER_NO").toString());
            assertEquals("uhv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("G", m.get("gLMark").toString());
            m = info.get(info.size() - 1);
            assertEquals("XL89897917", m.get("METER_NO").toString());
            assertNull(m.get("VOLTAGE_CLASS"));
            assertEquals("L", m.get("gLMark").toString());
        } else if (mteT1Mark || devMark) {
            assertEquals(365, info.size()); // 295 + 彈性分配 -> 365
            Map<String, Object> m = info.get(0);
            assertEquals(5, m.size());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("GB11932004", m.get("METER_NO").toString());
            assertEquals("hv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("0", m.get("flexible").toString());
            m = info.get(95);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("GE21903301", m.get("METER_NO").toString());
            assertEquals("hv", m.get("VOLTAGE_CLASS").toString()); // 11.4 kw 跟 22.7kw 都是 hv
            assertEquals("G", m.get("gLMark").toString());
            assertEquals("0", m.get("flexible").toString());
            m = info.get(110);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("TA19701273", m.get("METER_NO").toString());
            assertEquals("lv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("1", m.get("flexible").toString());
            m = info.get(137); // 135
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("TI19702302", m.get("METER_NO").toString()); // 140 TI22850473
            assertEquals("lv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("G", m.get("gLMark").toString());
            assertEquals("1", m.get("flexible").toString());
            m = info.get(247);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("TA19701273", m.get("METER_NO").toString());
            assertEquals("lv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("1", m.get("flexible").toString());
            m = info.get(296); // 349
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("WA16013445", m.get("METER_NO").toString());
            assertEquals("uhv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("1", m.get("flexible").toString());
            m = info.get(352);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("XL84092057", m.get("METER_NO").toString());
            assertEquals("lv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("1", m.get("flexible").toString());
            m = info.get(info.size()-1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("XL89897917", m.get("METER_NO").toString());
            assertEquals("uhv", m.get("VOLTAGE_CLASS").toString());
            assertEquals("L", m.get("gLMark").toString());
            assertEquals("0", m.get("flexible").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void findBillMeterElecApplicationIdYearsTest_extractComponent() {
        String billYM = devMark? "2024-12":"2025-05";
        List<Map<String, Object>> info = devMark? applicationMonthlySettlementRepository.findBillMeterElecApplicationIdYears(billYM)
                : repository.findBillMeterElecApplicationIdYears(billYM);
        //log.info("info size:" + info.size());
        Map<String, String> meterElec = new HashMap<>();
        Map<String, String> dateAppId = new HashMap<>();
        for (int i = 0; i < info.size(); i++) {
            Map<String, Object> m = info.get(i);
            String appId = m.get("APPLICATION_ID").toString();
            String mm = m.get("METER_NO").toString();
            String ee = m.get("NBS_CUSTOMER_NUMBER").toString();
            String ss = m.get("serviceDate").toString().substring(0, 7);
            String key = ss+"|"+ee +"|"+mm;
            if (meterElec.isEmpty() || null == meterElec.get(key))
                meterElec.put(key, appId);
            else meterElec.put(key, meterElec.get(key) + "," + appId);
            if (dateAppId.isEmpty() || null == dateAppId.get(ss))
                dateAppId.put(ss, appId);
            else dateAppId.put(ss, dateAppId.get(ss) +","+appId);
        }
        //log.info("meterElec:" + meterElec.keySet() + ", size:" + meterElec.size() + ", value:" + meterElec.values());
        //log.info("dateAppId:" + dateAppId + ", size:" + dateAppId.size());
        if (tpcMark) {
            assertEquals(100, info.size());
            assertEquals(87, meterElec.size());
            // [2025-03|TI19701899|08376141004, 2025-03|TA19700386|11252190006, 2025-03|WB20202189|07729897768, 2025-03|WB20201007|08376175049, 2025-03|TC18742732|10505503308, 2025-03|WB20204245|07729897882, 2025-03|WB20200555|04416456154, 2025-03|TC18742814|10505503397, 2025-03|WB20201457|20524573952, 2025-03|TU19601716|07338966170, 2025-03|TU22778876|11540680853, 2025-03|TI19702994|07729897815, 2025-03|XG87135111|08377135111, 2025-03|TC18743911|10505503342, 2025-03|XG89897951|07729897951, 2025-03|WB18081624|18442092154, 2025-03|TU22704868|05786851648, 2025-03|TC18742828|10505503331, 2025-03|TI22850680|07729897791, 2025-03|KU21377536|10505502056, 2025-03|KU21347860|09596475108, 2025-03|TC18744000|10505503386, 2025-03|GE21903021|00364792111, 2025-03|KU21330658|10505502067, 2025-03|TA20854925|10513336027, 2025-03|TI22850708|07729897859, 2025-03|TC18744352|10505503353, 2025-03|TB12604176|11252188002, 2025-03|WA12001353|10512918917, 2025-03|KU23702412|18857150021, 2025-03|TA20854367|10513336959, 2025-03|XL84110116|00444110116, 2025-03|TB20853525|04946961150, 2025-03|WB15003002|18442092165, 2025-03|KU21330382|10505502078, 2025-03|TB10600151|11476001002, 2025-03|WB18081648|18442092289, 2025-03|RU22030666|05917450904, 2025-03|WB15001444|18442092110, 2025-03|GE21903301|18584257018, 2025-03|XL89897917|07729897917, 2025-03|RU22033688|05917450880, 2025-03|TA19700714|10512918962, 2025-03|TI19702989|07729897940, 2025-03|GB12931337|11192605010, 2025-03|TI22850113|06256019101, 2025-03|WB18081630|18442092278, 2025-03|TC18743745|10505503320, 2025-03|GE12941072|19893602045, 2025-03|KU21357278|09474023806, 2025-03|RU19014595|04946986547, 2025-03|GB21900276|05785204210, 2025-03|TC18711992|00020002106, 2025-03|KU21377922|10505502125, 2025-03|WB20202185|01843330041, 2025-03|XL84959650|19124959650, 2025-03|KU21318071|08369525025, 2025-03|TC18743786|10505503364, 2025-03|TA20855027|10518691508, 2025-03|TU22778872|11540680864, 2025-03|GB16930714|18442134109, 2025-03|XL82092030|18442092030, 2025-03|TI20850636|07729897848, 2025-03|TC18744233|10505503400, 2025-03|TU21725289|10641031703, 2025-03|WB20202240|07729897780, 2025-03|WA12004025|10512920999, 2025-03|TC18744526|10505503079, 2025-03|WB20201611|10513336016, 2025-03|RU19016521|05460820114, 2025-03|TI23851750|07729897939, 2025-03|WB20204266|07729897871, 2025-03|WB20201152|07729897995, 2025-03|WB20202967|21954994920, 2025-03|TI19702995|07729897804, 2025-03|WB18080990|11719932016, 2025-03|XG86895017|09696895017, 2025-03|TC18744476|10505503284, 2025-03|WB20202681|18442092381, 2025-03|WB20200367|04414923212, 2025-03|XL88602908|10518602908, 2025-03|TI19702988|07729897928, 2025-03|TU21715680|10505502114, 2025-03|KU21376369|09281407286, 2025-03|TC18742744|10505503240, 2025-03|TC18743709|10505503319, 2025-03|TC18743760|10505503375]
            Set<String> pp = new HashSet<>(new ArrayList<>(Arrays.asList(dateAppId.get("2025-03").split(","))));
            assertEquals(24, pp.size());
            //log.info("[2025-03] appId:"+pp+", size:"+pp.size());
            //[2025-03] appId:[88, 92, 94, 95, 96, 97, 99, 73, 74, 75, 77, 78, 79, 100, 101, 102, 103, 80, 81, 82, 83, 84, 85, 86], size:24
            // [73, 74, 75, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 88, 92, 94, 95, 96, 97, 99, 100, 101, 102, 103]
        } else if (mteT1Mark || devMark) {
            assertEquals(332, info.size()); // 363 -> 332 少了沒有開票
            assertEquals(295, meterElec.size()); // 326 -> 295 少了沒有開票
            // [2024-09|KU21357278|09474023806, 2024-09|WA12000107|00608165112, 2024-08|XG89897951|07729897951, 2024-07|TI19701899|08376141004, 2024-09|TB11600165|06485610216, 2024-07|TB15800731|11083498008, 2024-07|TA20853650|04553195158, 2024-08|WU21200059|00253041208, 2024-07|XL88602908|10518602908, 2024-08|TI22850680|07729897791, 2024-08|**********|16455276118, 2024-07|TC18707859|16485445514, 2024-09|XL84621135|07614621135, 2024-09|KU23702412|18857150021, 2024-08|TI19702988|07729897928, 2024-09|XL82134007|18442134007, 2024-09|GE23903305|06256225112, 2024-09|XL84110116|00444110116, 2024-08|XG87135111|08377135111, 2024-07|TB16800727|10391807914, 2024-08|TU22724675|07329379604, 2024-09|WA16012640|00608270110, 2024-09|WB20204266|07729897871, 2024-07|GB22900002|05461010116, 2024-09|TB12604176|11252188002, 2024-07|XL89897917|07729897917, 2024-09|WA12004765|05052815112, 2024-09|XL85680050|07615680050, 2024-09|WB20201930|12106582003, 2024-08|WA12004025|10512920999, 2024-09|KU21347860|09596475108, 2024-08|WA12002483|21450409001, 2024-09|KV23810787|16588071909, 2024-09|KU21376369|09281407286, 2024-09|TU19619813|18857150021, 2024-09|GD18727051|00987304101, 2024-08|XL87580112|00447580112, 2024-09|TB12601219|06816425113, 2024-08|GU20417109|16581921935, 2024-09|WB20202005|12407527081, 2024-07|WB15003002|18442092165, 2024-08|TC18708933|00987304021, 2024-08|KU21318071|08369525025, 2024-08|WB20202681|18442092381, 2024-09|TB16800012|00957000112, 2024-09|TI19701899|08376141004, 2024-09|GE23903554|04943706892, 2024-07|WB18081630|18442092278, 2024-08|WB20201996|12400473286, 2024-08|XL82092030|18442092030, 2024-09|XL88602908|10518602908, 2024-08|XG83888006|10623888006, 2024-08|TB16801039|16605801114, 2024-09|GB12934269|00566630115, 2024-08|GE21903219|16644008114, 2024-07|GE21903789|10518602032, 2024-08|GB11930732|04074055254, 2024-08|TB15800358|06710610135, 2024-09|TB16800727|10391807914, 2024-07|WA16012640|00608270110, 2024-07|TB16800157|04193120119, 2024-07|WB20201930|12106582003, 2024-08|WB20201007|08376175049, 2024-08|TI20850636|07729897848, 2024-08|TI19702987|07729897939, 2024-08|TB20851746|10518601907, 2024-08|TI19702989|07729897940, 2024-07|GB12933516|04231070133, 2024-08|WA16014119|10513336027, 2024-08|GB21900391|00608280112, 2024-08|WB15001444|18442092110, 2024-09|WB15003002|18442092165, 2024-08|RU22020986|16056183906, 2024-07|KU21357278|09474023806, 2024-08|XL86652112|00566652112, 2024-08|TI22850473|04074055356, 2024-07|WB20201007|08376175049, 2024-09|TB11601627|06485610114, 2024-09|TA19700294|11120889001, 2024-09|XL89897917|07729897917, 2024-08|TB10600455|06250999137, 2024-09|GB22900002|05461010116, 2024-07|TU19601716|07338966170, 2024-09|WB18081630|18442092278, 2024-08|GE21903789|10518602032, 2024-08|TB16800157|04193120119, 2024-07|TI22850785|04953792159, 2024-07|**********|16455276118, 2024-08|GB12933516|04231070133, 2024-09|TB20851746|10518601907, 2024-08|TB12605623|06467077110, 2024-07|KU21376369|09281407286, 2024-07|KU21347860|09596475108, 2024-07|WA16014119|10513336027, 2024-08|WB20202240|07729897780, 2024-09|TB12605579|06467077212, 2024-07|TC18708575|16463595303, 2024-09|GB21900391|00608280112, 2024-07|TB12604176|11252188002, 2024-08|TA19700294|11120889001, 2024-07|GB12934269|00566630115, 2024-09|XL84959650|19124959650, 2024-08|GU20416695|16040559109, 2024-07|TB10600455|06250999137, 2024-07|WB20201457|20524573952, 2024-09|XL85219115|05785219115, 2024-08|WB20200555|04416456154, 2024-08|WT19002891|19030213001, 2024-09|WA16013416|06425470119, 2024-07|TI22850708|07729897859, 2024-07|WB18081648|18442092289, 2024-09|KT19106482|10540664987, 2024-07|TI22850034|20522460907, 2024-07|TA19700714|10512918962, 2024-07|TI19702989|07729897940, 2024-08|TB11601466|16277602115, 2024-09|XL85680005|07615680005, 2024-07|GE23903554|04943706892, 2024-09|WA16014081|10513336959, 2024-08|TA20853953|06250022158, 2024-09|XG83888006|10623888006, 2024-09|GB12931806|16460155511, 2024-08|XG86895017|09696895017, 2024-09|TI19702994|07729897815, 2024-08|TB16800467|07687878004, 2024-08|WB20204266|07729897871, 2024-08|WB20201152|07729897995, 2024-09|WB20202189|07729897768, 2024-08|WB20202967|21954994920, 2024-08|TI19702995|07729897804, 2024-09|TI22850785|04953792159, 2024-08|TC18708306|16484349400, 2024-09|TB15800731|11083498008, 2024-08|TB11601665|06256019101, 2024-08|WB20200479|06467028110, 2024-09|TC18708575|16463595303, 2024-08|KU21303454|08377070922, 2024-09|XL87580112|00447580112, 2024-09|GU20417109|16581921935, 2024-08|WB20202005|12407527081, 2024-09|WB20201457|20524573952, 2024-08|TB16800012|00957000112, 2024-08|WB18081624|18442092154, 2024-07|KU21318071|08369525025, 2024-07|XL84959650|19124959650, 2024-07|XL84110116|00444110116, 2024-09|TI22850034|20522460907, 2024-08|TA19700386|11252190006, 2024-07|WA16014081|10513336959, 2024-08|TA20854289|06485319110, 2024-07|WB20201611|10513336016, 2024-08|WB20204245|07729897882, 2024-08|RU19014595|04946986547, 2024-07|WA16013416|06425470119, 2024-09|TA19700714|10512918962, 2024-09|GB12931337|11192605010, 2024-09|TI22850708|07729897859, 2024-07|KV23810787|16588071909, 2024-09|TA20851858|04414947152, 2024-09|WB18081648|18442092289, 2024-07|**********|16455276118, 2024-07|XL83510118|01193510118, 2024-08|GE21903301|18584257018, 2024-09|TU19601716|07338966170, 2024-08|XL82134007|18442134007, 2024-08|TB20853525|04946961150, 2024-07|TI19702994|07729897815, 2024-07|WA12002483|21450409001, 2024-08|GB22900002|05461010116, 2024-07|TC18708933|00987304021, 2024-07|WB20204245|07729897882, 2024-08|WA16012640|00608270110, 2024-08|TB16800727|10391807914, 2024-09|RU19014595|04946986547, 2024-08|WA12004765|05052815112, 2024-07|WB20202189|07729897768, 2024-09|TI19702989|07729897940, 2024-09|TI20850636|07729897848, 2024-09|WA12004025|10512920999, 2024-09|TU21725289|10641031703, 2024-09|KU21303454|08377070922, 2024-08|KV23810787|16588071909, 2024-09|RU22020986|16056183906, 2024-09|WB15001444|18442092110, 2024-07|KT19106482|10540664987, 2024-09|KU21318071|08369525025, 2024-09|TI19702987|07729897939, 2024-07|GU20417109|16581921935, 2024-08|XL89897917|07729897917, 2024-07|WB20202681|18442092381, 2024-09|WB20201611|10513336016, 2024-08|TA20853650|04553195158, 2024-08|WA12000107|00608165112, 2024-09|WB20201996|12400473286, 2024-09|WU21200059|00253041208, 2024-09|WB20201152|07729897995, 2024-08|XL88602908|10518602908, 2024-08|TC18707859|16485445514, 2024-09|TI19702988|07729897928, 2024-09|XG89897951|07729897951, 2024-07|GE21903219|16644008114, 2024-09|**********|16455276118, 2024-09|XL83510118|01193510118, 2024-07|TB16801039|16605801114, 2024-09|TU22724675|07329379604, 2024-07|GB12931806|16460155511, 2024-07|TI20850636|07729897848, 2024-07|TI19702987|07729897939, 2024-09|WB20201007|08376175049, 2024-08|WB20201930|12106582003, 2024-07|TB20851746|10518601907, 2024-09|WA12002483|21450409001, 2024-09|TI22850680|07729897791, 2024-07|WB15001444|18442092110, 2024-09|WA16014119|10513336027, 2024-07|WA12004025|10512920999, 2024-07|XL86652112|00566652112, 2024-07|GB21900391|00608280112, 2024-08|GD18727051|00987304101, 2024-08|TB11600165|06485610216, 2024-07|RU22020986|16056183906, 2024-08|KU21357278|09474023806, 2024-09|TC18708933|00987304021, 2024-08|TB11601627|06485610114, 2024-09|XG86895017|09696895017, 2024-09|WB20202681|18442092381, 2024-08|WB15003002|18442092165, 2024-09|TA20853953|06250022158, 2024-09|TB10600455|06250999137, 2024-09|GE21903219|16644008114, 2024-07|XG83888006|10623888006, 2024-07|XG89897951|07729897951, 2024-07|XL87580112|00447580112, 2024-08|TI19701899|08376141004, 2024-09|TB12605623|06467077110, 2024-07|XL82092030|18442092030, 2024-08|KU21347860|09596475108, 2024-07|TI22850680|07729897791, 2024-09|TA20854367|10513336959, 2024-07|TI19702988|07729897928, 2024-07|TU22724675|07329379604, 2024-09|TB16801039|16605801114, 2024-08|TI22850034|20522460907, 2024-07|WT19002891|19030213001, 2024-08|WA16013416|06425470119, 2024-09|TA20854289|06485319110, 2024-09|XL86652112|00566652112, 2024-07|TA19700386|11252190006, 2024-07|GD18727051|00987304101, 2024-08|TI22850708|07729897859, 2024-08|TA20851858|04414947152, 2024-08|WB18081648|18442092289, 2024-07|TB11601466|16277602115, 2024-07|WB20201996|12400473286, 2024-08|GE23903554|04943706892, 2024-08|TA20854241|06710610113, 2024-08|WA16014081|10513336959, 2024-07|WB20204266|07729897871, 2024-07|GE21903301|18584257018, 2024-07|XG86895017|09696895017, 2024-09|TB20853525|04946961150, 2024-08|WB18081630|18442092278, 2024-08|GB12931806|16460155511, 2024-09|TB16800467|07687878004, 2024-09|TB16800157|04193120119, 2024-09|WB20202967|21954994920, 2024-09|TI19702995|07729897804, 2024-09|GE21903789|10518602032, 2024-09|TC18708306|16484349400, 2024-09|GB12933516|04231070133, 2024-07|TB11601665|06256019101, 2024-07|WB20202240|07729897780, 2024-09|XL82092030|18442092030, 2024-09|WB18081624|18442092154, 2024-07|WB20200555|04416456154, 2024-08|XT87023925|20657023925, 2024-08|GB12934269|00566630115, 2024-07|TA19700294|11120889001, 2024-08|TB12605579|06467077212, 2024-07|GU20416695|16040559109, 2024-08|WB20201457|20524573952, 2024-09|XG87135111|08377135111, 2024-09|TA20853650|04553195158, 2024-09|TA19700386|11252190006, 2024-08|WB20201611|10513336016, 2024-09|WB20200555|04416456154, 2024-07|RU19014595|04946986547, 2024-09|WB20204245|07729897882, 2024-07|WA12000107|00608165112, 2024-08|KT19106482|10540664987, 2024-08|TA19700714|10512918962, 2024-08|KU21376369|09281407286, 2024-09|GE21903301|18584257018, 2024-09|TC18707859|16485445514, 2024-09|WT19002891|19030213001, 2024-08|XL83510118|01193510118, 2024-07|TB20853525|04946961150, 2024-07|XL82134007|18442134007, 2024-08|GE23903305|06256225112, 2024-07|WB20201152|07729897995, 2024-08|TI19702994|07729897815, 2024-08|TI22850785|04953792159, 2024-08|TU19601716|07338966170, 2024-09|WB20204071|20657023914, 2024-07|TB16800467|07687878004, 2024-07|TI19702995|07729897804, 2024-07|WA12004765|05052815112, 2024-07|WB20202967|21954994920, 2024-08|TB15800731|11083498008, 2024-07|TC18708306|16484349400, 2024-09|TB11601466|16277602115, 2024-07|GB11932004|04946961150, 2024-08|WB20202189|07729897768, 2024-09|TB11601665|06256019101, 2024-09|WB20202240|07729897780, 2024-07|WU21200059|00253041208, 2024-07|KU21303454|08377070922, 2024-09|GU20416695|16040559109, 2024-08|TC18708575|16463595303, 2024-07|WB20202005|12407527081, 2024-08|XL84110116|00444110116, 2024-07|WB18081624|18442092154, 2024-08|TB12604176|11252188002, 2024-08|XL84959650|19124959650, 2024-07|XG87135111|08377135111, 2024-07|TB16800012|00957000112]
            for (Map.Entry<String, String> entry : dateAppId.entrySet()) {
                if (entry.getKey().equals("2024-07")) {
                    Set<String> pp = new HashSet<>(new ArrayList<>(Arrays.asList(dateAppId.get(entry.getKey()).split(","))));
                    //log.info("[2024-07] appId:"+pp+", size:"+pp.size());
                    assertEquals(25, pp.size()); // 26 -> 25 + APPLICATION.ID(16) 不會開票
                    //[2024-07] appId:[10, 11, 12, 14, 17, 19, 1, 2, 3, 5, 6, 8, 21, 22, 23, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35], size:26 - 1 (, 16)
                } else if (entry.getKey().equals("2024-08")) {
                    Set<String> pp = new HashSet<>(new ArrayList<>(Arrays.asList(dateAppId.get(entry.getKey()).split(","))));
                    //log.info("[2024-08] appId:"+pp+", size:"+pp.size());
                    assertEquals(26, pp.size()); // 27 -> 26 + APPLICATION.ID(16) 不會開票
                    //[2024-08] appId:[10, 11, 12, 14, 15, 17, 19, 1, 2, 3, 5, 6, 8, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35], size:27 - 1 (, 16)
                } else {
                    Set<String> pp = new HashSet<>(new ArrayList<>(Arrays.asList(dateAppId.get(entry.getKey()).split(","))));
                    //log.info("[2024-09] appId:"+pp+", size:"+pp.size());
                    assertEquals(28, pp.size());  // 29 -> 28
                    //[2024-09] appId:[10, 11, 12, 14, 17, 19, 1, 2, 3, 5, 6, 7, 8, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36], size:29 - 1 (, 16)
                }
            }
            // List.of(1L, 2L, 3L, 5L, 6L, 7L, 8L, 10L, 11L, 12L, 14L, 15L, 17L, 19L, 21L, 22L, 23L, 24L, 25L, 26L, 27L, 28L, 29L, 30L, 31L, 32L, 33L, 34L, 35L, 36L)
        } else assertEquals(0, info.size());
    }

    @Test
    public void findBillMeterElecApplicationIdYearsTest() {
        String billYM = tpcMark? "2025-05": devMark? "2024-11":"2025-04";
        List<Map<String, Object>> info = devMark? applicationMonthlySettlementRepository.findBillMeterElecApplicationIdYears(billYM)
                : repository.findBillMeterElecApplicationIdYears(billYM);
        //log.info("info size:"+info.size());
        // assertion
        if (tpcMark) {
            assertEquals(100, info.size());
            for (int i = 0; i< info.size(); i++)
                assertEquals("2025-03-01", info.get(i).get("serviceDate").toString());
            Map<String, Object> m = info.get(0);
            assertEquals(5, m.size());
            assertEquals("TC18711992", m.get("METER_NO").toString()); // TI19702988 - 07729897928 - 73
            assertEquals("00020002106", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("92", m.get("APPLICATION_ID").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("L", m.get("gLMark").toString());
            m = info.get(1);
            assertEquals("TC18711992", m.get("METER_NO").toString());
            assertEquals("00020002106", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("103", m.get("APPLICATION_ID").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("L", m.get("gLMark").toString());
            m = info.get(59);
            assertEquals("TC18743786", m.get("METER_NO").toString()); // XL84959650 - 19124959650 - 95
            assertEquals("10505503364", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("102", m.get("APPLICATION_ID").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("L", m.get("gLMark").toString());
            m = info.get(info.size()-2);
            assertEquals("WB20201457", m.get("METER_NO").toString()); // TC18711992 - 00020002106 - 103
            assertEquals("20524573952", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("94", m.get("APPLICATION_ID").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("G", m.get("gLMark").toString());
            m = info.get(info.size()-1);
            assertEquals("WB20202967", m.get("METER_NO").toString()); // WB20202185 - 01843330041 - 103
            assertEquals("21954994920", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("78", m.get("APPLICATION_ID").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("G", m.get("gLMark").toString());
        } else if (mteT1Mark || devMark) {
            assertEquals(332, info.size()); // 402 - 彈性分配 = 332
            Map<String, Object> m = info.get(0);
            assertEquals(5, m.size());
            assertEquals("WU21200059", m.get("METER_NO").toString()); // WB15003002 - 18442092165 - 1 - 2024-07-01
            assertEquals("00253041208", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("5", m.get("APPLICATION_ID").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("L", m.get("gLMark").toString());
            m = info.get(1);
            assertEquals("XL84110116", m.get("METER_NO").toString());
            assertEquals("00444110116", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("34", m.get("APPLICATION_ID").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("L", m.get("gLMark").toString());
            m = info.get(2); // 1 -2 電號表號相同 但 契約ID 不同
            assertEquals("XL84110116", m.get("METER_NO").toString());
            assertEquals("00444110116", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("35", m.get("APPLICATION_ID").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("L", m.get("gLMark").toString());
            /*m = info.get(120);
            assertEquals("TI19702302", m.get("METER_NO").toString()); // TI19702302 - 04640780312 - 18 彈性發電端
            assertEquals("04640780312", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("18", m.get("APPLICATION_ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("1", m.get("flexible").toString()); // 1
            //assertEquals("G", m.get("gLMark").toString());
            m = info.get(121);
            assertEquals("XL83275196", m.get("METER_NO").toString()); // XL83275196 - 04943275196 - 18 彈性用電端
            assertEquals("04943275196", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("18", m.get("APPLICATION_ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("1", m.get("flexible").toString()); // 1
            //assertEquals("L", m.get("gLMark").toString());*/
            m = info.get(181); // 214
            assertEquals("TB12604176", m.get("METER_NO").toString()); // WA12002483 - 21450409001 - 17
            assertEquals("11252188002", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("28", m.get("APPLICATION_ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("L", m.get("gLMark").toString());
            m = info.get(182); // 181 - 182 APPLICATION_ID 契約ID 相同 但電號表號不同 215
            assertEquals("TA19700386", m.get("METER_NO").toString());
            assertEquals("11252190006", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("28", m.get("APPLICATION_ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("L", m.get("gLMark").toString());
            /*m = info.get(info.size()-4);
            assertEquals("XL80400109", m.get("METER_NO").toString()); // XL80400109 - 21400400109 - 18 彈性分配 用電端
            assertEquals("21400400109", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("18", m.get("APPLICATION_ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("1", m.get("flexible").toString()); // 1
            //assertEquals("L", m.get("gLMark").toString());
            m = info.get(info.size()-3);
            assertEquals("XL80409017", m.get("METER_NO").toString()); // XL80409017 - 21400409017 - 18 彈性分配 用電端
            assertEquals("21400409017", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("18", m.get("APPLICATION_ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("1", m.get("flexible").toString()); // 1
            //assertEquals("L", m.get("gLMark").toString());*/
            m = info.get(info.size()-2);
            assertEquals("WA12002483", m.get("METER_NO").toString()); // GB12931337 - 11192605010 - 36
            assertEquals("21450409001", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("17", m.get("APPLICATION_ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("L", m.get("gLMark").toString());
            m = info.get(info.size()-1);
            assertEquals("WB20202967", m.get("METER_NO").toString()); // TU21725289 - 10641031703 - 36
            assertEquals("21954994920", m.get("NBS_CUSTOMER_NUMBER").toString());
            assertEquals("34", m.get("APPLICATION_ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("0", m.get("flexible").toString());
            //assertEquals("G", m.get("gLMark").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void sumLoadKwhByTpcUnitNameTest() {
        Date serviceStart = tpcMark? new GregorianCalendar(2025, Calendar.MARCH, 1).getTime()
                : devMark? new GregorianCalendar(2024, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2024, Calendar.JULY, 1).getTime();
        Date serviceEnd = tpcMark? new GregorianCalendar(2025, Calendar.APRIL, 1).getTime()
                : devMark? new GregorianCalendar(2024, Calendar.JULY, 1).getTime()
                : new GregorianCalendar(2024, Calendar.AUGUST, 1).getTime();
        List<Map<String, Object>> res = repository.sumLoadKwhByTpcUnitName(serviceStart, serviceEnd);
        // assertion
        if (mteT1Mark) {
            assertEquals(14, res.size());
            for (int i = 0; i < res.size(); i++)
                assertEquals("2024-07-01", res.get(i).get("serviceDate").toString());
            Map<String, Object> m = res.get(0);
            assertEquals(3, m.size());
            assertEquals("3.290000", m.get("A_KWH").toString());
            assertEquals("台北市區營業處", m.get("TPC_NAME").toString());
            m = res.get(1);
            assertEquals("1.740000", m.get("A_KWH").toString());
            assertEquals("台中區營業處", m.get("TPC_NAME").toString());
            m = res.get(2);
            assertEquals("1.460000", m.get("A_KWH").toString());
            assertEquals("台北南區營業處", m.get("TPC_NAME").toString());
            m = res.get(res.size()-1);
            assertEquals("0.020000", m.get("A_KWH").toString());
            assertEquals("屏東區營業處", m.get("TPC_NAME").toString());
        } else assertEquals(0, res.size());
    }

    @Test
    public void sumGeneratorKwhByTpcUnitNameTest() { //#21 發電電量 百萬度
        Date serviceStart = tpcMark? new GregorianCalendar(2025, Calendar.MARCH, 1).getTime()
                : devMark? new GregorianCalendar(2024, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2024, Calendar.SEPTEMBER, 1).getTime();
        Date serviceEnd = tpcMark? new GregorianCalendar(2025, Calendar.APRIL, 1).getTime()
                : devMark? new GregorianCalendar(2024, Calendar.JULY, 1).getTime()
                : new GregorianCalendar(2024, Calendar.OCTOBER, 1).getTime();
        List<Map<String, Object>> res = repository.sumGeneratorKwhByTpcUnitName(serviceStart, serviceEnd);
        // assertion
        if (mteT1Mark) {
            assertEquals(9, res.size());
            for (int i =0 ;i < res.size(); i++)
                assertEquals("2024-09-01", res.get(i).get("serviceDate").toString());
            Map<String, Object> m = res.get(0);
            assertEquals(3, m.size());
            assertEquals("8.520000", m.get("A_KWH").toString());
            assertEquals("新營區營業處", m.get("TPC_NAME").toString());
            m = res.get(1);
            assertEquals("4.930000", m.get("A_KWH").toString());
            assertEquals("屏東區營業處", m.get("TPC_NAME").toString());
            m = res.get(2);
            assertEquals("1.220000", m.get("A_KWH").toString());
            assertEquals("台中區營業處", m.get("TPC_NAME").toString());
            m = res.get(res.size()-1);
            assertEquals("0.100000", m.get("A_KWH").toString());
            assertEquals("苗栗區營業處", m.get("TPC_NAME").toString());
        } else assertEquals(0, res.size());
    }

    @Test
    public void countGenComByFuelLevelTest() {
        Date serviceStart = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.JUNE, 1).getTime();
        List<Map<String, Object>> out = repository.countGenComByFuelLevel(serviceStart, serviceEnd);
        //log.info("out:"+out);
        // assertion
        Map<String, Object> m = out.get(0);
        assertEquals("2025-01-01", m.get("serviceDate").toString());
        assertEquals("511", m.get("TOTAL").toString());
        assertEquals("24", m.get("WIND").toString());
        assertEquals("481", m.get("SUN").toString());
        assertEquals("6", m.get("WATER").toString());
        if (!devMark && !tpcMark) {
            m = out.get(1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("18", m.get("TOTAL").toString());
            assertEquals("3", m.get("WIND").toString());
            assertEquals("15", m.get("SUN").toString());
            assertEquals("0", m.get("WATER").toString());
        }
        m = out.get(out.size()-1);
        assertEquals("2020-12-01", m.get("serviceDate").toString());
        assertEquals("14", m.get("TOTAL").toString());
        assertEquals("7", m.get("WIND").toString());
        assertEquals("7", m.get("SUN").toString());
        assertNull(m.get("WATER"));
    }

    @Test
    public void findCapacityByFuelLevelTest() {
        Date serviceStart = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.JUNE, 1).getTime();
        //log.info("-- start --sumMatchedKwByFuelLevelTest");
        List<Map<String, Object>> out = repository.findCapacityByFuelLevel(serviceStart, serviceEnd);
        // assertion
        Map<String, Object> m = out.get(0);
        assertEquals(6, m.size());
        assertEquals("2025-01-01", m.get("serviceDate").toString());
        assertNull(m.get("mode"));
        assertNull(m.get("LICENSE_CAPACITY"));
        assertNull(m.get("TRIAL_RUN_CAPACITY"));
        assertEquals("600150,109200,2862558,39769", m.get("fuelsKw").toString());
        assertEquals("3611677.0000", m.get("TOTAL").toString());
        int idx = s1Mark? 132: mteT1Mark?131:(devMark||tpcMark)?6:9;
        assertEquals(idx, out.size()); // 129
        if (!mteT1Mark) {
            m = out.get(1);
            assertEquals("2024-12-01", m.get("serviceDate").toString());
            assertNull(m.get("mode"));
            assertNull(m.get("LICENSE_CAPACITY"));
            assertNull(m.get("TRIAL_RUN_CAPACITY"));
            assertEquals("591150,-,2431038,37604", m.get("fuelsKw").toString());
            assertEquals("3059792.0000", m.get("TOTAL").toString());
        }
        if (mteT1Mark) {
            m = out.get(1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("10", m.get("mode").toString()); // 非離岸風力
            assertEquals("7.4400", m.get("LICENSE_CAPACITY").toString());
            assertEquals("0.0000", m.get("TRIAL_RUN_CAPACITY").toString());
            assertNull(m.get("fuelsKw"));
            assertEquals("7.4400", m.get("TOTAL").toString());
            m = out.get(35); // application.Id = 26 沒有 Fuel_From
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("10", m.get("mode").toString()); // 非離岸風力
            assertEquals("12443.3000", m.get("LICENSE_CAPACITY").toString());
            assertEquals("37564.1500", m.get("TRIAL_RUN_CAPACITY").toString());
            assertNull(m.get("fuelsKw"));
            assertEquals("50007.4500", m.get("TOTAL").toString());
            m = out.get(40); // application.Id = 16 <- Q
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("10", m.get("mode").toString()); // 非離岸風力
            assertEquals("149999.0800", m.get("LICENSE_CAPACITY").toString());
            assertEquals("0.0000", m.get("TRIAL_RUN_CAPACITY").toString());
            assertNull(m.get("fuelsKw"));
            assertEquals("149999.0800", m.get("TOTAL").toString());
            m = out.get(41);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("20", m.get("mode").toString()); // 太陽
            assertEquals("0.0000", m.get("LICENSE_CAPACITY").toString());
            assertEquals("9000.0000", m.get("TRIAL_RUN_CAPACITY").toString());
            assertNull(m.get("fuelsKw"));
            assertEquals("9000.0000", m.get("TOTAL").toString());
            m = out.get(42); // application.Id = 18
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("20", m.get("mode").toString()); // 太陽
            assertEquals("4600.0000", m.get("LICENSE_CAPACITY").toString());
            assertEquals("0.0000", m.get("TRIAL_RUN_CAPACITY").toString());
            assertNull(m.get("fuelsKw"));
            assertEquals("4600.0000", m.get("TOTAL").toString());
            m = out.get(86);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("20", m.get("mode").toString()); // 太陽
            assertEquals("7200.0000", m.get("LICENSE_CAPACITY").toString());
            assertEquals("0.0000", m.get("TRIAL_RUN_CAPACITY").toString());
            assertNull(m.get("fuelsKw"));
            assertEquals("7200.0000", m.get("TOTAL").toString());
            m = out.get(128);
            assertEquals("2022-12-01", m.get("serviceDate").toString());
            assertNull(m.get("mode"));
            assertNull(m.get("LICENSE_CAPACITY"));
            assertNull(m.get("TRIAL_RUN_CAPACITY"));
            assertEquals("505450,-,325307,36974", m.get("fuelsKw").toString());
            assertEquals("867731.0000", m.get("TOTAL").toString());
        } else if (!devMark && !tpcMark) {
            m = out.get(2);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("10", m.get("mode").toString()); // 非離岸風力
            assertEquals("149999.0800", m.get("LICENSE_CAPACITY").toString());
            assertEquals("0.0000", m.get("TRIAL_RUN_CAPACITY").toString());
            assertNull(m.get("fuelsKw"));
            assertEquals("149999.0800", m.get("TOTAL").toString());
            m = out.get(3);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("10", m.get("mode").toString()); // 非離岸風力
            assertEquals("149999.0800", m.get("LICENSE_CAPACITY").toString());
            assertEquals("0.0000", m.get("TRIAL_RUN_CAPACITY").toString());
            assertNull(m.get("fuelsKw"));
            assertEquals("149999.0800", m.get("TOTAL").toString());
            m = out.get(4);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("10", m.get("mode").toString()); // 非離岸風力
            assertEquals("149999.0800", m.get("LICENSE_CAPACITY").toString());
            assertEquals("0.0000", m.get("TRIAL_RUN_CAPACITY").toString());
            assertNull(m.get("fuelsKw"));
            assertEquals("149999.0800", m.get("TOTAL").toString());
        }

        m = out.get(out.size()-1);
        assertEquals("2020-12-01", m.get("serviceDate").toString());
        assertNull(m.get("mode"));
        assertNull(m.get("LICENSE_CAPACITY"));
        assertNull(m.get("TRIAL_RUN_CAPACITY"));
        assertEquals("282100,-,95442,-", m.get("fuelsKw").toString());
        assertEquals("277542.0000", m.get("TOTAL").toString());
    }

    @Test
    public void sumMatchedKwByFuelLevelTest() { // 小樹取三位
        Date serviceStart = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        //log.info("-- start --sumMatchedKwByFuelLevelTest");
        List<Map<String, Object>> out = repository.sumMatchedKwByFuelLevel(serviceStart, serviceEnd);
        //log.info("-- end --");
        // assertion
        Map<String, Object> m = out.get(0);
        assertEquals(7, m.size());
        assertEquals("2020-05-01", m.get("serviceDate").toString());
        assertEquals("2020-12-01", m.get("endDate").toString());
        assertEquals("1.600000", m.get("windGKw").toString());
        assertEquals("0.940", m.get("sunRKw").toString());
        assertNull(m.get("waterWKw"));
        assertEquals("2.530", m.get("matchedKw").toString());
        assertEquals("1", m.get("mode").toString()); // 台電月報 年紀錄
        int idx = s1Mark? 27: mteT1Mark?23:(devMark|| tpcMark)?18:21;
        assertEquals(idx, out.size()); // 18 + 12 -> 23
        if (s1Mark) {
            m = out.get(4);
            assertEquals("2024-01-01", m.get("serviceDate").toString());
            assertEquals("2024-12-01", m.get("endDate").toString());
            assertEquals("11.790000", m.get("windGKw").toString());
            assertEquals("17.070", m.get("sunRKw").toString());
            assertEquals("1.120", m.get("waterWKw").toString());
            assertEquals("29.980", m.get("matchedKw").toString());
            assertEquals("1", m.get("mode").toString()); // 台電月報 年紀錄
            m = out.get(11);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2024-07-01", m.get("endDate").toString());
            assertEquals("0.490000", m.get("windGKw").toString());
            assertEquals("2.120", m.get("sunRKw").toString());
            assertEquals("0.120", m.get("waterWKw").toString());
            assertEquals("2.280", m.get("matchedKw").toString());
            assertEquals("2", m.get("mode").toString()); // 台電月報 月紀錄
        }
        if (!devMark && !tpcMark) {
            idx = mteT1Mark? 10: 12;
            m = out.get(idx);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertNull(m.get("endDate"));
            assertEquals("0.003000", m.get("windGKw").toString()); // 0.002509
            assertNull(m.get("sunRKw"));
            assertNull(m.get("waterWKw"));
            assertNull(m.get("matchedKw"));
            assertEquals("31", m.get("mode").toString()); // 結帳 台電太陽能 地面型
            idx = 14;
            m = out.get(idx); // 13
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertNull(m.get("endDate"));
            String kwh = mteT1Mark? "0.132000": "0.003000";//"0.131845": "0.002502";
            assertEquals(kwh, m.get("windGKw").toString()); // 0.205275 -> 0.107866 -> 0.108531 -> 0.217062
            assertNull(m.get("sunRKw"));
            assertNull(m.get("waterWKw"));
            assertNull(m.get("matchedKw"));
            String mode = mteT1Mark? "41": "31";
            assertEquals(mode, m.get("mode").toString()); // 非台電公司 風力
            idx = mteT1Mark? 18: 16;
            m = out.get(idx); // 14
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertNull(m.get("endDate"));
            kwh = mteT1Mark? "0.085000":"0.002000";//"0.085089": "0.002466";
            assertEquals(kwh, m.get("windGKw").toString()); // 0.205275 -> 0.004628 -> 0.008474
            assertNull(m.get("sunRKw"));
            assertNull(m.get("waterWKw"));
            assertNull(m.get("matchedKw"));
            mode = mteT1Mark? "42": "31";
            assertEquals(mode, m.get("mode").toString()); // 非台電公司 太陽
        }
        m = out.get(out.size()-1);
        assertEquals("2025-01-01", m.get("serviceDate").toString());
        assertEquals("2025-01-01", m.get("endDate").toString());
        assertEquals("1.830000", m.get("windGKw").toString());
        assertEquals("2.080", m.get("sunRKw").toString());
        assertEquals("0.040", m.get("waterWKw").toString());
        assertEquals("3.950", m.get("matchedKw").toString());
        assertEquals("2", m.get("mode").toString()); // 台電月報 月紀錄
    }

    @Test
    public void sumMatchedKwByServiceDateTest() {
        Date serviceStart = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> out = repository.sumMatchedKwByServiceDate(serviceStart, serviceEnd);
        // assertion
        int idx = mteT1Mark?17:(devMark||tpcMark)?18:21; // 20
        assertEquals(idx, out.size()); // 21 -> 20
        Map<String, Object> m = out.get(0);
        assertEquals(8, m.size());
        assertEquals("2020-05-01", m.get("serviceDate").toString());
        assertEquals("2020-12-01", m.get("endDate").toString());
        assertEquals("6.700000", m.get("A_COST").toString());
        assertEquals("1.200000", m.get("S_COST").toString());
        assertEquals("2.700000", m.get("T_COST").toString());
        assertEquals("0.700000", m.get("D_COST").toString());
        assertEquals("11.20", m.get("TOTAL").toString());
        assertEquals("1", m.get("yearMode").toString());
        if (!mteT1Mark) {
            m = out.get(4);
            assertEquals("2024-01-01", m.get("serviceDate").toString());
            assertEquals("2024-12-01", m.get("endDate").toString());
            assertEquals("47.400000", m.get("A_COST").toString());
            assertEquals("121.700000", m.get("S_COST").toString());
            assertEquals("102.500000", m.get("T_COST").toString());
            assertEquals("105.600000", m.get("D_COST").toString());
            assertEquals("377.20", m.get("TOTAL").toString());
            assertEquals("1", m.get("yearMode").toString());
            m = out.get(11);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2024-07-01", m.get("endDate").toString());
            assertEquals("3.600000", m.get("A_COST").toString());
            assertEquals("9.200000", m.get("S_COST").toString());
            assertEquals("7.700000", m.get("T_COST").toString());
            assertEquals("9.300000", m.get("D_COST").toString());
            assertEquals("29.80", m.get("TOTAL").toString());
            assertEquals("2", m.get("yearMode").toString());
            m = out.get(13);
            if (!devMark && !tpcMark) {
                assertEquals("2024-08-01", m.get("serviceDate").toString());
                assertEquals("2024-08-01", m.get("endDate").toString());
                assertEquals("3.400000", m.get("A_COST").toString());
                assertEquals("8.900000", m.get("S_COST").toString());
                assertEquals("7.400000", m.get("T_COST").toString());
                assertEquals("9.500000", m.get("D_COST").toString());
                assertEquals("29.20", m.get("TOTAL").toString());
                assertEquals("2", m.get("yearMode").toString());
                m = out.get(15);
                assertEquals("2024-09-01", m.get("serviceDate").toString());
                assertEquals("2024-09-01", m.get("endDate").toString());
                assertEquals("3.400000", m.get("A_COST").toString());
                assertEquals("8.700000", m.get("S_COST").toString());
                assertEquals("7.200000", m.get("T_COST").toString());
                assertEquals("8.900000", m.get("D_COST").toString());
                assertEquals("28.20", m.get("TOTAL").toString());
                assertEquals("2", m.get("yearMode").toString());
            }
        }
        if (!devMark && !tpcMark) {
            idx = mteT1Mark? 10:12;
            m = out.get(idx);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertNull(m.get("endDate"));
            if (mteT1Mark) {
                assertEquals("0.180000", m.get("A_COST").toString()); // 0.181716 -> 0.182766
                assertEquals("0.470000", m.get("S_COST").toString()); // 0.466908 -> 0.469605
                assertEquals("0.320000", m.get("T_COST").toString()); // 0.321012 -> 0.323332
                assertEquals("0.480000", m.get("D_COST").toString()); // 0.477301 -> 0.475715
                //assertEquals("1.451418", m.get("TOTAL").toString()); // 1.446937 -> 2.809453
                assertNull(m.get("TOTAL"));
            } else {
                assertEquals("0.000000", m.get("A_COST").toString()); // 0.181716 -> 0.003965
                assertEquals("0.010000", m.get("S_COST").toString()); // 0.466908 -> 0.010190
                assertEquals("0.010000", m.get("T_COST").toString()); // 0.321012 -> 0.008757
                assertEquals("0.020000", m.get("D_COST").toString()); // 0.477301 -> 0.017493
                //assertEquals("0.040405", m.get("TOTAL").toString()); // 1.446937 -> 2.809453
                assertNull(m.get("TOTAL"));
            }
            assertEquals("3", m.get("yearMode").toString());
            idx = mteT1Mark? 11:14;
            m = out.get(idx);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertNull(m.get("endDate"));
            if (mteT1Mark) {
                assertEquals("0.220000", m.get("A_COST").toString()); // 0.423022 -> 0.221808
                assertEquals("0.570000", m.get("S_COST").toString()); // 1.087030 -> 0.569978
                assertEquals("0.420000", m.get("T_COST").toString()); // 0.785105 -> 0.415296
                assertEquals("0.470000", m.get("D_COST").toString()); // 9.500000 -> 0.474722
                //assertEquals("1.681804", m.get("TOTAL").toString()); // 3.206837 -> 3.241604
                assertNull(m.get("TOTAL"));
            } else {
                assertEquals("0.000000", m.get("A_COST").toString()); // 0.423022 -> 0.003954
                assertEquals("0.010000", m.get("S_COST").toString()); // 1.087030 -> 0.010160
                assertEquals("0.010000", m.get("T_COST").toString()); // 0.785105 -> 0.008730
                assertEquals("0.020000", m.get("D_COST").toString()); // 9.500000 -> 0.017438
                //assertEquals("0.040282", m.get("TOTAL").toString()); // 3.206837 -> 3.241604
                assertNull(m.get("TOTAL"));
            }
            assertEquals("3", m.get("yearMode").toString());
            idx = mteT1Mark? 12:16;
            m = out.get(idx);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertNull(m.get("endDate"));
            if (mteT1Mark) {
                assertEquals("0.280000", m.get("A_COST").toString()); // 0.272929 -> 0.282891
                assertEquals("0.730000", m.get("S_COST").toString()); // 0.701319 -> 0.726919
                assertEquals("0.560000", m.get("T_COST").toString()); // 0.537606 -> 0.559610
                assertEquals("0.410000", m.get("D_COST").toString()); // 0.405295 -> 0.414725
                //assertEquals("1.984145", m.get("TOTAL").toString()); // 1.917149
                assertNull(m.get("TOTAL"));
            } else {
                assertEquals("0.000000", m.get("A_COST").toString()); // 0.272929 -> 0.003898
                assertEquals("0.010000", m.get("S_COST").toString()); // 0.701319 -> 0.010016
                assertEquals("0.010000", m.get("T_COST").toString()); // 0.537606 -> 0.008608
                assertEquals("0.020000", m.get("D_COST").toString()); // 0.405295 -> 0.017193
                //assertEquals("0.039715", m.get("TOTAL").toString()); // 1.917149
                assertNull(m.get("TOTAL"));
            }
            assertEquals("3", m.get("yearMode").toString());
        }
        m = out.get(out.size()-1);
        assertEquals("2025-01-01", m.get("serviceDate").toString());
        assertEquals("2025-01-01", m.get("endDate").toString());
        assertEquals("12.100000", m.get("A_COST").toString());
        assertEquals("21.400000", m.get("S_COST").toString());
        assertEquals("24.600000", m.get("T_COST").toString());
        assertEquals("23.300000", m.get("D_COST").toString());
        assertEquals("81.40", m.get("TOTAL").toString());
        assertEquals("2", m.get("yearMode").toString());
    }

    @Test
    public void sumKwhByADSTCostTypeTest() {
        Date serviceStart = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> out = repository.sumKwhByADSTCostType(serviceStart, serviceEnd);
        // assertion
        int idx = s1Mark? 24: mteT1Mark?17:(devMark)?15:tpcMark?18:21;
        assertEquals(idx, out.size());
        for (Map<String, Object> m : out) {
            assertTrue(((BigDecimal) m.get("T_KWH")).compareTo((BigDecimal) m.get("D_KWH")) >= 0);
        }
        Map<String, Object> m = out.get(0);
        assertEquals(6, m.size());
        assertEquals("2020-05-01", m.get("serviceDate").toString());
        assertEquals("2020-12-01", m.get("endDate").toString());
        assertEquals("2.530000", m.get("A_S_KWH").toString());
        assertEquals("2.530000", m.get("T_KWH").toString());
        assertEquals("0.410000", m.get("D_KWH").toString());
        assertEquals("1", m.get("yearMode").toString());
        if (!mteT1Mark && !devMark && !tpcMark) {
            m = out.get(11);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2024-07-01", m.get("endDate").toString());
            assertEquals("2.280000", m.get("A_S_KWH").toString());
            assertEquals("2.210000", m.get("T_KWH").toString());
            assertEquals("1.330000", m.get("D_KWH").toString());
            assertEquals("2", m.get("yearMode").toString());
            m = out.get(13);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2024-08-01", m.get("endDate").toString());
            assertEquals("2.180000", m.get("A_S_KWH").toString());
            assertEquals("2.120000", m.get("T_KWH").toString());
            assertEquals("1.370000", m.get("D_KWH").toString());
            assertEquals("2", m.get("yearMode").toString());
            m = out.get(15);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2024-09-01", m.get("endDate").toString());
            assertEquals("2.130000", m.get("A_S_KWH").toString());
            assertEquals("2.070000", m.get("T_KWH").toString());
            assertEquals("1.280000", m.get("D_KWH").toString());
            assertEquals("2", m.get("yearMode").toString());
        }
        if (!devMark && !tpcMark) {
            idx = mteT1Mark? 10:12;
            m = out.get(idx);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertNull(m.get("endDate"));
            String kwh = mteT1Mark? "0.120000":"0.000000";//"0.115674": "0.002509";
            assertEquals(kwh, m.get("A_S_KWH").toString()); // 0.115010
            kwh = mteT1Mark? "0.090000":"0.000000";//"0.092645": "0.002509";
            assertEquals(kwh, m.get("T_KWH").toString()); // 0.091980
            kwh = mteT1Mark? "0.070000":"0.000000";//"0.068251": "0.002509";
            assertEquals(kwh, m.get("D_KWH").toString()); // 0.068479
            assertEquals("3", m.get("yearMode").toString());
            idx = mteT1Mark? 11:14;
            m = out.get(idx);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertNull(m.get("endDate"));
            kwh = mteT1Mark? "0.140000":"0.000000";//"0.140384": "0.002502";
            assertEquals(kwh, m.get("A_S_KWH").toString()); // 0.133727
            kwh = mteT1Mark? "0.120000":"0.000000";//"0.118995": "0.002501";
            assertEquals(kwh, m.get("T_KWH").toString()); // 0.112340
            kwh = mteT1Mark? "0.070000":"0.000000";//"0.068109": "0.002501";
            assertEquals(kwh, m.get("D_KWH").toString()); // 0.066136
            assertEquals("3", m.get("yearMode").toString());
            idx = mteT1Mark? 12:16;
            m = out.get(idx);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertNull(m.get("endDate"));
            kwh = mteT1Mark? "0.180000":"0.000000";//"0.179044": "0.002467";
            assertEquals(kwh, m.get("A_S_KWH").toString()); // 0.172739
            kwh = mteT1Mark? "0.160000":"0.000000";//"0.160346": "0.002466";
            assertEquals(kwh, m.get("T_KWH").toString()); // 0.154041
            kwh = mteT1Mark? "0.060000":"0.000000";//"0.059501": "0.002466";
            assertEquals(kwh, m.get("D_KWH").toString()); // 0.058148
            assertEquals("3", m.get("yearMode").toString());
        }
        m = out.get(out.size()-1);
        assertEquals("2025-01-01", m.get("serviceDate").toString());
        assertEquals("2025-01-01", m.get("endDate").toString());
        assertEquals("3.950000", m.get("A_S_KWH").toString());
        assertEquals("3.910000", m.get("T_KWH").toString());
        assertEquals("1.800000", m.get("D_KWH").toString());
        assertEquals("2", m.get("yearMode").toString());
    }

    @Test
    public void sumDirectPowerMatchKwTest() {
        Date serviceStart = new GregorianCalendar(2023, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
        List<Map<String, Object>> info = repository.sumDirectPowerMatchKw(serviceStart, serviceEnd);
        // assertion
        Map<String, Object> m = info.get(0);
        assertEquals(5, m.size());
        assertEquals("2023-03-01", m.get("serviceDate").toString());
        assertEquals("2023-12-01", m.get("END_MONTH_DATE").toString());
        assertEquals("0.400000", m.get("TOTAL").toString());
        assertEquals("2", m.get("mode").toString());
        m = info.get(1);
        assertEquals("2024-01-01", m.get("serviceDate").toString());
        assertEquals("2", m.get("mode").toString());
        if (s1Mark) {
            assertEquals("2024-12-01", m.get("END_MONTH_DATE").toString());
            assertEquals("0.500000", m.get("TOTAL").toString());
            assertEquals(6, info.size());
        } else if (mteT1Mark) {
            assertEquals("2024-06-01", m.get("END_MONTH_DATE").toString());
            assertEquals("0.280000", m.get("TOTAL").toString());
            m = info.get(5);
            assertEquals("2024-10-01", m.get("serviceDate").toString());
            assertEquals("2024-12-01", m.get("END_MONTH_DATE").toString());
            assertEquals("0.060000", m.get("TOTAL").toString());
            assertEquals("2", m.get("mode").toString());
            assertEquals(7, info.size());
        } else if (devMark) {
            assertEquals("2024-04-01", m.get("END_MONTH_DATE").toString());
            assertEquals("0.180000", m.get("TOTAL").toString());
            assertEquals("2", m.get("mode").toString());
        } else {
            assertEquals("2024-12-01", m.get("END_MONTH_DATE").toString());
            assertEquals("0.500000", m.get("TOTAL").toString());
            assertEquals("2", m.get("mode").toString());
        }
        if (s1Mark || mteT1Mark) {
            m = info.get(2);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertNull(m.get("END_MONTH_DATE"));
            assertEquals("0.020000", m.get("TOTAL").toString()); // 0.022958
            assertEquals("3", m.get("mode").toString());
            m = info.get(3);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertNull(m.get("END_MONTH_DATE"));
            assertEquals("0.020000", m.get("TOTAL").toString()); // 0.021324
            assertEquals("3", m.get("mode").toString());
            m = info.get(4);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertNull(m.get("END_MONTH_DATE"));
            assertEquals("0.020000", m.get("TOTAL").toString()); // 0.018643
            assertEquals("3", m.get("mode").toString());
        }
        m = info.get(info.size()-1);
        assertEquals("2025-01-01", m.get("serviceDate").toString());
        assertEquals("2025-01-01", m.get("END_MONTH_DATE").toString());
        assertEquals("0.030000", m.get("TOTAL").toString());
        assertEquals("2", m.get("mode").toString());
    }

    @Test
    public void sumTransPowerMatchKwTest() {
        Date serviceStart = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
        List<Map<String, Object>> info = repository.sumTransPowerMatchKw(serviceStart, serviceEnd);
        // assertion
        Map<String, Object> m = info.get(0);
        assertEquals(5, m.size());
        assertEquals("2020-05-01", m.get("serviceDate").toString());
        assertEquals("2020-12-01", m.get("END_MONTH_DATE").toString());
        assertEquals("2.530000", m.get("TOTAL").toString());
        assertEquals("2", m.get("mode").toString());
        m = info.get(4);
        assertEquals("2024-01-01", m.get("serviceDate").toString());
        assertEquals("2", m.get("mode").toString());
        if (s1Mark) {
            assertEquals("2024-12-01", m.get("END_MONTH_DATE").toString());
            assertEquals("29.490000", m.get("TOTAL").toString());
            assertEquals(12, info.size());
        } else if (mteT1Mark) {
            assertEquals("2024-06-01", m.get("END_MONTH_DATE").toString());
            assertEquals("13.180000", m.get("TOTAL").toString());
            m = info.get(8);
            assertEquals("2024-10-01", m.get("serviceDate").toString());
            assertEquals("2024-12-01", m.get("END_MONTH_DATE").toString());
            assertEquals("9.870000", m.get("TOTAL").toString());
            assertEquals("2", m.get("mode").toString());
            assertEquals(10, info.size()); //13
        } else if (devMark) {
            assertEquals("2024-04-01", m.get("END_MONTH_DATE").toString());
            assertEquals("8.970000", m.get("TOTAL").toString());
        } else {
            assertEquals("2024-12-01", m.get("END_MONTH_DATE").toString());
            assertEquals("29.490000", m.get("TOTAL").toString());
        }
        if (!devMark && !tpcMark) {
            m = info.get(5);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertNull(m.get("END_MONTH_DATE"));
            String kwh = mteT1Mark? "0.120000":"0.000000";//"0.115668": "0.002509";
            assertEquals(kwh, m.get("TOTAL").toString()); // 0.115003
            assertEquals("3", m.get("mode").toString());
            m = info.get(6);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertNull(m.get("END_MONTH_DATE"));
            kwh = mteT1Mark? "0.140000":"0.000000";//"0.140386": "0.002502";
            assertEquals(kwh, m.get("TOTAL").toString()); // 0.133730
            assertEquals("3", m.get("mode").toString());
            m = info.get(7);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertNull(m.get("END_MONTH_DATE"));
            kwh = mteT1Mark? "0.180000":"0.000000";//"0.179045": "0.002466";
            assertEquals(kwh, m.get("TOTAL").toString()); // 0.172740
            assertEquals("3", m.get("mode").toString());
        }
        m = info.get(info.size()-1);
        assertEquals("2025-01-01", m.get("serviceDate").toString());
        assertEquals("2025-01-01", m.get("END_MONTH_DATE").toString());
        assertEquals("3.920000", m.get("TOTAL").toString());
    }

    @Test
    public void countLoadNBSNumberTest() {
        Date serviceStart = new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> info = repository.countLoadNBSNumber(serviceStart, serviceEnd);
        // assertion
        int idx = s1Mark ? 18 : mteT1Mark ? 15 : devMark ? 9 : 12;
        assertEquals(idx, info.size());
        Map<String, Object> m = info.get(0);
        assertEquals(4, m.size());
        assertEquals("1", m.get("office").toString()); // 月報
        assertEquals("409", m.get("loadNum").toString());
        assertEquals("3425", m.get("nbsNum").toString());
        assertEquals("2024-01-01", m.get("serviceDate").toString());
        m = info.get(6);
        if (s1Mark) {
            assertEquals("1", m.get("office").toString()); // 月報
            assertEquals("459", m.get("loadNum").toString());
            assertEquals("4783", m.get("nbsNum").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            m = info.get(9);
            assertEquals("1", m.get("office").toString()); // 月報
            assertEquals("481", m.get("loadNum").toString());
            assertEquals("4951", m.get("nbsNum").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
        } else if (devMark) {
            assertEquals("1", m.get("office").toString()); // 月報
            assertEquals("496", m.get("loadNum").toString());
            assertEquals("5640", m.get("nbsNum").toString());
            assertEquals("2024-10-01", m.get("serviceDate").toString());
        } else if (!mteT1Mark) {
            assertEquals("1", m.get("office").toString()); // 月報
            assertEquals("459", m.get("loadNum").toString());
            assertEquals("4783", m.get("nbsNum").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            m = info.get(7);
            assertEquals("1", m.get("office").toString()); // 月報
            assertEquals("481", m.get("loadNum").toString());
            assertEquals("4951", m.get("nbsNum").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
        }
        if (s1Mark || mteT1Mark) {
            idx = s1Mark? 7:6;
            m = info.get(idx);
            assertEquals("2", m.get("office").toString()); // 電號
            assertEquals("45", m.get("loadNum").toString());
            assertEquals("57", m.get("nbsNum").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            m = info.get(idx+1);
            assertEquals("3", m.get("office").toString()); // 虛擬集團
            assertEquals("34", m.get("loadNum").toString());
            assertEquals("34", m.get("nbsNum").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            idx = s1Mark? 10:8;
            m = info.get(idx);
            assertEquals("2", m.get("office").toString()); // 電號
            assertEquals("88", m.get("loadNum").toString()); // 55
            assertEquals("104", m.get("nbsNum").toString()); // 70
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            m = info.get(idx+1);
            assertEquals("3", m.get("office").toString()); // 虛擬集團
            assertEquals("43", m.get("loadNum").toString()); // 41
            assertEquals("43", m.get("nbsNum").toString()); // 41
            assertEquals("2024-08-01", m.get("serviceDate").toString());
        }
        m = info.get(info.size()-1);
        assertEquals("1", m.get("office").toString()); // 公報
        assertEquals("522", m.get("loadNum").toString());
        assertEquals("6724", m.get("nbsNum").toString());
        assertEquals("2024-12-01", m.get("serviceDate").toString());
    }

    @Test
    public void countGeneratorNBSNumberTest() {
        Date serviceStart = new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> info = repository.countGeneratorNBSNumber(serviceStart, serviceEnd);
        // assertion
        int idx = s1Mark? 18: mteT1Mark? 15:devMark?9:12; // 18:15
        assertEquals(idx, info.size());
        Map<String, Object> m = info.get(0);
        assertEquals(4, m.size());
        assertEquals("1", m.get("office").toString()); // 月報
        assertEquals("132", m.get("genNum").toString());
        assertEquals("540", m.get("nbsNum").toString());
        assertEquals("2024-01-01", m.get("serviceDate").toString());
        m = info.get(6);
        if (s1Mark) {
            assertEquals("1", m.get("office").toString()); // 公報
            assertEquals("206", m.get("genNum").toString());
            assertEquals("771", m.get("nbsNum").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            m = info.get(12);
            assertEquals("1", m.get("office").toString()); // 公報
            assertEquals("251", m.get("genNum").toString());
            assertEquals("1206", m.get("nbsNum").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
        } else if (!mteT1Mark && !devMark) {
            assertEquals("1", m.get("office").toString()); // 公報
            assertEquals("206", m.get("genNum").toString());
            assertEquals("771", m.get("nbsNum").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            m = info.get(8);
            assertEquals("1", m.get("office").toString()); // 公報
            assertEquals("251", m.get("genNum").toString());
            assertEquals("1206", m.get("nbsNum").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
        }
        if (s1Mark || mteT1Mark) {
            idx = s1Mark? 7:6;
            m = info.get(idx);
            assertEquals("2", m.get("office").toString()); // 電號
            assertEquals("14", m.get("genNum").toString());
            assertEquals("40", m.get("nbsNum").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            m = info.get(idx+1);
            assertEquals("3", m.get("office").toString()); // 虛擬集團
            assertEquals("13", m.get("genNum").toString());
            assertEquals("13", m.get("nbsNum").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            idx = s1Mark? 13:10;
            m = info.get(idx);
            assertEquals("2", m.get("office").toString()); // 電號
            assertEquals("18", m.get("genNum").toString()); // 17
            assertEquals("44", m.get("nbsNum").toString()); // 43
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            m = info.get(idx+1);
            assertEquals("3", m.get("office").toString()); // 虛擬集團
            assertEquals("17", m.get("genNum").toString()); // 16
            assertEquals("17", m.get("nbsNum").toString()); // 16
            assertEquals("2024-09-01", m.get("serviceDate").toString());
        }
        m = info.get(info.size()-1);
        assertEquals("1", m.get("office").toString()); // 公報
        assertEquals("425", m.get("genNum").toString());
        assertEquals("1732", m.get("nbsNum").toString());
        assertEquals("2024-12-01", m.get("serviceDate").toString());
    }

    @Test
    public void sumMatchedKwByLoadEntityNamesYearTest() {
        Date serviceStart = new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> info = repository.sumMatchedKwByLoadEntityNamesYear(serviceStart, serviceEnd);
        // assertion
        if (devMark || tpcMark) assertEquals(0, info.size());
        else {
            int idx = s1Mark || mteT1Mark? 167:36;
            assertEquals(idx, info.size()); // 161
            Map<String, Object> ms = info.get(0);
            if (s1Mark || mteT1Mark) {
                assertEquals("中華電信股份有限公司", ms.get("lEntityNAME").toString());
                assertEquals(new BigDecimal("16490059"), (BigDecimal) ms.get("matchedKw"));
                assertNull(ms.get("serviceDate"));
                ms = info.get(1);
                assertEquals("台灣美光記憶體股份有限公司", ms.get("lEntityNAME").toString()); // 中龍鋼鐵股份有限公司
                assertEquals(new BigDecimal("5483534"), (BigDecimal) ms.get("matchedKw")); // 4009251
                assertNull(ms.get("serviceDate"));
                ms = info.get(47); // 45
                assertEquals("中華電信股份有限公司", ms.get("lEntityNAME").toString());
                assertEquals(new BigDecimal("5944177"), (BigDecimal) ms.get("matchedKw"));
                assertEquals("2024-07-01", ms.get("serviceDate").toString());
                ms = info.get(82); // 79
                assertEquals("金像電子股份有限公司", ms.get("lEntityNAME").toString()); // 中華電信股份有限公司
                assertEquals(new BigDecimal("2200000"), (BigDecimal) ms.get("matchedKw")); // 5611142
                assertEquals("2024-08-01", ms.get("serviceDate").toString());
                ms = info.get(info.size()-2); // info.size()-1
                assertEquals("銓泰環能科技股份有限公司", ms.get("lEntityNAME").toString()); // 康舒科技股份有限公司
                assertEquals(new BigDecimal("1087"), (BigDecimal) ms.get("matchedKw")); // 0
                assertEquals("2024-09-01", ms.get("serviceDate").toString());
            } else { // 只有 noErp
                assertEquals("正美企業股份有限公司", ms.get("lEntityNAME").toString());
                assertEquals(new BigDecimal("300000"), (BigDecimal) ms.get("matchedKw")); // 16490059
                assertNull(ms.get("serviceDate"));
                ms = info.get(1);
                assertEquals("友訊科技股份有限公司", ms.get("lEntityNAME").toString()); // 中龍鋼鐵股份有限公司
                assertEquals(new BigDecimal("150000"), (BigDecimal) ms.get("matchedKw")); // 4009251
                assertNull(ms.get("serviceDate"));
                ms = info.get(9); // 45
                assertEquals("正美企業股份有限公司", ms.get("lEntityNAME").toString());
                assertEquals(new BigDecimal("100000"), (BigDecimal) ms.get("matchedKw")); // 5944177
                assertEquals("2024-07-01", ms.get("serviceDate").toString());
                ms = info.get(20); // 79
                assertEquals("康舒科技股份有限公司", ms.get("lEntityNAME").toString()); // 中華電信股份有限公司
                assertEquals(new BigDecimal("50000"), (BigDecimal) ms.get("matchedKw")); // 5611142
                assertEquals("2024-08-01", ms.get("serviceDate").toString());
                ms = info.get(info.size()-1);
                assertEquals("香港商榮惠電子有限公司", ms.get("lEntityNAME").toString()); // 康舒科技股份有限公司
                assertEquals(new BigDecimal("6220"), (BigDecimal) ms.get("matchedKw")); // 0
                assertEquals("2024-09-01", ms.get("serviceDate").toString());
            }
        }
    }

    @Test
    public void sumMatchedKwByGeneratorEntityNamesYearTest() {
        Date serviceStart = new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> info = repository.sumMatchedKwByGeneratorEntityNamesYear(serviceStart, serviceEnd);
        // assertion
        if (devMark || tpcMark) assertEquals(0, info.size());
        else {
            int idx = s1Mark || mteT1Mark? 62:4;
            assertEquals(idx, info.size()); // 59
            Map<String, Object> ms = info.get(0);
            if (s1Mark || mteT1Mark) {
                assertEquals("恩富資本太陽能股份有限公司", ms.get("gEntityNAME").toString());
                assertEquals(new BigDecimal("10589984"), (BigDecimal) ms.get("matchedKw"));
                assertNull(ms.get("serviceDate"));
                ms = info.get(2);
                assertEquals("玉衡智慧能源股份有限公司", ms.get("gEntityNAME").toString());
                assertEquals(new BigDecimal("8060956"), (BigDecimal) ms.get("matchedKw"));
                assertNull(ms.get("serviceDate"));
                ms = info.get(17); // 18
                assertEquals("天權智慧能源股份有限公司", ms.get("gEntityNAME").toString());
                assertEquals(new BigDecimal("3045532"), (BigDecimal) ms.get("matchedKw"));
                assertEquals("2024-07-01", ms.get("serviceDate").toString());
                ms = info.get(30); // 29 -> 32
                assertEquals("恩富資本太陽能股份有限公司", ms.get("gEntityNAME").toString());
                assertEquals(new BigDecimal("2910006"), (BigDecimal) ms.get("matchedKw"));
                assertEquals("2024-08-01", ms.get("serviceDate").toString());
                ms = info.get(info.size()-1);
                assertEquals("信通交通器材股份有限公司", ms.get("gEntityNAME").toString());
                assertEquals(new BigDecimal("5571"), (BigDecimal) ms.get("matchedKw"));
                assertEquals("2024-09-01", ms.get("serviceDate").toString());
            } else { // 只有 noErp
                assertEquals("台灣電力股份有限公司", ms.get("gEntityNAME").toString());
                assertEquals(new BigDecimal("747875"), (BigDecimal) ms.get("matchedKw")); // 10589984
                assertNull(ms.get("serviceDate"));
                ms = info.get(1);
                assertEquals("台灣電力股份有限公司", ms.get("gEntityNAME").toString());
                assertEquals(new BigDecimal("250959"), (BigDecimal) ms.get("matchedKw")); // 3045532
                assertEquals("2024-07-01", ms.get("serviceDate").toString());
                ms = info.get(2); // 29
                assertEquals("台灣電力股份有限公司", ms.get("gEntityNAME").toString());
                assertEquals(new BigDecimal("250222"), (BigDecimal) ms.get("matchedKw")); // 2910006
                assertEquals("2024-08-01", ms.get("serviceDate").toString());
                ms = info.get(info.size()-1);
                assertEquals("台灣電力股份有限公司", ms.get("gEntityNAME").toString());
                assertEquals(new BigDecimal("246694"), (BigDecimal) ms.get("matchedKw")); // 5571
                assertEquals("2024-09-01", ms.get("serviceDate").toString());
            }
        }
    }

    @Test
    public void sumMatchedKwByApplicantsYearTest() { // #1
        Date serviceStart = new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> info = repository.sumMatchedKwByApplicantsYear(serviceStart, serviceEnd);
        // assertion
        if (devMark||tpcMark) assertEquals(0, info.size());
        else {
            int idx = s1Mark || mteT1Mark? 56:4; // 60
            assertEquals(idx, info.size()); // 53
            Map<String, Object> ms = info.get(0);
            if (s1Mark || mteT1Mark) {
                assertEquals("太陽神電力股份有限公司", ms.get("APPL_NAME").toString());
                assertEquals(new BigDecimal("16490059"), (BigDecimal) ms.get("matchedKw"));
                assertNull(ms.get("serviceDate"));
                ms = info.get(2);
                assertEquals("中鋼光能股份有限公司", ms.get("APPL_NAME").toString());
                assertEquals(new BigDecimal("6292663"), (BigDecimal) ms.get("matchedKw"));
                assertNull(ms.get("serviceDate"));
                ms = info.get(15); // 14 -> 16
                assertEquals("太陽神電力股份有限公司", ms.get("APPL_NAME").toString());
                assertEquals(new BigDecimal("5944186"), (BigDecimal) ms.get("matchedKw")); // 5944186 -> 5944177
                assertEquals("2024-07-01", ms.get("serviceDate").toString());
                ms = info.get(27); // 26 -> 29
                assertEquals("太陽神電力股份有限公司", ms.get("APPL_NAME").toString());
                assertEquals(new BigDecimal("5611151"), (BigDecimal) ms.get("matchedKw")); // 5611151 -> 5611142
                assertEquals("2024-08-01", ms.get("serviceDate").toString());
                ms = info.get(info.size()-1);
                assertEquals("信通交通器材股份有限公司", ms.get("APPL_NAME").toString());
                assertEquals(new BigDecimal("5571"), (BigDecimal) ms.get("matchedKw"));
                assertEquals("2024-09-01", ms.get("serviceDate").toString());
            } else { // 只有 noErp
                assertEquals("台灣電力股份有限公司", ms.get("APPL_NAME").toString());
                assertEquals(new BigDecimal("747875"), (BigDecimal) ms.get("matchedKw")); // 10589984
                assertNull(ms.get("serviceDate"));
                ms = info.get(1);
                assertEquals("台灣電力股份有限公司", ms.get("APPL_NAME").toString());
                assertEquals(new BigDecimal("250962"), (BigDecimal) ms.get("matchedKw")); // 3045532 -> 250959
                assertEquals("2024-07-01", ms.get("serviceDate").toString());
                ms = info.get(2); // 29
                assertEquals("台灣電力股份有限公司", ms.get("APPL_NAME").toString());
                assertEquals(new BigDecimal("250224"), (BigDecimal) ms.get("matchedKw")); // 2910006 -> 250222
                assertEquals("2024-08-01", ms.get("serviceDate").toString());
                ms = info.get(info.size()-1);
                assertEquals("台灣電力股份有限公司", ms.get("APPL_NAME").toString());
                assertEquals(new BigDecimal("246696"), (BigDecimal) ms.get("matchedKw")); // 5571 -> 246694
                assertEquals("2024-09-01", ms.get("serviceDate").toString());
            }
        }
    }

    @Test
    public void sumMatchedKwCostsByIdsBillingDateTest() { // report #21
        Date dateStart = new GregorianCalendar(2022, Calendar.JANUARY, 1).getTime();
        Date dateEnd = new GregorianCalendar(2025, Calendar.JUNE, 1).getTime();
        List<Map<String, Object>> info = repository.sumMatchedKwCostsByIdsBillingDate(dateStart, dateEnd);
        log.info("info size:"+info.size());
        if (tpcMark) {
            assertEquals(0, info.size());
        } else if (mteT1Mark) {
            assertEquals(22, info.get(0).size());
            Map<String, String> idServiceId = new HashMap<>();
            Set<Long> appId = new HashSet<>();
            List<String> ans = new ArrayList<>();
            int count = 0;
            for (Map<String, Object>m:info) { // 可印出所有 取出欄位
                ans.add(count+","+m.get("applicationGeneratorId")+","+m.get("applicationLoadId")+","+m.get("billDate")+","+m.get("matchedKw")
                        +","+m.get("FEE")+","+m.get("CUST_METER_NO")+","+m.get("CUST_METER_CHANGE_DATE")+","+m.get("oldLoadMeterNo")
                        +","+m.get("CUST_ELEC_NO")+","+m.get("GEN_METER_NO")+","+m.get("GEN_METER_CHANGE_DATE")+","+m.get("oldGenMeterNo")
                        +","+m.get("GEN_ELEC_NO")+","+m.get("tRate")+","+m.get("dRate")+","+m.get("sRate")+","+m.get("aRate")
                        +","+m.get("appId")+","+m.get("NO")+","+m.get("CONTRACT_NO")+","+m.get("SERVICE_ID")+","+m.get("appType"));
                assertNotNull(m.get("billDate"));
                count ++;
                appId.add((Long)m.get("appId"));
                if (((Long)m.get("appId")).equals(16L)) assertEquals("22", m.get("applicationGeneratorId").toString());
                String serviceId = (String)m.get("SERVICE_ID");
                idServiceId.put(m.get("applicationGeneratorId")+"~"+m.get("applicationLoadId")+"~"+m.get("billDate"), serviceId+"~"+m.get("appId"));
            }
            //log.info("ans:"+ans);
            //log.info("ids-serviceId:"+idServiceId+",size:"+idServiceId.size()+", appId:"+appId);
            Map<String, Object>m = info.get(183); // applicationLoadId in 44L,45L,46L,47L,48L,49L,50L,51L,52L
            assertEquals("7", m.get("appId").toString());
            assertEquals("26", m.get("applicationLoadId").toString());
            m = info.get(184); // applicationLoadId in 44L,45L,46L,47L,48L,49L,50L,51L,52L
            assertEquals("16", m.get("appId").toString());
            assertEquals("45", m.get("applicationLoadId").toString());
            m = info.get(185); // applicationLoadId in 44L,45L,46L,47L,48L,49L,50L,51L,52L
            assertEquals("16", m.get("appId").toString());
            assertEquals("46", m.get("applicationLoadId").toString());
            assertEquals("2024-07-15 14:00:00.0", m.get("CUST_METER_CHANGE_DATE").toString());
            assertEquals("**********", m.get("oldLoadMeterNo").toString());
            assertEquals("**********", m.get("CUST_METER_NO").toString());
            m = info.get(186); // applicationLoadId in 44L,45L,46L,47L,48L,49L,50L,51L,52L
            assertEquals("16", m.get("appId").toString());
            assertEquals("46", m.get("applicationLoadId").toString());
            assertNull(m.get("CUST_METER_CHANGE_DATE"));
            assertNull(m.get("oldLoadMeterNo"));
            assertEquals("**********", m.get("CUST_METER_NO").toString());// 149 ->34 -> 158 -> 149(到 2025/5/1) -> 368 (到 2025/6/1)
            assertEquals(280, info.size());
        } else assertEquals(0, info.size());
    }

    @Test
    public void sumKwhADSTByBillingDateTest_billDate_april() throws Exception { // report #2
        Date billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
        List<Map<String, Object>> res = repository.sumKwhADSTByBillingDate(billDate);
        log.info("res size:"+res.size());

        // assertion
        if (mteT1Mark) {
            assertEquals(4, res.size());
            Map<String, Object> m = res.get(0);
            assertEquals(2, m.size());
            assertEquals("18634010", m.get("kwh").toString());
            assertEquals("12", m.get("powerType").toString());
            m = res.get(1);
            assertEquals("42575898", m.get("kwh").toString());
            assertEquals("3", m.get("powerType").toString());
            m = res.get(2);
            assertEquals("18151", m.get("kwh").toString());
            assertEquals("2", m.get("powerType").toString());
            m = res.get(res.size()-1);
            assertEquals("17631074", m.get("kwh").toString());
            assertEquals("1", m.get("powerType").toString());
        } else assertEquals(0, res.size());
    }

    @Test
    public void sumKwhADSTByBillingDateTest() throws Exception { // report #2 取出當月
        Date start = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> info = repository.sumKwhADSTByBillingDate(start);
        // assertion
        if (mteT1Mark) {
            assertEquals(4, info.size());
            Map<String, Object> m = info.get(0);
            assertEquals(2, m.size());
            assertEquals("19568141", m.get("kwh").toString()); // 6841730 -> 6568006 + 250959(noErp) -> 7月+8月+9月
            assertEquals("12", m.get("powerType").toString());
            m = info.get(1);
            assertEquals("42715629", m.get("kwh").toString()); // 11500386 -> 11315933 + 250959(noErp) -> 7月+8月+9月
            assertEquals("3", m.get("powerType").toString());
            m = info.get(2);
            assertEquals("18151", m.get("kwh").toString());
            assertEquals("2", m.get("powerType").toString());
            m = info.get(info.size()-1);
            assertEquals("16836674", m.get("kwh").toString());
            assertEquals("1", m.get("powerType").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void sumMatchedKwByVoltageClassFuelLabelApplicationTypeTest() {
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> info = repository.sumMatchedKwByVoltageClassFuelLabelApplicationType(billDate);
        log.info("info size:"+info.size());

        // assertion
        if (mteT1Mark) {
            assertEquals(12, info.size());
            assertEquals(5, info.get(0).size());
            //FUEL_LABEL[發電類型]  FUEL_FORM[能源來源] PWDS[直供 DS 轉供 PW 小額綠電 Q] VOLT_LEVEL_CLASS[發電端電壓別] matchedKw[電量(度)]
            Map<String, Object> m = info.get(0);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("其他", m.get("FUEL_FORM").toString());
            assertEquals("DS", m.get("PWDS").toString());
            assertEquals("5480129", m.get("matchedKw").toString());
            m = info.get(1);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("其他", m.get("FUEL_FORM").toString());
            assertEquals("DS", m.get("PWDS").toString());
            assertEquals("812534", m.get("matchedKw").toString());
            m = info.get(2);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("1905851", m.get("matchedKw").toString());
            m = info.get(3);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("陸域", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("1101212", m.get("matchedKw").toString());
            m = info.get(4);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertNull(m.get("FUEL_FORM"));
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("37382", m.get("matchedKw").toString()); // 2173746
            m = info.get(5);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("18151", m.get("matchedKw").toString());
            m = info.get(6);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("556530", m.get("matchedKw").toString()); // 830485
            m = info.get(7);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("屋頂型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("137211", m.get("matchedKw").toString());
            m = info.get(8);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertNull(m.get("FUEL_FORM"));
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("2136364", m.get("matchedKw").toString()); // 2173746
            m = info.get(9);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("22102412", m.get("matchedKw").toString()); // 9331430
            m = info.get(10);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("陸域", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("7679978", m.get("matchedKw").toString());
            m = info.get(info.size()-1);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("Q", m.get("PWDS").toString());
            assertEquals("747875", m.get("matchedKw").toString()); // 22198
            // --- 2025-04
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            info = repository.sumMatchedKwByVoltageClassFuelLabelApplicationType(billDate);
            assertEquals(12, info.size());
            assertEquals(5, info.get(0).size());
            m = info.get(0);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("其他", m.get("FUEL_FORM").toString());
            assertEquals("DS", m.get("PWDS").toString());
            assertEquals("5480129", m.get("matchedKw").toString());
            m = info.get(1);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("其他", m.get("FUEL_FORM").toString());
            assertEquals("DS", m.get("PWDS").toString());
            assertEquals("812534", m.get("matchedKw").toString());
            m = info.get(2);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("1905851", m.get("matchedKw").toString());
            m = info.get(3);
            assertEquals("hv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("陸域", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("914240", m.get("matchedKw").toString());
            m = info.get(4);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertNull(m.get("FUEL_FORM"));
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("37382", m.get("matchedKw").toString()); // 2173746
            m = info.get(5);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("18151", m.get("matchedKw").toString());
            m = info.get(6);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("556531", m.get("matchedKw").toString()); // 830485
            m = info.get(7);
            assertEquals("lv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("屋頂型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("37927", m.get("matchedKw").toString());
            m = info.get(8);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertNull(m.get("FUEL_FORM"));
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("2136364", m.get("matchedKw").toString()); // 2173746
            m = info.get(9);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("地面型", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("22202411", m.get("matchedKw").toString()); // 9331430
            m = info.get(10);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("陸域", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("794400", m.get("matchedKw").toString());
            m = info.get(info.size()-1);
            assertEquals("uhv", m.get("VOLT_LEVEL_CLASS").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("陸域", m.get("FUEL_FORM").toString());
            assertEquals("PW", m.get("PWDS").toString());
            assertEquals("7679978", m.get("matchedKw").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void sumMatchedKwByApplicationIdTest() { // #22
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> res = repository.sumMatchedKwByApplicationId(billDate);
        //log.info("res size:"+res.size());

        //assertion
        if (mteT1Mark) {
            assertEquals(82, res.size()); //22 -> 16 ->26
            for (int i =0;i<res.size();i++)
                assertEquals("2025-05-01", res.get(i).get("billDate").toString());
            assertEquals(4, res.get(0).size());
            Map<String, Object> m = res.get(0);
            assertEquals("1", m.get("ID").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("861172", m.get("matchedKw").toString());
            m = res.get(1);
            assertEquals("1", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("741419", m.get("matchedKw").toString());
            m = res.get(2);
            assertEquals("1", m.get("ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("680821", m.get("matchedKw").toString());
            m = res.get(4);
            assertEquals("2", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("6360", m.get("matchedKw").toString());
            m = res.get(13);
            assertEquals("6", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("5611142", m.get("matchedKw").toString());
            m = res.get(33);
            assertEquals("16", m.get("ID").toString()); // 小額綠電
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("250222", m.get("matchedKw").toString());
            m = res.get(36);
            assertEquals("17", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("13668", m.get("matchedKw").toString());
            m = res.get(39);
            assertEquals("19", m.get("ID").toString()); // 彈性分配
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("1391010", m.get("matchedKw").toString());
            m = res.get(76);
            assertEquals("34", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("171707", m.get("matchedKw").toString());
            m = res.get(res.size() - 3);
            assertEquals("35", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("13095", m.get("matchedKw").toString());
            m = res.get(res.size() - 2);
            assertEquals("35", m.get("ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("10859", m.get("matchedKw").toString());
            m = res.get(res.size() - 1);
            assertEquals("36", m.get("ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("34483", m.get("matchedKw").toString());
            // 2025-04
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = repository.sumMatchedKwByApplicationId(billDate);
            for (int i =0;i<res.size();i++)
                assertEquals("2025-04-01", res.get(i).get("billDate").toString());
            assertEquals(4, res.get(0).size());
            m = res.get(0);
            assertEquals("1", m.get("ID").toString());
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("861172", m.get("matchedKw").toString());
            m = res.get(1);
            assertEquals("1", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("741419", m.get("matchedKw").toString());
            m = res.get(2);
            assertEquals("1", m.get("ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("680821", m.get("matchedKw").toString());
            m = res.get(4);
            assertEquals("2", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("6360", m.get("matchedKw").toString());
            m = res.get(13);
            assertEquals("6", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("5611142", m.get("matchedKw").toString());
            m = res.get(33);
            assertEquals("17", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("13668", m.get("matchedKw").toString());
            m = res.get(38);
            assertEquals("19", m.get("ID").toString()); // 彈性分配
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("1391010", m.get("matchedKw").toString());
            m = res.get(70);
            assertEquals("34", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("171707", m.get("matchedKw").toString());
            m = res.get(res.size() - 3);
            assertEquals("35", m.get("ID").toString());
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("13095", m.get("matchedKw").toString());
            m = res.get(res.size() - 2);
            assertEquals("35", m.get("ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("10859", m.get("matchedKw").toString());
            m = res.get(res.size() - 1);
            assertEquals("36", m.get("ID").toString());
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("34483", m.get("matchedKw").toString());
        } else assertEquals(0, res.size());
    }

    @Test
    public void sumMatchedKmByEachApplicationGeneratorIdTest() {
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> res = repository.sumMatchedKmByEachApplicationGeneratorId(billDate);
        //log.info("res size:"+res.size());

        // assertion
        if (devMark || tpcMark) assertEquals(0, res.size());
        else {
            assertEquals(12, res.size()); // 10月-6月轉直 9月-6,5月轉直 8月-5月轉直 = 8個
            assertEquals(4, res.get(0).size());
            //serviceDate(Date), billDate(Date), MATCHED_KW[計費度數] PWDS[轉供直供 2,3 直供; 其他轉供]
            Map<String, Object> m = res.get(0);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("9271027", m.get("matchedKw").toString()); // 9204521 -> 9020068(bill 5/2) + 250959(noErp 5/2)
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(1);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("2295865", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(2);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("8941958", m.get("matchedKw").toString());
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(3);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("2295865", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(4);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("11525386", m.get("matchedKw").toString()); // 11240598 -> 11275164(bill 5/3) + 250222(noErp 5/2)
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(5);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("2132429", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(6);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("11233724", m.get("matchedKw").toString()); // 11240598
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(7);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("2132429", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(8);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("15626553", m.get("matchedKw").toString()); // 15409652 -> 15379859(bill 5/3) + 246694(noErp 5/2)
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(9);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("1864369", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(res.size()-2);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("15313153", m.get("matchedKw").toString());
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(res.size()-1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("1864369", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            // 2025-04
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = repository.sumMatchedKmByEachApplicationGeneratorId(billDate);
            assertEquals(6, res.size());
            m = res.get(0);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("8941958", m.get("matchedKw").toString());
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(1);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("2295865", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(2);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("11233724", m.get("matchedKw").toString()); // 11240598
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(3);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("2132429", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
            m = res.get(res.size()-2);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("15313153", m.get("matchedKw").toString());
            assertEquals("PW", m.get("PWDS").toString());
            m = res.get(res.size()-1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-04-01", m.get("billDate").toString());
            assertEquals("1864369", m.get("matchedKw").toString());
            assertEquals("DS", m.get("PWDS").toString());
        }
    }

    @Test
    public void sumMatchedKmGeneratorEndInfoTest() {
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> res = repository.sumMatchedKmGeneratorEndInfo(billDate);
        //log.info("res size:"+res.size());

        // assertion
        if (devMark || tpcMark) assertEquals(0, res.size());
        else {
            Map<String, Object> m = res.get(0);
            assertNull(m.get("applicationGeneratorId"));
            assertNull(m.get("matchedKw"));
            assertNull(m.get("PWDS"));
            assertNull(m.get("PWDS_CONTRACT_TYPE"));
            assertNull(m.get("GEN_NAME"));
            assertNull(m.get("GEN_ELEC_NO"));
            assertNull(m.get("gEntityId"));
            assertEquals("2024-07-01", m.get("serviceStart").toString()); // 服務起始月初
            assertEquals("2024-10-01", m.get("serviceEndNext").toString()); // 服務結束隔月月初
            for (int i = 0; i < 52; i++)
                assertEquals("2025-05-01", res.get(i).get("billDate").toString());
            for (int i = 52; i < res.size(); i++) {
                m = res.get(i);
                assertEquals("2025-04-01", m.get("billDate").toString());
                assertNull(m.get("PWDS"));
                assertNull(m.get("PWDS_CONTRACT_TYPE"));
                assertNull(m.get("GEN_NAME"));
                assertNull(m.get("GEN_ELEC_NO"));
                assertNull(m.get("gEntityId"));
            }
            for (int i = 1; i < res.size(); i++) {
                m = res.get(i);
                assertNull(m.get("serviceStart"));
                assertNull(m.get("serviceEndNext"));
            }
            res.remove(0);
            assertEquals(99, res.size()); // app.TYPE != 4 // 51(2025-05) + 48(2025-04)= 99
            assertEquals(10, res.get(0).size());
            // serviceDate(Date), billDate(Date), applicationGeneratorId(long) gEntityId(long)
            // -- MATCHED_KW[計費度數]  PWDS_CONTRACT_TYPE[說明: 契約類型] GEN_NAME[發電端名稱] GEN_ELEC_NO[發電端電號] PWDS[轉供直供 2,3 直供 其他轉供]
            m = res.get(0);
            assertEquals("1", m.get("applicationGeneratorId").toString());
            assertEquals("1295584", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供 (一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("18442092110", m.get("GEN_ELEC_NO").toString());
            assertEquals("26", m.get("gEntityId").toString());
            m = res.get(5);
            assertEquals("6", m.get("applicationGeneratorId").toString());
            assertEquals("277094", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供 (一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("18442092381", m.get("GEN_ELEC_NO").toString());
            assertEquals("46", m.get("gEntityId").toString());
            m = res.get(6);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("18151", m.get("matchedKw").toString()); // 2024-07-01
            assertEquals("5", m.get("PWDS").toString());
            assertEquals("轉供 (自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("信通交通器材股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("04946986547", m.get("GEN_ELEC_NO").toString());
            assertEquals("450", m.get("gEntityId").toString());
            m = res.get(18);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("747875", m.get("matchedKw").toString());
            assertEquals("Q", m.get("PWDS").toString());
            assertEquals("轉供 (小額綠電)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("台電公司南鹽光", m.get("GEN_NAME").toString());
            assertEquals("10623888006", m.get("GEN_ELEC_NO").toString());
            assertEquals("1282", m.get("gEntityId").toString());
            m = res.get(35);
            assertEquals("41", m.get("applicationGeneratorId").toString());
            assertEquals("186972", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供 (非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("台泥綠能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("08376141004", m.get("GEN_ELEC_NO").toString());
            assertEquals("487", m.get("gEntityId").toString());
            m = res.get(49);
            assertEquals("55", m.get("applicationGeneratorId").toString());
            assertEquals("37382", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供 (非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("存量能源股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("08369525025", m.get("GEN_ELEC_NO").toString());
            assertEquals("1048", m.get("gEntityId").toString());
            m = res.get(50);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("34483", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供 (非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("普雷嘉工程有限公司\t", m.get("GEN_NAME").toString());
            assertEquals("10641031703", m.get("GEN_ELEC_NO").toString());
            assertEquals("297", m.get("gEntityId").toString());
            m = res.get(51);
            assertEquals("1", m.get("applicationGeneratorId").toString());
            assertEquals("1295584", m.get("matchedKw").toString());
            m = res.get(56);
            assertEquals("6", m.get("applicationGeneratorId").toString());
            assertEquals("277094", m.get("matchedKw").toString());
            m = res.get(57);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("18151", m.get("matchedKw").toString()); // 2024-07-01
            m = res.get(70);
            assertEquals("25", m.get("applicationGeneratorId").toString()); // 2025-04 沒有 小額綠電 22
            assertEquals("72420", m.get("matchedKw").toString());
            m = res.get(86);
            assertEquals("44", m.get("applicationGeneratorId").toString()); // APPLICATION_ID = 22 APPLICATION_GENERATOR_ID = 41 2025-04 沒有結帳
            assertEquals("549934", m.get("matchedKw").toString());
            m = res.get(res.size()-2);
            assertEquals("55", m.get("applicationGeneratorId").toString());
            assertEquals("37382", m.get("matchedKw").toString());
            m = res.get(res.size()-1);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("34483", m.get("matchedKw").toString());
            // 2025-04
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = repository.sumMatchedKmGeneratorEndInfo(billDate);
            m = res.get(0);
            assertNull(m.get("applicationGeneratorId"));
            assertNull(m.get("matchedKw"));
            assertNull(m.get("PWDS"));
            assertNull(m.get("PWDS_CONTRACT_TYPE"));
            assertNull(m.get("GEN_NAME"));
            assertNull(m.get("GEN_ELEC_NO"));
            assertNull(m.get("gEntityId"));
            assertEquals("2024-07-01", m.get("serviceStart").toString()); // 服務起始月初
            assertEquals("2024-10-01", m.get("serviceEndNext").toString()); // 服務結束隔月月初
            for (int i = 0; i < res.size(); i++)
                assertEquals("2025-04-01", res.get(i).get("billDate").toString());
            for (int i = 1; i < res.size(); i++) {
                m = res.get(i);
                assertNull(m.get("serviceStart"));
                assertNull(m.get("serviceEndNext"));
            }
            res.remove(0);
            assertEquals(48, res.size());
            m = res.get(0);
            assertEquals("1", m.get("applicationGeneratorId").toString());
            assertEquals("1295584", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供 (一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("18442092110", m.get("GEN_ELEC_NO").toString());
            assertEquals("26", m.get("gEntityId").toString());
            m = res.get(5);
            assertEquals("6", m.get("applicationGeneratorId").toString());
            assertEquals("277094", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供 (一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("18442092381", m.get("GEN_ELEC_NO").toString());
            assertEquals("46", m.get("gEntityId").toString());
            m = res.get(6);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("18151", m.get("matchedKw").toString()); // 2024-07-01
            assertEquals("5", m.get("PWDS").toString());
            assertEquals("轉供 (自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("信通交通器材股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("04946986547", m.get("GEN_ELEC_NO").toString());
            assertEquals("450", m.get("gEntityId").toString());
            m = res.get(19);
            assertEquals("25", m.get("applicationGeneratorId").toString()); // 2025-04 沒有 小額綠電 22
            assertEquals("72420", m.get("matchedKw").toString());
            assertEquals("2", m.get("PWDS").toString());
            assertEquals("併網型直供 (一般直供)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("中鋼光能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("07729897768", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("gEntityId").toString());
            m = res.get(35);
            assertEquals("44", m.get("applicationGeneratorId").toString()); // APPLICATION_ID = 22 APPLICATION_GENERATOR_ID = 41 2025-04 沒有結帳
            assertEquals("549934", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供 (非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("台泥綠能股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("08376141004", m.get("GEN_ELEC_NO").toString());
            assertEquals("487", m.get("gEntityId").toString());
            m = res.get(46);
            assertEquals("55", m.get("applicationGeneratorId").toString());
            assertEquals("37382", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供 (非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("存量能源股份有限公司", m.get("GEN_NAME").toString());
            assertEquals("08369525025", m.get("GEN_ELEC_NO").toString());
            assertEquals("1048", m.get("gEntityId").toString());
            m = res.get(res.size()-1);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("34483", m.get("matchedKw").toString());
            assertEquals("1", m.get("PWDS").toString());
            assertEquals("轉供 (非自用)", m.get("PWDS_CONTRACT_TYPE").toString());
            assertEquals("普雷嘉工程有限公司\t", m.get("GEN_NAME").toString());
            assertEquals("10641031703", m.get("GEN_ELEC_NO").toString());
            assertEquals("297", m.get("gEntityId").toString());
        }
    }

    @Test // #14 調度處 發轉餘報表(單月查詢) 統計 - 再媒合表 餘電總和(一階餘電減去二階轉供)[再媒合未媒合電量]
    public void sumGenMatchedRmUnmatchedRmByDateTest() {
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> res = repository.sumGenMatchedRmUnmatchedRmByDate(billDate);

        // assertion
        if (mteT1Mark) {
            //log.info("res size:" + res.size());
            assertEquals(26, res.size());
            assertEquals(3, res.get(0).size()); // applicationGeneratorId, time, matchedRm, unmatchedRm
            Map<String, Object> m = res.get(0);
            assertEquals("8", m.get("applicationGeneratorId").toString());
            assertEquals("0.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(1);
            assertEquals("10", m.get("applicationGeneratorId").toString());
            assertEquals("14523.0000", m.get("matchedRm").toString());
            assertEquals("32021.0000", m.get("unmatchedRm").toString());
            m = res.get(2);
            assertEquals("11", m.get("applicationGeneratorId").toString());
            assertEquals("6.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(9);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("26173.0000", m.get("matchedRm").toString());
            assertEquals("5624539.0000", m.get("unmatchedRm").toString());
            m = res.get(12);
            assertEquals("41", m.get("applicationGeneratorId").toString());
            assertEquals("1.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(21);
            assertEquals("52", m.get("applicationGeneratorId").toString());
            assertEquals("6927.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(res.size()-1);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("5249.0000", m.get("matchedRm").toString());
            assertEquals("8316.0000", m.get("unmatchedRm").toString());
            // 2025-04
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = repository.sumGenMatchedRmUnmatchedRmByDate(billDate);
            //log.info("2025-04 res size:" + res.size());
            assertEquals(25, res.size());
            m = res.get(0);
            assertEquals("8", m.get("applicationGeneratorId").toString());
            assertEquals("0.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(1);
            assertEquals("10", m.get("applicationGeneratorId").toString());
            assertEquals("14523.0000", m.get("matchedRm").toString());
            assertEquals("32021.0000", m.get("unmatchedRm").toString());
            m = res.get(2);
            assertEquals("11", m.get("applicationGeneratorId").toString());
            assertEquals("6.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(8);
            assertEquals("21", m.get("applicationGeneratorId").toString());
            assertEquals("2867.0000", m.get("matchedRm").toString());
            assertEquals("2913853.0000", m.get("unmatchedRm").toString());
            m = res.get(10);
            assertEquals("40", m.get("applicationGeneratorId").toString()); // 2025-04 沒有 applicationGeneratorId = 22
            assertEquals("1960.0000", m.get("matchedRm").toString());
            assertEquals("14590.0000", m.get("unmatchedRm").toString());
            m = res.get(11);
            assertEquals("41", m.get("applicationGeneratorId").toString());
            assertEquals("1.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(20);
            assertEquals("52", m.get("applicationGeneratorId").toString());
            assertEquals("6927.0000", m.get("matchedRm").toString());
            assertEquals("0.0000", m.get("unmatchedRm").toString());
            m = res.get(res.size()-1);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("5249.0000", m.get("matchedRm").toString());
            assertEquals("8316.0000", m.get("unmatchedRm").toString());
        } else {
            assertEquals(0, res.size());
            /*serviceStart = new GregorianCalendar(2024, Calendar.JUNE, 1).getTime();
            serviceEnd = new GregorianCalendar(2024, Calendar.JULY, 1).getTime();
            res = repository.sumGenMatchedRmUnmatchedRmByDate(serviceStart, serviceEnd);
            //log.info("res size:" + res.size());
            assertEquals(6, res.size());
            assertEquals(4, res.get(0).size()); // applicationGeneratorId, time, matchedRm, unmatchedRm*/
        }
    }

    @Test
    public void findLoadFuelTypeTpcCompanyVoltLevelTest() { // #17 用電端
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> info = repository.findLoadFuelTypeTpcCompanyVoltLevel(billDate);
        //log.info("size:"+info.size());
        // assertion
        if (mteT1Mark) {
            assertEquals(128, info.size()); // matchedKw 1294
            Map<String, Object> m = info.get(0);
            assertEquals(7, m.size());
            //assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("10", m.get("applicationGeneratorId").toString());
            assertEquals("88", m.get("applicationLoadId").toString());
            assertEquals("10729", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("00", m.get("TPC_CODE").toString());
            m = info.get(1);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("10", m.get("applicationGeneratorId").toString());
            assertEquals("89", m.get("applicationLoadId").toString());
            assertEquals("7929", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("00", m.get("TPC_CODE").toString());
            m = info.get(4);
            //assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("11", m.get("applicationGeneratorId").toString());
            assertEquals("92", m.get("applicationLoadId").toString());
            assertEquals("427361", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("00", m.get("TPC_CODE").toString());
            m = info.get(33);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("44", m.get("applicationGeneratorId").toString());
            assertEquals("116", m.get("applicationLoadId").toString());
            assertEquals("549934", m.get("A_KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("04", m.get("TPC_CODE").toString());
            m = info.get(60);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("41", m.get("applicationGeneratorId").toString());
            assertEquals("110", m.get("applicationLoadId").toString());
            assertEquals("9021", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("06", m.get("TPC_CODE").toString());
            m = info.get(90);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("11", m.get("applicationGeneratorId").toString());
            assertEquals("15", m.get("applicationLoadId").toString());
            assertEquals("180163", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("11", m.get("TPC_CODE").toString());
            m = info.get(118);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("6", m.get("applicationGeneratorId").toString());
            assertEquals("1", m.get("applicationLoadId").toString());
            assertEquals("277094", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("18", m.get("TPC_CODE").toString());
            m = info.get(info.size()-1);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("23", m.get("applicationGeneratorId").toString());
            assertEquals("53", m.get("applicationLoadId").toString());
            assertEquals("56347", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("21", m.get("TPC_CODE").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void findGeneratorFuelTypeTpcCompanyVoltLevelTest() { // #17 發電端
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> info = repository.findGeneratorFuelTypeTpcCompanyVoltLevel(billDate);
        //log.info("size:"+info.size());
        // assertion
        if (mteT1Mark) {
            assertEquals(128, info.size()); // matchedKw 1294
            Map<String, Object> m = info.get(0);
            assertEquals(7, m.size());
            //assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("2", m.get("applicationLoadId").toString());
            assertEquals("18151", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("04", m.get("TPC_CODE").toString());
            m = info.get(1);
            //assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("42", m.get("applicationGeneratorId").toString());
            assertEquals("114", m.get("applicationLoadId").toString());
            assertEquals("37927", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("04", m.get("TPC_CODE").toString());
            m = info.get(25);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("48", m.get("applicationGeneratorId").toString());
            assertEquals("122", m.get("applicationLoadId").toString());
            assertEquals("3383", m.get("A_KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("08", m.get("TPC_CODE").toString());
            m = info.get(36);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("47", m.get("applicationGeneratorId").toString());
            assertEquals("121", m.get("applicationLoadId").toString());
            assertEquals("30737", m.get("A_KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("風力能(WP)", m.get("FUEL_LABEL").toString());
            assertEquals("08", m.get("TPC_CODE").toString());
            m = info.get(40);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("50", m.get("applicationGeneratorId").toString());
            assertEquals("125", m.get("applicationLoadId").toString());
            assertEquals("128034", m.get("A_KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("09", m.get("TPC_CODE").toString());
            m = info.get(94);
            //assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("40", m.get("applicationGeneratorId").toString());
            assertEquals("109", m.get("applicationLoadId").toString());
            assertEquals("701379", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("18", m.get("TPC_CODE").toString());
            m = info.get(99);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("18", m.get("applicationGeneratorId").toString());
            assertEquals("32", m.get("applicationLoadId").toString());
            assertEquals("381300", m.get("A_KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("20", m.get("TPC_CODE").toString());
            m = info.get(info.size()-1);
            //assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("2025-05-01", m.get("billDate").toString());
            assertEquals("54", m.get("applicationGeneratorId").toString());
            assertEquals("128", m.get("applicationLoadId").toString());
            assertEquals("382857", m.get("A_KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("太陽能(PV)", m.get("FUEL_LABEL").toString());
            assertEquals("21", m.get("TPC_CODE").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void findSettleServiceDatesTest() { // #16 #18 #19 調度處 取出 所有服務月列表
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> info = repository.findSettleServiceDates(billDate);
        // assertion
        if (mteT1Mark) {
            assertEquals(3, info.size());
            assertEquals("2024-07-01", info.get(0).get("serviceDate").toString());
            assertEquals("2024-08-01", info.get(1).get("serviceDate").toString());
            assertEquals("2024-09-01", info.get(info.size()-1).get("serviceDate").toString());
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            info = repository.findSettleServiceDates(billDate);
            assertEquals(3, info.size());
            assertEquals("2024-07-01", info.get(0).get("serviceDate").toString());
            assertEquals("2024-08-01", info.get(1).get("serviceDate").toString());
            assertEquals("2024-09-01", info.get(info.size()-1).get("serviceDate").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void sumExpsByServiceDateBillDateTest() { // #15
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> info = repository.sumExpsByServiceDateBillDate(billDate);
        //log.info("info size:"+info.size());
        // assertion
        if (mteT1Mark) {
            assertEquals(5, info.size());
            for (int i =0;i <info.size();i++) {
                assertEquals("2025-05-01", info.get(i).get("billDate").toString());
            }
            assertEquals("16836674", info.get(0).get("matchedKw").toString());
            assertEquals("1", info.get(0).get("powerType").toString());
            assertEquals("18820266", info.get(1).get("matchedKw").toString());
            assertEquals("12", info.get(1).get("powerType").toString());
            assertEquals("18151", info.get(2).get("matchedKw").toString());
            assertEquals("2", info.get(2).get("powerType").toString());
            assertEquals("41967754", info.get(3).get("matchedKw").toString());
            assertEquals("3", info.get(3).get("powerType").toString());
            assertEquals("747875", info.get(info.size()-1).get("matchedKw").toString());
            assertEquals("Q", info.get(info.size()-1).get("powerType").toString());
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            info = repository.sumExpsByServiceDateBillDate(billDate); // 2025-04-01 小額綠電無結帳
            assertEquals(4, info.size());
            for (int i =0;i <info.size();i++) {
                assertEquals("2025-04-01", info.get(i).get("billDate").toString());
            }
            assertEquals("17631074", info.get(0).get("matchedKw").toString());
            assertEquals("1", info.get(0).get("powerType").toString());
            assertEquals("18634010", info.get(1).get("matchedKw").toString());
            assertEquals("12", info.get(1).get("powerType").toString());
            assertEquals("18151", info.get(2).get("matchedKw").toString());
            assertEquals("2", info.get(2).get("powerType").toString());
            assertEquals("42575898", info.get(info.size()-1).get("matchedKw").toString());
            assertEquals("3", info.get(info.size()-1).get("powerType").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void findExpVoltInfoByServiceDatesTest() {
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> info = repository.findExpVoltInfoByServiceDates(billDate);
        //log.info("findExpVoltInfoByServiceDates-info size:"+info.size());
        // assertion
        if (mteT1Mark) {
            assertEquals(15, info.size()); // 1572
            Map<String, Object> m = info.get(0);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("16427.0000", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(1);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("108363.0000", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(2);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("10984.0000", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(3);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("38615.0000", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(4);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("8377.0000", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
            m = info.get(5);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("15496.0000", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(6);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("148797.0000", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(7);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("7173.0000", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(8);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("35995.0000", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(9);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("8331.0000", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
            m = info.get(10);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("14491.0000", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(11);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("212556.0000", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(12);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("9634.0000", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(13);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("31702.0000", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(info.size()-1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("7972.0000", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
            // ---- 2025-04
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            info = repository.findExpVoltInfoByServiceDates(billDate);
            assertEquals(15, info.size()); // 1572
            m = info.get(0);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("12462.0000", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(1);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("108363.0000", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(2);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("9750.0000", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(3);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("38615.0000", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(4);
            assertEquals("2024-07-01", m.get("serviceDate").toString());
            assertEquals("8377.0000", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
            m = info.get(5);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("18348.0000", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(6);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("148797.0000", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(7);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("6582.0000", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(8);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("35995.0000", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(9);
            assertEquals("2024-08-01", m.get("serviceDate").toString());
            assertEquals("7478.0000", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
            m = info.get(10);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("17919.0000", m.get("KWH").toString());
            assertEquals("161kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("2", m.get("ID").toString());
            m = info.get(11);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("212556.0000", m.get("KWH").toString());
            assertEquals("69kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("3", m.get("ID").toString());
            m = info.get(12);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("8505.0000", m.get("KWH").toString());
            assertEquals("22.8kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("4", m.get("ID").toString());
            m = info.get(13);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("31702.0000", m.get("KWH").toString());
            assertEquals("11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("5", m.get("ID").toString());
            m = info.get(info.size()-1);
            assertEquals("2024-09-01", m.get("serviceDate").toString());
            assertEquals("7255.0000", m.get("KWH").toString());
            assertEquals("不滿11.4kV", m.get("EQUIP_VOLT_LEVEL").toString());
            assertEquals("6", m.get("ID").toString());
        } else assertEquals(0, info.size());
    }

    @Test
    public void findApplicationGeneratorLoadContractsByFixTypeTest() { // #10
        Date start = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date end = new GregorianCalendar(2026, Calendar.JANUARY, 31).getTime();
        List<Map<String, Object>> info = repository.findApplicationGeneratorLoadContractsByFixType(start, end);
        //log.info("findApplicationGeneratorLoadContractsByFixType-info-size:"+info.size());

        if (tpcMark) assertEquals(3, info.size());
        else if (mteT1Mark) assertEquals(27, info.size());
        else assertEquals(0, info.size());
    }

    @Test
    public void findApplicationGeneratorLoadSettleInfoByFixTypeTest() { // #5
        Date start = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date end = new GregorianCalendar(2025, Calendar.AUGUST, 1).getTime();
        List<Map<String, Object>> info = repository.findApplicationGeneratorLoadSettleInfoByFixType(start, end);
        //log.info("findApplicationGeneratorLoadSettleInfoByFixType-info-size:"+info.size());

        // assertion
        if (tpcMark) assertEquals(3, info.size());
        else if (mteT1Mark) assertEquals(27, info.size());
        else assertEquals(0, info.size());
    }

    @Test // #7 15分鐘發電端媒合度數(限定 單月 服務年月 搜尋)
    public void find15MinutesGeneratorSettleInfoByServiceDateTest_sizeAsDistinctGeneratorIdSet_noApplicationType4_noChangeMeter() {
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> m15minGenList = repository.find15MinutesGeneratorSettleInfoByServiceDate(billDate);
        //log.info("month mLoadsList size:" + m15minGenList.size()); // 12 個 有換表

        // assertion
        if (mteT1Mark) {
            /*List<String> allApGenId = repository.findAllApplicationGeneratorIdApplicationLoadId(startDate, endDate); // 30 個
            assertTrue(m15minGenList.size() <= allApGenId.size());
            Set<Long> apGenIdSet = new HashSet<>();
            for (String ml : allApGenId) {
                if (apGenIdSet.add(Long.parseLong(ml.split("-")[0]))) {}
            }
            List<Long> applicationGeneratorIdList = new ArrayList<>(apGenIdSet);
            List<Map<String, Object>> res = applicationGeneratorRepository.findApplicationMonthlyGeneratorsByServiceDate(applicationGeneratorIdList
                    , startDate);
            //log.info("apGenIdSet:"+applicationGeneratorIdList+", res size:"+res.size()); //17 個 =16 個apGenId + 1個換表 -> 18
            assertEquals(res.size(), m15minGenList.size()); // 18 >= 12
             */
            assertEquals(142, m15minGenList.size());
            //for (int i =0;i<m15minGenList.size();i++)
            //    assertEquals("2025-05-01", m15minGenList.get(i).get("billDate").toString());
            for (int i =0;i<45;i++) {
                Map<String, Object> m = m15minGenList.get(i);
                assertEquals("367", m.get("SETTLEMENT_ID").toString());
                assertEquals("2024-07-01", m.get("serviceDate").toString());
                assertNull(m.get("genMeterReplaceDate"));
                assertNull(m.get("oldGenMeterNo"));
                assertNull(m.get("oGMeterNext"));
            }
            for (int i =46;i<93;i++) {
                Map<String, Object> m = m15minGenList.get(i);
                assertEquals("368", m.get("SETTLEMENT_ID").toString());
                assertEquals("2024-08-01", m.get("serviceDate").toString());
                assertNull(m.get("genMeterReplaceDate"));
                assertNull(m.get("oldGenMeterNo"));
                assertNull(m.get("oGMeterNext"));
            }
            for (int i =93;i<m15minGenList.size();i++) {
                Map<String, Object> m = m15minGenList.get(i);
                assertEquals("372", m.get("SETTLEMENT_ID").toString());
                assertEquals("2024-09-01", m.get("serviceDate").toString());
                if (null != m.get("genMeterReplaceDate") && m.get("genMeterReplaceDate").toString().equals("2024-09-02 12:00:00.0")) {
                    assertEquals("TU19619813", m.get("oldGenMeterNo").toString());
                } else {
                    assertNull(m.get("genMeterReplaceDate"));
                    assertNull(m.get("oldGenMeterNo"));
                    assertNull(m.get("oGMeterNext"));
                }
            }
            Map<String, Object> m = m15minGenList.get(0);
            assertEquals("1", m.get("applicationGeneratorId").toString());
            assertEquals("WB15001444", m.get("GEN_METER_NO").toString());
            assertEquals("18442092110", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-18-001-01", m.get("SERVICE_ID").toString());
            m = m15minGenList.get(6);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("RU19014595", m.get("GEN_METER_NO").toString());
            assertEquals("04946986547", m.get("GEN_ELEC_NO").toString());
            assertEquals("5", m.get("appType").toString());
            assertEquals("1-10902-04-001-02", m.get("SERVICE_ID").toString());
            m = m15minGenList.get(16);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("XG83888006", m.get("GEN_METER_NO").toString());
            assertEquals("10623888006", m.get("GEN_ELEC_NO").toString());
            assertEquals("Q", m.get("appType").toString());
            assertEquals("1-11211-10-004-00", m.get("SERVICE_ID").toString()); // 18
            m = m15minGenList.get(45);
            assertEquals("55", m.get("applicationGeneratorId").toString());
            assertEquals("KU21318071", m.get("GEN_METER_NO").toString());
            assertEquals("08369525025", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11204-08-002-00", m.get("SERVICE_ID").toString());
            m = m15minGenList.get(51);
            assertEquals("6", m.get("applicationGeneratorId").toString()); // 2024-08-01
            assertEquals("WB20202681", m.get("GEN_METER_NO").toString());
            assertEquals("18442092381", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-18-001-01", m.get("SERVICE_ID").toString());
            m = m15minGenList.get(52);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("RU19014595", m.get("GEN_METER_NO").toString());
            assertEquals("04946986547", m.get("GEN_ELEC_NO").toString());
            assertEquals("5", m.get("appType").toString());
            assertEquals("1-10902-04-001-02", m.get("SERVICE_ID").toString());
            m = m15minGenList.get(63);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("XG83888006", m.get("GEN_METER_NO").toString());
            assertEquals("10623888006", m.get("GEN_ELEC_NO").toString());
            assertEquals("Q", m.get("appType").toString());
            assertEquals("1-11211-10-004-00", m.get("SERVICE_ID").toString()); // 18
            m = m15minGenList.get(91);
            assertEquals("54", m.get("applicationGeneratorId").toString());
            assertEquals("WB20202967", m.get("GEN_METER_NO").toString());
            assertEquals("21954994920", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11112-21-002-01", m.get("SERVICE_ID").toString());
            m = m15minGenList.get(94);
            assertEquals("2", m.get("applicationGeneratorId").toString()); // 2024-09-01
            assertEquals("WB18081624", m.get("GEN_METER_NO").toString());
            assertEquals("18442092154", m.get("GEN_ELEC_NO").toString());
            assertEquals("2", m.get("appType").toString());
            assertEquals("2-11112-18-001-01", m.get("SERVICE_ID").toString());
            m = m15minGenList.get(99);
            assertEquals("7", m.get("applicationGeneratorId").toString());
            assertEquals("RU19014595", m.get("GEN_METER_NO").toString());
            assertEquals("04946986547", m.get("GEN_ELEC_NO").toString());
            assertEquals("5", m.get("appType").toString());
            assertEquals("1-10902-04-001-02", m.get("SERVICE_ID").toString());
            m = m15minGenList.get(110);
            assertEquals("22", m.get("applicationGeneratorId").toString());
            assertEquals("XG83888006", m.get("GEN_METER_NO").toString());
            assertEquals("10623888006", m.get("GEN_ELEC_NO").toString());
            assertEquals("Q", m.get("appType").toString());
            assertEquals("1-11211-10-004-00", m.get("SERVICE_ID").toString()); // 18
            m = m15minGenList.get(137);
            assertEquals("52", m.get("applicationGeneratorId").toString());
            assertEquals("KU23702412", m.get("GEN_METER_NO").toString());
            assertEquals("18857150021", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11307-18-002-00", m.get("SERVICE_ID").toString());
            m = m15minGenList.get(m15minGenList.size()-1);
            assertEquals("56", m.get("applicationGeneratorId").toString());
            assertEquals("TU21725289", m.get("GEN_METER_NO").toString());
            assertEquals("10641031703", m.get("GEN_ELEC_NO").toString());
            assertEquals("1", m.get("appType").toString());
            assertEquals("1-11307-10-002-00", m.get("SERVICE_ID").toString());

            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            m15minGenList = repository.find15MinutesGeneratorSettleInfoByServiceDate(billDate);
            assertEquals(134, m15minGenList.size());
        } else assertTrue(m15minGenList.isEmpty());
    }

    @Test
    public void findMonthlyApplicationLoadByBillDateRangeTest() { // #11
        Date billStart = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date billEnd = new GregorianCalendar(2026, Calendar.JANUARY, 1).getTime();
        List<Map<String, Object>> res = repository.findMonthlyApplicationLoadByBillDateRange(billStart, billEnd);
        // assertion
        assertEquals(1140, res.size()); // 1140
        Map<String, Object> m = res.get(0);
        assertEquals("2024-07-01", m.get("serviceDate").toString());
        assertEquals("2024-09-01", res.get(res.size()-1).get("serviceDate").toString());
        String keepKey = m.get("serviceDate").toString()+"~"+m.get("applicationLoadId").toString()+"~"+m.get("ENERGY_CHARGE_SECTION_ID").toString();
        String keepSettleId = m.get("SETTLEMENT_ID").toString();
        for (int i = 1; i < res.size()-1; i++) {
            m = res.get(i);
            String thisKey = m.get("serviceDate").toString()+"~"+m.get("applicationLoadId").toString()+"~"+m.get("ENERGY_CHARGE_SECTION_ID").toString();
            if (keepKey.equals(thisKey))
                assertEquals(keepSettleId, m.get("SETTLEMENT_ID").toString());
            assertNotEquals(keepKey, thisKey);
            keepKey = thisKey;
        }
    }

    @Test
    public void findApplicationGeneratorSettleInfoTest() { // #3 #4
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Map<String, Object>> res = repository.findApplicationGeneratorSettleInfo(billDate);
        // assertion
        assertEquals(568, res.size());
        Map<String, Object> m = res.get(0);
        assertEquals("2024-07-01", m.get("serviceDate").toString());
        assertEquals("2024-09-01", res.get(res.size()-1).get("serviceDate").toString());

        for (int i =0; i< res.size(); i++) {
            m = res.get(i);
            if (m.get("GEN_ELEC_NO").toString().equals("18857150021") && m.get("serviceDate").toString().equals("2024-09-01")) {
                assertEquals("2024-09-02 12:00:00.0", m.get("GEN_METER_CHANGE_DATE").toString());
                assertEquals("KU23702412", m.get("METER_NO").toString());
                assertEquals("TU19619813", m.get("oldGenMeterNo").toString());
            } else {
                assertNull(m.get("GEN_METER_CHANGE_DATE"));
                assertNull(m.get("oldGenMeterNo"));
            }
        }
        // 2025-04
        billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
        res = repository.findApplicationGeneratorSettleInfo(billDate);
        assertEquals(544, res.size());
        m = res.get(0);
        assertEquals("2024-07-01", m.get("serviceDate").toString());
        assertEquals("2024-09-01", res.get(res.size()-1).get("serviceDate").toString());

        for (int i =0; i< res.size(); i++) {
            m = res.get(i);
            if (m.get("GEN_ELEC_NO").toString().equals("18857150021") && m.get("serviceDate").toString().equals("2024-09-01")) {
                assertEquals("2024-09-02 12:00:00.0", m.get("GEN_METER_CHANGE_DATE").toString());
                assertEquals("KU23702412", m.get("METER_NO").toString());
                assertEquals("TU19619813", m.get("oldGenMeterNo").toString());
            } else {
                assertNull(m.get("GEN_METER_CHANGE_DATE"));
                assertNull(m.get("oldGenMeterNo"));
            }
        }
    }

    @Test
    public void findAllSettlementIdTest() {
        Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<Long> settleIds = repository.findAllSettlementId(billDate);
        // assertion
        assertEquals(3, settleIds.size());
        assertEquals("367", settleIds.get(0).toString());
        assertEquals("368", settleIds.get(1).toString());
        assertEquals("372", settleIds.get(settleIds.size()-1).toString());
        // 2025-04
        billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
        settleIds = repository.findAllSettlementId(billDate);
        assertEquals(5, settleIds.size());
        assertEquals("360", settleIds.get(0).toString());
        assertEquals("361", settleIds.get(1).toString());
        assertEquals("362", settleIds.get(2).toString());
        assertEquals("375", settleIds.get(3).toString());
        assertEquals("376", settleIds.get(settleIds.size()-1).toString());
    }

    @Test
    public void findApplicationGeneratorIdsByServiceDateTest() {
        Date startDate = tpcMark? new GregorianCalendar(2025, Calendar.MARCH, 1).getTime()
                : new GregorianCalendar(2024, Calendar.JULY, 1).getTime();
        Date endDate = tpcMark? new GregorianCalendar(2025, Calendar.APRIL, 1).getTime()
                : new GregorianCalendar(2024, Calendar.AUGUST, 1).getTime();
        List<Long> applicationGeneratorIdList = repository.findApplicationGeneratorIdsByServiceDate(startDate, endDate);
        //log.info("month applicationGeneratorIdList:"+applicationGeneratorIdList+", size:" + applicationGeneratorIdList.size()); //16
        // assertion
        if (mteT1Mark) assertEquals(46, applicationGeneratorIdList.size());
        else assertEquals(0, applicationGeneratorIdList.size());
    }

    @Test
    public void findByIdTest() {
        Long ss = applicationMonthlySettlementRepository.count();
        //log.info("[mte-s1]"+ss);
        //if (devMark) assertEquals(73, ss);
        //else assertEquals(0L, ss);
        assertEquals(0L, ss);
    }
}