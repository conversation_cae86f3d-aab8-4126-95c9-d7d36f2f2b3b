package tw.com.taipower.data.repository.pwoms;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.data.AbstractRepositoryTest;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorRecord;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @class: ApplicationMonthlyGeneratorRecordRepositoryTest
 * @author: ting
 * @version:
 * @since: 2024-05-30 17:31
 * @see:
 **/

@Log4j2
@SpringBootTest
@ActiveProfiles("mte-t1-test-s1")
public class ApplicationMonthlyGeneratorRecordRepositoryTest extends AbstractRepositoryTest {

    @Autowired
    private ApplicationMonthlyGeneratorRecordRepository repository;

    @Test
    public void findAll(){
        Long generatorRecordCount = repository.count();
        //System.out.println(generatorRecordCount);
    }

    @Test
    public void findMatchedRmTimeSlotByApplicationGeneratorIdsDateRangeTest() {
        List<Long> applicationGeneratorIdList = List.of(1L,2L,3L,4L,5L,6L,7L,8L,10L,11L,12L,14L,16L,17L,18L,20L,21L,22L,23L,24L,25L,26L,27L,28L,29L,30L,31L,32L,33L,34L,35L,36L,37L,38L,40L,41L,43L,44L,45L,46L,47L,48L,49L,50L,51L,53L,54L,55L);
                //List.of(51L,52L,53L,54L,55L,56L,88L,89L,90L,91L,3485L,3486L,3487L,3488L,3489L,3490L); //,108L,109L,110L,111L,112L,113L
        Date serviceDate = new GregorianCalendar(2024, Calendar.AUGUST, 1).getTime();
        List<Map<String, Object>> res = repository.findMatchedRmTimeSlotByApplicationGeneratorIdsDate(
                serviceDate);
        //System.out.println("res size:"+res.size()); // 64 -> 72

        // assertion
        assertEquals(192, res.size());
        assertEquals(5, res.get(0).size()); // applicationGeneratorId, energyChangeSectionId, matchedRM, TIME_PRICE_STAGE, dirMatched
        assertEquals(0, res.size()%4);
        assertEquals(applicationGeneratorIdList.size()*4, res.size());
    }

    @Test
    public void findMatchedByServiceDateRangeTest() {
        //List<Long> applicationGeneratorIdList = List.of(51L, 52L, 53L, 54L, 55L);
        Date billStart = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date billEnd = new GregorianCalendar(2025, Calendar.OCTOBER, 31).getTime();
        List<Map<String, Object>> res = repository.findMatchedByServiceDateRange(billStart, billEnd);
        //System.out.println("findMatchedByServiceDateRange size:"+res.size());

        // assertion
        assertEquals(1300, res.size());
    }
}