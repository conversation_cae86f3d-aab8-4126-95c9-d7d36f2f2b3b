package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Application Relation
 *
 * @class: ApplicationRelation
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-11 09:52
 * @see:
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Entity
@Table(name = "SIMULATION_APPLICATION_RELATION")
@IdClass(ApplicationRelationId.class)
public class SimulationApplicationRelation {

    @Id
    private Date date;

    @Id
    @Column(name = "APPLICATION_ID")
    private Long applicationId;

    @Id
    @Column(name = "RELATION_ID")
    private Long appRelationId;
}