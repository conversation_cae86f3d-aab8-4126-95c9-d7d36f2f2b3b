package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationDailyGeneratorRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationDailyGeneratorRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * Repository of TempApplicationDailyGeneratorRecord
 *
 * @class: TempApplicationDailyGeneratorRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationDailyGeneratorRecordRepository extends JpaRepository<SimulationTempApplicationDailyGeneratorRecord, ApplicationMonthlyGeneratorRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD(   " +
            "        DATE   " +
            "      , ENERGY_CHARGE_SECTION_ID   " +
            "      , APPLICATION_GENERATOR_ID   " +
            "      , UNMATCHED_RM   " +
            "      , MATCHED_RM   " +
            "      , SETTLEMENT_ID)   " +
            "SELECT CAST(DATETIME AS DATE) AS DATE   " +
            "     , ENERGY_CHARGE_SECTION_ID   " +
            "     , APPLICATION_GENERATOR_ID   " +
            "     , SUM(UNMATCHED_RM) AS SUM_UNMATCHED_RM   " +
            "     , SUM(MATCHED_RM) AS SUM_MATCHED_RM   " +
            "     , :settlementId   " +
            "FROM SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD   " +
            "WHERE DATETIME BETWEEN :startTime AND :endTime   " +
            "  AND SETTLEMENT_ID = :settlementId   " +
            "GROUP BY CAST(DATETIME AS DATE), ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID   ", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId);

    @Query(value = "SELECT APPLICATION_GENERATOR_ID AS ID, ENERGY_CHARGE_SECTION_ID, SUM(UNMATCHED_RM) AS UNMATCHED_CAPACITY, SUM(MATCHED_RM) AS MATCHED_CAPACITY     " +
            "FROM SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD     " +
            "    WHERE  APPLICATION_GENERATOR_ID IN (:appGenIdList)     " +
            "      AND SETTLEMENT_ID = :settlementId     " +
            "      AND DATE BETWEEN :startDate AND :endDate     " +
            "GROUP BY APPLICATION_GENERATOR_ID, ENERGY_CHARGE_SECTION_ID     ", nativeQuery = true)
    List<Map<String, Object>> sumByDateGroupByApplicationLoadIdAndEnergyChargeSectionId(@Param("startDate") Date startDate, @Param("endDate")Date endDate, @Param("settlementId") Long settlementId, @Param("appGenIdList") List<Long> appGenIdList);


}