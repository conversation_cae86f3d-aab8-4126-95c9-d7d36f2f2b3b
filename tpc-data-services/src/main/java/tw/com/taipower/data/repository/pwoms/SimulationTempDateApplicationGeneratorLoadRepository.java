package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationTempDateApplicationGeneratorLoad;
import tw.com.taipower.data.entity.pwoms.TempDateApplicationGeneratorLoad;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of TempDateApplicationGeneratorLoad
 *
 * @class: TempDateApplicationGeneratorLoadRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-03 21:38
 * @see:
 **/

public interface SimulationTempDateApplicationGeneratorLoadRepository extends JpaRepository<SimulationTempDateApplicationGeneratorLoad, Long> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN   " +
            "    DECLARE @date DATE = :startDate   " +
            "   " +
            "    WHILE @date <= :endDate   " +
            "        BEGIN   " +
            "            INSERT INTO SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD(   " +
            "                                                              DATE   " +
            "                                                            , APPLICATION_ID   " +
            "                                                            , APPLICATION_LOAD_ID   " +
            "                                                            , APPLICATION_GENERATOR_ID   " +
            "                                                            , SETTLEMENT_ID)   " +
            "            SELECT DISTINCT @date, SUBQUERY.APPLICATION_ID, SUBQUERY.APPLICATION_LOAD_ID, SUBQUERY.APPLICATION_GENERATOR_ID, :settlementId   " +
            "                FROM (SELECT VALM.APPLICATION_ID, VALM.APPLICATION_LOAD_ID, VAGM.APPLICATION_GENERATOR_ID         " +
            "                               FROM VIEW_APPLICATION_LOAD_METER AS VALM         " +
            "                                        INNER JOIN dbo.APPLICATION A         " +
            "                                                   ON A.ID = VALM.APPLICATION_ID    " +
            "                                                   AND A.ONLINE_AT <= @date " +
            "                                                   AND CONTRACT_STATUS != -1      " +
            "                                                   AND ((A.CONTRACTED_END >= @date   ) OR (A.CONTRACTED_END IS NULL))    " +
            "                                  INNER JOIN VIEW_APPLICATION_GENERATOR_METER AS VAGM    " +
            "                                                   ON VAGM.APPLICATION_ID = A.ID         " +
            "                                                       AND (VAGM.USE_FROM <= @date   )    " +
            "                                                       AND ((VAGM.USE_TO > @date   ) OR (VAGM.USE_TO IS NULL))    " +
            "                               WHERE VALM.USE_FROM <= @date       " +
            "                                 AND (CONTRACT_NO  + '-' +  VERSION) = :contractVer    " +
            "                                 AND ((VALM.USE_TO > @date   ) OR (VALM.USE_TO IS NULL))) AS SUBQUERY" +
            "            SET @date = DATEADD(day, 1, @date)   " +
            "        END   " +
            "END", nativeQuery = true)
    void saveAllByDateIntervalAndSettlementId(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId, @Param("contractVer") String contractVer);

    @Query(value = "SELECT * FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL   " +
            "    WHERE TDAGL.APPLICATION_ID IN (:appIdList)   " +
            "      AND TDAGL.SETTLEMENT_ID = :settlementId  " +
            "      AND TDAGL.DATE = :date", nativeQuery = true)
    List<TempDateApplicationGeneratorLoad> findDateAndSettlementIdAndApplicationIdIn(@Param("date") Date date, @Param("settlementId") Long settlementId, @Param("appIdList") List<Long> appIdList);

    @Query(value = "SELECT DISTINCT APPLICATION_ID, APPLICATION_LOAD_ID, APPLICATION_GENERATOR_ID FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL    " +
            "                  WHERE TDAGL.APPLICATION_ID IN (:appIdList)          " +
            "                    AND TDAGL.SETTLEMENT_ID = :settlementId         " +
            "                    AND TDAGL.DATE BETWEEN :startDate AND :endDate", nativeQuery = true)
    List<Map<String, Object>> findDateAndSettlementIdIntervalAndApplicationIdIn(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId, @Param("appIdList") List<Long> appIdList);


}
