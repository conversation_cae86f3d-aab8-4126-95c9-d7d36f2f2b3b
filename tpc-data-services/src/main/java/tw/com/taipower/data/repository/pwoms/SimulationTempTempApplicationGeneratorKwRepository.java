package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationTempTempApplicationGeneratorKw;
import tw.com.taipower.data.entity.pwoms.TempTempApplicationGeneratorKw;
import tw.com.taipower.data.entity.pwoms.TempTempApplicationGeneratorKwId;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of TempTempApplicationGeneratorKw
 *
 * @class: SimulationTempTempApplicationGeneratorKwRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-14 22:02
 * @see:
 **/
public interface SimulationTempTempApplicationGeneratorKwRepository extends JpaRepository<SimulationTempTempApplicationGeneratorKw, TempTempApplicationGeneratorKwId> {

    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW", nativeQuery = true)
    void deleteAll();

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW " +
            "    (DATETIME, APPLICATION_GENERATOR_ID, ENERGY_CHARGE_SECTION_ID, KW_RATIO, GMI, KW_UPDATETIME) " +
            "    SELECT DATETIME, :destAppGenId, ENERGY_CHARGE_SECTION_ID, KW_RATIO, (IIF(KW_RATIO <= (:capacity *0.25), KW_RATIO, (:capacity *0.25))) * (:pmi * 0.01), KW_UPDATETIME FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW " +
            "            WHERE APPLICATION_GENERATOR_ID = :sourceAppGenId " +
            "                 AND DATETIME BETWEEN :startTime AND :endTime ", nativeQuery = true)
    void saveByApplicationGenerator(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("sourceAppGenId") Long sourceAppGenId, @Param("destAppGenId")Long destAppGenId, @Param("capacity")BigDecimal capacity, @Param("pmi")BigDecimal pmi);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW    " +
            "(DATETIME, APPLICATION_GENERATOR_ID, ENERGY_CHARGE_SECTION_ID, KW_RATIO, GMI, KW_UPDATETIME)    " +
            "    SELECT DATETIME, AG.ID, ENERGY_CHARGE_SECTION_ID    " +
            "         , KW_RATIO    " +
            "         , (IIF(KW_RATIO <= (:capacity *0.25), KW_RATIO, (:capacity *0.25))) * (AG.PMI * 0.01) AS GMI    " +
            "         , KW_UPDATETIME    " +
            "    FROM    " +
            "    (SELECT DATETIME, CONVERT(BIGINT, value) AS GENERATOR_ID, ENERGY_CHARGE_SECTION_ID, KW_RATIO, KW_UPDATETIME FROM    " +
            "        (SELECT DATETIME, ENERGY_CHARGE_SECTION_ID    " +
            "            , KW_RATIO    " +
            "            , KW_UPDATETIME    " +
            "            FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW TTAGK    " +
            "            WHERE APPLICATION_GENERATOR_ID = :srcAppGenId    " +
            "            AND DATETIME BETWEEN :startTime AND :endTime) SQL_LAYER1    " +
            "        CROSS APPLY STRING_SPLIT(:destIdList, ',')) SQL_LAYER2    " +
            "        INNER JOIN SIMULATION_APPLICATION_GENERATOR AG    " +
            "            ON AG.ID = SQL_LAYER2.GENERATOR_ID ", nativeQuery = true)
    void saveByApplicationGenerator(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("srcAppGenId") Long srcAppGenId,  @Param("destIdList")String destIdList, @Param("capacity")BigDecimal capacity);

    @Query(value = "SELECT DATETIME, SUM(GMI) AS SUM_GMI FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW " +
            "    WHERE DATETIME BETWEEN :startTime AND :endTime " +
            "        AND APPLICATION_GENERATOR_ID IN (:appGenIdList) " +
            "GROUP BY DATETIME ", nativeQuery = true)
    List<Map<String, Object>> sumGmiByDatetimeIntervalAndApplicationGeneratorIdIn(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appGenIdList") List<Long> appGenIdList);

    @Query(value = "SELECT * FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW " +
            "WHERE DATETIME BETWEEN :startTime AND :endTime " +
            "  AND APPLICATION_GENERATOR_ID IN (:appGenIdList) " +
            "ORDER BY APPLICATION_GENERATOR_ID, DATETIME ", nativeQuery = true)
    List<TempTempApplicationGeneratorKw> findByDatetimeIntervalAndApplicationLoadIdIn(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appGenIdList") List<Long> appGenIdList);

    @Transactional
    @Modifying
    @Query(value = "UPDATE SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW  " +
            "SET GMI = TATGR.UNMATCHED_RM  " +
            "FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW  AS TTAGK  " +
            "         INNER JOIN SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD AS TATGR  " +
            "                    ON TATGR.APPLICATION_GENERATOR_ID = TTAGK.APPLICATION_GENERATOR_ID  " +
            "                    AND TATGR.DATETIME = TTAGK.DATETIME  " +
            "                    AND TATGR.SETTLEMENT_ID = :settlementId  " +
            "WHERE TTAGK.APPLICATION_GENERATOR_ID IN  (:appGenIdList)  " +
            "  AND TTAGK.DATETIME BETWEEN :startTime AND :endTime ", nativeQuery = true)
    void updateDirectGmiByUnmatchedRmAndDatetimeIntervalAndApplicationGeneratorIdIn(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("settlementId") Long settlementId, @Param("appGenIdList") List<Long> appGenIdList);

    @Transactional
    @Modifying
    @Query(value = "BEGIN                                                                 " +
            "       DECLARE @datetime DATETIME = :startTime              " +
            "       WHILE @datetime <= :endTime              " +
            "           BEGIN              " +
            "               DECLARE @positiveNumber INT = (SELECT COUNT(APPLICATION_GENERATOR_ID) FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW              " +
            "                                              WHERE KW_RATIO > 0              " +
            "                                                AND APPLICATION_GENERATOR_ID IN (:appGenIdList)              " +
            "                                                AND DATETIME = @datetime)              " +
            "              " +
            "               DECLARE @zeroNegative BIT = IIF(@positiveNumber = 0, 1, 0)              " +
            "               IF @zeroNegative = 1              " +
            "                BEGIN              " +
            "                       UPDATE SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW              " +
            "                       SET GMI = (SSCC.COMPUTABLE_CAPACITY * SSCC.PERCENTAGE * PMI * 0.01)              " +
            "                       FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW  AS TTAGK              " +
            "                                INNER JOIN SIMULATION_APPLICATION_GENERATOR AS AG              " +
            "                                           ON AG.ID = TTAGK.APPLICATION_GENERATOR_ID              " +
            "                                INNER JOIN SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION SSCC              " +
            "                                           ON SSCC.APPLICATION_GENERATOR_ID = TTAGK.APPLICATION_GENERATOR_ID              " +
            "                                               AND SSCC.SETTLEMENT_ID = :settlementId              " +
            "                       WHERE TTAGK.APPLICATION_GENERATOR_ID IN  (:appGenIdList)              " +
            "                         AND TTAGK.DATETIME = @datetime              " +
            "                END              " +
            "           SET @datetime = DATEADD(minute, 15, @datetime);              " +
            "       END              " +
            " END ", nativeQuery = true)
    void updateZeroGmiByDatetimeAndApplicationGeneratorIdIn(@Param("settlementId") Long settlementId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appGenIdList") List<Long> appGenIdList);

    @Query(value = "SELECT DATETIME, SUM(GMI) AS SUM_GMI, SUM(KW_RATIO) AS SUM_KW_RATIO FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW " +
            "    WHERE DATETIME BETWEEN :startTime AND :endTime " +
            "       AND APPLICATION_GENERATOR_ID IN (:appGenIdList) " +
            "    GROUP BY DATETIME ", nativeQuery = true)
    List<Map<String, Object>> sumGmiAndKwByDatetimeIntervalAndApplicationGeneratorIdIn(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appGenIdList") List<Long> appGenIdList);

    @Transactional
    @Modifying
    @Query(value = "BEGIN     " +
            "   DECLARE @datetime DATETIME = :startTime   " +
            "   WHILE @datetime <= :endTime   " +
            "       BEGIN   " +
            "            DECLARE @positiveNumber INT = (SELECT COUNT(APPLICATION_GENERATOR_ID) FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW " +
            "                                           WHERE KW_RATIO > 0 " +
            "                                             AND APPLICATION_GENERATOR_ID IN (:appGenIdList) " +
            "                                             AND DATETIME = @datetime) " +
            " " +
            "            DECLARE @zeroNegative BIT = IIF(@positiveNumber = 0, 1, 0) " +
            "            IF @zeroNegative = 1 " +
            "               BEGIN   " +
            "                   UPDATE SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW   " +
            "                   SET GMI = 0   " +
            "                   FROM SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW  AS TTAGK   " +
            "                            INNER JOIN SIMULATION_APPLICATION_GENERATOR AS AG   " +
            "                                       ON AG.ID = TTAGK.APPLICATION_GENERATOR_ID   " +
            "                   WHERE TTAGK.APPLICATION_GENERATOR_ID IN  (:appGenIdList)   " +
            "                     AND TTAGK.DATETIME = @datetime   " +
            "               END   " +
            "           SET @datetime = DATEADD(minute, 15, @datetime)   " +
            "       END   " +
            " END ", nativeQuery = true)
    void updateGmiToZeroByDatetimeAndApplicationGeneratorIdIn(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appGenIdList") List<Long> appGenIdList);



}
