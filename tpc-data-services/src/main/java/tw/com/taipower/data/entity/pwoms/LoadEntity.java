package tw.com.taipower.data.entity.pwoms;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

import java.util.Date;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Entity
@Table(name = "LOAD_ENTITY")
@EqualsAndHashCode(callSuper = true)
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoadEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Schema(description = "名稱")
    @Column(name = "NAME")
    private String name;

    @Schema(description = "簡稱")
    @Column(name = "ALIAS")
    private String alias;

    /**
     * map to LoadType
     */
    @Schema(description = "用戶類型")
    @Column(name = "TYPE")
    private Long type;

    @Schema(description = "用戶電號")
    @Column(name = "NBS_CUSTOMER_NUMBER")
    private String nbsCustomerNumber;

    @Schema(description = "公司統編")
    @Column(name = "TAX_ID")
    private String taxId;

    @Schema(description = "用電端負責人名稱")
    @Column(name = "RESPONSIBLE_PERSON")
    private String responsiblePerson;

    @Schema(description = "用電端負責人電話")
    @Column(name = "RESPONSIBLE_PERSON_PHONE")
    private String responsiblePersonPhone;

    @Schema(description = "用電端負責人通訊地址＿縣市_區")
    @Column(name = "RESPONSIBLE_PERSON_ADDRESS_AREA")
    private Integer responsiblePersonAddressArea;

    @Schema(description = "用電端負責人通訊地址＿其他")
    @Column(name = "RESPONSIBLE_PERSON_ADDRESS_OTHER")
    private String responsiblePersonAddressOther;

    @Schema(description = "用電端備註")
    @Column(name = "NOTES")
    private String notes;

    @Schema(description = "用電端基本資料審核通過日期")
    @Column(name = "BASIC_INFO_APPROVAL_AT")
    private java.util.Date basicInfoApprovalAt;

    @Column(name = "CREATED_AT")
    private java.util.Date createdAt;

    @Column(name = "CREATED_BY")
    private Long createdBy;

    @Column(name = "MODIFIED_BY")
    private Long modifiedBy;

    @Schema(description = "1:final, 0:WIP")
    @Column(name = "STATUS")
    private Boolean status;

    @Schema(description = "用電地址＿縣市_區")
    @Column(name = "ADDRESS_AREA")
    private Integer addressArea;

    @Schema(description = "用電地址＿其他")
    @Column(name = "ADDRESS_OTHER")
    private String addressOther;

    @Column(name = "NBS_METER_NUMBER")
    private String nbsMeterNumber;

    @Schema(description = "是否已具再生能源發電電能購售契約同意終止契約函？")
    @Column(name = "FIT_TERMINATED")
    private Boolean fitTerminated;

    /**
     * map to VoltageLevel
     */
    @Deprecated
    @Schema(description = "併網電壓等級，不使用")
    @Column(name = "VOLTAGE")
    private Integer voltage;

    @Schema(description = "饋線代號")
    @Column(name = "FEEDER")
    private String feeder;

    @Schema(description = "電表組別")
    @Column(name = "METER")
    private String meter;

    /**
     * map to PccType
     */
    @Schema(description = "併網類型")
    @Column(name = "PCC_TYPE")
    private Long pccType;

    @Column(name = "PCC_LOCATION")
    private String pccLocation;

    /**
     * 圖檔，檔案類型需要額外開一張Table儲存。
     */
    @Schema(description = "示意圖（圖檔）")
    @Column(name = "VM_ILLUSTRATION")
    private String vmIllustration;

    @Schema(description = "是否符合電能轉供及併網直供營運規章第21點之申請條件？")
    @Column(name = "LINE_21_QUALIFIED")
    private Boolean line21Qualified;

    @Transient
    private List<LoadEntityMeter> loadEntityMeterList;

    @Schema(description = "2:兩段式\n" +
            "3:三段式")
    @Column(name = "TIME_STG")
    private Integer timeStg;

    @Schema(description = "契約類型：\n" +
            "K:\n" +
            "A\n" +
            "6\n" +
            "D\n" +
            "F\n" +
            "7\n" +
            "C\n" +
            "1\n" +
            "9\n" +
            "4\n" +
            "8")
    @Column(name = "CONTRACT_STG")
    private String contractStg;

    @Schema(description = "是否為臨時電號")
    @Column(name = "IS_TEMP_NBS_CUSTOMER_NUMBER")
    private Boolean isTempNbsCustomerNumber;

    @Schema(description = "臨時電號狀態")
    @Column(name = "IS_TEMP_NBS_CUSTOMER_NUMBER_REASON")
    private Integer isTempNbsCustomerNumberReason;

    @Schema(description = "負責區處")
    @Column(name = "TPC_DEPT_ID")
    private Integer tpcDeptId;

    @Transient
    @Schema(description = "作為暫存時，是否僅能唯獨")
    private Boolean readOnly;

    @Schema(description = "責任分界點電壓層級")
    @Column(name = "RESPONSIBILITY_VOLTAGE")
    private Integer responsibilityVoltage;

    @Schema(description = "併聯方式")
    @Column(name = "COMBINE_METHOD")
    private Integer combineMethod;

    @Schema(description = "暫存發/用電端電表")
    @Transient
    List<WorkflowEntityMeter> customerMeterList;

    @Schema(description = "行業別")
    @Column(name = "NB_MST_SICC_CODE")
    private String nbMstSiccCode;

    @Schema(description = "用電別，（例如，5 非營業用電）")
    @Column(name = "USAGE_STG")
    private String usageStg;

    @Schema(description = "特殊計費")
    @Column(name = "CUSTOM_BILLING")
    private String customBilling;

    @Schema(description = "最後一次審查通過日期")
    @Column(name = "LAST_REVIEW_PASSED_DATE")
    private Date lastReviewPassedDate;

    @Schema(description = "用電契約容量")
    @Column(name = "CONTRACT_CAPACITY")
    private java.math.BigDecimal contractCapacity;

    @Schema(description = "NBS關聯編號")
    @Column(name = "NB_MST_ACCTID")
    private String nbMstAccountId;

}
