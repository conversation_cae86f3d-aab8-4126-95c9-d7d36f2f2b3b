package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyGeneratorRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyGeneratorRecord;

import java.util.Date;
import java.util.List;


/**
 * Repository of ApplicationMonthlyGeneratorRecord
 *
 * @class: ApplicationMonthlyGeneratorRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationMonthlyGeneratorRecordRepository extends JpaRepository<SimulationTempApplicationMonthlyGeneratorRecord, ApplicationMonthlyGeneratorRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD(            " +
            "                                                                 DATE            " +
            "                                                               , ENERGY_CHARGE_SECTION_ID            " +
            "                                                               , APPLICATION_GENERATOR_ID            " +
            "                                                               , UNMATCHED_RM            " +
            "                                                               , MATCHED_RM            " +
            "                                                               , SETTLEMENT_ID)            " +
            "    SELECT DISTINCT FIRST_DAY            " +
            "        , ENERGY_CHARGE_SECTION_ID            " +
            "        , APPLICATION_GENERATOR_ID            " +
            "        , REMATCH.UNMATCHED_RM       " +
            "        , REMATCH.FIRST_SECOND_MATCHED       " +
            "        , SETTLEMENT_ID            " +
            "    FROM (SELECT DISTINCT FIRST_DAY            " +
            "              , SUBQUERY.ENERGY_CHARGE_SECTION_ID            " +
            "              , SUBQUERY.APPLICATION_GENERATOR_ID            " +
            "              , AMRGR.UNMATCHED_RM       " +
            "              , (SUM_MATCHED_RM + AMRGR.MATCHED_RM) AS FIRST_SECOND_MATCHED       " +
            "              , SUBQUERY.SETTLEMENT_ID            " +
            "    FROM (SELECT CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)) AS FIRST_DAY            " +
            "             , ENERGY_CHARGE_SECTION_ID            " +
            "             , APPLICATION_GENERATOR_ID            " +
            "             , SUM(UNMATCHED_RM) AS SUM_UNMATCHED_RM            " +
            "             , SUM(MATCHED_RM) AS SUM_MATCHED_RM            " +
            "             , SETTLEMENT_ID            " +
            "        FROM SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD            " +
            "        WHERE DATE BETWEEN :startTime AND :endTime            " +
            "          AND SETTLEMENT_ID = :settlementId            " +
            "          AND APPLICATION_GENERATOR_ID IN (:appGenList)            " +
            "        GROUP BY CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)), ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, SETTLEMENT_ID) AS SUBQUERY            " +
            "    INNER JOIN SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD AMRGR            " +
            "              ON AMRGR.APPLICATION_GENERATOR_ID = SUBQUERY.APPLICATION_GENERATOR_ID            " +
            "              AND FIRST_DAY = AMRGR.DATE       " +
            "              AND AMRGR.SETTLEMENT_ID = :settlementId " +
            "              AND AMRGR.ENERGY_CHARGE_SECTION_ID = SUBQUERY.ENERGY_CHARGE_SECTION_ID) AS REMATCH", nativeQuery = true)
    void saveByRematchRecordAndDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId
            , @Param("appGenList") List<Long> appGenList);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD   " +
            "WHERE DATE = :date   " +
            "  AND SETTLEMENT_ID = :settlementId", nativeQuery = true)
    void deleteByDateAndSettlementId(@Param("date") Date date, @Param("settlementId") Long settlementId);


    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD(     " +
            "                      DATE     " +
            "                    , ENERGY_CHARGE_SECTION_ID     " +
            "                    , APPLICATION_GENERATOR_ID     " +
            "                    , UNMATCHED_RM     " +
            "                    , MATCHED_RM     " +
            "                    , SETTLEMENT_ID)     " +
            "SELECT FIRST_DAY     " +
            "     , ENERGY_CHARGE_SECTION_ID     " +
            "     , APPLICATION_GENERATOR_ID     " +
            "     , SUM_UNMATCHED_RM     " +
            "     , SUM_MATCHED_RM     " +
            "     , SETTLEMENT_ID     " +
            "FROM(SELECT CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)) AS FIRST_DAY     " +
            "          , ENERGY_CHARGE_SECTION_ID     " +
            "          , APPLICATION_GENERATOR_ID     " +
            "          , SUM(UNMATCHED_RM) AS SUM_UNMATCHED_RM     " +
            "          , SUM(MATCHED_RM) AS SUM_MATCHED_RM     " +
            "          , SETTLEMENT_ID     " +
            "     FROM SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD     " +
            "     WHERE DATE BETWEEN :startTime AND :endTime     " +
            "       AND SETTLEMENT_ID = :settlementId     " +
            "       AND APPLICATION_GENERATOR_ID IN (:appGenList)     " +
            "     GROUP BY CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)), ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, SETTLEMENT_ID) AS SUBQUERY", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId
            , @Param("appGenList") List<Long> appGenList);

}