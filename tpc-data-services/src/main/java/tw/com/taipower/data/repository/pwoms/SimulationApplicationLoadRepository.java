package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import tw.com.taipower.data.entity.pwoms.ApplicationLoad;
import tw.com.taipower.data.entity.pwoms.SimulationApplicationLoad;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static tw.com.taipower.data.constant.Constants.*;

public interface SimulationApplicationLoadRepository extends JpaRepository<SimulationApplicationLoad, Long> {

    @Query(value = "SELECT DISTINCT AG.ID FROM SIMULATION_APPLICATION_GENERATOR AG " +
            "    WHERE APPLICATION_ID IN ( SELECT APPLICATION_ID FROM SIMULATION_APPLICATION_LOAD " +
            "        WHERE ID IN (:idList)) ", nativeQuery = true)
    List<Long> findApplicationGeneratorIdByIdIn(@Param("idList") List<Long> idList);

    @Query(value = " SELECT ID FROM SIMULATION_APPLICATION_LOAD WHERE APPLICATION_ID IN (?1)", nativeQuery = true)
    List<Long> findIdByApplicationIdIn(List<Long> appIdList);

    @Query(value = "SELECT * FROM SIMULATION_APPLICATION_LOAD   " +
            "    WHERE APPLICATION_ID IN (:appIdList)   " +
            "        AND MONTHLY_CONTRACT_CAP IS NULL OR ANNUAL_CONTRACT_CAP IS NULL ", nativeQuery = true)
    List<ApplicationLoad> findByAppIdInAndContractCapIsNull(@Param("appIdList") List<Long> appIdList);

    @Query(value = "SELECT ID FROM SIMULATION_APPLICATION_LOAD " +
            "    WHERE ID IN (:idList) " +
            "        AND IS_DIRECT IS NOT NULL " +
            "        AND IS_DIRECT = :isDirect" , nativeQuery = true)
    List<Long> findIdByIdInAndIsDirect(@Param("idList") List<Long> idList, @Param("isDirect") boolean isDirect);


}
