package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationTimelyGeneratorRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationTimelyDirectGeneratorRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationTimelyDirectGeneratorRecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Repository of TempApplicationTimelyGeneratorRecord
 *
 * @class: TempApplicationTimelyGeneratorRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationTimelyDirectGeneratorRecordRepository extends JpaRepository<SimulationTempApplicationTimelyDirectGeneratorRecord, ApplicationTimelyGeneratorRecordColumnId> {

    List<SimulationTempApplicationTimelyDirectGeneratorRecord> findByGeneratorIdIn(List<Long> generatorIdList);

    List<SimulationTempApplicationTimelyDirectGeneratorRecord> findByDatetimeBetweenOrderByDatetimeAsc(Date startTime, Date endTime);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD(APPLICATION_GENERATOR_ID  " +
            "            , DATETIME  " +
            "            , ENERGY_CHARGE_SECTION_ID  " +
            "            , UNMATCHED_RM  " +
            "            , MATCHED_RM  " +
            "            , ACTUAL_GEN  " +
            "            , GMI  " +
            "            , KW_UPDATETIME  " +
            "            , SETTLEMENT_ID)  " +
            "SELECT TATGLR.APPLICATION_GENERATOR_ID  " +
            "     , TATGLR.DATETIME  " +
            "     , TTAGK.ENERGY_CHARGE_SECTION_ID  " +
            "     , (GMI - SUM(TATGLR.MATCHED_RM)) AS UNMATCHED_RM  " +
            "     , SUM(TATGLR.MATCHED_RM) AS SUM_MATCHED_RM  " +
            "     , KW_RATIO  " +
            "     , GMI  " +
            "     , KW_UPDATETIME  " +
            "     , :settlementId  " +
            "    FROM SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD AS TATGLR  " +
            "        INNER JOIN SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW AS TTAGK  " +
            "            ON TTAGK.APPLICATION_GENERATOR_ID = TATGLR.APPLICATION_GENERATOR_ID  " +
            "            AND TTAGK.DATETIME = TATGLR.DATETIME  " +
            "    WHERE TATGLR.DATETIME BETWEEN :startTime AND :endTime  " +
            "        AND TATGLR.APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "        AND TATGLR.SETTLEMENT_ID = :settlementId  " +
            "    GROUP BY TATGLR.APPLICATION_GENERATOR_ID, TATGLR.DATETIME" +
            ", TTAGK.ENERGY_CHARGE_SECTION_ID, KW_RATIO, GMI, KW_UPDATETIME   "
            , nativeQuery = true)
    void saveAllByDateIntervalApplicationGeneratorIdIn(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("settlementId") Long settlementId, @Param("appGenIdList") List<Long> appGenIdList);


    @Query(value = "SELECT SUM(UNMATCHED_RM) FROM SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD TATDGR   " +
            "    WHERE APPLICATION_GENERATOR_ID IN (   " +
            "        SELECT APPLICATION_GENERATOR_ID FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD TDAGL   " +
            "            WHERE TDAGL.APPLICATION_ID = :appId   " +
            "                AND TDAGL.SETTLEMENT_ID = :settlementId   " +
            "                AND TDAGL.DATE = :startTime)   " +
            "        AND TATDGR.DATETIME BETWEEN :startTime AND :endTime   " +
            "        AND TATDGR.SETTLEMENT_ID = :settlementId ", nativeQuery = true)
    BigDecimal findByUnmatchedRmByDateIntervalAndApplicationId(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("settlementId") Long settlementId, @Param("appId") Long appId);
}
