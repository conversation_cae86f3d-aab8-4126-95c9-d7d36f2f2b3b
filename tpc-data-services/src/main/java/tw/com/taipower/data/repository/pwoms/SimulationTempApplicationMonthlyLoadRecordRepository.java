package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyLoadRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyLoadRecord;

import java.util.Date;
import java.util.List;


/**
 * Repository of ApplicationMonthlyLoadRecord
 *
 * @class: TempApplicationMonthlyLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationMonthlyLoadRecordRepository extends JpaRepository<SimulationTempApplicationMonthlyLoadRecord, ApplicationMonthlyLoadRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD   " +
            "WHERE DATE = :date   " +
            "  AND SETTLEMENT_ID = :settlementId", nativeQuery = true)
    void deleteByDateAndSettlementId(@Param("date") Date date, @Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD(" +
            "                                                  DATE" +
            "                                                , ENERGY_CHARGE_SECTION_ID" +
            "                                                , APPLICATION_LOAD_ID" +
            "                                                , UNMATCHED_CN" +
            "                                                , MATCHED_CN" +
            "                                                , SETTLEMENT_ID)" +
            " SELECT DISTINCT FIRST_DAY       " +
            "              , ENERGY_CHARGE_SECTION_ID       " +
            "              , APPLICATION_LOAD_ID       " +
            "              , REMATCH.UNMATCHED_CN       " +
            "              , REMATCH.FIRST_SECOND_MATCHED       " +
            "              , SETTLEMENT_ID       " +
            " FROM (SELECT DISTINCT FIRST_DAY       " +
            "                    , SUBQUERY.ENERGY_CHARGE_SECTION_ID       " +
            "                    , SUBQUERY.APPLICATION_LOAD_ID       " +
            "                    , AMRLR.UNMATCHED_CN       " +
            "                    , (SUM_MATCHED_CN + AMRLR.MATCHED_CN) AS FIRST_SECOND_MATCHED       " +
            "                    , SUBQUERY.SETTLEMENT_ID       " +
            "      FROM(SELECT CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)) AS FIRST_DAY       " +
            "                , ENERGY_CHARGE_SECTION_ID       " +
            "                , APPLICATION_LOAD_ID       " +
            "                , SUM(UNMATCHED_CN) AS SUM_UNMATCHED_CN       " +
            "                , SUM(MATCHED_CN) AS SUM_MATCHED_CN       " +
            "                , SETTLEMENT_ID       " +
            "           FROM SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD       " +
            "           WHERE DATE BETWEEN :startTime AND :endTime       " +
            "             AND SETTLEMENT_ID = :settlementId       " +
            "             AND APPLICATION_LOAD_ID IN (:appLoadList)       " +
            "           GROUP BY CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)), ENERGY_CHARGE_SECTION_ID, APPLICATION_LOAD_ID, SETTLEMENT_ID) AS SUBQUERY       " +
            "              INNER JOIN SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD AMRLR       " +
            "                         ON AMRLR.APPLICATION_LOAD_ID = SUBQUERY.APPLICATION_LOAD_ID       " +
            "                             AND FIRST_DAY = AMRLR.DATE       " +
            "                             AND AMRLR.SETTLEMENT_ID = :settlementId " +
            "                             AND AMRLR.ENERGY_CHARGE_SECTION_ID = SUBQUERY.ENERGY_CHARGE_SECTION_ID) AS REMATCH", nativeQuery = true)
    void saveByRematchRecordAndDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId
            , @Param("appLoadList") List<Long> appLoadList);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD(     " +
            "                         DATE     " +
            "                       , ENERGY_CHARGE_SECTION_ID     " +
            "                       , APPLICATION_LOAD_ID     " +
            "                       , UNMATCHED_CN     " +
            "                       , MATCHED_CN     " +
            "                       , SETTLEMENT_ID)     " +
            "SELECT FIRST_DAY     " +
            "     , ENERGY_CHARGE_SECTION_ID     " +
            "     , APPLICATION_LOAD_ID     " +
            "     , SUM_UNMATCHED_CN     " +
            "     , SUM_MATCHED_CN     " +
            "     , SETTLEMENT_ID     " +
            "FROM(SELECT CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)) AS FIRST_DAY     " +
            "          , ENERGY_CHARGE_SECTION_ID     " +
            "          , APPLICATION_LOAD_ID     " +
            "          , SUM(UNMATCHED_CN) AS SUM_UNMATCHED_CN     " +
            "          , SUM(MATCHED_CN) AS SUM_MATCHED_CN     " +
            "          , SETTLEMENT_ID     " +
            "     FROM SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD     " +
            "     WHERE DATE BETWEEN :startTime AND :endTime     " +
            "       AND SETTLEMENT_ID = :settlementId     " +
            "       AND APPLICATION_LOAD_ID IN (:appLoadList)     " +
            "     GROUP BY CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)), ENERGY_CHARGE_SECTION_ID, APPLICATION_LOAD_ID, SETTLEMENT_ID) AS SUBQUERY     " +
            "     ", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId
            , @Param("appLoadList") List<Long> appLoadList);
}