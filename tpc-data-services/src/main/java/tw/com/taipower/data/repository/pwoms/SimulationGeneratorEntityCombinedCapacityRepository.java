package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import tw.com.taipower.data.entity.pwoms.GeneratorEntityCombinedCapacity;
import tw.com.taipower.data.entity.pwoms.SimulationGeneratorEntityCombinedCapacity;

import java.util.List;

import static tw.com.taipower.data.constant.Constants.FIELD_NAME_GENERATOR_ENTITY_ID;
import static tw.com.taipower.data.constant.Constants.FIELD_NAME_GENERATOR_SUM_CAPACITY;

public interface SimulationGeneratorEntityCombinedCapacityRepository extends JpaRepository<SimulationGeneratorEntityCombinedCapacity, Long> {



}