package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationDailyDirectGeneratorRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationDailyDirectGeneratorRecord;

import java.util.Date;


/**
 * Repository of TempApplicationDailyGeneratorRecord
 *
 * @class: TempApplicationDailyGeneratorRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationDailyDirectGeneratorRecordRepository extends JpaRepository<SimulationTempApplicationDailyDirectGeneratorRecord, ApplicationMonthlyGeneratorRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD(   " +
            "       DATE   " +
            "      , ENERGY_CHARGE_SECTION_ID   " +
            "      , APPLICATION_GENERATOR_ID   " +
            "      , UNMATCHED_RM   " +
            "      , MATCHED_RM   " +
            "     , SETTLEMENT_ID)   " +
            "SELECT CAST(DATETIME AS DATE) AS DATE   " +
            "     , ENERGY_CHARGE_SECTION_ID   " +
            "     , APPLICATION_GENERATOR_ID   " +
            "     , SUM(UNMATCHED_RM) AS SUM_UNMATCHED_CN   " +
            "     , SUM(MATCHED_RM) AS SUM_MATCHED_CN   " +
            "     , SETTLEMENT_ID   " +
            "FROM SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD   " +
            "WHERE DATETIME BETWEEN :startTime AND :endTime   " +
            "  AND SETTLEMENT_ID = :settlementId   " +
            "GROUP BY CAST(DATETIME AS DATE), ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, SETTLEMENT_ID   ", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId);


}