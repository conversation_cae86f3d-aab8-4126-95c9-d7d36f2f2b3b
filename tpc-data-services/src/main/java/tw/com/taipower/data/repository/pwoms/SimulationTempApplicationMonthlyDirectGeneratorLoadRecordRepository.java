package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyDirectGeneratorLoadRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of ApplicationMonthlyGeneratorLoadRecord
 *
 * @class: SimulationTempApplicationMonthlyDirectGeneratorLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationMonthlyDirectGeneratorLoadRecordRepository extends JpaRepository<SimulationTempApplicationMonthlyDirectGeneratorLoadRecord, ApplicationMonthlyGeneratorLoadRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "END ", nativeQuery = true)
    void deleteAllRecordBySettlementId(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD(     " +
            "             DATE     " +
            "           , ENERGY_CHARGE_SECTION_ID     " +
            "           , APPLICATION_GENERATOR_ID     " +
            "           , APPLICATION_LOAD_ID     " +
            "           , MATCHED_RM" +
            "           , ADJUSTED_MATCHED_RM     " +
            "           , SETTLEMENT_ID)     " +
            "SELECT FIRST_DAY     " +
            "     , ENERGY_CHARGE_SECTION_ID     " +
            "     , APPLICATION_GENERATOR_ID     " +
            "     , APPLICATION_LOAD_ID     " +
            "     , SUM_MATCHED_RM     " +
            "     , ADJUSTED_MATCHED_RM   " +
            "     , SETTLEMENT_ID     " +
            "FROM(SELECT CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)) AS FIRST_DAY     " +
            "          , ENERGY_CHARGE_SECTION_ID     " +
            "          , APPLICATION_GENERATOR_ID     " +
            "          , APPLICATION_LOAD_ID     " +
            "          , SUM(MATCHED_RM) AS SUM_MATCHED_RM     " +
            "          , FLOOR(SUM(MATCHED_RM)) AS ADJUSTED_MATCHED_RM      " +
            "          , SETTLEMENT_ID     " +
            "          FROM SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD TADDGLR   " +
            "               INNER JOIN  SIMULATION_APPLICATION_LOAD AS AL   " +
            "                           ON TADDGLR.APPLICATION_LOAD_ID = AL.ID   " +
            "               INNER JOIN APPLICATION AS A   " +
            "                          ON AL.APPLICATION_ID = A.ID   " +
            "         WHERE TYPE IN (:appTypeList)   " +
            "            AND DATE BETWEEN :startTime AND :endTime   " +
            "            AND SETTLEMENT_ID = :settlementId     " +
            "         GROUP BY CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)), ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID, SETTLEMENT_ID) AS SUBQUERY     ", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId, @Param("appTypeList") List<String> appTypeList);


}