package tw.com.taipower.data.entity.pwoms;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Settlement Property
 *
 * @class: SimulationSettlementProperty
 * @author: Silver<PERSON><PERSON>
 * @version: 0.1.0
 * @since: 2025-06-20 10:53
 * @see:
 **/

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Entity
@Table(name = "SIMULATION_SETTLEMENT_PROPERTY")
public class SimulationSettlementProperty {

    @Id
    private String id;

    private String value;

    @Column(name = "VALUE_TYPE")
    private String valueType;

    @Version
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @Column(name = "UPDATE_TIME")
    private Timestamp updateTime;
}