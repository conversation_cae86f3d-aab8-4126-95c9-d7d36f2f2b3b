package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Calculation Trial Capacity Percentage
 *
 * @class: SettlementCalculation
 * @author:  ting
 * @version: 0.1.0
 * @since: 2025/02/05 17:14:28
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "SIMULATION_SETTLEMENT_CAPACITY_CALCULATION")
public class SimulationSettlementCapacityCalculation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "SETTLEMENT_ID", nullable = false)
    private Long settlementId;

    @Column(name = "APPLICATION_GENERATOR_ID")
    private Long generatorId;

    @Column(name = "CAPACITY_CODE")
    private String capCode;

    @Column(name = "CAPACITY")
    private BigDecimal capacity;

    @Column(name = "CAPACITY_PARALLEL_DAY")
    private BigDecimal capParallelDay;

    @Column(name = "SALE_IN_TRIAL_OP")
    private Short saleInTrialOp;

    @Column(name = "PARALLEL_DAY")
    private Short parallelDay;

    @Column(name = "PERCENTAGE")
    private BigDecimal percentage;


}
