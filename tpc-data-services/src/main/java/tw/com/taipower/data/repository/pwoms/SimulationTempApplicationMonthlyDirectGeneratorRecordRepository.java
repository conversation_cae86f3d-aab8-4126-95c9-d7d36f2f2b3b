package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyDirectGeneratorRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyDirectGeneratorRecord;

import java.util.Date;
import java.util.List;


/**
 * Repository of ApplicationMonthlyGeneratorRecord
 *
 * @class: ApplicationMonthlyGeneratorRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationMonthlyDirectGeneratorRecordRepository extends JpaRepository<SimulationTempApplicationMonthlyDirectGeneratorRecord, ApplicationMonthlyGeneratorRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD(     " +
            "              DATE     " +
            "            , ENERGY_CHARGE_SECTION_ID     " +
            "            , APPLICATION_GENERATOR_ID     " +
            "            , UNMATCHED_RM     " +
            "            , MATCHED_RM     " +
            "            , SETTLEMENT_ID)     " +
            "SELECT FIRST_DAY     " +
            "     , ENERGY_CHARGE_SECTION_ID     " +
            "     , APPLICATION_GENERATOR_ID     " +
            "     , SUM_UNMATCHED_RM     " +
            "     , SUM_MATCHED_RM     " +
            "     , SETTLEMENT_ID     " +
            "FROM(SELECT CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)) AS FIRST_DAY     " +
            "          , ENERGY_CHARGE_SECTION_ID     " +
            "          , APPLICATION_GENERATOR_ID     " +
            "          , SUM(UNMATCHED_RM) AS SUM_UNMATCHED_RM     " +
            "          , SUM(MATCHED_RM) AS SUM_MATCHED_RM     " +
            "          , SETTLEMENT_ID     " +
            "     FROM SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD  TADDGR   " +
            "               INNER JOIN  SIMULATION_APPLICATION_GENERATOR AS AG   " +
            "                           ON TADDGR.APPLICATION_GENERATOR_ID = AG.ID   " +
            "               INNER JOIN APPLICATION AS A   " +
            "                          ON AG.APPLICATION_ID = A.ID   " +
            "         WHERE TYPE IN (:appTypeList)   " +
            "           AND DATE BETWEEN :startTime AND :endTime     " +
            "           AND SETTLEMENT_ID = :settlementId    " +
            "     GROUP BY CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)), ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, SETTLEMENT_ID) AS SUBQUERY     ", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId, @Param("appTypeList") List<String> appTypeList);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD   " +
            "WHERE DATE = :date   " +
            "  AND SETTLEMENT_ID = :settlementId", nativeQuery = true)
    void deleteByDateAndSettlementId(@Param("date") Date date, @Param("settlementId") Long settlementId);
}