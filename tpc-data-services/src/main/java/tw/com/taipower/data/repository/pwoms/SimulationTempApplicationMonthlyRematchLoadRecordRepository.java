package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyRematchLoadRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyRematchLoadRecord;


/**
 * Repository of ApplicationMonthlyLoadRecord
 *
 * @class: TempApplicationMonthlyLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationMonthlyRematchLoadRecordRepository extends JpaRepository<SimulationTempApplicationMonthlyRematchLoadRecord, ApplicationMonthlyLoadRecordColumnId> {

}