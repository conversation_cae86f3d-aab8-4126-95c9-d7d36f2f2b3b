package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Persistable;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Temp Application Load Kw
 *
 * @class: TempTempApplicationLoadKw
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-14 21:40
 * @see:
 **/

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Entity
@IdClass(TempTempApplicationLoadKwId.class)
@Table(name = "SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW")
public class SimulationTempTempApplicationLoadKw implements Persistable<TempTempApplicationLoadKwId> {

    @Id
    @Column(name = "DATETIME")
    private Date datetime;

    @Column(name = "APPLICATION_LOAD_ID")
    private Long appLoadId;

    @Id
    @Column(name = "ENERGY_CHARGE_SECTION_ID")
    protected Integer energyChargeSectionId;

    @Column(name = "KW_RATIO")
    private BigDecimal kwRatio;

    @Column(name = "KW_UPDATETIME")
    private Date kwUpdateTime;

    @Override
    public TempTempApplicationLoadKwId getId() {
        return TempTempApplicationLoadKwId.builder()
                .appLoadId(appLoadId)
                .datetime(datetime)
                .energyChargeSectionId(energyChargeSectionId)
                .build();
    }

    @Override
    public boolean isNew() {
        return true;
    }
}