package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Settlement Calculation
 *
 * @class: SimulationSettlementCalculation
 * @author:  ting
 * @version: 0.1.0
 * @since: 2025/02/05 17:14:28
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "SIMULATION_SETTLEMENT_CALCULATION")
public class SimulationSettlementCalculation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SETTLEMENT_ID", nullable = false)
    private Long settlementId;

    @Column(name = "SERVICE_DATE")
    private Date serviceDate;

    @Column(name = "CALCULATION_METHOD")
    private Short calculationMethod;

    @Column(name = "EXECUTION_START")
    private Date executionStart;

    @Column(name = "EXECUTION_END")
    private Date executionEnd;

    @Column(name = "EXECUTION_PROGRESS")
    private String executionLog;

    @Column(name = "EXECUTION_RESULT")
    private Boolean executionResult;

    @Column(name = "EXECUTION_PERCENTAGE")
    private BigDecimal executionPercentage;

    @Column(name = "USER_ID")
    private Integer userId;
}
