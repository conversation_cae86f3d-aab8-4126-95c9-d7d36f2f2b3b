package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationDailyLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationDailyLoadRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationDailyLoadRecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * Repository of Temp ApplicationDailyLoadRecord
 *
 * @class: TempApplicationDailyLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationDailyLoadRecordRepository extends JpaRepository<SimulationTempApplicationDailyLoadRecord, ApplicationDailyLoadRecordColumnId> {


    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD(   " +
            "    DATE   " +
            "    , ENERGY_CHARGE_SECTION_ID   " +
            "    , APPLICATION_LOAD_ID   " +
            "    , UNMATCHED_CN   " +
            "    , MATCHED_CN   " +
            "    , SETTLEMENT_ID)   " +
            "SELECT CAST(DATETIME AS DATE) AS DATE   " +
            "     , ENERGY_CHARGE_SECTION_ID   " +
            "     , APPLICATION_LOAD_ID   " +
            "     , SUM(UNMATCHED_CN) AS SUM_UNMATCHED_CN   " +
            "     , SUM(MATCHED_CN) AS SUM_MATCHED_CN   " +
            "     , SETTLEMENT_ID   " +
            "    FROM SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD   " +
            "    WHERE DATETIME BETWEEN :startTime AND :endTime   " +
            "      AND SETTLEMENT_ID = :settlementId   " +
            "GROUP BY CAST(DATETIME AS DATE), ENERGY_CHARGE_SECTION_ID, APPLICATION_LOAD_ID, SETTLEMENT_ID   ", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId);

    @Query(value = "SELECT (:monthlyCap - SUM(AMDLR.MATCHED_CN)) AS RESIDUAL_CN  " +
            "    FROM SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD AS AMDLR  " +
            "        WHERE APPLICATION_LOAD_ID IN (  " +
            "            SELECT SUBQUERY.APPLICATION_LOAD_ID  " +
            "                FROM APPLICATION AS A  " +
            "                      LEFT JOIN(SELECT APPLICATION_ID, AL2.ID AS APPLICATION_LOAD_ID  " +
            "                                FROM SIMULATION_APPLICATION_LOAD AL2  " +
            "                                WHERE LOAD_ID IN (SELECT LOAD_ID  " +
            "                                                  FROM SIMULATION_APPLICATION_LOAD AL  " +
            "                                                  WHERE AL.ID = :appLoadId)  " +
            "                                ) AS SUBQUERY  " +
            "                    ON APPLICATION_ID = A.ID  " +
            "                WHERE CONTRACT_NO = :contractNo)  " +
            "        AND AMDLR.SETTLEMENT_ID = :settlementId  " +
            "        AND AMDLR.DATE BETWEEN :startTime AND :endTime", nativeQuery = true)
    BigDecimal findMonthlyContractResidualCapacity(@Param("startTime") Date startTime, @Param("endTime")Date endTime
            , @Param("settlementId") Long settlementId, @Param("appLoadId") Long appLoadId, @Param("contractNo") String contractNo, @Param("monthlyCap") BigDecimal monthlyCap);

    @Query(value = "SELECT APPLICATION_LOAD_ID AS ID, ENERGY_CHARGE_SECTION_ID, SUM(UNMATCHED_CN) AS UNMATCHED_CAPACITY, SUM(MATCHED_CN) AS MATCHED_CAPACITY     " +
            " FROM SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD     " +
            "        WHERE  APPLICATION_LOAD_ID IN (:appLoadIdList)     " +
            "          AND SETTLEMENT_ID = :settlementId     " +
            "          AND DATE BETWEEN :startDate AND :endDate     " +
            " GROUP BY APPLICATION_LOAD_ID, ENERGY_CHARGE_SECTION_ID     ", nativeQuery = true)
    List<Map<String, Object>> sumByDateGroupByApplicationLoadIdAndEnergyChargeSectionId(@Param("startDate") Date startDate, @Param("endDate")Date endDate, @Param("settlementId") Long settlementId, @Param("appLoadIdList") List<Long> appGenIdList);


}