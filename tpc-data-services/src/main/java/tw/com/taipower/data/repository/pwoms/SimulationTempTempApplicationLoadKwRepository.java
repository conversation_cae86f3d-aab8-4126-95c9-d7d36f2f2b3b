package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationTempTempApplicationLoadKw;
import tw.com.taipower.data.entity.pwoms.TempTempApplicationLoadKw;
import tw.com.taipower.data.entity.pwoms.TempTempApplicationLoadKwId;

import java.util.Date;
import java.util.List;

/**
 * Repository of TempTempApplicationLoadKw
 *
 * @class: SimulationTempTempApplicationLoadKwRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-14 22:02
 * @see:
 **/
public interface SimulationTempTempApplicationLoadKwRepository extends JpaRepository<SimulationTempTempApplicationLoadKw, TempTempApplicationLoadKwId> {

    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW", nativeQuery = true)
    void deleteAll();

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW " +
            "    (DATETIME, APPLICATION_LOAD_ID, ENERGY_CHARGE_SECTION_ID, KW_RATIO, KW_UPDATETIME) " +
            "    SELECT DATETIME, :destAppLoadId, ENERGY_CHARGE_SECTION_ID, KW_RATIO, KW_UPDATETIME FROM SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW " +
            "            WHERE APPLICATION_LOAD_ID = :sourceAppLoadId " +
            "                 AND DATETIME BETWEEN :startTime AND :endTime ", nativeQuery = true)
    void saveByApplicationLoad(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("sourceAppLoadId") Long sourceAppLoadId, @Param("destAppLoadId")Long destAppLoadId);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW   " +
            "    (DATETIME, APPLICATION_LOAD_ID, ENERGY_CHARGE_SECTION_ID, KW_RATIO, KW_UPDATETIME)   " +
            "        SELECT DATETIME, CONVERT(BIGINT, value), ENERGY_CHARGE_SECTION_ID, KW_RATIO, KW_UPDATETIME FROM (   " +
            "            SELECT * FROM SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW   " +
            "                WHERE APPLICATION_LOAD_ID = :srcAppLoadId   " +
            "                  AND DATETIME BETWEEN :startTime AND :endTime) sourceRecord   " +
            "        CROSS APPLY STRING_SPLIT(:destIdList, ',') ", nativeQuery = true)
    void saveByApplicationLoad(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("srcAppLoadId") Long srcAppLoadId, @Param("destIdList")String destIdList);

    @Query(value = "SELECT * FROM SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW " +
            "WHERE DATETIME BETWEEN :startTime AND :endTime " +
            "  AND APPLICATION_LOAD_ID IN (:appLoadIdList) " +
            "ORDER BY APPLICATION_LOAD_ID, DATETIME ", nativeQuery = true)
    List<TempTempApplicationLoadKw> findByDatetimeIntervalAndApplicationLoadIdIn(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appLoadIdList") List<Long> appGenIdList);


}
