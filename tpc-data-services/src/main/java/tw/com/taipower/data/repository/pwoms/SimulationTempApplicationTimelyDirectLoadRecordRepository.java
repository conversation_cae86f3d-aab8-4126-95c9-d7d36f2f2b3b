package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import tw.com.taipower.data.entity.pwoms.ApplicationTimelyLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationTimelyDirectLoadRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationTimelyDirectLoadRecord;

import java.util.Date;
import java.util.List;

/**
 * Repository of ApplicationTimelyLoadRecord
 *
 * @class: ApplicationTimelyLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationTimelyDirectLoadRecordRepository extends JpaRepository<SimulationTempApplicationTimelyDirectLoadRecord, ApplicationTimelyLoadRecordColumnId> {

    List<SimulationTempApplicationTimelyDirectLoadRecord> findByLoadIdIn(List<Long> loadList);

    List<SimulationTempApplicationTimelyDirectLoadRecord> findByDatetimeBetweenOrderByDatetimeAsc(Date startTime, Date endTime);

}
