package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import tw.com.taipower.data.entity.pwoms.Application;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static tw.com.taipower.data.constant.Constants.*;

public interface ApplicationRepository extends JpaRepository<Application, Long>, JpaSpecificationExecutor<Application> {

    String selectApplicantEntityType = " join APPLICATION on APPLICANT_ENTITY.ID = APPLICATION.APPLICANT_ID" +
            " join APPLICANT_TYPE on APPLICANT_ENTITY.TYPE = APPLICANT_TYPE.ID and APPLICANT_TYPE.ID =";
    String joinVirtualApplicant =
            "  Left outer join VIRTUAL_APPLICANT_COMPANY_DETAIL on APPLICATION.APPLICANT_ID = VIRTUAL_APPLICANT_COMPANY_DETAIL.APPLICANT_ID" +
                    "  Left outer join VIRTUAL_APPLICANT_COMPANY as virCom on VIRTUAL_APPLICANT_COMPANY_DETAIL.VIRTUAL_COMPANY_ID = virCom.ID";

    /**
     * #23 視覺化分析 各區處 轉供中 契約數量
     *
     * @return List
     */
    @Query(value =
            "Select DISTINCT count(app.ID) as operator, tpCom.UNIT_NAME, tpCom.CODE from APPLICATION as app" +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpCom on app.TPC_DEPT_ID = tpCom.ID" +
                    " where app.CONTRACTED_END is null and app.ONLINE_AT < DATEADD(day, 1, GETDATE()) and app.ONLINE_AT is not null" +
                    "   and app.CONTRACT_STATUS > 4 and app.CONTRACT_STATUS != 6" +
                    " group by tpCom.UNIT_NAME, tpCom.CODE" +
                    " order by operator DESC"
            , nativeQuery = true)
    List<Map<String, Object>> countMainTpcApplication();

    /**
     * #19 視覺化分析 契約流程進度統計 sign 簽約 change 變更 finish 終止
     *
     * @param firstDay 當年第一天
     * @return List
     */
    @Query(value =
            "Select count(app.ID) as sign, null as change, null as finish from APPLICATION as app" +
                    "  where app.VERSION = '00' and app.CONTRACTED_START >= ?1" +
                    " UNION Select DISTINCT null as sign, count(app.ID) as change, null as finish from APPLICATION as app" +
                    "    join APPLICATION as app2 on app.CONTRACT_NO = app2.CONTRACT_NO and app.VERSION > app2.VERSION" +
                    "      and (app2.CONTRACTED_START < ?1 or app2.ONLINE_AT < ?1) and app2.CONTRACTED_END >= DATEADD(year, -1, ?1)" +
                    "  where app.CONTRACT_STATUS = 5 and app.CONTRACTED_END is not null and app.CONTRACTED_START >= ?1" +
                    " UNION Select DISTINCT null as sign, null as change, count(app.ID) as finish from APPLICATION as app" +
                    "  where app.CONTRACT_STATUS = 6 and app.CONTRACTED_END >= ?1" +
                    " order by sign DESC, change DESC"
            , nativeQuery = true)
    List<Map<String, Object>> countContractStatus(Date firstDay);

    /**
     * #18 視覺化分析 計劃書申請流程進度統計
     * review 審查中 back 已函覆 apply 申請中 sign 已簽約 operator 轉供中/運轉中
     *
     * @param firstDay 當年-01-01
     * @return List
     */
    @Query(value =
            "Select count(app.ID) as review, null as back, null as apply, null as sign, null as operator from APPLICATION as app" +
                    " where app.CONTRACTED_END is null and app.STATUS != -1 and app.STATUS < 4 and app.CONTRACT_STATUS is null" +
                    " UNION Select null as review, count(app.ID) as back, null as apply, null as sign, null as operator" +
                    "  from APPLICATION as app" +
                    " where app.CONTRACTED_END is null and app.STATUS = 4 and (app.CONTRACT_STATUS = 0 OR app.CONTRACT_STATUS is null)" +
                    " UNION Select null as review, null as back, count(app.ID) as apply, null as sign, null as operator" +
                    "  from APPLICATION as app" +
                    " where app.CONTRACTED_END is null and app.CONTRACT_STATUS is not null and app.CONTRACT_STATUS != 0 and app.CONTRACT_STATUS < 4" +
                    " UNION Select null as review, null as back, null as apply, count(app.ID) as sign, null as operator" +
                    "  from APPLICATION as app" +
                    " where app.CONTRACTED_START >= ?1 and app.VERSION = '00'" +
                    " UNION Select null as review, null as back, null as apply, null as sign, count(app.ID) as operator" +
                    "  from APPLICATION as app" +
                    " where app.CONTRACTED_END is null and app.ONLINE_AT < DATEADD(day, 1, GETDATE())" +
                    "   and app.ONLINE_AT is not null and app.CONTRACT_STATUS > 4 and app.CONTRACT_STATUS != 6" +
                    " order by review DESC, back DESC, apply DESC, sign DESC"
            , nativeQuery = true)
    List<Map<String, Object>> countApplicationStatus(Date firstDay);

    /**
     * #8 視覺化分析 - 轉直供契約數量統計 (搭配 公開月報資料庫名稱 OFFICIAL_APPLICATION_QUANTITY - SQL 使用 UPDATE 更新
     *
     * @param start 輸入年月-01
     * @param end   結束年隔月-01
     * @return List
     */
    @Query(value =
            "Select office.SERVICE_DATE, office.TOTAL" +
                    ", (Select count(*) from APPLICATION where (ONLINE_AT >= office.SERVICE_DATE" +
                    "     or CONTRACTED_START >= office.SERVICE_DATE) and VERSION = '00') as new" +
                    ", count(CASE WHEN app.VERSION=app2.VERSION THEN 1 ELSE NULL END) as exist" +
                    ", count(CASE WHEN app.VERSION != app2.VERSION THEN 1 ELSE NULL END) as change, null as yearMode" +
                    " from OFFICIAL_APPLICATION_QUANTITY as office" +
                    "  left outer join APPLICATION as app on (app.ONLINE_AT >= office.SERVICE_DATE" +
                    "    or app.CONTRACTED_START >= office.SERVICE_DATE) and (" +
                    "   app.ONLINE_AT < DATEADD(month, 1, office.SERVICE_DATE) or app.CONTRACTED_START < DATEADD(month, 1, office.SERVICE_DATE)" +
                    ") and (app.CONTRACTED_END is null or app.CONTRACTED_END >= office.SERVICE_DATE)" +
                    "  left outer join APPLICATION as app2 on app.CONTRACT_NO = app2.CONTRACT_NO and (" +
                    "    app2.ONLINE_AT >= DATEADD(month, -1, office.SERVICE_DATE) or app2.CONTRACTED_START >= DATEADD(month, -1, office.SERVICE_DATE" +
                    "      )) and (app2.ONLINE_AT < office.SERVICE_DATE or app2.CONTRACTED_START < office.SERVICE_DATE)" +
                    "    and (app2.CONTRACTED_END is null or app2.CONTRACTED_END >= DATEADD(month, -1, office.SERVICE_DATE))" +
                    " where office.SERVICE_DATE >= ?1 and office.SERVICE_DATE < ?2" +
                    "  group by office.SERVICE_DATE, office.TOTAL UNION " +
                    "Select (CASE WHEN MONTH(office.SERVICE_DATE) = 12 THEN office.SERVICE_DATE ELSE NULL END), null as TOTAL" +
                    ", (Select count(*) from APPLICATION where (ONLINE_AT >= office.SERVICE_DATE or CONTRACTED_START >= office.SERVICE_DATE)" +
                    "    and VERSION = '00' and MONTH(office.SERVICE_DATE) = 12) as new" +
                    ", count(CASE WHEN (app.VERSION=app2.VERSION and MONTH(office.SERVICE_DATE) = 12) THEN 1 ELSE NULL END) as exist" +
                    ", count(CASE WHEN (app.VERSION != app2.VERSION and MONTH(office.SERVICE_DATE) = 12) THEN 1 ELSE NULL END) as change" +
                    ", 1 as yearMode from OFFICIAL_APPLICATION_QUANTITY as office" +
                    "  left outer join APPLICATION as app on (app.ONLINE_AT >= office.SERVICE_DATE or app.CONTRACTED_START >= office.SERVICE_DATE) and (" +
                    "    app.ONLINE_AT < DATEADD(year, 1, office.SERVICE_DATE) or app.CONTRACTED_START < DATEADD(year, 1, office.SERVICE_DATE)) and (" +
                    "app.CONTRACTED_END is null or app.CONTRACTED_END >= office.SERVICE_DATE)" +
                    "  left outer join APPLICATION as app2 on app.CONTRACT_NO = app2.CONTRACT_NO and (" +
                    "    app2.ONLINE_AT >= DATEADD(year, -1, office.SERVICE_DATE) or app2.CONTRACTED_START >= DATEADD(year, -1, office.SERVICE_DATE)) and (" +
                    "      app2.ONLINE_AT < office.SERVICE_DATE or app2.CONTRACTED_START < office.SERVICE_DATE) and (" +
                    "    app2.CONTRACTED_END is null or app2.CONTRACTED_END >= DATEADD(year, -1, office.SERVICE_DATE))" +
                    "  where office.SERVICE_DATE >= ?1 and office.SERVICE_DATE < ?2" +
                    "  group by office.SERVICE_DATE, office.TOTAL" +
                    "  order by SERVICE_DATE, yearMode"
            , nativeQuery = true)
    List<Map<String, Object>> countApplicationByMonthRange(Date start, Date end);

    /**
     * #5 視覺化分析 - 參與轉直供申請業者類別統計 + 虛擬集團 (搭配 公開月報資料庫名稱 OFFICIAL_APPLICANT_QUANTITY - SQL 使用 INSERT 更新
     * sale: APPLICANT_TYPE.ID = 2 - 再生能源售電業, gen: APPLICANT_TYPE.ID = 1 - 再生能源發電業
     * , device: APPLICANT_TYPE.ID = 3 - 自用發電設備設置者
     * office: 1: 公報, 2: 直接計算, 3: 虛擬集團計算
     *
     * @param date      輸入隔月/1
     * @param yearMonth 輸入民國年月 ex.11301
     * @return List
     */
    @Query(value =
            "Select count(DISTINCT IIF(virCom.NAME is null, apcEntity.NAME, virCom.NAME)) as sale, " +
                    "(Select count(DISTINCT IIF(virCom.NAME is null, APPLICANT_ENTITY.NAME, virCom.NAME)) from APPLICANT_ENTITY" +
                    selectApplicantEntityType + " 1" + joinVirtualApplicant +
                    " where ONLINE_AT < ?1) as gen," +
                    "(Select count(DISTINCT IIF(virCom.NAME is null, APPLICANT_ENTITY.NAME, virCom.NAME)) from APPLICANT_ENTITY" +
                    selectApplicantEntityType + " 3" + joinVirtualApplicant +
                    " where ONLINE_AT < ?1) as device, 3 as office" +
                    " from APPLICANT_ENTITY as apcEntity" +
                    "  join APPLICATION on apcEntity.ID = APPLICATION.APPLICANT_ID join APPLICANT_TYPE as apcType on apcEntity.TYPE = apcType.ID and apcType.ID = 2" +
                    joinVirtualApplicant +
                    " where APPLICATION.ONLINE_AT < ?1 UNION " +
                    "Select count(DISTINCT apcEntity.NAME) as sale, " +
                    "(Select count(DISTINCT APPLICANT_ENTITY.NAME) from APPLICANT_ENTITY" +
                    selectApplicantEntityType + " 1 where ONLINE_AT < ?1) as gen," +
                    "(Select count(DISTINCT APPLICANT_ENTITY.NAME) from APPLICANT_ENTITY" +
                    selectApplicantEntityType + " 3 where ONLINE_AT < ?1) as device, 2 as office" +
                    " from APPLICANT_ENTITY as apcEntity" +
                    "  join APPLICATION on apcEntity.ID = APPLICATION.APPLICANT_ID join APPLICANT_TYPE as apcType on apcEntity.TYPE = apcType.ID and apcType.ID = 2" +
                    " where APPLICATION.ONLINE_AT < ?1 UNION " +
                    "Select SALE as sale, GENERATOR as gen, DEVICE as device, 1 as office from OFFICIAL_APPLICANT_QUANTITY" +
                    " where YEAR_MONTH = ?2" +
                    " order by office"
            , nativeQuery = true)
    List<Map<String, Object>> countApplicantEntityByType(Date date, Integer yearMonth);

    /**
     * #20 標檢局取檔清單 輸入 與結帳契約資料檢查 搜尋起訖契約範圍 欄位16 主要目的取得 可結帳的父契約
     * APPLICATION 欄位(16) NO,CONTRACT_NO,SERVICE_ID[SERVICE_ID+VERSION], ID,CONTRACTED_START,ONLINE_AT,CONTRACTED_END,CONTRACT_STATUS
     * PARENT_ID,fatherContract ....
     *
     * @param settleContractList 所有已結帳資料的 settleContractList 列表
     * @param applyContractList  取檔清單輸入最大月份的簽單列表-少後2碼 applyContractList 列表
     * @return java.util.List
     */
    @Query(value =
            "Select app.CONTRACT_NO, app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, app.ID as appId" +
                    ", null as PARENT_ID, null as fatherContract, null as PId2, null as pContractNo3, null as PId3" +
                    ", null as pContractNo4, null as PId4, null as pContractNo5, null as PId5" +
                    " from APPLICATION as app" +
                    "  inner join (Select CONTRACT_NO, min(VERSION) as version from APPLICATION group by CONTRACT_NO) cc" +
                    "    on app.CONTRACT_NO = cc.CONTRACT_NO and app.VERSION = cc.version" +
                    " where (app.CONTRACT_NO in ?1 or app.CONTRACT_NO in ?2) and app.PARENT_ID is null UNION " +
                    "Select app.CONTRACT_NO, app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, app.ID as appId" +
                    ", app.PARENT_ID as PARENT_ID, app2.CONTRACT_NO+'-'+app2.VERSION as fatherContract, app2.PARENT_ID as PId2" +
                    ", app3.CONTRACT_NO+'-'+app3.VERSION as pContractNo3, app3.PARENT_ID as PId3" +
                    ", app4.CONTRACT_NO+'-'+app4.VERSION as pContractNo4, app4.PARENT_ID as PId4" +
                    ", app5.CONTRACT_NO+'-'+app5.VERSION as pContractNo5, app5.PARENT_ID as PId5" +
                    " from APPLICATION as app" +
                    " join APPLICATION as app2 on app.PARENT_ID = app2.ID" +
                    " left outer join APPLICATION as app3 on app2.PARENT_ID = app3.ID" +
                    " left outer join APPLICATION as app4 on app3.PARENT_ID = app4.ID" +
                    " left outer join APPLICATION as app5 on app4.PARENT_ID = app5.ID" +
                    " where app.PARENT_ID is not null and (app.CONTRACT_NO in ?1 or app.CONTRACT_NO in ?2) order by SERVICE_ID"
            , nativeQuery = true)
    List<Map<String, Object>> findParentIdByContractNos(List<String> settleContractList,
                                                        List<String> applyContractList);

    /**
     * RFP3.4 使用 ami用電端電號 起迄服務日 取得用電端公司資料 與 申請契約ID appId 方便計算契約件數
     * ELEC_NO[用電端電號] LOAD_NAME[用電端名稱] GROUP_LOAD_NAME[用電端虛擬集團名稱]
     *
     * @param ElecNo       ami取出單月用電端電號
     * @param serviceStart 單月月初
     * @param serviceEnd   隔月月初
     * @return
     */
    @Query(value =
            "Select lEntity.NBS_CUSTOMER_NUMBER as ELEC_NO, app.ID as appId, lEntity.NAME as LOAD_NAME" +
                    ", virLo.NAME as GROUP_LOAD_NAME from APPLICATION as app" +
                    " inner join (Select CONTRACT_NO, max(VERSION) as version from APPLICATION" +
                    "  where (CONTRACTED_END is null or CONTRACTED_END >= ?2) and ONLINE_AT < ?3 and CONTRACT_STATUS > 3" +
                    "   group by CONTRACT_NO) cc on app.CONTRACT_NO = cc.CONTRACT_NO and app.VERSION = cc.version" +
                    " join APPLICATION_LOAD as lap on app.ID = lap.APPLICATION_ID" +
                    " join LOAD_ENTITY as lEntity on lap.LOAD_ID = lEntity.ID" +
                    " Left outer join VIRTUAL_LOAD_COMPANY_DETAIL as virLoDetail on virLoDetail.LOAD_ID= lEntity.ID" +
                    "    and virLoDetail.VIRTUAL_COMPANY_ID > 0" +
                    " Left outer join VIRTUAL_LOAD_COMPANY as virLo on virLoDetail.VIRTUAL_COMPANY_ID = virLo.ID" +
                    " where lEntity.NBS_CUSTOMER_NUMBER in ?1 and (app.CONTRACTED_END is null or app.CONTRACTED_END >= ?2) and app.ONLINE_AT < ?3" +
                    "  and app.CONTRACT_STATUS > 3 order by appId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findLoadElecNoComApplicationId(List<String> ElecNo, Date serviceStart, Date serviceEnd);

    /**
     * RFP3.4 使用 ami發電端電號 起迄服務日 取得發電端公司資料 與 申請契約ID appId 方便計算契約件數
     * ELEC_NO[發電端電號] GEN_NAME[發電端名稱] GROUP_GEN_NAME[發電端虛擬集團名稱]
     *
     * @param ElecNo       ami取出單月發電端電號
     * @param serviceStart 單月月初
     * @param serviceEnd   隔月月初
     * @return
     */
    @Query(value =
            "Select gEntity.NBS_CUSTOMER_NUMBER as ELEC_NO, app.ID as appId, gEntity.NAME as GEN_NAME" +
                    ", virGen.NAME as GROUP_GEN_NAME from APPLICATION as app" +
                    " inner join (Select CONTRACT_NO, max(VERSION) as version from APPLICATION" +
                    "  where (CONTRACTED_END is null or CONTRACTED_END >= ?2) and ONLINE_AT < ?3 and CONTRACT_STATUS > 3" +
                    "   group by CONTRACT_NO) cc on app.CONTRACT_NO = cc.CONTRACT_NO and app.VERSION = cc.version" +
                    " join APPLICATION_GENERATOR as ap on app.ID = ap.APPLICATION_ID" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" +
                    " Left outer join VIRTUAL_GENERATOR_COMPANY_DETAIL as virGenDetail on virGenDetail.GENERATOR_ID= gEntity.ID" +
                    "   and virGenDetail.VIRTUAL_COMPANY_ID > 0" +
                    " Left outer join VIRTUAL_GENERATOR_COMPANY as virGen on virGenDetail.VIRTUAL_COMPANY_ID = virGen.ID" +
                    " where gEntity.NBS_CUSTOMER_NUMBER in ?1 and (app.CONTRACTED_END is null or app.CONTRACTED_END >= ?2) and app.ONLINE_AT < ?3" +
                    "  and app.CONTRACT_STATUS > 3 order by appId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findGeneratorElecNoComApplicationId(List<String> ElecNo, Date serviceStart,
                                                                  Date serviceEnd);

    /**
     * 系統管理統計報表 RFP2.7 每月案件狀態統計報表 MODULE.ID = 32  appId 提供比對
     * NO[遞件編號] SERVICE_ID[契約編號] lElecNo[用電端電號] loadVComId[用電端虛擬集團] gElecNo[發電端電號] genVComId[發電端虛擬集團]
     * CONTRACTED_END[契約終止日]
     * 目前進度: STATUS[計畫書狀態: 2:提交審查 3:待補件 4:審查通過] + CONTRACT_STATUS[契約狀態: 1:草稿 2:變更 3:待補件 4:已簽約 5:轉/直供中 6:已終止]
     *
     * @param startDate 當月1日
     * @param endDate   隔月1日
     * @return java.util.List
     */
    @Query(value =
            "Select app.ID as appId, app.NO, app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, lEntity.NBS_CUSTOMER_NUMBER as lElecNo" +
                    ", virLoadDetail.VIRTUAL_COMPANY_ID as loadVComId, gEntity.NBS_CUSTOMER_NUMBER as gElecNo" +
                    ", virGenDetail.VIRTUAL_COMPANY_ID as genVComId, app.CONTRACTED_END, app.STATUS, app.CONTRACT_STATUS" +
                    " from APPLICATION as app" +
                    " join APPLICATION_LOAD as lap on lap.APPLICATION_ID = app.ID" +
                    " join LOAD_ENTITY as lEntity on lEntity.ID = lap.LOAD_ID" +
                    " Left outer join VIRTUAL_LOAD_COMPANY_DETAIL as virLoadDetail on virLoadDetail.LOAD_ID= lEntity.ID" +
                    "    and virLoadDetail.VIRTUAL_COMPANY_ID > 0" +
                    " join APPLICATION_GENERATOR as ap on ap.APPLICATION_ID = app.ID" +
                    " join GENERATOR_ENTITY as gEntity on gEntity.ID = ap.GENERATOR_ID" +
                    " Left outer join VIRTUAL_GENERATOR_COMPANY_DETAIL as virGenDetail on virGenDetail.GENERATOR_ID= gEntity.ID" +
                    "    and virGenDetail.VIRTUAL_COMPANY_ID > 0" +
                    " where (app.CONTRACTED_END is null or app.CONTRACTED_END >= ?1)" +
                    " and (app.ONLINE_AT is null or app.ONLINE_AT < ?2 or app.SUBMITTED_AT < ?2)" +
                    " and app.CONTRACT_STATUS > 3 and app.NO is not null and app.NO != '' order by app.CONTRACT_NO ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findApplicationGeneratorLoadElecNos(Date startDate, Date endDate);

    /**
     * #22 企劃室 電能轉直供資訊報表(起迄單月) appId  迄固定隔年1/1 共27 欄位 增加 app.CONTRACT_NO app.PARENT_ID + app2.PARENT_ID方便整理
     * 申請類(16) NO[遞件編號] SERVICE_ID[契約編號] TPC_NAME[主辦區處] CONTRACT_TYPE[契約種類] APPLICANT_TYPE[申請者身分] APPL_NAME[申請者名稱]
     * SUBMITTED_AT[遞件日期] ALL_CONFIRM_DATE[審查函覆日] CONTRACT_REQUEST_AT[申請日期] CONTRACTED_START[簽約完成日] ONLINE_AT[正式轉供日]
     * 目前進度: STATUS[計畫書狀態: -1:廢棄 2:提交審查 3:待補件 4:審查通過] + CONTRACT_STATUS[契約狀態: -1:廢棄 1:草稿 2:變更 3:待補件 4:已簽約 5:轉/直供中 6:已終止]
     * CONTRACTED_END[契約終止日] fatherContract[父契約編號] TPC_CONTRACT[契約執行單位] PWDS[提供 PWDS 欄位判斷]
     *
     * @param start 固定該年1/1
     * @param end   固定隔年1/1
     * @return java.util.List
     */
    @Query(value =
            "Select app.ID as appId, app.NO, app.CONTRACT_NO, app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, tpCom.UNIT_NAME as TPC_MAIN" +
                    ", appType.LABEL as CONTRACT_TYPE, applicantType.LABEL as APPLICANT_TYPE, applicantEntity.NAME as APPL_NAME" +
                    ", app.SUBMITTED_AT, app.ALL_CONFIRM_DATE, app.CONTRACT_REQUEST_AT, app.CONTRACTED_START, app.ONLINE_AT" +
                    ", app.CONTRACTED_END, app.STATUS, app.CONTRACT_STATUS, null as PARENT_ID, null as fatherContract, null as PId2" +
                    ", null as pContractNo3, null as PId3, null as pContractNo4, null as PId4, null as pContractNo5, null as PId5" +
                    ", tpComC.UNIT_NAME as TPC_CONTRACT, app.TYPE as PWDS from APPLICATION as app" +
                    " Left outer join APPLICATION_TYPE as appType on appType.ID = app.TYPE" +
                    " Left outer join APPLICANT_ENTITY as applicantEntity on app.APPLICANT_ID = applicantEntity.ID" +
                    " Left outer join APPLICANT_TYPE as applicantType on applicantEntity.TYPE = applicantType.ID" +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpCom on tpCom.ID = app.TPC_DEPT_ID" +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpComC on tpComC.Id = app.CONTRACT_DEPT_ID" +
                    " where (app.CONTRACTED_END is null or app.CONTRACTED_END >= ?1) and app.ONLINE_AT < ?2" +
                    "   and app.ONLINE_AT is not null and app.PARENT_ID is null UNION " +
                    "Select app.ID as appId, app.NO, app.CONTRACT_NO, app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, tpCom.UNIT_NAME as TPC_MAIN" +
                    ", appType.LABEL as CONTRACT_TYPE, applicantType.LABEL as APPLICANT_TYPE, applicantEntity.NAME as APPL_NAME" +
                    ", app.SUBMITTED_AT, app.ALL_CONFIRM_DATE, app.CONTRACT_REQUEST_AT, app.CONTRACTED_START, app.ONLINE_AT" +
                    ", app.CONTRACTED_END, app.STATUS, app.CONTRACT_STATUS, null as PARENT_ID, null as fatherContract, null as PId2" +
                    ", null as pContractNo3, null as PId3, null as pContractNo4, null as PId4, null as pContractNo5, null as PId5" +
                    ", tpComC.UNIT_NAME as TPC_CONTRACT, app.TYPE as PWDS from APPLICATION as app" +
                    " inner join (Select CONTRACT_NO, min(VERSION) as version from APPLICATION" +
                    "    group by CONTRACT_NO) cc on app.CONTRACT_NO = cc.CONTRACT_NO and app.VERSION = cc.version" +
                    " Left outer join APPLICATION_TYPE as appType on appType.ID = app.TYPE" +
                    " Left outer join APPLICANT_ENTITY as applicantEntity on app.APPLICANT_ID = applicantEntity.ID" +
                    " Left outer join APPLICANT_TYPE as applicantType on applicantEntity.TYPE = applicantType.ID" +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpCom on tpCom.ID = app.TPC_DEPT_ID" +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpComC on tpComC.Id = app.CONTRACT_DEPT_ID" +
                    " where app.ONLINE_AT is not null and app.PARENT_ID is null UNION " +
                    "Select app.ID as appId, app.NO, app.CONTRACT_NO, app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, tpCom.UNIT_NAME as TPC_MAIN" +
                    ", appType.LABEL as CONTRACT_TYPE, applicantType.LABEL as APPLICANT_TYPE, applicantEntity.NAME as APPL_NAME" +
                    ", app.SUBMITTED_AT, app.ALL_CONFIRM_DATE, app.CONTRACT_REQUEST_AT, app.CONTRACTED_START, app.ONLINE_AT" +
                    ", app.CONTRACTED_END, app.STATUS, app.CONTRACT_STATUS, app.PARENT_ID, app2.CONTRACT_NO+'-'+app2.VERSION as fatherContract" +
                    ", app2.PARENT_ID as PId2, app3.CONTRACT_NO+'-'+app3.VERSION as pContractNo3, app3.PARENT_ID as PId3" +
                    ", app4.CONTRACT_NO+'-'+app4.VERSION as pContractNo4, app4.PARENT_ID as PId4" +
                    ", app5.CONTRACT_NO+'-'+app5.VERSION as pContractNo5, app5.PARENT_ID as PId5" +
                    ", tpComC.UNIT_NAME as TPC_CONTRACT, app.TYPE as PWDS from APPLICATION as app" +
                    " join APPLICATION as app2 on app.PARENT_ID = app2.ID" +
                    " left outer join APPLICATION as app3 on app2.PARENT_ID = app3.ID" +
                    " left outer join APPLICATION as app4 on app3.PARENT_ID = app4.ID" +
                    " left outer join APPLICATION as app5 on app4.PARENT_ID = app5.ID" +
                    " Left outer join APPLICATION_TYPE as appType on appType.ID = app.TYPE" +
                    " Left outer join APPLICANT_ENTITY as applicantEntity on app.APPLICANT_ID = applicantEntity.ID" +
                    " Left outer join APPLICANT_TYPE as applicantType on applicantEntity.TYPE = applicantType.ID" +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpCom on tpCom.ID = app.TPC_DEPT_ID" +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpComC on tpComC.Id = app.CONTRACT_DEPT_ID" +
                    " where app.PARENT_ID is not null" +
                    " order by SERVICE_ID, PARENT_ID ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findApplicationApplicantTpcCompany(Date start, Date end);

    /** #19 調度處 15分鐘用電量 ami channel = 1 用來查找契約內有效 用電電號列表
     * @param ELEC_NO   ami用電端電號列表
     * @param startDate 單月首日
     * @param endDate   隔月首日
     * @return java.util.List
     */
    @Query(value =
            "Select lEntity.NBS_CUSTOMER_NUMBER as CUST_ELEC_NO from APPLICATION as app inner join (" +
                    "Select CONTRACT_NO, max(VERSION) as version from APPLICATION" +
                    " where (CONTRACTED_END is null or CONTRACTED_END >= ?2) and ONLINE_AT < ?3 and CONTRACT_STATUS > 3 group by CONTRACT_NO" +
                    ") cc on app.CONTRACT_NO = cc.CONTRACT_NO and app.VERSION = cc.version" +
                    " join APPLICATION_LOAD as lap on app.ID = lap.APPLICATION_ID" +
                    " join LOAD_ENTITY as lEntity on lap.LOAD_ID = lEntity.ID" +
                    " where lEntity.NBS_CUSTOMER_NUMBER in ?1 and" +
                    " (app.CONTRACTED_END is null or app.CONTRACTED_END >= ?2) and app.ONLINE_AT < ?3" +
                    " and app.CONTRACT_STATUS > 3 group by lEntity.NBS_CUSTOMER_NUMBER, lEntity.ID order by CUST_ELEC_NO ASC"
            , nativeQuery = true)
    List<String> findLoadEntityNbsCustomerNumbers(List<String> ELEC_NO, Date startDate, Date endDate);

    /** #19 調度處 15分鐘發電量 ami channel = 3 用來查找契約內有效 發電電號列表
     * @param ELEC_NO   ami發電端電號列表
     * @param startDate 單月首日
     * @param endDate   隔月首日
     * @return java.util.List
     */
    @Query(value =
            "Select gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO from APPLICATION as app" +
                    " inner join (Select CONTRACT_NO, max(VERSION) as version from APPLICATION" +
                    " where (CONTRACTED_END is null or CONTRACTED_END >= ?2) and ONLINE_AT <?3 and CONTRACT_STATUS > 3 group by CONTRACT_NO" +
                    ") cc on app.CONTRACT_NO = cc.CONTRACT_NO and app.VERSION = cc.version" +
                    " join APPLICATION_GENERATOR as ap on app.ID = ap.APPLICATION_ID" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" +
                    " where gEntity.NBS_CUSTOMER_NUMBER in ?1 and" +
                    " (app.CONTRACTED_END is null or app.CONTRACTED_END >= ?2) and app.ONLINE_AT < ?3" +
                    " and app.CONTRACT_STATUS > 3 group by gEntity.NBS_CUSTOMER_NUMBER, gEntity.ID order by GEN_ELEC_NO ASC"
            , nativeQuery = true)
    List<String> findGeneratorEntityNbsCustomerNumbers(List<String> ELEC_NO, Date startDate, Date endDate);

    /**
     * 列出 generatorEntityId 作為查找參考
     * #16 調度處 發電月報 使用電號GEN_ELEC_NO 查 SERVICE_ID表單編號[=契約編號] GEN_NAME發電業者名稱 跟 DEVICE_ADDRESS 設備地址
     * 加上 APPLICATION.CONTRACTED_END 契約結束日 + APPLICATION.ONLINE_AT 開始執行日 + APPLICATION.CONTRACT_STATUS > 3 判斷
     * 電號相同處理方式: 以最新契約編號為主, 之後台電有意見再修改
     * EQUIP_VOLT_LEVEL[發電設備電壓層級] GENERATOR_ENTITY.COMBINE_METHOD = 1 選 GENERATOR_ENTITY.VOLTAGE[拼接點電壓層級]
     *           ; GENERATOR_ENTITY.COMBINE_METHOD != 1 選 GENERATOR_ENTITY.RESPONSIBILITY_VOLTAGE[則分點電壓層級]
     * DEVICE_CAPACITY[裝置容量=APPLICATION_GENERATOR.LICENSE_CAPACITY] ENERGY_TYPE[能源別]
     * CONTRACT_EFFE_DATE[契約生效日期] TERMINATE_DATE[契約終止日期]
     *
     * @param startDate 當月1日
     * @param endDate   隔月1日
     * @return
     */
    @Query(value =
            "Select (app.CONTRACT_NO+'-'+app.VERSION) as SERVICE_ID, app.Type as PWDS" +
                    ", gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO, gEntity.NAME as GEN_NAME" +
                    ", ca.CITY_NAME+ca.AREA_NAME+gEntity.ADDRESS_OTHER as DEVICE_ADDRESS, app.CONTRACTED_START as CONTRACT_EFFE_DATE" +
                    ", app.CONTRACTED_END as TERMINATE_DATE, ap.LICENSE_CAPACITY as DEVICE_CAPACITY" +
                    ", volLevel.LABEL as EQUIP_VOLT_LEVEL, fulType.LABEL as ENERGY_TYPE, gEntity.ID as generatorEntityId" +
                    " from APPLICATION as app" +
                    " inner join (Select CONTRACT_NO, max(VERSION) as version from APPLICATION" +
                    "   where (CONTRACTED_END is null or CONTRACTED_END >= ?1) and ONLINE_AT < ?2 and CONTRACT_STATUS > 3" +
                    "   group by CONTRACT_NO) cc on app.CONTRACT_NO = cc.CONTRACT_NO and app.VERSION = cc.version" +
                    " join APPLICATION_GENERATOR as ap on ap.APPLICATION_ID = app.ID" +
                    " join GENERATOR_ENTITY as gEntity on gEntity.ID = ap.GENERATOR_ID" +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " join VOLTAGE_LEVEL as volLevel on ((gEntity.COMBINE_METHOD = 1 and gEntity.VOLTAGE = volLevel.ID)" +
                    "   OR (gEntity.COMBINE_METHOD != 1 and gEntity.RESPONSIBILITY_VOLTAGE = volLevel.ID))" +
                    "   and volLevel.VOLTAGE_CLASSIFICATIONS_ID is not null" +
                    " join CITY_AREA as ca on ca.ID = gEntity.ADDRESS_AREA" +
                    " where (app.CONTRACTED_END >= ?1 or app.CONTRACTED_END is null) and app.ONLINE_AT < ?2 and app.CONTRACT_STATUS > 3" +
                    " and ap.TRIAL_RUN_CAPACITY >= 0 and (ap.LICENSE_CAPACITY + ap.TRIAL_RUN_CAPACITY) > 0" +
                    " order by gEntity.NBS_CUSTOMER_NUMBER, app.CONTRACT_NO DESC"
            , nativeQuery = true)
    List<Map<String, Object>> findGeneratorContractInfo(Date startDate, Date endDate);

    /**
     * 查找 contractNo 列表
     *
     * @param contractNos
     * @return
     */
    @Query(value =
            "Select app.CONTRACT_NO, app.VERSION, app.CONTRACT_NO+'-'+app.VERSION as FATHER_CONTRACT" +
                    ", app.CONTRACTED_END as FATHER_CONTRACT_TERMINATE_DATE" +
                    " from APPLICATION as app where app.CONTRACT_NO in ?1 order by app.CONTRACTED_END DESC"
            , nativeQuery = true)
    List<Map<String, Object>> findFatherContractListByContractNos(List<String> contractNos);

    List<Application> findByContractedStartLessThanEqualAndContractedEndGreaterThanEqual(Date startTime, Date endTime);

    Page<Application> findByIdIn(Iterable<Long> longs, Pageable pageable);

    List<Application> findAllByApplicantId(Long applicantId);

    /***
     *  sql
     *     SELECT DISTINCT APPLICATION_ID
     *     FROM DATE_APPLICATION_COMPUTABLE_SETTLEMENT
     *     WHERE DATE BETWEEN '2024-06-01' AND '2024-06-30'
     *     AND COMPUTABLE = 1
     *
     * @author: ting
     * @date: 2024/06/23 13:45:11
     * @param: [startTime, endTime]
     * @return: java.util.List<?>
     **/

    @Query("   SELECT new map(contractNo AS " + FIELD_NAME_APPLICATION_CONTRACT_NO +
            ", id AS " + FIELD_NAME_APPLICATION_ID + ")" +
            "   FROM Application" +
            "   WHERE id In (?1)" +
            "   ORDER BY id ASC")
    List<?> findIdAndContractNoByIdIn(List<Long> idList);

    /**
     * 變更計畫書 審查階段狀態
     *
     * @param applicationId //    * @param stage2Status
     */
//    @Transactional
//    @Modifying
//    @Query("update Application a set a.stage2Status = ?2 where a.id = ?1")
//    void updateStage2Status(Long applicationId, Integer stage2Status);

//    /**
//     * 變更計畫書 簽約階段狀態
//     *
//     * @param applicationId
//     * @param stage2Status
//     */
//    @Transactional
//    @Modifying
//    @Query("update Application a set a.stage3Status = ?2 where a.id = ?1")
//    void updateStage3Status(Long applicationId, Integer stage2Status);
    @Transactional
    @Modifying
    @Query("update Application a set a.contractStep = ?2 where a.id = ?1")
    void updateContractStep(Long applicationId, Integer contractStep);

    @Transactional
    @Modifying
    @Query("update Application a set a.contractRequestAt = ?2 where a.id = ?1")
    void updateContractRequestAt(Long applicationId, Date contractRequestAt);


    @Query("   SELECT new map(type AS " + FIELD_NAME_APPLICATION_TYPE +
            ", id AS " + FIELD_NAME_APPLICATION_ID + ")" +
            "   FROM Application" +
            "   WHERE id In (?1)" +
            "   ORDER BY id ASC")
    List<?> findIdAndTypeByIdInOrderByIdAsc(List<Long> idList);

    Application findByNo(String no);

    /**
     * 變更計畫書 免稅狀態
     *
     * @param applicationId
     * @param isTaxFree
     * @param accountId
     * @param modifiedAt
     */
    @Transactional
    @Modifying
    @Query("update Application a set a.isTaxFree = ?2 , a.modifiedBy = ?3 , a.modifiedAt = ?4 where a.id = ?1")
    void updateIsTaxFreeStatus(Long applicationId, Boolean isTaxFree, Long accountId, Timestamp modifiedAt);

    /**
     * 變更計畫書 不需送ERP狀態
     *
     * @param applicationId
     * @param isSkipERP
     * @param accountId
     * @param modifiedAt
     */
    @Transactional
    @Modifying
    @Query("update Application a set a.isSkipERP = ?2 , a.modifiedBy = ?3 , a.modifiedAt = ?4 where a.id = ?1")
    void updateIsSkipERPStatus(Long applicationId, Boolean isSkipERP, Long accountId, Timestamp modifiedAt);

    /**
     * 用契約編號以及版本來尋找父契約
     *
     * @param contractNo
     * @param version
     * @return
     */
    Optional<Application> findOneByContractNoAndVersion(String contractNo, String version);

    /**
     * 根據服務單號查詢計畫書
     *
     * @param no
     * @return
     */
    Optional<Application> findOneByNo(String no);


    @Query(value = " SELECT a.id FROM Application a " +
            "WHERE concat( a.contractNo ,'-', a.version ) like ?1 and a.stage3Status > 0  and (a.contractedEnd is " +
            "null or a.contractedEnd >= ?2 ) ")
    List<Long> findAllByContractNo(String contractNo, Date today);

    List<Application> findByContractNo(String contractNo);

    @Query(value = " SELECT a.id FROM Application a " +
            "WHERE a.stage3Status > 0  and (a.contractedEnd is " +
            "null or a.contractedEnd >= ?2 ) and ( ( a.id in ( SELECT al.applicationId FROM ApplicationLoad al WHERE " +
            "al" +
            ".loadId IN ( " +
            "SELECT al2.loadId FROM ApplicationLoad al2 WHERE al2.applicationId in ?1 )" +
            " ) ) or a.id in ( SELECT ag.applicationId FROM ApplicationGenerator ag WHERE ag.generatorId IN (SELECT " +
            "ag2.generatorId FROM ApplicationGenerator" +
            " ag2 WHERE ag2.applicationId IN ?1 ) ) )")
    List<Long> findAllByContractNo(List<Long> applicationIds, Date today);

    @Query(value = " SELECT a.id FROM Application a " +
            "WHERE a.stage3Status > 0  and (a.contractedEnd is " +
            "null or a.contractedEnd >= ?2 ) and( ( a.id in ( SELECT al.applicationId FROM ApplicationLoad al WHERE " +
            "al" +
            ".loadId IN ( " +
            "SELECT al2.loadId FROM ApplicationLoad al2 WHERE al2.applicationId in ?1 )" +
            " ) ) or a.id in ( SELECT ag.applicationId FROM ApplicationGenerator ag WHERE ag.generatorId IN (SELECT " +
            "ag2.generatorId FROM ApplicationGenerator" +
            " ag2 WHERE ag2.applicationId IN ?1 ) ) ) and ( ?3 is null or a.contractStatus = ?3 )")
    List<Long> findAllByContractNo(List<Long> applicationIds, Date today, Integer contractStatus);

    @Query(value = " SELECT a.id FROM Application a " +
            "WHERE a.stage3Status > 0  and (a.contractedEnd is " +
            "null or a.contractedEnd >= ?2 ) and ( a.id in ( SELECT al.applicationId FROM ApplicationLoad al WHERE " +
            "al" +
            ".loadId IN ( " +
            "SELECT al2.loadId FROM ApplicationLoad al2 WHERE al2.applicationId in ?1 )" +
            " ) ) and ( ?3 is null or a.contractStatus = ?3 )")
    List<Long> findAllByContractNoLoad(List<Long> applicationIds, Date today, Integer contractStatus);

    @Query(value = "SELECT ID FROM APPLICATION " +
            "    WHERE CONTRACT_STATUS != -1 " +
            "       AND ONLINE_AT <= :date  " +
            "       AND ((CONTRACTED_END >= :date) OR (CONTRACTED_END IS NULL))", nativeQuery = true)
    List<Long> findApplicationIdByDate(@Param("date") Date date);

    /**
     * 查詢關聯契約，但不根據發電端數量一直往外增長，僅用電端增長
     *
     * @param applicationIds
     * @param today
     * @return
     */
    @Query(value = " SELECT a.id FROM Application a " +
            "WHERE a.stage3Status > 0  and (a.contractedEnd is " +
            "null or a.contractedEnd >= ?2 ) and ( ( a.id in ( SELECT al.applicationId FROM ApplicationLoad al WHERE " +
            "al" +
            ".loadId IN ( " +
            "SELECT al2.loadId FROM ApplicationLoad al2 WHERE al2.applicationId in ?1 )" +
            " ) )  )")
    List<Long> findAllByContractNoWithoutAgGrow(List<Long> applicationIds, Date today);

    @Query(value = "SELECT TYPE FROM APPLICATION WHERE ID = :id", nativeQuery = true)
    String findTypeById(@Param("id") Long id);

    @Query(value = "SELECT MAX(CAST(VERSION AS int)) FROM APPLICATION WHERE CONTRACT_NO = ?1", nativeQuery = true)
    Optional<Integer> findMaxUseVersionWithContractNo(String contractNo);

    @Query(value = "SELECT (CONTRACT_NO + '-' + VERSION) FROM APPLICATION " +
            "    WHERE TYPE = :type" +
            "        AND (CONTRACT_NO + '-' + VERSION)  IN (:contractNoList)", nativeQuery = true)
    List<String> findContractNoByTypeAndContractNoIn(@Param("type") String type,
                                                     @Param("contractNoList") List<String> contractNoList);

    @Query(value = "SELECT ID FROM APPLICATION " +
            "    WHERE (CONTRACT_NO + '-' + VERSION) IN (:contractNoList)", nativeQuery = true)
    List<Long> findByContractNoAndVersion(@Param("contractNoList") List<String> contractNoList);

    @Query(value = "WITH VERSION_SQL AS (SELECT CONTRACT_NO, CAST(VERSION AS int) - 1 AS PRE_VERSION FROM APPLICATION           " +
            "        WHERE CAST(VERSION AS int) > 1           " +
            "          AND CONTRACT_STATUS != -1           " +
            "          AND ONLINE_AT > :startDate)           " +
            "SELECT A.CONTRACT_NO, A.VERSION, A.CONTRACTED_END FROM APPLICATION A           " +
            "    INNER JOIN VERSION_SQL VS           " +
            "        ON A.CONTRACT_NO = VS.CONTRACT_NO           " +
            "        AND RIGHT('00' + CAST(PRE_VERSION AS VARCHAR(2)), 2) = A.VERSION           " +
            "        AND A.CONTRACTED_END >= :startDate           " +
            "        AND A.ONLINE_AT <= :endDate", nativeQuery = true)
    List<Map<String, Object>> findByContractEndAndOnlinAtAndVersionGreaterThan(@Param("startDate") Date startDate
            , @Param("endDate") Date endDate);


    @Query(value = "SELECT ID, CONTRACT_NO, ONLINE_AT FROM APPLICATION           " +
            "    WHERE CONTRACT_NO + '-' + VERSION IN (:contractNoList)", nativeQuery = true)
    List<Map<String, Object>> findContractNoAndOnlineAtByContractNoAndVersion(
            @Param("contractNoList") List<String> contractNoList);

    @Query("SELECT count(a.id) FROM Application a JOIN Account b ON a.applicantId = b.applicant.id WHERE a.id = :applicationId AND b.id = :accountId")
    Long findApplicationIdByApplicantIdAndAccountId(@Param("applicationId") Long applicationId,
                                                    @Param("accountId") Long accountId);

    @Query("SELECT distinct r.appRelationId from ApplicationRelation r JOIN Application a on a.id = r.applicationId where r.applicationId in ?1 and ( ?2 is null or a.contractStatus = ?2 )")
    List<Long> findContractRelation(List<Long> applicationIds, Integer contractStatus);

    @Query(value = " SELECT a FROM Application a " +
            "WHERE concat( a.contractNo ,'-', a.version ) = ?1  ")
    List<Application> findAllByFullContractNoVersion(String contractNo);

}
