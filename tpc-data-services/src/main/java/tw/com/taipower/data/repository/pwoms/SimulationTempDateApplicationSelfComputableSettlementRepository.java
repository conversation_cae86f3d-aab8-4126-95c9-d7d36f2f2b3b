package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.DateApplicationComputableSettlementColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempDateApplicationSelfComputableSettlement;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of TempDateApplicationComputableSettlement
 *
 * @class: TempDateApplicationComputableSettlementRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-03 21:18
 * @see:
 **/
public interface SimulationTempDateApplicationSelfComputableSettlementRepository extends JpaRepository<SimulationTempDateApplicationSelfComputableSettlement, DateApplicationComputableSettlementColumnId> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN " +
            " DECLARE @date DATE = :startDate  " +
            " WHILE @date <= :endDate  " +
            "    BEGIN  " +
            "        BEGIN  " +
            "            INSERT INTO SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT(DATE  " +
            "                                                                        , APPLICATION_ID  " +
            "                                                                        , COMPUTABLE  " +
            "                                                                        , SETTLEMENT_ID)  " +
            "            SELECT DISTINCT  @date, SUBQUERY.APPLICATION_ID, 0, :settlementId  " +
            "            FROM (SELECT DISTINCT TDAGL.APPLICATION_ID FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL  " +
            "                                                                INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_METER AS TDAM  " +
            "                                                                           ON TDAGL.ID = TDAM.DATE_APPLICATION_ID  " +
            "                                                                INNER JOIN SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT AS TDMCS  " +
            "                                                                           ON TDMCS.ID = TDAM.DATE_METER_ID  " +
            "                  WHERE TDMCS.DATE =  @date  " +
            "                    AND TDMCS.SETTLEMENT_ID = :settlementId  " +
            "                    AND TDAGL.SETTLEMENT_ID = :settlementId " +
            "                    AND TDMCS.COMPUTABLE = 0) AS SUBQUERY  " +
            "  " +
            "            INSERT INTO SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT(DATE  " +
            "                                                                        , APPLICATION_ID  " +
            "                                                                        , COMPUTABLE  " +
            "                                                                        , SETTLEMENT_ID)  " +
            "            SELECT DISTINCT DATE, TDAGL.APPLICATION_ID, 1, :settlementId  " +
            "            FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL  " +
            "            WHERE TDAGL.APPLICATION_ID NOT IN (SELECT DISTINCT TDASCS.APPLICATION_ID FROM SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT AS TDASCS  " +
            "                                               WHERE DATE =  @date  " +
            "                                                 AND SETTLEMENT_ID = :settlementId)  " +
            "              AND TDAGL.DATE =  @date  " +
            "              AND TDAGL.SETTLEMENT_ID = :settlementId" +
            "        END  " +
            "  " +
            "        SET @date = DATEADD(day, 1, @date)  " +
            "    END " +
            " END ", nativeQuery = true)
    void saveAllByDateIntervalAndSettlementId(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId);

}
