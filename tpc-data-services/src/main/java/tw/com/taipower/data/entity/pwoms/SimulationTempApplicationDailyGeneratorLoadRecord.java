package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Temp Application Daily Generator & Load Record
 *
 * @class: TempApplicationDailyGeneratorLoadRecord
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:35
 * @see:
 **/

@AllArgsConstructor
@SuperBuilder
@Entity
@Table(name = "SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD")
public class SimulationTempApplicationDailyGeneratorLoadRecord extends ApplicationDailyGeneratorLoadRecordColumn{


}