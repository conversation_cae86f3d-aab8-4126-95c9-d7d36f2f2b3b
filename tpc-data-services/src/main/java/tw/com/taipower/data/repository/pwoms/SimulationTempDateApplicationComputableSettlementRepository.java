package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.DateApplicationComputableSettlementColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempDateApplicationComputableSettlement;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of TempDateApplicationComputableSettlement
 *
 * @class: TempDateApplicationComputableSettlementRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-03 21:18
 * @see:
 **/
public interface SimulationTempDateApplicationComputableSettlementRepository extends JpaRepository<SimulationTempDateApplicationComputableSettlement, DateApplicationComputableSettlementColumnId> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN " +
            "    DECLARE @date DATE = :startDate " +
            " " +
            "    DELETE SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT " +
            "    WHERE DATE BETWEEN :startDate and :endDate " +
            "           AND SETTLEMENT_ID = :settlementId" +
            " " +
            "    WHILE @date <= :endDate " +
            "        BEGIN " +
            " " +
            "            BEGIN " +
            "                INSERT INTO SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT(DATE " +
            "                                                                       , APPLICATION_ID " +
            "                                                                       , COMPUTABLE " +
            "                                                                       , SETTLEMENT_ID) " +
            "                    SELECT DISTINCT @date, AR.APPLICATION_ID, 0, :settlementId FROM SIMULATION_APPLICATION_RELATION AS AR " +
            "                         INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT AS TDASCS " +
            "                                    ON TDASCS.APPLICATION_ID = AR.RELATION_ID " +
            "                                        AND TDASCS.DATE = AR.DATE " +
            "                                        AND TDASCS.SETTLEMENT_ID = :settlementId " +
            "                        WHERE AR.APPLICATION_ID IN " +
            "                              (SELECT APPLICATION_ID FROM SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT AS TDASCS2 " +
            "                               WHERE TDASCS2.DATE = @date " +
            "                                 AND TDASCS2.SETTLEMENT_ID = :settlementId) " +
            "                          AND AR.DATE = @date " +
            "                          AND TDASCS.COMPUTABLE = 0 " +
            "            END " +
            " " +
            "            BEGIN " +
            "                INSERT INTO SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT(DATE " +
            "                                                                       , APPLICATION_ID " +
            "                                                                       , COMPUTABLE " +
            "                                                                       , SETTLEMENT_ID) " +
            "                    SELECT @date, APPLICATION_ID, 1, :settlementId FROM SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT AS TDASCS " +
            "                        WHERE TDASCS.DATE = @date " +
            "                          AND TDASCS.SETTLEMENT_ID = :settlementId " +
            "                          AND APPLICATION_ID NOT IN ( " +
            "                          SELECT APPLICATION_ID FROM SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS2 " +
            "                          WHERE TDACS2.DATE = @date " +
            "                            AND TDACS2.SETTLEMENT_ID = :settlementId) " +
            "            END " +
            "            SET @date = DATEADD(day, 1, @date)  " +
            "        END " +
            "END ", nativeQuery = true)
    void saveAllByDateIntervalAndSettlementId(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId);

    @Query(value = "SELECT DISTINCT AL.APPLICATION_ID, A.CONTRACT_NO, AL.ID AS APPLICATION_LOAD_ID, AL.IS_DIRECT, A.TYPE, MONTHLY_CONTRACT_CAP, ANNUAL_CONTRACT_CAP FROM SIMULATION_APPLICATION_LOAD AS AL " +
            "    INNER JOIN dbo.APPLICATION A " +
            "        ON A.ID = AL.APPLICATION_ID " +
            "    WHERE AL.ID IN ( " +
            "        SELECT TDAGL.APPLICATION_LOAD_ID FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL " +
            "            WHERE TDAGL.APPLICATION_ID IN ( " +
            "                SELECT TDACS.APPLICATION_ID FROM SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT AS TDACS " +
            "                    WHERE TDACS.COMPUTABLE = 1 " +
            "                        AND TDACS.SETTLEMENT_ID = :settlementId " +
            "                        AND TDACS.DATE BETWEEN :startDate AND :endDate) " +
            "            AND TDAGL.SETTLEMENT_ID = :settlementId)", nativeQuery = true)
    List<Map<String, Object>> findComputableApplicationByDateIntervalAndSettlementId(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId);

    @Query(value = "SELECT DISTINCT TDACS1.APPLICATION_ID FROM SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS1   " +
            "    WHERE APPLICATION_ID NOT IN (   " +
            "        SELECT DISTINCT APPLICATION_ID FROM SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS2   " +
            "        WHERE DATE BETWEEN :startDate AND :endDate   " +
            "          AND TDACS2.SETTLEMENT_ID = :settlementId   " +
            "          AND TDACS2.COMPUTABLE = 0)", nativeQuery = true)
    List<Long> findComputableApplicationIdByDateIntervalAndSettlementId(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId);

    @Query(value = "SELECT ID, TYPE FROM SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS " +
            "    INNER JOIN APPLICATION A " +
            "        ON A.ID = TDACS.APPLICATION_ID " +
            "    WHERE   TDACS.DATE = :date " +
            "      AND TDACS.SETTLEMENT_ID = :settlementId " +
            "      AND TDACS.COMPUTABLE = :computable", nativeQuery = true)
    List<Map<String, Object>> findIdAndTypeByDateAndSettlementId(@Param("date") Date date, @Param("settlementId") Long settlementId, @Param("computable") boolean computable);

    @Query(value = "SELECT DISTINCT APPLICATION_ID, TYPE FROM SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS1  " +
            "    INNER JOIN APPLICATION A  " +
            "        ON A.ID = TDACS1.APPLICATION_ID  " +
            "    WHERE APPLICATION_ID NOT IN (  " +
            "        SELECT DISTINCT APPLICATION_ID FROM SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS2  " +
            "            WHERE DATE BETWEEN :startDate AND :endDate  " +
            "              AND TDACS2.SETTLEMENT_ID = :settlementId  " +
            "              AND TDACS2.COMPUTABLE = 0) ", nativeQuery = true)
    List<Map<String, Object>> findComputableApplicationAndTypeByDateIntervalAndSettlementId(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId);


}
