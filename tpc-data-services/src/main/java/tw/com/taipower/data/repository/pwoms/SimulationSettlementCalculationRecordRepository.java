package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationSettlementCalculationRecord;

import java.util.List;
import java.util.Map;

/**
 * Repository of SettlementCalculationRecord
 *
 * @class: SettlementCalculationRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-08 10:34
 * @see:
 **/

public interface SimulationSettlementCalculationRecordRepository extends JpaRepository<SimulationSettlementCalculationRecord, Long> {

    @Query(value = "SELECT ID, APPLICATION_ID FROM SIMULATION_SETTLEMENT_CALCULATION_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            "        AND APPLICATION_ID IN (:appIdList) ", nativeQuery = true)
    List<Map<String, Object>> findBySettlementIdAndAppIdList(@Param("settlementId") Long settlementId, @Param("appIdList") List<Long> appIdList);

    @Transactional
    @Modifying
    @Query(value = "UPDATE SIMULATION_SETTLEMENT_CALCULATION_RECORD         " +
            "          SET REASON_OF_FAILURE = :reason         " +
            "          , EXECUTION_RESULT = :success         " +
            "          WHERE ID IN (:idList)", nativeQuery = true)
    void updateExecution(@Param("idList") List<Long> idList, @Param("success") Boolean success, @Param("reason") String reason);


}
