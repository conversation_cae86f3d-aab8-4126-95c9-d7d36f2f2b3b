package tw.com.taipower.data.repository.pwoms;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tw.com.taipower.data.entity.pwoms.ReSettlement;
import tw.com.taipower.data.repository.ami.AmiPwrFixedRepository;
import tw.com.taipower.data.vo.powms.AmiRecord;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

/**
 * RE30結算功能
 *
 * @class: public class ReAmiService
 * @author: charlene
 * @version: 1.0
 * @since: 2025-03-17 10:21
 * @see:
 **/

@Log4j2
@Service
@RequiredArgsConstructor
public class AmiStagingService {

    private final AmiPwrFixedRepository amiRepo;
    private final JdbcTemplate jdbcTemplate;

    @PersistenceContext
    private final EntityManager entityManager;

    public void loadAmiStagingData(ReSettlement reSettlement, List<String> custIdList, int channel) {
        log.info("== oout loadAmiStagingData. =" + reSettlement+ " , channel: " + channel);
        // clean existing staging records for this RE_SETTLEMENT_ID and channel
        deleteAmiStagingData(reSettlement.getId(), channel);

        LocalDate from = reSettlement.getYymm();
        LocalDate to = from.plusMonths(1).withDayOfMonth(1);

        LocalDate current = from;

        while (!current.isEqual(to)) {
            LocalDate nextDay = current.plusDays(1);
            java.sql.Date dayStart = java.sql.Date.valueOf(current);
            java.sql.Date dayEnd = java.sql.Date.valueOf(nextDay);

            List<AmiRecord> amiRecords = amiRepo.getByPwrdateCustIdListAndChannel(dayStart, dayEnd, custIdList, channel);
            List<AmiRecord> latestRecords = filterLatestPerKey(amiRecords).stream()
                    .peek(r -> r.setPwrTime(Timestamp.from(r.getPwrTime().toInstant().minus(15, ChronoUnit.MINUTES))))
                    .toList();

            insertAmiStaging(reSettlement.getId(), latestRecords);
            current = nextDay;
        }
        log.info("== oout loadAmiStagingData. done. =" + reSettlement+ " , channel: " + channel);
    }

    private List<AmiRecord> filterLatestPerKey(List<AmiRecord> records) {
        return records.stream()
                .collect(Collectors.toMap(
                        r -> r.getMeterId() + "|" + r.getPwrTime() + "|" + r.getChannel(),
                        r -> r,
                        (r1, r2) -> r1.getUpdateTime().after(r2.getUpdateTime()) ? r1 : r2
                ))
                .values()
                .stream()
                .toList();
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertAmiStaging(Long reSettlementId, List<AmiRecord> records) {
        log.info("== oout insertAmiStaging. =" + reSettlementId );
        if (records.isEmpty()) return;

        final int batchSize = 1000;
        for (int i = 0; i < records.size(); i += batchSize) {
            List<AmiRecord> batch = records.subList(i, Math.min(i + batchSize, records.size()));

            String sql = """
                INSERT INTO RE_AMI_STAGING
                (pwrDate, pwrTime, meterId, updateTime, custId, channel, kw, ratio, RE_SETTLEMENT_ID)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                public void setValues(PreparedStatement ps, int j) throws SQLException {
                    AmiRecord r = batch.get(j);
                    ps.setDate(1, new java.sql.Date(r.getPwrDate().getTime()));
                    ps.setTimestamp(2, r.getPwrTime());
                    ps.setString(3, r.getMeterId());
                    ps.setTimestamp(4, new java.sql.Timestamp(r.getUpdateTime().getTime()));
                    ps.setString(5, r.getCustId());
                    ps.setInt(6, r.getChannel());
                    ps.setBigDecimal(7, r.getKw());
                    ps.setBigDecimal(8, r.getRatio());
                    ps.setLong(9, reSettlementId);
                }

                public int getBatchSize() {
                    return batch.size();
                }
            });
        }
        log.info("== oout insertAmiStaging. done. =" + reSettlementId );
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteAmiStagingData(Long reSettlementId, int channel) {
        jdbcTemplate.update("DELETE FROM RE_AMI_STAGING WHERE RE_SETTLEMENT_ID = ? AND CHANNEL = ?", reSettlementId, channel);
    }


    @Transactional
    public void insertReAmiGeneratorDetail_bak(Long reSettlementId) {
        String sql = """
           delete from RE_AMI_GENERATOR_DETAIL where RE_SETTLEMENT_ID = :reSettlementId ;
           INSERT INTO RE_AMI_GENERATOR_DETAIL (RE_SETTLEMENT_ID, DATATIME, GENERATOR_ID, ENERGY_CHARGE_SECTION_ID, KWH)
           SELECT
               ami.RE_SETTLEMENT_ID,
               pwrTime,
               le.id,
               v.ENERGY_CHARGE_SECTION_ID,
               SUM(kw * ratio) AS SUM_KWH
           FROM RE_AMI_STAGING ami
           JOIN GENERATOR_entity le ON le.NBS_CUSTOMER_NUMBER = ami.custId
           JOIN re_GENERATOR rl ON rl.GENERATOR_id = le.ID AND rl.status = 1
           OUTER APPLY (
               SELECT TOP 1 v.*
               FROM VIEW_ENERGY_SECTION v
               WHERE v.time_slot = 3
                 AND CONVERT(TIME, ami.pwrTime) = v.time
                 AND v.voltage_id in ('hv','uhv')
                 AND ((DATEPART(WEEKDAY, ami.pwrTime) + 5) % 7) + 1 = v.day
                 AND is_summer = CASE WHEN CONVERT(DATE, ami.pwrTime) BETWEEN v.SUMMER_START AND v.SUMMER_END THEN 1 ELSE 0 END
               ORDER BY v.[from] DESC
           ) v
           WHERE ami.channel = 3 
             AND ami.RE_SETTLEMENT_ID = :reSettlementId
             AND v.ENERGY_CHARGE_SECTION_ID IS NOT NULL
           GROUP BY ami.RE_SETTLEMENT_ID, pwrtime, le.id, v.ENERGY_CHARGE_SECTION_ID
        """;
        entityManager.createNativeQuery(sql)
                .setParameter("reSettlementId", reSettlementId)
                .executeUpdate();
    }


    @Transactional
    public void insertReAmiGeneratorDetail(Long reSettlementId) {
        log.info("== oout insertReAmiGeneratorDetail. =" + reSettlementId );
        // todo : got data @ssms , but not in code
        String sql = """
            delete from RE_AMI_GENERATOR_DETAIL where RE_SETTLEMENT_ID = :reSettlementId ;
            WITH AmiGrouped AS (
                SELECT
                    RE_SETTLEMENT_ID,
                    custId,
                    pwrTime,
                    SUM(kw * ratio) AS kwh
                FROM RE_AMI_STAGING
                WHERE channel = 3
                  AND RE_SETTLEMENT_ID = :reSettlementId
                GROUP BY RE_SETTLEMENT_ID, custId, pwrTime
            ),
            WithGenerator AS (
                SELECT
                    ag.*,
                    le.id AS GENERATOR_ID,
                    CONVERT(TIME, ag.pwrTime) AS time_only,
                    CONVERT(DATE, ag.pwrTime) AS date_only,
                    (((DATEPART(WEEKDAY, ag.pwrTime) + 5) % 7) + 1) AS weekday_transformed
                FROM AmiGrouped ag
                JOIN GENERATOR_entity le ON le.NBS_CUSTOMER_NUMBER = ag.custId
                JOIN re_GENERATOR rl ON rl.GENERATOR_id = le.ID AND rl.status = 1
            ),
            Final AS (
                SELECT
                    wg.RE_SETTLEMENT_ID,
                    wg.pwrTime,
                    wg.GENERATOR_ID,
                    v.ENERGY_CHARGE_SECTION_ID,
                    wg.kwh
                FROM WithGenerator wg
                OUTER APPLY (
                    SELECT TOP 1 v.ENERGY_CHARGE_SECTION_ID
                    FROM VIEW_ENERGY_SECTION v
                    WHERE v.time_slot = 3
                      AND v.time = wg.time_only
                      AND v.day = wg.weekday_transformed
                      AND v.voltage_id IN ('hv', 'uhv')
                      AND (
                        (wg.date_only BETWEEN v.SUMMER_START AND v.SUMMER_END AND v.is_summer = 1)
                        OR (wg.date_only NOT BETWEEN v.SUMMER_START AND v.SUMMER_END AND v.is_summer = 0)
                      )
                    ORDER BY v.[from] DESC
                ) v
            )
            INSERT INTO RE_AMI_GENERATOR_DETAIL (
                RE_SETTLEMENT_ID, DATATIME, GENERATOR_ID, ENERGY_CHARGE_SECTION_ID, KWH
            )
            SELECT
                RE_SETTLEMENT_ID,
                pwrTime,
                GENERATOR_ID,
                ENERGY_CHARGE_SECTION_ID,
                kwh
            FROM Final
            WHERE ENERGY_CHARGE_SECTION_ID IS NOT NULL;

        """;

        entityManager.createNativeQuery(sql)
                .setParameter("reSettlementId", reSettlementId)
                .executeUpdate();
    }


    @Transactional
    public void insertReAmiLoadDetail_bak(Long reSettlementId) {
        log.info("== oout insertReAmiLoadDetail_bak. =" + reSettlementId );
        String sql = """
              delete from RE_AMI_LOAD_DETAIL where RE_SETTLEMENT_ID = :reSettlementId ;
              INSERT INTO RE_AMI_LOAD_DETAIL (RE_SETTLEMENT_ID, DATATIME, LOAD_ID, ENERGY_CHARGE_SECTION_ID, KWH)
                SELECT
                    ami.RE_SETTLEMENT_ID,
                    pwrTime,
                    le.id,
                    v.ENERGY_CHARGE_SECTION_ID,
                    SUM(kw * ratio) AS SUM_KWH 
                FROM RE_AMI_STAGING ami
                JOIN load_entity le ON le.NBS_CUSTOMER_NUMBER = ami.custId
                JOIN voltage_level vl ON vl.id = le.RESPONSIBILITY_VOLTAGE
                JOIN CONTRACT_STG cs ON cs.CONTRACT_STG = le.CONTRACT_STG
                JOIN re_load rl ON rl.load_id = le.ID AND rl.status = 1
                CROSS APPLY (
                    SELECT TOP 1
                        CASE
                            WHEN CONVERT(DATE, ami.pwrTime) BETWEEN vd.SUMMER_START AND vd.SUMMER_END THEN 1
                            ELSE 0
                        END AS IS_SUMMER
                    FROM VIEW_ENERGY_SECTION vd
                    WHERE vd.time_slot = 3
                      AND vd.CONTRACT_ID = cs.ID
                      AND vd.VOLTAGE_ID = vl.voltage_classifications_id
                      AND CONVERT(TIME, ami.pwrTime) = vd.time
                      AND ((DATEPART(WEEKDAY, ami.pwrTime) + 5) % 7) + 1 = vd.day
                ) summer
                OUTER APPLY (
                    SELECT TOP 1 v.*
                    FROM VIEW_ENERGY_SECTION v
                    WHERE v.time_slot = 3
                      AND v.ENERGY_CHARGE_TABLE_ID = CASE WHEN vl.label = 'lv' THEN 4 ELSE 5 END
                      AND CONVERT(TIME, ami.pwrTime) = v.time
                      AND ((DATEPART(WEEKDAY, ami.pwrTime) + 5) % 7) + 1 = v.day
                      AND v.is_summer = summer.IS_SUMMER
                    ORDER BY v.[from] DESC
                ) v
                WHERE ami.channel = 1
                  AND ami.RE_SETTLEMENT_ID = :reSettlementId
                  AND v.ENERGY_CHARGE_SECTION_ID IS NOT NULL
                GROUP BY ami.RE_SETTLEMENT_ID, pwrtime, le.id, v.ENERGY_CHARGE_SECTION_ID;

        """;

        entityManager.createNativeQuery(sql)
                .setParameter("reSettlementId", reSettlementId)
                .executeUpdate();
    }


    @Transactional
    public void insertReAmiLoadDetail(Long reSettlementId) {
        log.info("== oout insertReAmiLoadDetail. =" + reSettlementId );
        String sql = """
              delete from RE_AMI_LOAD_DETAIL where RE_SETTLEMENT_ID = :reSettlementId ;
               -- ami data
                WITH AmiPrepared AS (
                    SELECT
                        ami.*,
                        CONVERT(TIME, ami.pwrTime) AS time_only,
                        CONVERT(DATE, ami.pwrTime) AS date_only,
                        (((DATEPART(WEEKDAY, ami.pwrTime) + 5) % 7) + 1) AS weekday_transformed
                    FROM RE_AMI_STAGING ami
                    WHERE ami.channel = 1
                      AND ami.RE_SETTLEMENT_ID = :reSettlementId
                ),
                -- IS_SUMMER flag 
                SummerFlag AS (
                    SELECT
                        ap.*,
                        CASE
                            WHEN vd.SUMMER_START IS NOT NULL
                             AND vd.SUMMER_END IS NOT NULL
                             AND ap.date_only BETWEEN vd.SUMMER_START AND vd.SUMMER_END
                            THEN 1 ELSE 0
                        END AS IS_SUMMER
                    FROM AmiPrepared ap
                    JOIN load_entity le ON le.NBS_CUSTOMER_NUMBER = ap.custId
                    JOIN voltage_level vl ON vl.id = le.RESPONSIBILITY_VOLTAGE
                    JOIN CONTRACT_STG cs ON cs.CONTRACT_STG = le.CONTRACT_STG
                    JOIN re_load rl ON rl.load_id = le.ID AND rl.status = 1
                    LEFT JOIN VIEW_ENERGY_SECTION vd
                        ON vd.time_slot = 3
                       AND vd.CONTRACT_ID = cs.ID
                       AND vd.VOLTAGE_ID = vl.voltage_classifications_id
                       AND vd.time = ap.time_only
                       AND vd.day = ap.weekday_transformed
                ),
                
                --  is_summer ->ENERGY_CHARGE_SECTION_ID based on is_summer
                MatchedSection AS (
                    SELECT
                        sf.RE_SETTLEMENT_ID,
                        sf.pwrTime,
                        le.id AS LOAD_ENTITY_ID,
                        v.ENERGY_CHARGE_SECTION_ID,
                        sf.kw,
                        sf.ratio
                    FROM SummerFlag sf
                    JOIN load_entity le ON le.NBS_CUSTOMER_NUMBER = sf.custId
                    JOIN voltage_level vl ON vl.id = le.RESPONSIBILITY_VOLTAGE
                    JOIN CONTRACT_STG cs ON cs.CONTRACT_STG = le.CONTRACT_STG
                    JOIN re_load rl ON rl.load_id = le.ID AND rl.status = 1
                    OUTER APPLY (
                        SELECT TOP 1 v.ENERGY_CHARGE_SECTION_ID
                        FROM VIEW_ENERGY_SECTION v
                        WHERE v.time_slot = 3
                          AND v.ENERGY_CHARGE_TABLE_ID = CASE WHEN vl.label = 'lv' THEN 4 ELSE 5 END
                          AND v.time = sf.time_only
                          AND v.day = sf.weekday_transformed
                          AND v.is_summer = sf.IS_SUMMER
                        ORDER BY v.[from] DESC
                    ) v
                    WHERE v.ENERGY_CHARGE_SECTION_ID IS NOT NULL
                )
                INSERT INTO RE_AMI_LOAD_DETAIL  (RE_SETTLEMENT_ID, DATATIME, LOAD_ID, ENERGY_CHARGE_SECTION_ID, KWH)
                SELECT
                    RE_SETTLEMENT_ID,
                    pwrTime,
                    LOAD_ENTITY_ID,
                    ENERGY_CHARGE_SECTION_ID,
                    SUM(kw * ratio) AS SUM_KWH
                FROM MatchedSection
                GROUP BY RE_SETTLEMENT_ID, pwrTime, LOAD_ENTITY_ID, ENERGY_CHARGE_SECTION_ID;
   
                """;

        entityManager.createNativeQuery(sql)
                .setParameter("reSettlementId", reSettlementId)
                .executeUpdate();
    }

}
