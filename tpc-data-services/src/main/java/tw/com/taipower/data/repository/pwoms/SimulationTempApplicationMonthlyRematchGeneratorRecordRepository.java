package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyRematchGeneratorRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyRematchGeneratorRecord;


/**
 * Repository of ApplicationMonthlyGeneratorRecord
 *
 * @class: ApplicationMonthlyGeneratorRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationMonthlyRematchGeneratorRecordRepository extends JpaRepository<SimulationTempApplicationMonthlyRematchGeneratorRecord, ApplicationMonthlyGeneratorRecordColumnId> {

}