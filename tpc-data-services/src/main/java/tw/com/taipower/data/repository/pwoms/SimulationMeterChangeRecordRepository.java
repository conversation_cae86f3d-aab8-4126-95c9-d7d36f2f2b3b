package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.MeterChangeRecord;
import tw.com.taipower.data.entity.pwoms.MeterChangeRecordId;
import tw.com.taipower.data.entity.pwoms.SimulationMeterChangeRecord;

import java.util.List;

/**
 * Repository of ParallelCapacityCode
 *
 * @class: ParallelCapacityCodeRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-08 10:34
 * @see:
 **/

public interface SimulationMeterChangeRecordRepository extends JpaRepository<SimulationMeterChangeRecord, MeterChangeRecordId> {

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_METER_CHANGE_RECORD(     " +
            "        SETTLEMENT_ID     " +
            "        , NBS_CUSTOMER_NUMBER     " +
            "        , METER_NO     " +
            "        , APPLICATION_LOAD_ID     " +
            "        , APPLICATION_GENERATOR_ID     " +
            "        , USE_FROM     " +
            "        , USE_TO     " +
            "    )     " +
            "    SELECT DISTINCT :settlementId     " +
            "                  , TDMCS.NBS_CUSTOMER_NUMBER     " +
            "                  , TDMCS.METER_NO     " +
            "                  , null     " +
            "                  , TDAGL.APPLICATION_GENERATOR_ID     " +
            "                  , VAGM.USE_FROM     " +
            "                  , VAGM.USE_TO     " +
            "    FROM (     " +
            "             SELECT DATE, GENERATOR_METER_ID, COUNT(GENERATOR_METER_ID) METER_COUNT,  NBS_CUSTOMER_NUMBER FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT     " +
            "             WHERE SETTLEMENT_ID = :settlementId     " +
            "               AND GENERATOR_METER_ID IS NOT NULL     " +
            "               AND COMPUTABLE = 1     " +
            "             GROUP BY GENERATOR_METER_ID, NBS_CUSTOMER_NUMBER, DATE) INNER_SQL     " +
            "             INNER JOIN SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT TDMCS     " +
            "                        ON TDMCS.GENERATOR_METER_ID = INNER_SQL.GENERATOR_METER_ID     " +
            "                            AND TDMCS.DATE = INNER_SQL.DATE     " +
            "                            AND TDMCS.SETTLEMENT_ID = :settlementId     " +
            "             INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_METER TDAM     " +
            "                        ON TDMCS.ID = TDAM.DATE_METER_ID     " +
            "             INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD TDAGL     " +
            "                        ON TDAM.DATE_APPLICATION_ID = TDAGL.ID     " +
            "             INNER JOIN VIEW_APPLICATION_GENERATOR_METER VAGM     " +
            "                        ON VAGM.APPLICATION_GENERATOR_ID = TDAGL.APPLICATION_GENERATOR_ID     " +
            "                            AND VAGM.METER_NO = TDMCS.METER_NO     " +
            "    WHERE METER_COUNT > 1     " +
            "     " +
            "     " +
            "     " +
            "    INSERT INTO SIMULATION_METER_CHANGE_RECORD(     " +
            "                                     SETTLEMENT_ID     " +
            "                                   , NBS_CUSTOMER_NUMBER     " +
            "                                   , METER_NO     " +
            "                                   , APPLICATION_LOAD_ID     " +
            "                                   , APPLICATION_GENERATOR_ID     " +
            "                                   , USE_FROM     " +
            "                                   , USE_TO     " +
            "    )     " +
            "    SELECT DISTINCT :settlementId     " +
            "                  , TDMCS.NBS_CUSTOMER_NUMBER     " +
            "                  , TDMCS.METER_NO     " +
            "                  , TDAGL.APPLICATION_LOAD_ID     " +
            "                  , null     " +
            "                  , VALM.USE_FROM     " +
            "                  , VALM.USE_TO     " +
            "    FROM (     " +
            "             SELECT DATE, LOAD_METER_ID, COUNT(LOAD_METER_ID) METER_COUNT,  NBS_CUSTOMER_NUMBER FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT     " +
            "             WHERE SETTLEMENT_ID = :settlementId     " +
            "               AND LOAD_METER_ID IS NOT NULL     " +
            "               AND COMPUTABLE = 1     " +
            "             GROUP BY LOAD_METER_ID, NBS_CUSTOMER_NUMBER, DATE) INNER_SQL     " +
            "             INNER JOIN SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT TDMCS     " +
            "                        ON TDMCS.LOAD_METER_ID = INNER_SQL.LOAD_METER_ID     " +
            "                            AND TDMCS.DATE = INNER_SQL.DATE     " +
            "                            AND TDMCS.SETTLEMENT_ID = :settlementId     " +
            "             INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_METER TDAM     " +
            "                        ON TDMCS.ID = TDAM.DATE_METER_ID     " +
            "             INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD TDAGL     " +
            "                        ON TDAM.DATE_APPLICATION_ID = TDAGL.ID     " +
            "             INNER JOIN VIEW_APPLICATION_LOAD_METER VALM     " +
            "                        ON VALM.APPLICATION_LOAD_ID = TDAGL.APPLICATION_LOAD_ID     " +
            "                            AND VALM.METER_NO = TDMCS.METER_NO     " +
            "    WHERE METER_COUNT > 1" , nativeQuery = true)
    void saveBySettlementId(@Param("settlementId") Long settlementId);

    @Query(value = "SELECT MCR.* FROM SIMULATION_METER_CHANGE_RECORD MCR     " +
            "       INNER JOIN SIMULATION_SETTLEMENT_CALCULATION SC     " +
            "         ON SC.SETTLEMENT_ID = MCR.SETTLEMENT_ID     " +
            "    WHERE MCR.SETTLEMENT_ID = :settlementId     " +
            "        AND DATEDIFF(year, SC.SERVICE_DATE, MCR.USE_FROM) = 0     " +
            "        AND DATEDIFF(month, SC.SERVICE_DATE, MCR.USE_FROM) = 0     " +
            "        AND APPLICATION_GENERATOR_ID IS NOT NULL" +
            "      ORDER BY USE_FROM ASC ", nativeQuery = true)
    List<SimulationMeterChangeRecord> findBySettlementIdAndApplicationGeneratorIdIsNotNullAndUseFrom(@Param("settlementId") Long settlementId);

    @Query(value = "SELECT MCR.* FROM SIMULATION_METER_CHANGE_RECORD MCR     " +
            "    INNER JOIN SIMULATION_SETTLEMENT_CALCULATION SC     " +
            "        ON SC.SETTLEMENT_ID = MCR.SETTLEMENT_ID     " +
            "    WHERE MCR.SETTLEMENT_ID = :settlementId     " +
            "      AND DATEDIFF(year, SC.SERVICE_DATE, MCR.USE_FROM) = 0     " +
            "      AND DATEDIFF(month, SC.SERVICE_DATE, MCR.USE_FROM) = 0     " +
            "      AND APPLICATION_LOAD_ID IS NOT NULL" +
            "      ORDER BY USE_FROM ASC ", nativeQuery = true)
    List<SimulationMeterChangeRecord> findBySettlementIdAndApplicationLoadIdIsNotNullAndUseFrom(@Param("settlementId") Long settlementId);


    @Query(value = "SELECT MCR.* FROM SIMULATION_METER_CHANGE_RECORD MCR     " +
            "       INNER JOIN SIMULATION_SETTLEMENT_CALCULATION SC     " +
            "         ON SC.SETTLEMENT_ID = MCR.SETTLEMENT_ID     " +
            "    WHERE MCR.SETTLEMENT_ID = :settlementId     " +
            "        AND DATEDIFF(year, SC.SERVICE_DATE, MCR.USE_TO) = 0     " +
            "        AND DATEDIFF(month, SC.SERVICE_DATE, MCR.USE_TO) = 0     " +
            "        AND APPLICATION_GENERATOR_ID IS NOT NULL" +
            "      ORDER BY USE_FROM ASC ", nativeQuery = true)
    List<SimulationMeterChangeRecord> findBySettlementIdAndApplicationGeneratorIdIsNotNullAndUseTo(@Param("settlementId") Long settlementId);

    @Query(value = "SELECT MCR.* FROM SIMULATION_METER_CHANGE_RECORD MCR     " +
            "    INNER JOIN SIMULATION_SETTLEMENT_CALCULATION SC     " +
            "        ON SC.SETTLEMENT_ID = MCR.SETTLEMENT_ID     " +
            "    WHERE MCR.SETTLEMENT_ID = :settlementId     " +
            "      AND DATEDIFF(year, SC.SERVICE_DATE, MCR.USE_TO) = 0     " +
            "      AND DATEDIFF(month, SC.SERVICE_DATE, MCR.USE_TO) = 0     " +
            "      AND APPLICATION_LOAD_ID IS NOT NULL" +
            "      ORDER BY USE_FROM ASC ", nativeQuery = true)
    List<SimulationMeterChangeRecord> findBySettlementIdAndApplicationLoadIdIsNotNullAndUseTo(@Param("settlementId") Long settlementId);
}
