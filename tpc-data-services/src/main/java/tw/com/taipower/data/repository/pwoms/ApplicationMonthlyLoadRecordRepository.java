package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyLoadRecord;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyLoadRecordColumnId;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static tw.com.taipower.data.constant.Constants.*;


/**
 * Repository of ApplicationMonthlyLoadRecord
 *
 * @class: Application Monthly LoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-30 17:27
 * @see:
 **/

public interface ApplicationMonthlyLoadRecordRepository extends JpaRepository<ApplicationMonthlyLoadRecord, ApplicationMonthlyLoadRecordColumnId> {

    /***
     * unmatched_cn 計算剩餘量
     * sql
     *   SELECT APPLICATION_LOAD_ID, SUM(UNMATCHED_CN) AS SUM_UNMATCHED_CN, SUM(MATCHED_CN) AS SUM_MATCHED_CN
     *    FROM [pwoms].[dbo].[APPLICATION_MONTHLY_LOAD_RECORD]
     *    WHERE DATE BETWEEN '2024-05-01' and '2024-05-03'
     *    GROUP BY APPLICATION_LOAD_ID
     *
     * @author:  ting
     * @date:    2024/06/23 12:54:12
     * @param:   [startTime, endTime]
     * @return:  java.util.List<?>
     **/

    @Query("  SELECT new map( loadId AS " + FIELD_NAME_APPLICATION_GENERATOR_LOAD_ID +
            ", SUM(unmatchedCn) AS " + FIELD_NAME_SUM_UNMATCHED_CAPCAITY +
            ", SUM(matchedCn) AS " + FIELD_NAME_SUM_MATCHED_CAPACITY + ")" +
            "   FROM ApplicationMonthlyLoadRecord" +
            "   WHERE date BETWEEN (?1) and (?2)" +
            "   GROUP BY loadId")
    List<?> sumByDateGroupByApplicationLoadId(Date startTime, Date endTime);

    /***
     *  sql
     *  SELECT APPLICATION_LOAD_ID, ENERGY_CHARGE_SECTION_ID, SUM(UNMATCHED_CN) AS SUM_UNMATCHED_CN, SUM(MATCHED_CN) AS SUM_MATCHED_CN
     *    FROM [pwoms].[dbo].[APPLICATION_MONTHLY_LOAD_RECORD]
     *    WHERE DATE BETWEEN '2024-05-01' and '2024-05-03'
     *    GROUP BY APPLICATION_LOAD_ID, ENERGY_CHARGE_SECTION_ID
     *    ORDER BY APPLICATION_LOAD_ID ASC
     * @author:  ting
     * @date:    2024/06/23 13:45:11
     * @param:   [startTime, endTime]
     * @return:  java.util.List<?>
     **/

    @Query("   SELECT new map(loadId AS " + FIELD_NAME_APPLICATION_GENERATOR_LOAD_ID +
            ", energyChargeSectionId " + FIELD_NAME_ENERGY_CHARGE_SECTION_ID +
            ", SUM(unmatchedCn) AS " + FIELD_NAME_SUM_UNMATCHED_CAPCAITY +
            ", SUM(matchedCn) AS " + FIELD_NAME_SUM_MATCHED_CAPACITY + ")" +
            "   FROM ApplicationMonthlyLoadRecord" +
            "   WHERE date BETWEEN (?1) and (?2)" +
            "   GROUP BY loadId, energyChargeSectionId " +
            "   ORDER BY loadId ASC")
    List<?> sumByDateGroupByApplicationLoadIdAndEnergyChargeSectionId(Date startTime, Date endTime);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_MONTHLY_LOAD_RECORD WHERE DATE = ?1", nativeQuery = true)
    void deleteByDate(Date date);

    /**
     * DELETE FROM APPLICATION_MONTHLY_LOAD_RECORD
     * WHERE APPLICATION_LOAD_ID IN
     *       (SELECT APPLICATION_LOAD_ID FROM APPLICATION_MONTHLY_LOAD_RECORD AS AMLR
     *                                            INNER JOIN  APPLICATION_LOAD AS AL
     *                                                        ON AMLR.APPLICATION_LOAD_ID = AL.ID
     *                                            INNER JOIN APPLICATION AS A
     *                                                       ON AL.APPLICATION_ID = A.ID
     *        WHERE TYPE IN ('1', '2', '3')
     *          AND AMGLR.DATE = '2024-06-01')
     *
     * @author:  ting
     * @date:    2024/09/17 15:50:58
     * @param:   [date, appTypeList]
     * @return:  void
     **/
    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_MONTHLY_LOAD_RECORD   " +
            "WHERE APPLICATION_LOAD_ID IN   " +
            "      (SELECT APPLICATION_LOAD_ID FROM APPLICATION_MONTHLY_LOAD_RECORD AS AMLR   " +
            "        INNER JOIN  APPLICATION_LOAD AS AL   " +
            "                    ON AMLR.APPLICATION_LOAD_ID = AL.ID   " +
            "        INNER JOIN APPLICATION AS A   " +
            "                   ON AL.APPLICATION_ID = A.ID   " +
            "       WHERE TYPE IN (:appTypeList)   " +
            "         AND AMLR.DATE = :date)", nativeQuery = true)
    void deleteByDateAndApplicationTypeIn(@Param("date") Date date, @Param("appTypeList") List<String> appTypeList);

    /**
     * 年剩餘量
     *
     * @author:  ting
     * @date:    2024/10/19 10:21:16
     * @param:   [startDate, endDate, appLoadId, contractNo, annualCap]
     * @return:  java.math.BigDecimal
     **/
    @Query(value = "SELECT (:annualCap - SUM(AMGLR.ADJUSTED_MATCHED_RM)) AS RESIDUAL_CN           " +
            "    FROM APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD AS AMGLR           " +
            "    LEFT JOIN           " +
            "        (SELECT APPLICATION_LOAD_ID, MAX(SETTLEMENT_ID) AS SETTLEMENT_ID, SERVICE_DATE FROM (           " +
            "                SELECT APPLICATION_LOAD_ID, SETTLEMENT_ID, SERVICE_DATE FROM VIEW_BILL_SETTLEMENT_APPLICATION VBSA           " +
            "                  INNER JOIN  " +
            "                    (SELECT APPLICATION_ID, ID AS APPLICATION_LOAD_ID  " +
            "                        FROM APPLICATION_LOAD AL   " +
            "                       WHERE AL.ID = :appLoadId) AS SUBQUERY  " +
            "                ON SUBQUERY.APPLICATION_ID = VBSA.APPLICATION_ID           " +
            "            WHERE VBSA.ERP_CANCELLATION_DATE IS NULL           " +
            "                AND SERVICE_DATE BETWEEN :startDate AND :endDate) SETTLEMENT_SQL           " +
            "        GROUP BY APPLICATION_LOAD_ID, SERVICE_DATE) INNER_SQL           " +
            "    ON AMGLR.SETTLEMENT_ID = INNER_SQL.SETTLEMENT_ID           " +
            "    WHERE AMGLR.DATE BETWEEN :startDate AND :endDate           " +
            "      AND AMGLR.APPLICATION_LOAD_ID = INNER_SQL.APPLICATION_LOAD_ID", nativeQuery = true)
    BigDecimal findAnnualContractResidualCapacity(@Param("startDate")Date startDate, @Param("endDate")Date endDate, @Param("appLoadId") Long appLoadId, @Param("annualCap") BigDecimal annualCap);
    
    @Query(value = "SELECT AMGLR.APPLICATION_LOAD_ID,  (ANNUAL_CONTRACT_CAP - SUM(AMGLR.ADJUSTED_MATCHED_RM)) AS RESIDUAL_CN           " +
            "    FROM APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD AS AMGLR           " +
            "    LEFT JOIN           " +
            "        (SELECT APPLICATION_LOAD_ID, MAX(SETTLEMENT_ID) AS SETTLEMENT_ID, SERVICE_DATE, ANNUAL_CONTRACT_CAP FROM (           " +
            "         SELECT SETTLEMENT_ID, AL.ID AS APPLICATION_LOAD_ID, SERVICE_DATE, AL.ANNUAL_CONTRACT_CAP FROM VIEW_BILL_SETTLEMENT_APPLICATION VBSA           " +
            "                  INNER JOIN APPLICATION_LOAD AL           " +
            "                             ON AL.ID = VBSA.APPLICATION_ID           " +
            "         WHERE AL.ID IN (:appLoadIdList)           " +
            "           AND VBSA.ERP_CANCELLATION_DATE IS NULL           " +
            "           AND SERVICE_DATE BETWEEN :startDate AND :endDate) SETTLEMENT_SQL           " +
            "        GROUP BY APPLICATION_LOAD_ID, SERVICE_DATE, ANNUAL_CONTRACT_CAP) INNER_SQL           " +
            "    ON AMGLR.SETTLEMENT_ID = INNER_SQL.SETTLEMENT_ID           " +
            "WHERE AMGLR.DATE BETWEEN :startDate AND :endDate           " +
            "  AND AMGLR.APPLICATION_LOAD_ID = INNER_SQL.APPLICATION_LOAD_ID           " +
            "    GROUP BY AMGLR.APPLICATION_LOAD_ID, ANNUAL_CONTRACT_CAP ", nativeQuery = true)
    List<Map<String, Object>> findAnnualContractResidualCapacity(@Param("startDate")Date startDate, @Param("endDate")Date endDate, @Param("appLoadIdList") List<Long> appLoadIdList);

    @Query(value = "SELECT (:annualCap - SUM(AMGLR.ADJUSTED_MATCHED_RM)) AS RESIDUAL_CN           " +
            "    FROM APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD AS AMGLR           " +
            "    LEFT JOIN           " +
            "        (SELECT APPLICATION_LOAD_ID, MAX(SETTLEMENT_ID) AS SETTLEMENT_ID, SERVICE_DATE FROM (           " +
            "                SELECT APPLICATION_LOAD_ID, SETTLEMENT_ID, SERVICE_DATE FROM VIEW_BILL_SETTLEMENT_APPLICATION VBSA           " +
            "                  INNER JOIN  " +
            "                    (SELECT APPLICATION_ID, ID AS APPLICATION_LOAD_ID  " +
            "                        FROM SIMULATION_APPLICATION_LOAD AL   " +
            "                       WHERE AL.ID = :appLoadId) AS SUBQUERY  " +
            "                ON SUBQUERY.APPLICATION_ID = VBSA.APPLICATION_ID           " +
            "            WHERE VBSA.ERP_CANCELLATION_DATE IS NULL           " +
            "                AND SERVICE_DATE BETWEEN :startDate AND :endDate) SETTLEMENT_SQL           " +
            "        GROUP BY APPLICATION_LOAD_ID, SERVICE_DATE) INNER_SQL           " +
            "    ON AMGLR.SETTLEMENT_ID = INNER_SQL.SETTLEMENT_ID           " +
            "    WHERE AMGLR.DATE BETWEEN :startDate AND :endDate           " +
            "      AND AMGLR.APPLICATION_LOAD_ID = INNER_SQL.APPLICATION_LOAD_ID", nativeQuery = true)
    BigDecimal findAnnualContractResidualCapacityForSimulation(@Param("startDate")Date startDate, @Param("endDate")Date endDate, @Param("appLoadId") Long appLoadId, @Param("annualCap") BigDecimal annualCap);

    @Query(value = "SELECT AMGLR.APPLICATION_LOAD_ID,  (ANNUAL_CONTRACT_CAP - SUM(AMGLR.ADJUSTED_MATCHED_RM)) AS RESIDUAL_CN           " +
            "    FROM APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD AS AMGLR           " +
            "    LEFT JOIN           " +
            "        (SELECT APPLICATION_LOAD_ID, MAX(SETTLEMENT_ID) AS SETTLEMENT_ID, SERVICE_DATE, ANNUAL_CONTRACT_CAP FROM (           " +
            "         SELECT SETTLEMENT_ID, AL.ID AS APPLICATION_LOAD_ID, SERVICE_DATE, AL.ANNUAL_CONTRACT_CAP FROM VIEW_BILL_SETTLEMENT_APPLICATION VBSA           " +
            "                  INNER JOIN SIMULATION_APPLICATION_LOAD AL           " +
            "                             ON AL.ID = VBSA.APPLICATION_ID           " +
            "         WHERE AL.ID IN (:appLoadIdList)           " +
            "           AND VBSA.ERP_CANCELLATION_DATE IS NULL           " +
            "           AND SERVICE_DATE BETWEEN :startDate AND :endDate) SETTLEMENT_SQL           " +
            "        GROUP BY APPLICATION_LOAD_ID, SERVICE_DATE, ANNUAL_CONTRACT_CAP) INNER_SQL           " +
            "    ON AMGLR.SETTLEMENT_ID = INNER_SQL.SETTLEMENT_ID           " +
            "WHERE AMGLR.DATE BETWEEN :startDate AND :endDate           " +
            "  AND AMGLR.APPLICATION_LOAD_ID = INNER_SQL.APPLICATION_LOAD_ID           " +
            "    GROUP BY AMGLR.APPLICATION_LOAD_ID, ANNUAL_CONTRACT_CAP ", nativeQuery = true)
    List<Map<String, Object>> findAnnualContractResidualCapacityForSimulation(@Param("startDate")Date startDate, @Param("endDate")Date endDate, @Param("appLoadIdList") List<Long> appLoadIdList);

}