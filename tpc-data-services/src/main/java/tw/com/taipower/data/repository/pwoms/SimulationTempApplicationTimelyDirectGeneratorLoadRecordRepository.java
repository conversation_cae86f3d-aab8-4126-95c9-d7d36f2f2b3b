package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationTimelyGeneratorLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationTimelyDirectGeneratorLoadRecord;

import java.util.Date;
import java.util.List;

/**
 * Repository of MonthlyApplicationTimelyLoadRecord
 *
 * @class: MonthlyApplicationTimelyLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationTimelyDirectGeneratorLoadRecordRepository extends JpaRepository<SimulationTempApplicationTimelyDirectGeneratorLoadRecord, ApplicationTimelyGeneratorLoadRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "END ", nativeQuery = true)
    void deleteAllRecordBySettlementId(@Param("settlementId") Long settlementId);


}
