package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyTrialSettlement;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyTrialSettlement;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyTrialSettlementId;

import java.util.List;
import java.util.Map;


/**
 * Repository of TempApplicationMonthlyTrial
 *
 * @class: TempApplicationMonthlyTrialSettlementRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationMonthlyTrialSettlementRepository extends JpaRepository<SimulationTempApplicationMonthlyTrialSettlement, TempApplicationMonthlyTrialSettlementId> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN           " +
            "    INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT(           " +
            "           SETTLEMENT_ID           " +
            "         , APPLICATION_GENERATOR_ID           " +
            "         , APPLICATION_LOAD_ID           " +
            "         , CAPACITY_CODE           " +
            "         , CURRENT_KW           " +
            "         , TRIAL_KW           " +
            "         , VARIANCE_KW           " +
            "         , VARIANCE_KW_PERCENT           " +
            "         , CURRENT_COST           " +
            "         , TRAIL_COST           " +
            "         , VARIANCE_COST           " +
            "         , VARIANCE_COST_PERCENT           " +
            "    )           " +
            "    SELECT AFTER.SETTLEMENT_ID           " +
            "         , AFTER.APPLICATION_GENERATOR_ID           " +
            "         , AFTER.APPLICATION_LOAD_ID           " +
            "         , AFTER.CAPACITY_CODE           " +
            "         , null           " +
            "         , IIF(AFTER.ADJUSTED_MATCHED_KW IS NOT NULL, AFTER.ADJUSTED_MATCHED_KW, 0.0)           " +
            "         , null           " +
            "         , null           " +
            "         , null           " +
            "         , IIF(AFTER.ANCILLARY_SERVICE_COST IS NOT NULL, (AFTER.ANCILLARY_SERVICE_COST + AFTER.DISPATCH_SERVICE_COST + AFTER.POWER_DIST_COST + AFTER.POWER_TRANS_COST), 0.0)           " +
            "         , null           " +
            "         , null           " +
            "    FROM SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT AFTER           " +
            "        WHERE AFTER.SETTLEMENT_ID = :afterSettlementId           " +
            "          AND AFTER.APPLICATION_GENERATOR_ID IN (           " +
            "            SELECT DISTINCT APPLICATION_GENERATOR_ID           " +
            "            FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD           " +
            "            WHERE APPLICATION_ID IN (:appIdList))           " +
            " END", nativeQuery = true)
    void saveBySettlementIdAndAppIdIn(@Param("afterSettlementId") Long afterSettlementId, @Param("appIdList") List<Long> appIdList);

    @Transactional
    @Modifying
    @Query(value = "BEGIN           " +
            "    INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT(           " +
            "           SETTLEMENT_ID           " +
            "         , APPLICATION_GENERATOR_ID           " +
            "         , APPLICATION_LOAD_ID           " +
            "         , CAPACITY_CODE           " +
            "         , CURRENT_KW           " +
            "         , TRIAL_KW           " +
            "         , VARIANCE_KW           " +
            "         , VARIANCE_KW_PERCENT           " +
            "         , CURRENT_COST           " +
            "         , TRAIL_COST           " +
            "         , VARIANCE_COST           " +
            "         , VARIANCE_COST_PERCENT           " +
            "    )           " +
            "SELECT SETTLEMENT_ID           " +
            "     , APPLICATION_GENERATOR_ID           " +
            "     , APPLICATION_LOAD_ID           " +
            "     , CAPACITY_CODE           " +
            "     , CURRENT_KW           " +
            "     , TRIAL_KW           " +
            "     , TRIAL_KW - CURRENT_KW           " +
            "     , convert(decimal(18,2), (IIF(((0 != TRIAL_KW - CURRENT_KW) AND (0 < CURRENT_KW)), ((((TRIAL_KW - CURRENT_KW) * 1.0)/CURRENT_KW * 1.0) * 100.0),  IIF(0 = (TRIAL_KW - CURRENT_KW), 0, IIF(0 = CURRENT_KW, TRIAL_KW* 100.0, - CURRENT_KW* 100.0)))))           " +
            "     , CURRENT_COST           " +
            "     , TRIAL_COST           " +
            "     , TRIAL_COST - CURRENT_COST           " +
            "     , convert(decimal(18,2), (IIF((0 != (TRIAL_COST - CURRENT_COST) AND  (0 < CURRENT_COST)), ((((TRIAL_COST - CURRENT_COST) * 1.0) / (CURRENT_COST* 1.0)) * 100.0), IIF(0 = (TRIAL_COST - CURRENT_COST), 0, IIF(0 = CURRENT_COST, TRIAL_COST* 100.0, - CURRENT_COST* 100.0)))))           " +
            "FROM (           " +
            "SELECT AFTER.SETTLEMENT_ID           " +
            "     , AFTER.APPLICATION_GENERATOR_ID           " +
            "     , AFTER.APPLICATION_LOAD_ID           " +
            "     , AFTER.CAPACITY_CODE           " +
            "     , IIF(BEFORE.ADJUSTED_MATCHED_KW IS NOT NULL, BEFORE.ADJUSTED_MATCHED_KW, 0.0) AS CURRENT_KW           " +
            "     , IIF(AFTER.ADJUSTED_MATCHED_KW IS NOT NULL, AFTER.ADJUSTED_MATCHED_KW, 0.0) AS TRIAL_KW           " +
            "     , IIF(BEFORE.ANCILLARY_SERVICE_COST IS NOT NULL, (BEFORE.ANCILLARY_SERVICE_COST + BEFORE.DISPATCH_SERVICE_COST + BEFORE.POWER_DIST_COST + BEFORE.POWER_TRANS_COST) , 0.0) AS CURRENT_COST           " +
            "     , IIF(AFTER.ANCILLARY_SERVICE_COST IS NOT NULL, (AFTER.ANCILLARY_SERVICE_COST + AFTER.DISPATCH_SERVICE_COST + AFTER.POWER_DIST_COST + AFTER.POWER_TRANS_COST), 0.0) AS TRIAL_COST           " +
            "        FROM SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT AFTER           " +
            "    INNER JOIN APPLICATION_MONTHLY_CAPACITY_SETTLEMENT  BEFORE           " +
            "         ON BEFORE.SETTLEMENT_ID = :beforeSettlementId           " +
            "         AND AFTER.APPLICATION_LOAD_ID = BEFORE.APPLICATION_LOAD_ID           " +
            "         AND AFTER.APPLICATION_GENERATOR_ID = BEFORE.APPLICATION_GENERATOR_ID           " +
            "         AND AFTER.CAPACITY_CODE = BEFORE.CAPACITY_CODE           " +
            "    WHERE AFTER.SETTLEMENT_ID = :afterSettlementId           " +
            "        AND AFTER.APPLICATION_GENERATOR_ID IN (           " +
            "            SELECT DISTINCT APPLICATION_GENERATOR_ID           " +
            "               FROM DATE_APPLICATION_GENERATOR_LOAD           " +
            "               WHERE APPLICATION_ID IN (:appIdList))) AS SUBQUERY           " +
            "END ", nativeQuery = true)
    void saveBySettlementIdAndAppIdIn(@Param("beforeSettlementId") Long beforeSettlementId
            , @Param("afterSettlementId") Long afterSettlementId
            , @Param("appIdList") List<Long> appIdList);

    @Query(value = "  SELECT (A.CONTRACT_NO + '-' + VERSION) AS CONTRACT    " +
            "                , (YEAR(SERVICE_DATE) - 1911) AS YEAR    " +
            "                , (MONTH(SERVICE_DATE)) AS MONTH    " +
            "                , GE.NBS_CUSTOMER_NUMBER AS GENERATOR_CUSTOMER_NUM    " +
            "                , GE.NAME AS GENERATOR_CUSTOMER_NAME    " +
            "                , LE.NBS_CUSTOMER_NUMBER AS LOAD_CUSTOMER_NUM    " +
            "                , LE.NAME AS LOAD_CUSTOMER_NAME    " +
            "                , CURRENT_KW    " +
            "                , TRIAL_KW    " +
            "                , VARIANCE_KW    " +
            "                , VARIANCE_KW_PERCENT    " +
            "                , CURRENT_COST    " +
            "                , TRAIL_COST    " +
            "                , VARIANCE_COST    " +
            "                , VARIANCE_COST_PERCENT    " +
            "                FROM SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT TAMTS    " +
            "                INNER JOIN VIEW_APPLICATION_GENERATOR_METER VAGM    " +
            "                    ON VAGM.APPLICATION_GENERATOR_ID = TAMTS.APPLICATION_GENERATOR_ID    " +
            "                INNER JOIN dbo.SIMULATION_APPLICATION_GENERATOR AG    " +
            "                    ON AG.ID = TAMTS.APPLICATION_GENERATOR_ID    " +
            "                INNER JOIN dbo.SIMULATION_APPLICATION_LOAD AL    " +
            "                    ON AL.ID = TAMTS.APPLICATION_LOAD_ID    " +
            "                INNER JOIN SIMULATION_GENERATOR_ENTITY GE    " +
            "                    ON AG.GENERATOR_ID = GE.ID    " +
            "                INNER JOIN SIMULATION_LOAD_ENTITY  LE    " +
            "                    ON AL.LOAD_ID = LE.ID    " +
            "                INNER JOIN APPLICATION A    " +
            "                    ON A.ID = VAGM.APPLICATION_ID " +
            "                INNER JOIN SIMULATION_SETTLEMENT_CALCULATION SC" +
            "                    ON SC.SETTLEMENT_ID = TAMTS.SETTLEMENT_ID" +
            "            WHERE TAMTS.SETTLEMENT_ID = :settlementId " +
            "               GROUP BY CONTRACT_NO, VERSION, SERVICE_DATE, CURRENT_KW, TRIAL_KW, VARIANCE_KW, VARIANCE_KW_PERCENT, CURRENT_COST, TRAIL_COST, VARIANCE_COST, VARIANCE_COST_PERCENT, GE.NAME, GE.NBS_CUSTOMER_NUMBER, LE.NAME, LE.NBS_CUSTOMER_NUMBER    " +
            "           ", nativeQuery = true)
    List<Map<String, Object>>  findBySettlementId(@Param("settlementId") Long settlementId);


    Boolean existsBySettlementId(@Param("settlementId") Long settlementId);
}