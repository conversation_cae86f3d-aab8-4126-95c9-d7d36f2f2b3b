package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyCapacityRecord;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyCapacityRecordId;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of ApplicationMonthlyCapacityRecord
 *
 * @class: ApplicationMonthlyCapacityRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-17 11:02
 * @see:
 **/
public interface ApplicationMonthlyCapacityRecordRepository extends JpaRepository<ApplicationMonthlyCapacityRecord, ApplicationMonthlyCapacityRecordId> {

    String joinLoadEntityChildTheMonthSettleServiceDate = " Left outer join LOAD_ENTITY_METER_CHILD as lMeterChild on lMeter.ID = lMeterChild.LOAD_ENTITY_METER_ID" +
            "   and lMeterChild.USE_FROM != lMeterChild.USE_TO and MONTH(lMeterChild.USE_TO) = MONTH(vBill.SERVICE_DATE)" +
            "   and YEAR(lMeterChild.USE_TO) = YEAR(vBill.SERVICE_DATE)" +
            "  Left outer join METER_CHANGE_RECORD as mCR2 on lap.ID = mCR2.APPLICATION_LOAD_ID and mCR2.USE_FROM != mCR2.USE_TO" +
            "    and MONTH(mCR2.USE_TO) = MONTH(vBill.SERVICE_DATE) and YEAR(mCR2.USE_TO) = YEAR(vBill.SERVICE_DATE)" +
            "    and vBill.SETTLEMENT_ID = mCR2.SETTLEMENT_ID";
    String innerSelectSettleInfo = " inner join (Select ENERGY_CHARGE_SECTION_ID, APPLICATION_LOAD_ID, APPLICATION_GENERATOR_ID, MAX(FORMAT(CALCULATED_AT, 'yyyy-MM')) as calAt" +
            "    from APPLICATION_MONTHLY_CAPACITY_RECORD" +
            "       group by ENERGY_CHARGE_SECTION_ID, APPLICATION_LOAD_ID, APPLICATION_GENERATOR_ID) cc" +
            "     on settle.APPLICATION_LOAD_ID=cc.APPLICATION_LOAD_ID and settle.APPLICATION_GENERATOR_ID=cc.APPLICATION_GENERATOR_ID" +
            "       and settle.ENERGY_CHARGE_SECTION_ID=cc.ENERGY_CHARGE_SECTION_ID and FORMAT(settle.CALCULATED_AT, 'yyyy-MM')=cc.calAt";
    String joinLoadEntityChildNextMonthSettleServiceDate = " Left outer join LOAD_ENTITY_METER_CHILD as lC2 on lMeter.ID = lC2.LOAD_ENTITY_METER_ID and lC2.USE_FROM != lC2.USE_TO" +
            "   and lC2.USE_TO in (Select min(USE_TO) from LOAD_ENTITY_METER_CHILD where USE_FROM != USE_TO and USE_TO >= DATEADD(month, 1, vBill.SERVICE_DATE)" +
            "   and lC2.LOAD_ENTITY_METER_ID = LOAD_ENTITY_METER_ID group by CUSTOMER_NO)";
    String joinGenEntityChildTheMonthSettleServiceDate = " Left outer join GENERATOR_ENTITY_METER_CHILD as gMeterChild on gMeter.ID = gMeterChild.GENERATOR_ENTITY_METER_ID" +
            "   and gMeterChild.USE_FROM != gMeterChild.USE_TO and MONTH(gMeterChild.USE_TO) = MONTH(vBill.SERVICE_DATE)" +
            "   and YEAR(gMeterChild.USE_TO) = YEAR(vBill.SERVICE_DATE)" +
            "  Left outer join METER_CHANGE_RECORD as mCR on ap.ID = mCR.APPLICATION_GENERATOR_ID and mCR.USE_FROM != mCR.USE_TO" +
            "    and MONTH(mCR.USE_TO) = MONTH(vBill.SERVICE_DATE) and YEAR(mCR.USE_TO) = YEAR(vBill.SERVICE_DATE)" +
            "    and vBill.SETTLEMENT_ID = mCR.SETTLEMENT_ID";
    String joinGenEntityChildNextMonthSettleServiceDate = " Left outer join GENERATOR_ENTITY_METER_CHILD as gC2 on gMeter.ID = gC2.GENERATOR_ENTITY_METER_ID and gC2.USE_FROM != gC2.USE_TO" +
            "    and gC2.USE_TO in (Select min(USE_TO) from GENERATOR_ENTITY_METER_CHILD where USE_FROM != USE_TO and USE_TO >= DATEADD(month, 1, vBill.SERVICE_DATE)" +
            "    and gC2.GENERATOR_ENTITY_METER_ID = GENERATOR_ENTITY_METER_ID group by CUSTOMER_NO)";
    String innerSettlementBeforeBillDate = " inner join (Select APPLICATION_ID as appId, SERVICE_DATE as service, MAX(BILL_DATE) as billing" +
            " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP where BILL_DATE < ?1 group by APPLICATION_ID, SERVICE_DATE) cc" +
            "      on vBill.SERVICE_DATE = cc.service and vBill.BILL_DATE = cc.billing and vBill.APPLICATION_ID = cc.appId";
    String joinSettleAppGen = "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
            "  join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID";
    String joinApplicants = "  join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
            "  join APPLICATION as app on vBill.APPLICATION_ID = app.ID and ap.APPLICATION_ID = app.ID" +
            "  join APPLICANT_ENTITY as applicantEntity on app.APPLICANT_ID = applicantEntity.ID";
    String joinGeneratorEnd = "  join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
            "  join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID";
    String joinLoadEnd = "  join APPLICATION_LOAD as lap on settle.APPLICATION_LOAD_ID = lap.ID and vBill.APPLICATION_ID = lap.APPLICATION_ID" +
            "  join LOAD_ENTITY as lEntity on lap.LOAD_ID = lEntity.ID";
    String joinVirtualApplicant = "  Left outer join VIRTUAL_APPLICANT_COMPANY_DETAIL as virComDetail on app.APPLICANT_ID = virComDetail.APPLICANT_ID" +
            "  Left outer join VIRTUAL_APPLICANT_COMPANY as virCom on virComDetail.VIRTUAL_COMPANY_ID = virCom.ID";
    String joinVirtualGeneratorEnd = " Left outer join VIRTUAL_GENERATOR_COMPANY_DETAIL as virGenDetail on virGenDetail.GENERATOR_ID= gEntity.ID" +
            " Left outer join VIRTUAL_GENERATOR_COMPANY as virGen on virGenDetail.VIRTUAL_COMPANY_ID = virGen.ID";
    String joinVirtualLoadEnd = " Left outer join VIRTUAL_LOAD_COMPANY_DETAIL as virLoadDetail on virLoadDetail.LOAD_ID= lEntity.ID" +
            " Left outer join VIRTUAL_LOAD_COMPANY as virLoad on virLoadDetail.VIRTUAL_COMPANY_ID = virLoad.ID";
    String joinSettleApGenApGenLoadType = " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
            " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
            " join APPLICATION_GENERATOR_LOAD_TYPE as aplType on settle.APPLICATION_GENERATOR_ID=aplType.APPLICATION_GENERATOR_ID" +
            "   and settle.APPLICATION_LOAD_ID=aplType.APPLICATION_LOAD_ID";
    String joinVoltageLevel = " join VOLTAGE_LEVEL as volLevel on ((gEntity.COMBINE_METHOD = 1 and gEntity.VOLTAGE = volLevel.ID)" +
            "   OR (gEntity.COMBINE_METHOD != 1 and gEntity.RESPONSIBILITY_VOLTAGE = volLevel.ID))" +
            "     and volLevel.VOLTAGE_CLASSIFICATIONS_ID is not null";

    /** #20 視覺化分析 取得結帳月 所有表號 與 服務月
     * @param billYM
     * @return List
     */
    @Query(value =
            "SELECT DISTINCT DMEC.METER_NO, SC.SERVICE_DATE as serviceDate" +
                    ", IIF(volLevel.VOLTAGE_CLASSIFICATIONS_ID is null, lVolLevel.VOLTAGE_CLASSIFICATIONS_ID, volLevel.VOLTAGE_CLASSIFICATIONS_ID) as VOLTAGE_CLASS" +
                    ", IIF(volLevel.VOLTAGE_CLASSIFICATIONS_ID is null, 'L', 'G') as gLMark" +
                    ", 0 as flexible" +
                    " FROM DATE_APPLICATION_GENERATOR_LOAD as DAGL" +
                    "  INNER JOIN dbo.DATE_APPLICATION_METER as DAM ON DAGL.ID = DAM.DATE_APPLICATION_ID" +
                    "  INNER JOIN DATE_METER_COMPUTABLE_SETTLEMENT as DMEC ON DAM.DATE_METER_ID = DMEC.ID" +
                    "  INNER JOIN SETTLEMENT_CALCULATION as SC ON SC.SETTLEMENT_ID = DAGL.SETTLEMENT_ID" +
                    "  join APPLICATION_GENERATOR as ap on DAGL.APPLICATION_GENERATOR_ID = ap.ID" +
                    "  Left outer join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID and DMEC.NBS_CUSTOMER_NUMBER = gEntity.NBS_CUSTOMER_NUMBER" +
                    "  Left outer join VOLTAGE_LEVEL as volLevel on gEntity.VOLTAGE = volLevel.ID" +
                    "  join APPLICATION_LOAD as lap on DAGL.APPLICATION_LOAD_ID = lap.ID" +
                    "  left outer join LOAD_ENTITY as lEntity on lap.LOAD_ID = lEntity.ID and DMEC.NBS_CUSTOMER_NUMBER = lEntity.NBS_CUSTOMER_NUMBER" +
                    "  Left outer join VOLTAGE_LEVEL as lVolLevel on lEntity.VOLTAGE = lVolLevel.ID" +
                    " WHERE SC.CALCULATION_METHOD IN (3, 5, 6) AND FORMAT(SC.EXECUTION_START, 'yyyy-MM') = ?1 UNION " +
                    "Select DISTINCT vApLMeter.METER_NO, apMFGL.DATE as serviceDate, volLevel.VOLTAGE_CLASSIFICATIONS_ID as VOLTAGE_CLASS" +
                    ", 'L' as gLMark, 1 as flexible" +
                    " FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD as apMFGL" +
                    "  left outer join VIEW_APPLICATION_LOAD_METER as vapLMeter on apMFGL.APPLICATION_LOAD_ID = vapLMeter.APPLICATION_LOAD_ID" +
                    "     and (vapLMeter.USE_TO is null or vapLMeter.USE_TO > apMFGL.DATE)" +
                    "  join LOAD_ENTITY as lEntity on vapLMeter.LOAD_METER_ID = lEntity.ID" +
                    "  Left outer join VOLTAGE_LEVEL as volLevel on lEntity.VOLTAGE = volLevel.ID" +
                    " where apMFGL.SETTLEMENT_ID in (Select SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "    where CALCULATION_METHOD = 6 and FORMAT(EXECUTION_START, 'yyyy-MM') = ?1) UNION " +
                    "Select DISTINCT vApGMeter.METER_NO, apMFGL.DATE as serviceDate, volLevel.VOLTAGE_CLASSIFICATIONS_ID as VOLTAGE_CLASS" +
                    ", 'G' as gLMark, 1 as flexible" +
                    " FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD as apMFGL" +
                    "  left outer join VIEW_APPLICATION_GENERATOR_METER as vApGMeter on apMFGL.APPLICATION_GENERATOR_ID = vApGMeter.APPLICATION_GENERATOR_ID" +
                    "    and (vApGMeter.USE_TO is null or vApGMeter.USE_TO > apMFGL.DATE)" +
                    "  join GENERATOR_ENTITY as gEntity on vapGMeter.GENERATOR_METER_ID = gEntity.ID" +
                    "  Left outer join VOLTAGE_LEVEL as volLevel on gEntity.VOLTAGE = volLevel.ID" +
                    " where apMFGL.SETTLEMENT_ID in (Select SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "    where CALCULATION_METHOD = 6 and FORMAT(EXECUTION_START, 'yyyy-MM') = ?1)" +
                    " order by serviceDate, METER_NO, VOLTAGE_CLASS"
            , nativeQuery = true)
    List<Map<String, Object>> findAllSettleMeterYears(String billYM);

    /** #16 視覺化分析 取得結帳月 所有 開票 電號 表號 契約ID 與 服務月
     * 電號(電表的契約編號)，共11碼組成，表現形式為「xx-xx-xxxx-xx-x」，分別為「區碼2碼-營業區2碼-戶號4碼-分號2碼-檢算號1碼
     * 無彈性分配
     * @param billYM
     * @return List
     */
    @Query(value =
            "SELECT DISTINCT DMEC.METER_NO, DMEC.NBS_CUSTOMER_NUMBER, DAGL.APPLICATION_ID, SC.SERVICE_DATE as serviceDate" +
                    ", 0 as flexible" +
                    " FROM DATE_APPLICATION_GENERATOR_LOAD as DAGL" +
                    "  join APPLICATION as app on DAGL.APPLICATION_ID = app.ID and app.IS_SKIP_ERP != 1" +
                    "  INNER JOIN dbo.DATE_APPLICATION_METER as DAM ON DAGL.ID = DAM.DATE_APPLICATION_ID" +
                    "  INNER JOIN DATE_METER_COMPUTABLE_SETTLEMENT as DMEC ON DAM.DATE_METER_ID = DMEC.ID" +
                    "  INNER JOIN SETTLEMENT_CALCULATION as SC ON SC.SETTLEMENT_ID = DAGL.SETTLEMENT_ID" +
                    " WHERE SC.CALCULATION_METHOD IN (3, 5) AND FORMAT(SC.EXECUTION_START, 'yyyy-MM') = ?1" +
                    " order by serviceDate, NBS_CUSTOMER_NUMBER"
            , nativeQuery = true)
    List<Map<String, Object>> findBillMeterElecApplicationIdYears(String billYM);

    /** #22 視覺化分析 台電區處轄區 用電量 百萬度 排名限定單月服務年月 小數點兩位
     * serviceDate, TP_NAME[台電轄區單位名稱] 提供查找比對 取出 settle.MATCHED_KW as A_KWH
     * @param serviceStart 服務月初
     * @param serviceEnd 服務隔月初
     * @return List
     */
    @Query(value =
            "Select vBill.SERVICE_DATE as serviceDate, ROUND(sum(settle.ADJUSTED_MATCHED_KW)/1000000, 2) as A_KWH, tpcCom.UNIT_NAME as TPC_NAME" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinLoadEnd +
                    "  Left outer join TAIPOWER_COMPANY_UNIT as tpcCom on lENtity.TPC_DEPT_ID = tpcCom.ID" +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2" +
                    "  group by vBill.SERVICE_DATE, tpcCom.UNIT_NAME order by A_KWH DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumLoadKwhByTpcUnitName(Date serviceStart, Date serviceEnd);

    /** #21 視覺化分析 台電區處轄區 發電量 百萬度 排名限定單月服務年月 小數點兩位
     * serviceDate, TP_NAME[台電轄區單位名稱] 提供查找比對 取出 settle.MATCHED_KW as A_KWH
     * @param serviceStart 服務月初
     * @param serviceEnd 服務隔月初
     * @return List
     */
    @Query(value =
            "Select vBill.SERVICE_DATE as serviceDate, ROUND(sum(settle.ADJUSTED_MATCHED_KW)/1000000, 2) as A_KWH, tpcCom.UNIT_NAME as TPC_NAME" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinGeneratorEnd +
                    "  Left outer join TAIPOWER_COMPANY_UNIT as tpcCom on gEntity.TPC_DEPT_ID = tpcCom.ID" +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2" +
                    "  group by vBill.SERVICE_DATE, tpcCom.UNIT_NAME order by A_KWH DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumGeneratorKwhByTpcUnitName(Date serviceStart, Date serviceEnd);

    /** #15 發電端家數以能源別統計 單位 KW (搭配 公開月報資料庫名稱 OFFICIAL_FUEL_COMPANY  - SQL 使用 INSERT 2025 使用 UPDATE
     * WIND 風力 SUN 太陽 WATER 水力
     * @param start 起始年-01-01
     * @param end 結束隔年-01-01
     * @return List
     */
    @Query(value =
            "Select SERVICE_DATE as serviceDate, WIND, SUN, WATER, TOTAL" +
                    " from OFFICIAL_FUEL_COMPANY where SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 UNION " +
                    "Select vBill.SERVICE_DATE as serviceDate" +
                    ", count(DISTINCT IIF(fulType.ID=2, gEntity.NAME, null)) as WIND" +
                    ", count(DISTINCT IIF(fulType.ID=1, gEntity.NAME, null)) as SUN" +
                    ", count(DISTINCT IIF(fulType.ID=4, gEntity.NAME, null)) as WATER" +
                    ", count(DISTINCT IIF(fulType.ID=2, gEntity.NAME, null))+count(DISTINCT IIF(fulType.ID=1, gEntity.NAME, null))+count(DISTINCT IIF(fulType.ID=4, gEntity.NAME, null)) as TOTAL" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    joinGeneratorEnd + "  join APPLICATION as app on ap.APPLICATION_ID = app.ID" +
                    "  join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 group by vBill.SERVICE_DATE" +
                    "  order by serviceDate DESC"
            , nativeQuery = true)
    List<Map<String, Object>> countGenComByFuelLevel(Date start, Date end);

    /** #14 視覺分析 發電端裝置容量以能源別統計 單位 KW (搭配 公開月報資料庫名稱 OFFICIAL_FUEL_CAPACITY  - SQL 使用 INSERT
     * 執照容量 LICENSE_CAPACITY 試運轉容量 TRIAL_RUN_CAPACITY 全部容量 total
     * mode: 非離岸風力 mode = 10 離岸風力 mode = 15 太陽 mode = 20 水力 mode = 40
     * @param start 起始年-01-01
     * @param end 結束隔年-01-01
     * @return List
     */
    @Query(value =
            "Select SERVICE_DATE as serviceDate, null as LICENSE_CAPACITY, null as TRIAL_RUN_CAPACITY" +
                    ", FUEL_CAPACITY_KW as fuelsKw, TOTAL as total" +
                    ", null as mode from OFFICIAL_FUEL_CAPACITY where SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 UNION " +
                    "Select DISTINCT vBill.SERVICE_DATE as serviceDate" +
                    ", IIF(ap.LICENSE_CAPACITY is null, 0, ap.LICENSE_CAPACITY) as LICENSE_CAPACITY" +
                    ", IIF(ap.TRIAL_RUN_CAPACITY is null, 0, ap.TRIAL_RUN_CAPACITY) as TRIAL_RUN_CAPACITY, null as fuelsKw" +
                    ", IIF(ap.LICENSE_CAPACITY is null, 0, ap.LICENSE_CAPACITY)+IIF(ap.TRIAL_RUN_CAPACITY is null, 0" +
                    ", ap.TRIAL_RUN_CAPACITY) as total, fulType.ID*10+IIF(fuelForm.ID=5, 5, 0) as mode" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinGeneratorEnd +
                    "   join APPLICATION as app on ap.APPLICATION_ID = app.ID" +
                    "   join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    "   left outer join FUEL_FORM as fuelForm on gEntity.FUEL_FORM = fuelForm.ID" +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 UNION " +
                    "Select DISTINCT vBill.SERVICE_DATE as serviceDate" +
                    ", IIF(ap.LICENSE_CAPACITY is null, 0, ap.LICENSE_CAPACITY) as LICENSE_CAPACITY" +
                    ", IIF(ap.TRIAL_RUN_CAPACITY is null, 0, ap.TRIAL_RUN_CAPACITY) as TRIAL_RUN_CAPACITY, null as fuelsKw" +
                    ", IIF(ap.LICENSE_CAPACITY is null, 0, ap.LICENSE_CAPACITY)+IIF(ap.TRIAL_RUN_CAPACITY is null, 0" +
                    ", ap.TRIAL_RUN_CAPACITY) as total, fulType.ID*10+IIF(fuelForm.ID=5, 5, 0) as mode" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinGeneratorEnd +
                    "   join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE = 'Q'" +
                    "   join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    "   left outer join FUEL_FORM as fuelForm on gEntity.FUEL_FORM = fuelForm.ID" +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 order by serviceDate DESC, mode"
            , nativeQuery = true)
    List<Map<String, Object>> findCapacityByFuelLevel(Date start, Date end);

    /** #13 視覺化分析 轉直供度數以能源別統計 單位 億度 (搭配 公開月報資料庫名稱 OFFICIAL_FUEL_KWH  - SQL 使用 INSERT
     * 台電月報-月紀錄 mode = 1, 年紀錄 mode = 2; 結帳資料 台電 mode = 3, 非台電公司 mode = 4, 單月轉直供度數 mode = 5
     * 台電太陽能地面型 mode = 31 屋頂型 mode = 32 水上型 mode = 33 [小數3位]
     * 台電以外公司 風力 mode = 41 太陽 mode = 42 水力 mode = 44
     * @param start 起始月份-01
     * @param end 結束隔月-01
     * @return List
     */
    @Query(value =
            "Select SERVICE_DATE as serviceDate, END_MONTH_DATE as endDate, WIND_KW as windGKw, SUN_KW as sunRKw" +
                    ", WATER_KW as waterWKw, TOTAL as matchedKw, 1 as mode" +
                    " from OFFICIAL_FUEL_KWH where YEAR_MODE = 1 and END_MONTH_DATE >= ?1 and SERVICE_DATE < ?2 UNION " +
                    "Select SERVICE_DATE as serviceDate, END_MONTH_DATE as endDate, WIND_KW as windGKw, SUN_KW as sunRKw" +
                    ", WATER_KW as waterWKw, TOTAL as matchedKw, 2 as mode" +
                    " from OFFICIAL_FUEL_KWH where YEAR_MODE = 0 and END_MONTH_DATE >= ?1 and SERVICE_DATE < ?2 UNION " +
                    "Select DISTINCT vBill.SERVICE_DATE as serviceDate, null as endDate, CASE" +
                    " WHEN fuelForm.ID = 1 THEN ROUND(sum(settle.ADJUSTED_MATCHED_KW) over (partition by vBill.SERVICE_DATE, fuelForm.ID)/100000000, 3)" +
                    " WHEN fuelForm.ID = 2 THEN ROUND(sum(settle.ADJUSTED_MATCHED_KW) over (partition by vBill.SERVICE_DATE, fuelForm.ID) /100000000, 3)" +
                    " WHEN fuelForm.ID = 3 THEN ROUND(sum(settle.ADJUSTED_MATCHED_KW) over (partition by vBill.SERVICE_DATE, fuelForm.ID) /100000000, 3)" +
                    "  ELSE null END as windGKw, null as sunRKw, null as waterWKw, null as matchedKw, 30+fuelForm.ID as mode" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinGeneratorEnd +
                    "  join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE = 'Q'" +
                    "  left outer join FUEL_FORM as fuelForm on gEntity.FUEL_FORM = fuelForm.ID" +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 UNION " +
                    "Select DISTINCT vBill.SERVICE_DATE as serviceDate, null as endDate, CASE" +
                    " WHEN fulType.ID = 1 THEN ROUND(sum(settle.ADJUSTED_MATCHED_KW) over (partition by vBill.SERVICE_DATE, fulType.ID) /100000000, 3)" +
                    " WHEN fulType.ID = 2 THEN ROUND(sum(settle.ADJUSTED_MATCHED_KW) over (partition by vBill.SERVICE_DATE, fulType.ID) /100000000, 3)" +
                    " WHEN fulType.ID = 4 THEN ROUND(sum(settle.ADJUSTED_MATCHED_KW) over (partition by vBill.SERVICE_DATE, fulType.ID) /100000000, 3)" +
                    "  ELSE null END as windGKw, null as sunRKw, null as waterWKw, null as matchedKw, 40+fulType.ID as mode" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinGeneratorEnd +
                    "  join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != 'Q'" +
                    "  join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2" +
                    "  order by serviceDate, mode"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwByFuelLevel(Date start, Date end);

    /** #12 視覺化分析 轉直供費用統計 單位 百萬元 (搭配 公開月報資料庫名稱 OFFICIAL_ADST_COST - SQL 使用 INSERT
     * yearMode 1: year 2: month 3: settle [小數2位]
     * @param start 起始月份-01
     * @param end 結束隔月-01
     * @return List
     */
    @Query(value =
            "Select SERVICE_DATE as serviceDate, END_MONTH_DATE as endDate, A_COST, S_COST, T_COST, D_COST, TOTAL" +
                    ", 1 as yearMode from OFFICIAL_ADST_COST where YEAR_MODE = 1 and END_MONTH_DATE >= ?1 and SERVICE_DATE < ?2" +
                    " UNION Select SERVICE_DATE as serviceDate, END_MONTH_DATE as endDate, A_COST, S_COST, T_COST, D_COST, TOTAL" +
                    ", 2 as yearMode from OFFICIAL_ADST_COST where YEAR_MODE = 0 and END_MONTH_DATE >= ?1 and SERVICE_DATE < ?2" +
                    " UNION Select vBill.SERVICE_DATE as serviceDate, null as endDate" +
                    ", ROUND(sum(settle.ANCILLARY_SERVICE_COST)/1000000, 2) as A_COST, ROUND(sum(settle.DISPATCH_SERVICE_COST)/1000000, 2) as S_COST" +
                    ", ROUND(sum(settle.POWER_TRANS_COST)/1000000, 2) as T_COST, ROUND(sum(settle.POWER_DIST_COST)/1000000, 2) as D_COST" +
                    ", null as TOTAL" +
                    ", 3 as yearMode from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 group by vBill.SERVICE_DATE" +
                    "  order by serviceDate, yearMode"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwByServiceDate(Date start, Date end);

    /** #11 視覺化分析 - 轉直供度數以費用類別統計 單位 億度 (搭配 公開月報資料庫名稱 OFFICIAL_ADST_KWH - SQL 使用 INSERT
     * serviceDate: 服務起始日 END_MONTH_DATE: 台電月報 - 與服務起始日同一年的服務結束日, 結帳月份則 為 null
     * A_S_KWH: 輔助/調度 億度 T_KWH: 輸電 D_KWH: 配電 億度 億度 [小數2位]
     * yearMode 1: year 2: month 3: settle
     * @param start 起始月份-01
     * @param end 結束隔月-01
     * @return List
     */
    @Query(value =
            "Select SERVICE_DATE as serviceDate, END_MONTH_DATE as endDate, A_S_KWH, T_KWH, D_KWH, 1 as yearMode" +
                    " from OFFICIAL_ADST_KWH where YEAR_MODE = 1 and END_MONTH_DATE >= ?1 and SERVICE_DATE < ?2" +
                    " UNION Select SERVICE_DATE as serviceDate, END_MONTH_DATE as endDate, A_S_KWH, T_KWH, D_KWH, 2 as yearMode" +
                    " from OFFICIAL_ADST_KWH where YEAR_MODE = 0 and END_MONTH_DATE >= ?1 and SERVICE_DATE < ?2" +
                    " UNION Select vBill.SERVICE_DATE as serviceDate, null as endDate, ROUND(sum(settle.ANCILLARY_SERVICE_COST)/1000000/(" +
                    "Select RATE from PW_FUEL_RATE join PRICE_TYPE as price on PW_FUEL_RATE.PRICE_TYPE_ID = price.ID and price.CODE = 'A'" +
                    "  where PW_FUEL_RATE.[FROM] <= vBill.SERVICE_DATE and PW_FUEL_RATE.[TO] >= vBill.SERVICE_DATE" +
                    "    and PW_FUEL_RATE.PW_FUEL_TYPE_ID = 1)/100, 2) as A_S_KWH, ROUND(sum(settle.POWER_TRANS_COST)/1000000/(" +
                    "Select RATE from PW_FUEL_RATE join PRICE_TYPE as price on PW_FUEL_RATE.PRICE_TYPE_ID = price.ID and price.CODE = 'T'" +
                    "  where PW_FUEL_RATE.[FROM] <= vBill.SERVICE_DATE and PW_FUEL_RATE.[TO] >= vBill.SERVICE_DATE" +
                    "    and PW_FUEL_RATE.PW_FUEL_TYPE_ID = 1)/100, 2) as T_KWH, ROUND(sum(settle.POWER_DIST_COST)/1000000/(" +
                    "Select RATE from PW_FUEL_RATE join PRICE_TYPE as price on PW_FUEL_RATE.PRICE_TYPE_ID = price.ID and price.CODE = 'D'" +
                    "  where PW_FUEL_RATE.[FROM] <= vBill.SERVICE_DATE and PW_FUEL_RATE.[TO] >= vBill.SERVICE_DATE" +
                    "    and PW_FUEL_RATE.PW_FUEL_TYPE_ID = 1)/100, 2) as D_KWH, 3 as yearMode" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" + joinSettleAppGen +
                    "  where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 group by vBill.SERVICE_DATE" +
                    " order by serviceDate, yearMode"
            , nativeQuery = true)
    List<Map<String, Object>> sumKwhByADSTCostType(Date start, Date end);

    /** #10 視覺化分析 - 直供度數統計 單位 億度 (搭配 公開月報資料庫名稱 OFFICIAL_TRANS_DIRECT_KWH - SQL 使用 UPDATE 更新 [PWDS = 0]
     * serviceDate: 服務起始日 ACC_KW: 台電月報連續數月 億度 紀錄 END_MONTH_DATE: 台電月報 - 與服務起始日同一年的服務結束日, 結帳月份則 為 null
     * TOTAL: ACC_KW 累計 億度數 或 結帳月份累計的轉供 億度數 [小數2位]
     * mode: 2 台電月報 3 結帳紀錄
     * @param start 輸入起始年月-01
     * @param end 輸入結束隔月-01
     * @return List
     */
    @Query(value =
            "Select SERVICE_DATE as serviceDate, ACC_KW, END_MONTH_DATE, TOTAL, 2 as mode from OFFICIAL_TRANS_DIRECT_KWH" +
                    " where PWDS = 0 and END_MONTH_DATE >= ?1 and SERVICE_DATE < ?2" +
                    " UNION Select vBill.SERVICE_DATE as serviceDate, null, null, ROUND(sum(settle.ADJUSTED_MATCHED_KW)/100000000, 2) as TOTAL, 3 as mode" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and (app.TYPE = '2' or app.TYPE = '3')" +
                    "  where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 group by vBill.SERVICE_DATE" +
                    " order by serviceDate, mode"
            , nativeQuery = true)
    List<Map<String, Object>> sumDirectPowerMatchKw(Date start, Date end);

    /** #9 視覺化分析 - 轉供度數統計 單位 億度 (搭配 公開月報資料庫名稱 OFFICIAL_TRANS_DIRECT_KWH - SQL 使用 UPDATE 更新 [PWDS = 1]
     * serviceDate: 服務起始日 ACC_KW: 台電月報連續數月 億度 紀錄 END_MONTH_DATE: 台電月報 - 與服務起始日同一年的服務結束日, 結帳月份 - null
     * TOTAL: ACC_KW 累計 億度數 或 結帳月份累計的轉供 億度數 [小數2位]
     * mode: 2 台電月報 3 結帳紀錄
     * @param start 輸入起始年月-01
     * @param end 輸入結束隔月-01
     * @return List
     */
    @Query(value =
            "Select SERVICE_DATE as serviceDate, ACC_KW, END_MONTH_DATE, TOTAL, 2 as mode from OFFICIAL_TRANS_DIRECT_KWH" +
                    " where PWDS = 1 and END_MONTH_DATE >= ?1 and SERVICE_DATE < ?2" +
                    " UNION Select vBill.SERVICE_DATE as serviceDate, null, null, ROUND(sum(settle.ADJUSTED_MATCHED_KW)/100000000, 2) as TOTAL, 3 as mode" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and (app.TYPE != '2' or app.TYPE != '3')" +
                    "  where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 group by vBill.SERVICE_DATE" +
                    " order by serviceDate, mode"
            , nativeQuery = true)
    List<Map<String, Object>> sumTransPowerMatchKw(Date start, Date end);

    /** #7 視覺化分析 轉直供用電端數量統計 + 虛擬集團
     * genNum: 發電業者數量, nbsNum: 電號數量, serviceDate: 服務日
     * office: 1: 公報, 2: 直接計算, 3: 虛擬集團計算; 用電端 channel = 0
     * @param serviceStart 輸入月初
     * @param serviceEnd 輸入隔月月初
     * @return List
     */
    @Query(value =
            "Select COM_NUM as loadNum, NBS_NUM as nbsNum, SERVICE_DATE as serviceDate, 1 as office from OFFICIAL_GENERATOR_LOAD_QUANTITY" +
                    " where CHANNEL = 0 and SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 UNION " +
                    "Select count(DISTINCT lEntity.NAME) as loadNum, count(DISTINCT lEntity.NBS_CUSTOMER_NUMBER) as nbsNum" +
                    ", vBill.SERVICE_DATE as serviceDate, 2 as office from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "   join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinLoadEnd +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 group by vBill.SERVICE_DATE UNION " +
                    "Select count(DISTINCT IIF(virLoad.NAME is null, lEntity.NAME, virLoad.NAME)) as loadNum" +
                    ", count(DISTINCT IIF(virLoad.NAME is null, lEntity.NBS_CUSTOMER_NUMBER, virLoad.NAME)) as nbsNum" +
                    ", vBill.SERVICE_DATE as serviceDate, 3 as office from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "   join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    joinLoadEnd + joinVirtualLoadEnd +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 group by vBill.SERVICE_DATE" +
                    " order by serviceDate, office"
            , nativeQuery = true)
    List<Map<String, Object>> countLoadNBSNumber(Date serviceStart, Date serviceEnd);

    /** #6 視覺化分析 轉直供發電端數量統計 + 虛擬集團
     * genNum: 發電業者數量, nbsNum: 電號數量, serviceDate: 服務日
     * office: 1: 公報, 2: 直接計算, 3: 虛擬集團計算; 發電端 channel = 1
     * @param serviceStart 輸入月初
     * @param serviceEnd 輸入隔月月初
     * @return List
     */
    @Query(value =
            "Select COM_NUM as genNum, NBS_NUM as nbsNum, SERVICE_DATE as serviceDate, 1 as office from OFFICIAL_GENERATOR_LOAD_QUANTITY" +
                    " where CHANNEL = 1 and SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 UNION " +
                    "Select count(DISTINCT gEntity.NAME) as genNum, count(DISTINCT gEntity.NBS_CUSTOMER_NUMBER) as nbsNum" +
                    ", vBill.SERVICE_DATE as serviceDate, 2 as office from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "   join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    "   join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    "   join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 group by vBill.SERVICE_DATE UNION " +
                    "Select count(DISTINCT IIF(virGen.NAME is null, gEntity.NAME, virGen.NAME)) as genNum" +
                    ", count(DISTINCT IIF(virGen.NAME is null, gEntity.NBS_CUSTOMER_NUMBER, virGen.NAME)) as nbsNum" +
                    ", vBill.SERVICE_DATE as serviceDate, 3 as office from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "   join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    "   join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    "   join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" + joinVirtualGeneratorEnd +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2 group by vBill.SERVICE_DATE" +
                    " order by serviceDate, office"
            , nativeQuery = true)
    List<Map<String, Object>> countGeneratorNBSNumber(Date serviceStart, Date serviceEnd);

    /**
     * #3 視覺化分析 列表與圖 用電端轉直供度數 整年所有單月 + 整年加總 + 虛擬集團
     * @param serviceStart 輸入年1/1
     * @param serviceEnd 隔年1/1
     * @return
     */
    @Query(value =
            "Select IIF(virLoad.NAME is null, lEntity.NAME, virLoad.NAME) as lEntityNAME, null as serviceDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    joinLoadEnd + joinVirtualLoadEnd +
                    " where SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 and ERP_CANCELLATION_DATE is null" +
                    "  group by IIF(virLoad.NAME is null, lEntity.NAME, virLoad.NAME) UNION " +
                    "Select IIF(virLoad.NAME is null, lEntity.NAME, virLoad.NAME) as lEntityNAME, vBill.SERVICE_DATE as serviceDate" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    joinLoadEnd + joinVirtualLoadEnd +
                    " where SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 and ERP_CANCELLATION_DATE is null" +
                    "  group by vBill.SERVICE_DATE, IIF(virLoad.NAME is null, lEntity.NAME, virLoad.NAME)" +
                    " order by serviceDate, matchedKw DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwByLoadEntityNamesYear(Date serviceStart, Date serviceEnd);

    /**
     * #2 視覺化分析 列表與圖 發電端轉直供度數 整年所有單月 + 整年加總 + 虛擬集團
     * @param serviceStart 輸入年1/1
     * @param serviceEnd 隔年1/1
     * @return
     */
    @Query(value =
            "Select IIF(virGen.NAME is null, gEntity.NAME, virGen.NAME) as gEntityNAME, null as serviceDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    joinGeneratorEnd + joinVirtualGeneratorEnd +
                    " where SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 and ERP_CANCELLATION_DATE is null" +
                    "  group by IIF(virGen.NAME is null, gEntity.NAME, virGen.NAME) UNION " +
                    "Select IIF(virGen.NAME is null, gEntity.NAME, virGen.NAME) as gEntityNAME, vBill.SERVICE_DATE as serviceDate" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    joinGeneratorEnd + joinVirtualGeneratorEnd +
                    " where SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 and ERP_CANCELLATION_DATE is null" +
                    "  group by vBill.SERVICE_DATE, IIF(virGen.NAME is null, gEntity.NAME, virGen.NAME)" +
                    " order by serviceDate, matchedKw DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwByGeneratorEntityNamesYear(Date serviceStart, Date serviceEnd);

    /**
     * #1 視覺化分析 列表與圖 申請者轉直供度數 整年所有單月 + 整年加總 + 虛擬集團
     * @param serviceStart 輸入年1/1
     * @param serviceEnd 隔年1/1
     * @return
     */
    @Query(value =
            "Select IIF(virCom.NAME is null, applicantEntity.NAME, virCom.NAME) as APPL_NAME, null as serviceDate" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    joinApplicants + joinVirtualApplicant +
                    " where SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 and ERP_CANCELLATION_DATE is null" +
                    "  group by IIF(virCom.NAME is null, applicantEntity.NAME, virCom.NAME) UNION " +
                    "Select IIF(virCom.NAME is null, applicantEntity.NAME, virCom.NAME) as APPL_NAME, vBill.SERVICE_DATE as serviceDate" +
                    ", sum(settle.MATCHED_KW) as matchedKw from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    joinApplicants + joinVirtualApplicant +
                    " where SERVICE_DATE >= ?1 and SERVICE_DATE < ?2 and ERP_CANCELLATION_DATE is null" +
                    "  group by vBill.SERVICE_DATE, IIF(virCom.NAME is null, applicantEntity.NAME, virCom.NAME)" +
                    " order by serviceDate, matchedKw DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwByApplicantsYear(Date serviceStart, Date serviceEnd);

    // ------------------------  以下提供報表類 使用 ---------------------//

    /** #21 標檢局 所有結帳輸出 照結帳年月區間 整理 18+4欄位 目前固定起始日 = 2022-01-01
     * APPLICATION_MONTHLY_SETTLEMENT(6) 包含 matchedKw[度數] 跟 費用 FEE[4種費用相加]
     * LOAD_ENTITY_METER(1) CUST_METER_NO[用電表號] + LOAD_ENTITY_METER_CHILD(2) CUST_METER_CHANGE_DATE[用電換表日]
     * , oldLoadMeterNop[用電端舊表號]
     * LOAD_ENTITY(1) CUST_ELEC_NO[用電電號] GENERATOR_ENTITY_METER(1) GEN_METER_NO[發電表號]
     * + GENERATOR_ENTITY_METER_CHILD(2) GEN_METER_CHANGE_DATE[發電端換表日] oldGenMeterNo[發電舊表號]
     * GENERATOR_ENTITY(2) GEN_ELEC_NO[發電電號] + gId[GENERATOR_ENTITY_ID]
     * PE_FUEL_RATE(4) tRate, dRATE, sRATE, aRATE
     * APPLICATION(4) appId, CONTRACT_NO, SERVICE_ID[契約編號], appType[案件類型 = APPLICATION_TYPE.ID]
     * @param serviceStart 服務起始日 固定 2022-01-01
     * @param billEnd 結帳結束隔月首日
     * @return
     */
    @Query(value =
            "Select DISTINCT settle.APPLICATION_GENERATOR_ID as applicationGeneratorId, settle.APPLICATION_LOAD_ID as applicationLoadId" +
                    ", vBill.BILL_DATE as billDate, sum(settle.ADJUSTED_MATCHED_KW) over (partition by vBill.BILL_DATE" +
                    ", settle.APPLICATION_GENERATOR_ID, settle.APPLICATION_LOAD_ID) as matchedKw" +
                    ", sum(settle.POWER_TRANS_COST+settle.POWER_DIST_COST+settle.ANCILLARY_SERVICE_COST+settle.DISPATCH_SERVICE_COST)" +
                    " over (partition by vBill.BILL_DATE, settle.APPLICATION_GENERATOR_ID, settle.APPLICATION_LOAD_ID) as FEE" +
                    ", IIF(lC2.METER_NO is null, lMeter.METER_NO, lC2.METER_NO) as CUST_METER_NO, lMeterChild.USE_TO as CUST_METER_CHANGE_DATE" +
                    ", lMeterChild.METER_NO as oldLoadMeterNo, lEntity.NBS_CUSTOMER_NUMBER as CUST_ELEC_NO, lEntity.ID as lEntityId" +
                    ", IIF(gC2.METER_NO is null, gMeter.METER_NO, gC2.METER_NO) as GEN_METER_NO, gMeterChild.USE_TO as GEN_METER_CHANGE_DATE" +
                    ", gMeterChild.METER_NO as oldGenMeterNo, gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO" +
                    ", (Select RATE from PW_FUEL_RATE join PRICE_TYPE as price on PW_FUEL_RATE.PRICE_TYPE_ID = price.ID and price.CODE = 'T'" +
                    "    where PW_FUEL_RATE.[FROM] <= vBill.SERVICE_DATE and PW_FUEL_RATE.[TO] >= vBill.SERVICE_DATE and PW_FUEL_RATE.PW_FUEL_TYPE_ID = 1) as tRate" +
                    ", (Select RATE from PW_FUEL_RATE join PRICE_TYPE as price on PW_FUEL_RATE.PRICE_TYPE_ID = price.ID and price.CODE = 'D'" +
                    "    where PW_FUEL_RATE.[FROM] <= vBill.SERVICE_DATE and PW_FUEL_RATE.[TO] >= vBill.SERVICE_DATE and PW_FUEL_RATE.PW_FUEL_TYPE_ID = 1) as dRate" +
                    ", (Select RATE from PW_FUEL_RATE join PRICE_TYPE as price on PW_FUEL_RATE.PRICE_TYPE_ID = price.ID and price.CODE = 'S'" +
                    "    where PW_FUEL_RATE.[FROM] <= vBill.SERVICE_DATE and PW_FUEL_RATE.[TO] >= vBill.SERVICE_DATE and PW_FUEL_RATE.PW_FUEL_TYPE_ID = 1) as sRate" +
                    ", (Select RATE from PW_FUEL_RATE join PRICE_TYPE as price on PW_FUEL_RATE.PRICE_TYPE_ID = price.ID and price.CODE = 'A'" +
                    "    where PW_FUEL_RATE.[FROM] <= vBill.SERVICE_DATE and PW_FUEL_RATE.[TO] >= vBill.SERVICE_DATE and PW_FUEL_RATE.PW_FUEL_TYPE_ID = 1) as aRate" +
                    ", app.ID as appId, app.CONTRACT_NO, app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, app.TYPE as appType" +
                    " from APPLICATION_MONTHLY_CAPACITY_RECORD as settle" +
                    " join VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill on settle.SETTLEMENT_ID = vBill.SETTLEMENT_ID" +
                    "   and vBill.ERP_CANCELLATION_DATE is null and vBill.SERVICE_DATE >= ?1 and vBill.BILL_DATE < ?2" + joinLoadEnd +
                    " Left outer join LOAD_ENTITY_METER as lMeter on lap.LOAD_METER_ID = lMeter.ID" +
                    joinLoadEntityChildTheMonthSettleServiceDate + joinLoadEntityChildNextMonthSettleServiceDate + joinGeneratorEnd +
                    " Left outer join GENERATOR_ENTITY_METER as gMeter on ap.GENERATOR_METER_ID = gMeter.ID" +
                    joinGenEntityChildTheMonthSettleServiceDate + joinGenEntityChildNextMonthSettleServiceDate +
                    " join APPLICATION as app on lap.APPLICATION_ID = app.ID and ap.APPLICATION_ID = app.ID" +
                    " order by billDate DESC, SERVICE_ID ASC, applicationGeneratorId ASC, applicationLoadId ASC" +
                    ", GEN_METER_CHANGE_DATE DESC, CUST_METER_CHANGE_DATE DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwCostsByIdsBillingDate(Date serviceStart, Date billEnd);

    /** #2 會計室 勞務收入明細表 單帳單年月 取出帳單當月度數資料
     * noErp - 小額綠電的資料 （只有 12 跟 3 有值)
     * @param billDate 帳單月首日
     * @return java.util.List
     */
    @Query(value =
            "Select sum(settle.ADJUSTED_MATCHED_KW) as kwh, 3 as powerType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleApGenApGenLoadType +
                    " where vBill.BILL_DATE = ?1" +
                    " UNION Select sum(settle.ADJUSTED_MATCHED_KW) as kwh, 12 as powerType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleApGenApGenLoadType + " and aplType.TYPE = 3" +
                    "  join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2'" +
                    " where vBill.BILL_DATE = ?1" +
                    " UNION Select sum(settle.ADJUSTED_MATCHED_KW) as kwh, 2 as powerType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleApGenApGenLoadType + " and aplType.TYPE = 2" +
                    "  join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2'" +
                    " where vBill.BILL_DATE = ?1" +
                    " UNION Select sum(settle.ADJUSTED_MATCHED_KW) as kwh, 1 as powerType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleApGenApGenLoadType + " and aplType.TYPE = 1" +
                    "  join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2'"+
                    " where vBill.BILL_DATE = ?1" +
                    " order by powerType DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumKwhADSTByBillingDate(Date billDate);

    /** #23 環保處 能源別發電報表(單月服務年月) FUEL_LABEL[發電類型] FUEL_FORM[能源來源] PWDS[直供 DS 轉供 PW 小額綠電 Q]
     *  VOLT_LEVEL_CLASS[發電端電壓別] matchedKw[電量(度)]
     * @param billDate 帳單月首日
     * @return java.util.List
     */
    @Query(value =
            "Select volLevel.VOLTAGE_CLASSIFICATIONS_ID as VOLT_LEVEL_CLASS, fulType.LABEL as FUEL_LABEL, fuelForm.LABEL as FUEL_FORM, 'DS' as PWDS" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != 'Q' and (app.TYPE = '2' or app.TYPE = '3')" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" + joinVoltageLevel +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " left outer join FUEL_FORM as fuelForm on gEntity.FUEL_FORM = fuelForm.ID" +
                    " where vBill.BILL_DATE = ?1" +
                    " group by volLevel.VOLTAGE_CLASSIFICATIONS_ID, fulType.LABEL, fuelForm.LABEL, app.TYPE UNION " +
                    "Select volLevel.VOLTAGE_CLASSIFICATIONS_ID as VOLT_LEVEL_CLASS, fulType.LABEL as FUEL_LABEL, fuelForm.LABEL as FUEL_FORM, 'PW' as PWDS" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != 'Q' and app.TYPE != '2' and app.TYPE != '3'" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" + joinVoltageLevel +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " left outer join FUEL_FORM as fuelForm on gEntity.FUEL_FORM = fuelForm.ID" +
                    " where vBill.BILL_DATE = ?1" +
                    " group by volLevel.VOLTAGE_CLASSIFICATIONS_ID, fulType.LABEL, fuelForm.LABEL, app.TYPE UNION " +
                    "Select volLevel.VOLTAGE_CLASSIFICATIONS_ID as VOLT_LEVEL_CLASS, fulType.LABEL as FUEL_LABEL, fuelForm.LABEL as FUEL_FORM, app.TYPE as PWDS" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE = 'Q'" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" + joinVoltageLevel +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " left outer join FUEL_FORM as fuelForm on gEntity.FUEL_FORM = fuelForm.ID" +
                    " where vBill.BILL_DATE = ?1" +
                    " group by volLevel.VOLTAGE_CLASSIFICATIONS_ID, fulType.LABEL, fuelForm.LABEL, app.TYPE" +
                    " order by PWDS ASC, VOLT_LEVEL_CLASS ASC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwByVoltageClassFuelLabelApplicationType(Date billDate);

    /**
     * #22 企劃室 電能轉直供資訊報表(單月服務年月) - 取得整年度服務年月 依照APPLICATION.ID 加總取整媒合電量 matchedKw
     * @param billDate 帳單月首日
     * @return
     */
    @Query(value =
            "Select vBill.APPLICATION_ID as ID, vBill.SERVICE_DATE as serviceDate, vBill.BILL_DATE as billDate" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" +
                    "   and vBill.ERP_CANCELLATION_DATE is null and vBill.BILL_DATE = ?1" + joinGeneratorEnd +
                    " group by vBill.SERVICE_DATE, vBill.BILL_DATE, vBill.APPLICATION_ID" +
                    " order by vBill.APPLICATION_ID"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwByApplicationId(Date billDate);

    /** #13 調度處 每月發轉餘總計 每月發轉餘統計 以年為週期取出每月資料 需排除 彈性分配 APPLICATION.TYPE != 4
     * serviceDate(Date), billDate(Date) MATCHED_KW[計費度數] PWDS[轉供直供 2,3 直供; 其他轉供]
     * @param billDate 帳單月月初
     * @return
     */
    @Query(value =
            "Select DISTINCT vBill.SERVICE_DATE as serviceDate, vBill.BILL_DATE as billDate" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'PW' as PWDS" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on settle.SETTLEMENT_ID = vBill.SETTLEMENT_ID and vBill.ERP_CANCELLATION_DATE is null" +
                    "   and vBill.TYPE != '4' and vBill.TYPE != '2' and vBill.TYPE != '3' and vBill.BILL_DATE = ?1" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    "  group by vBill.SERVICE_DATE, vBill.BILL_DATE" +
                    " Union Select DISTINCT vBill.SERVICE_DATE as serviceDate, vBill.BILL_DATE as billDate" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'DS' as PWDS" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on settle.SETTLEMENT_ID = vBill.SETTLEMENT_ID and vBill.ERP_CANCELLATION_DATE is null" +
                    "   and (vBill.TYPE = '2' or vBill.TYPE = '3') and vBill.BILL_DATE = ?1" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    "  group by vBill.SERVICE_DATE, vBill.BILL_DATE UNION " +
                    "Select DISTINCT vBill.SERVICE_DATE as serviceDate, vBill.BILL_DATE as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'PW' as PWDS" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + innerSettlementBeforeBillDate +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on settle.SETTLEMENT_ID = vBill.SETTLEMENT_ID and vBill.ERP_CANCELLATION_DATE is null" +
                    "    and vBill.TYPE != '4' and vBill.TYPE != '2' and vBill.TYPE != '3'" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    "  group by vBill.SERVICE_DATE, vBill.BILL_DATE UNION " +
                    " Select DISTINCT vBill.SERVICE_DATE as serviceDate, vBill.BILL_DATE as billDate" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'DS' as PWDS" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + innerSettlementBeforeBillDate +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on settle.SETTLEMENT_ID = vBill.SETTLEMENT_ID and vBill.ERP_CANCELLATION_DATE is null" +
                    "    and (vBill.TYPE = '2' or vBill.TYPE = '3')" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    "  group by vBill.SERVICE_DATE, vBill.BILL_DATE" +
                    " order by serviceDate, billDate DESC, PWDS DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKmByEachApplicationGeneratorId(Date billDate);

    /** #14 調度處 發轉餘報表 電號發電公司訊息 (帳單年月搜尋) 統計 billDate(Date), serviceStart(Date)：起始服務月月初, serviceEndNext(Date): 結束服務隔月月初
     * , applicationGeneratorId(long) gEntityId(long)  需排除 彈性分配 APPLICATION.TYPE != 4
     * MATCHED_KW[計費度數] PWDS_CONTRACT_TYPE[說明: 契約類型] GEN_NAME[發電端名稱] GEN_ELEC_NO[發電端電號] PWDS[轉供直供 2,3 直供 其他轉供]
     * @param billDate 帳單月首日
     * @return
     */
    @Query(value =
            "Select DISTINCT settle.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) over (partition by settle.APPLICATION_GENERATOR_ID, vBill.BILL_DATE) as matchedKw" +
                    ", vBill.TYPE as PWDS, apType.LABEL as PWDS_CONTRACT_TYPE, gEntity.NAME as GEN_NAME" +
                    ", gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO, gEntity.ID as gEntityId" +
                    ", vBill.BILL_DATE as billDate, null as serviceStart, null as serviceEndNext" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID and vBill.TYPE != '4'" + joinGeneratorEnd +
                    "  join APPLICATION_TYPE as apType on vBill.TYPE = apType.ID" +
                    " where vBill.BILL_DATE = ?1 UNION " +
                    "Select DISTINCT settle.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) over (partition by settle.APPLICATION_GENERATOR_ID, vBill.BILL_DATE) as matchedKw" +
                    ", null as PWDS, null as PWDS_CONTRACT_TYPE, null as GEN_NAME, null as GEN_ELEC_NO, null as gEntityId" +
                    ", vBill.BILL_DATE as billDate, null as serviceStart, null as serviceEndNext" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + innerSettlementBeforeBillDate +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID and vBill.TYPE != '4'" +
                    "  join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    " UNION " +
                    "Select DISTINCT null as applicationGeneratorId, null as matchedKw, null as PWDS, null as PWDS_CONTRACT_TYPE" +
                    ", null as GEN_NAME, null as GEN_ELEC_NO, null as gEntityId, vBill.BILL_DATE as billDate" +
                    ", (Select MIN(VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP.SERVICE_DATE)" +
                    "    from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP where VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP.BILL_DATE = ?1" +
                    "    group by VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP.BILL_DATE) as servicStart" +
                    ", DATEADD(month, 1, (Select MAX(VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP.SERVICE_DATE)" +
                    "    from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP where VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP.BILL_DATE = ?1" +
                    "    group by VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP.BILL_DATE)) as serviceEndNext" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " where vBill.BILL_DATE = ?1" +
                    "  order by billDate DESC, applicationGeneratorId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKmGeneratorEndInfo(Date billDate);

    /**
     * #14 調度處 發轉餘報表(帳單月查詢) 統計 - 餘電總和(一階餘電減去二階轉供)[再媒合未媒合電量]
     * @param billDate 帳單月首日
     * @return
     */
    @Query(value =
            "Select DISTINCT reGen.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", ROUND(sum(reGen.MATCHED_RM), 0) as matchedRm, ROUND(sum(reGen.UNMATCHED_RM), 0) as unmatchedRm" +
                    " from APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD as reGen" +
                    " where reGen.SETTLEMENT_ID in (" +
                    "Select DISTINCT SETTLEMENT_ID from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP" +
                    "   where VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP.BILL_DATE = ?1)" +
                    " group by reGen.APPLICATION_GENERATOR_ID" +
                    " ORDER BY applicationGeneratorId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> sumGenMatchedRmUnmatchedRmByDate(Date billDate);

    /** #17 調度處 線損計算 用戶 用電端 限定單月帳單年月
     * serviceDate, billDate, applicationGeneratorId, applicationLoadId 提供查找比對
     * 取出 settle.ADJUSTED_MATCHED_KW as A_KWH
     * EQUIP_VOLT_LEVEL[電壓層級 = 用電端責任分界點電壓層級] FUEL_LABEL[能源類別] TP_CODE[用戶地區 又稱電電公司單位代碼]
     * @param billDate 月初
     * @return List
     */
    @Query(value =
            "Select vBill.BILL_DATE as billDate" +
                    ", settle.APPLICATION_GENERATOR_ID as applicationGeneratorId, settle.APPLICATION_LOAD_ID as applicationLoadId" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as A_KWH, volLevel.LABEL as EQUIP_VOLT_LEVEL" +
                    ", fulType.LABEL as FUEL_LABEL, tpcCom.CODE as TPC_CODE from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinLoadEnd +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpcCom on lEntity.TPC_DEPT_ID = tpcCom.ID" + joinGeneratorEnd +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " Left outer join VOLTAGE_LEVEL as volLevel on lEntity.RESPONSIBILITY_VOLTAGE = volLevel.ID" +
                    " where vBill.BILL_DATE = ?1" +
                    "  group by vBill.BILL_DATE, settle.APPLICATION_GENERATOR_ID" +
                    ", settle.APPLICATION_LOAD_ID, volLevel.LABEL, fulType.LABEL, tpcCom.CODE" +
                    " order by tpcCom.CODE ASC, fulType.LABEL ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findLoadFuelTypeTpcCompanyVoltLevel(Date billDate);

    /** #17 調度處 線損計算 電源 發電端 限定單月帳單年月
     *  serviceDate, billDate, applicationGeneratorId, applicationLoadId 提供查找比對
     * 取出 settle.MATCHED_KW as A_KWH
     * EQUIP_VOLT_LEVEL[電壓層級 又稱發電設備併接點電壓層級] FUEL_LABEL[能源類別] TP_CODE[電源地區 又稱電電公司單位代碼]
     * @param billDate 月初
     * @return
     */
    @Query(value =
            "Select vBill.BILL_DATE as billDate" +
                    ", settle.APPLICATION_GENERATOR_ID as applicationGeneratorId, settle.APPLICATION_LOAD_ID as applicationLoadId" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as A_KWH, volLevel.LABEL as EQUIP_VOLT_LEVEL" +
                    ", fulType.LABEL as FUEL_LABEL, tpcCom.CODE as TPC_CODE from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinGeneratorEnd +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpcCom on gEntity.TPC_DEPT_ID = tpcCom.ID" +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" + joinVoltageLevel +
                    " where vBill.BILL_DATE = ?1" +
                    "  group by vBill.BILL_DATE, settle.APPLICATION_GENERATOR_ID" +
                    ", settle.APPLICATION_LOAD_ID, volLevel.LABEL, fulType.LABEL, tpcCom.CODE" +
                    " order by tpcCom.CODE ASC, fulType.LABEL ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findGeneratorFuelTypeTpcCompanyVoltLevel(Date billDate);

    /** #16 #18 #19 調度處 帳單月份 取出 所有服務月列表
     * @param billDate
     * @return List
     */
    @Query(value =
            "Select DISTINCT VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP.SERVICE_DATE as serviceDate from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP" +
                    " where VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP.BILL_DATE = ?1" +
                    "  order by serviceDate"
            , nativeQuery = true)
    List<Map<String, Object>> findSettleServiceDates(Date billDate);

    /** #15 每月轉直供服務各類度數(單月結帳年月) 調度處報表 取出 MATCHED_KW 已經四捨五入到整數的 kwh 搭配 APPLICATION_GENERATOR_LOAD_TYPE
     * 與 APPLICATION.TYPE 分成 非小額綠電類 與 小額綠電類 加總 (度數應進位[四捨五入到整數]至整數再統計加總
     * @param billDate 月初
     * @return
     */
    @Query(value =
            "Select vBill.BILL_DATE as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, '3' as powerType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION_GENERATOR_LOAD_TYPE as apGLType on settle.APPLICATION_LOAD_ID = apGLType.APPLICATION_LOAD_ID" +
                    "   and settle.APPLICATION_GENERATOR_ID = apGLType.APPLICATION_GENERATOR_ID" +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != 'Q'" +
                    " where vBill.BILL_DATE = ?1 group by vBill.BILL_DATE UNION " +
                    "Select vBill.BILL_DATE as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, '12' as powerType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION_GENERATOR_LOAD_TYPE as apGLType on settle.APPLICATION_LOAD_ID = apGLType.APPLICATION_LOAD_ID" +
                    "   and settle.APPLICATION_GENERATOR_ID = apGLType.APPLICATION_GENERATOR_ID and apGLType.TYPE = 3" +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2' and app.TYPE != 'Q'" +
                    " where vBill.BILL_DATE = ?1 group by vBill.BILL_DATE UNION " +
                    "Select vBill.BILL_DATE as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, '2' as powerType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION_GENERATOR_LOAD_TYPE as apGLType on settle.APPLICATION_LOAD_ID = apGLType.APPLICATION_LOAD_ID" +
                    "   and settle.APPLICATION_GENERATOR_ID = apGLType.APPLICATION_GENERATOR_ID and apGLType.TYPE = 2" +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2' and app.TYPE != 'Q'" +
                    " where vBill.BILL_DATE = ?1 group by vBill.BILL_DATE UNION " +
                    "Select vBill.BILL_DATE as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, '1' as powerType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION_GENERATOR_LOAD_TYPE as apGLType on settle.APPLICATION_LOAD_ID = apGLType.APPLICATION_LOAD_ID" +
                    "   and settle.APPLICATION_GENERATOR_ID = apGLType.APPLICATION_GENERATOR_ID and apGLType.TYPE = 1" +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2' and app.TYPE != 'Q'" +
                    " where vBill.BILL_DATE = ?1 group by vBill.BILL_DATE UNION " +
                    "Select vBill.BILL_DATE as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'Q' as powerType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE = 'Q'" +
                    " where vBill.BILL_DATE = ?1 group by vBill.BILL_DATE" +
                    " order by powerType ASC"
            , nativeQuery = true)
    List<Map<String, Object>> sumExpsByServiceDateBillDate(Date billDate);

    /** #12 各電壓層級度數報表(帳單限定年月搜尋) 業務處費率組 serviceDate(Date), billDate(Date), applicationGeneratorId(long), applicationLoadId(long)
     * 表格輸出當年份到 輸入月份之 累計 不同電壓層級累積資料 KWH[MATCHED_KW] = kwh EQUIP_VOLT_LEVEL[發電設備併接點電壓層級]
     * @param billDate 帳單月初
     * @return
     */
    @Query(value =
            "Select vBill.SERVICE_DATE as serviceDate, sum(settle.ANCILLARY_SERVICE_COST) as KWH, volLevel.LABEL as EQUIP_VOLT_LEVEL, volLevel.ID" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + joinSettleAppGen +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" + joinVoltageLevel +
                    " where vBill.BILL_DATE = ?1" +
                    "  group by vBill.SERVICE_DATE, volLevel.LABEL, volLevel.ID" +
                    "  order by vBill.SERVICE_DATE, volLevel.ID"
            , nativeQuery = true)
    List<Map<String, Object>> findExpVoltInfoByServiceDates(Date billDate);

    /** #10 小額綠電契約報表(起迄 帳單年月搜尋) applicationGeneratorId(long), applicationLoadId(long) serviceDate, billDate + 父契約相關欄位 contractNo, version
     * 表格 用電端(4欄位) + 申請者(4欄位) + 發電端(3欄位)
     * MO_CONTRACT_KWH(BigDecimal)[轉供月上限] YR_CONTRACT_KWH(BigDecimal)[轉供年上限] CUST_NAME[用戶名稱] ELEC_NO[用戶電號]
     * SERVICE_ID[契約號碼 = SON_CONTRACT(子契約號碼)] CONTRACT_EFFE_DATE[契約生效日期] TERMINATE_DATE[契約終止日期]
     * CONTRACT_EXEC_UNIT[契約執行單位]
     * GEN_ELEC_NO[電源電號] DEVICE_CAPACITY[裝置容量=APPLICATION_GENERATOR.LICENSE_CAPACITY] GEN_PW_PERCENT[電源轉供比例]
     * -- 舊的 DEVICE_CAPACITY = GENERATOR_ENTITY.CAPACITY_APPLIED
     * @param start 帳單月初
     * @param end 帳單隔月月初
     * @return
     */
    @Query(value =
            "Select vBill.SERVICE_DATE as serviceDate, vBill.BILL_DATE as billDate" +
                    ", settle.APPLICATION_GENERATOR_ID as applicationGeneratorId, settle.APPLICATION_LOAD_ID as applicationLoadId" +
                    ", lap.MONTHLY_CONTRACT_CAP as MO_CONTRACT_KWH, lap.ANNUAL_CONTRACT_CAP as YR_CONTRACT_KWH" +
                    ", lENtity.NAME as CUST_NAME, lEntity.NBS_CUSTOMER_NUMBER as ELEC_NO" +
                    ", app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, app.CONTRACTED_START as CONTRACT_EFFE_DATE" +
                    ", app.CONTRACTED_END as TERMINATE_DATE, app.CONTRACT_NO as contractNo, app.VERSION as version" +
                    ", tpCom.CODE as CONTRACT_EXEC_UNIT, ap.PMI as GEN_PW_PERCENT" +
                    ", gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO , ap.LICENSE_CAPACITY as DEVICE_CAPACITY" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " join APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinLoadEnd +
                    " join APPLICATION as app on app.ID = lap.APPLICATION_ID and app.TYPE = 'Q'" +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpCom on app.CONTRACT_DEPT_ID = tpCom.ID" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" +
                    " where vBill.SETTLEMENT_ID in (Select SETTLEMENT_ID from VIEW_SETTLEMENT_APPLICATION_NON_ERP" +
                    "   where BILL_DATE >= ?1 and BILL_DATE < ?2)" +
                    " order by VBill.BILL_DATE DESC, vBill.SERVICE_DATE ASC, settle.APPLICATION_GENERATOR_ID ASC, settle.APPLICATION_LOAD_ID ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findApplicationGeneratorLoadContractsByFixType(Date start, Date end);

    /**
     * #9 小額綠電 轉供度數(帳單起迄年月) 用電端(2欄位) + 申請者(1欄位) + 發電端(2欄位) 月結算(4欄位) -- EQUIP_FUEL_TYPE[使用燃料別(固定1 不排碳)]
     * 目前暫時輸入 APPLICATION.TYPE =1 (因沒有Q的契約資料, 但之後需要改回 Q
     * SINK_METER_NO[用戶表號] SINK_ELEC_NO[用戶電號] SERVICE_ID[契約號碼] CONTRACT_EXEC_UNIT[契約執行單位=TAIPOWER_COMPANY_UNIT.CODE]
     * SOURCE_METER_NO[電源表號] SOURCE_ELEC_NO[電源電號], KWH[轉供度數]
     * EXP[費用] 欄位 依序放置 月結算的 A_EXP(輔助費用), D_EXP(配電費用), S_EXP(調度費用), T_EXP(輸電費用)
     * @param billStart 月初
     * @param billEnd 隔月初
     * @return
     */
    @Query(value =
            "Select vBill.SERVICE_DATE as serviceDate, vBill.BILL_DATE as billDate, settle.SETTLEMENT_ID" +
                    ", settle.APPLICATION_GENERATOR_ID as applicationGeneratorId, settle.APPLICATION_LOAD_ID as applicationLoadId" +
                    ", settle.ANCILLARY_SERVICE_COST as A_EXP, settle.DISPATCH_SERVICE_COST as S_EXP, settle.POWER_TRANS_COST as T_EXP" +
                    ", settle.POWER_DIST_COST as D_EXP, settle.ADJUSTED_MATCHED_KW as KWH" +
                    ", lMeter.METER_NO as SINK_METER_NO, lC2.METER_NO as oLMeterNext, lEntity.NBS_CUSTOMER_NUMBER as SINK_ELEC_NO" +
                    ", app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, tpCom.CODE as CONTRACT_EXEC_UNIT" +
                    ", gMeter.METER_NO as SOURCE_METER_NO, gEntity.NBS_CUSTOMER_NUMBER as SOURCE_ELEC_NO" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " join APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinLoadEnd +
                    " Left outer join LOAD_ENTITY_METER as lMeter on lap.LOAD_METER_ID = lMeter.ID" +
                    joinLoadEntityChildNextMonthSettleServiceDate +
                    " join APPLICATION as app on app.ID = lap.APPLICATION_ID and app.TYPE = 'Q'" +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpCom on app.CONTRACT_DEPT_ID = tpCom.ID" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
                    " Left outer join GENERATOR_ENTITY_METER as gMeter on ap.GENERATOR_METER_ID = gMeter.ID" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" +
                    "  where vBill.BILL_DATE >= ?1 and vBill.BILL_DATE < ?2" +
                    " order by VBill.BILL_DATE DESC, vBill.SERVICE_DATE ASC, settle.APPLICATION_GENERATOR_ID ASC, settle.APPLICATION_LOAD_ID ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findApplicationGeneratorLoadSettleInfoByFixType(Date billStart, Date billEnd);

    /** #7 #14 15分鐘發電端媒合度數(帳單年月 搜尋) TransRelatRaw 資料 SETTLEMENT_ID + applicationGeneratorId + serviceTime + bllDate 需排除 彈性分配 APPLICATION.TYPE != 4
     *    , appType[方便換算 PWDS] 該列 運用 genMeterReplaceDate[發電端換表日] 搭配 oldGenMeterNo[舊發電端表號] 可替換 METER_NO[表號] + oGMeterNext[舊電表換表隔月換表號]
     * 業務處>再購組 每月發電端每15分鐘轉直供度數(3欄位+已併網裝置容量) GEN_METER_NO[表號]] GEN_ELEC_NO[電號] combineCapacity[已併網裝置容量
     * SERVICE_ID[轉供契約編號]
     * @param billDate 帳單月初
     * @return java.util.List
     */
    @Query(value =
            "Select DISTINCT settle.SETTLEMENT_ID, settle.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", vBill.SERVICE_DATE as serviceDate, gMeter.METER_NO as GEN_METER_NO" +
                    ", IIF(mCR.USE_TO is not null, mCR.USE_TO, gMeterChild.USE_TO) as genMeterReplaceDate, gMeterChild.METER_NO as oldGenMeterNo" +
                    ", gC2.METER_NO as oGMeterNext, gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO" +
                    ", app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, app.TYPE as appType" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " join APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID and vBill.TYPE != '4'" + joinGeneratorEnd +
                    " Left outer join GENERATOR_ENTITY_METER as gMeter on ap.GENERATOR_METER_ID = gMeter.ID" +
                    joinGenEntityChildTheMonthSettleServiceDate + joinGenEntityChildNextMonthSettleServiceDate +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '4'" +
                    " where vBill.BILL_DATE = ?1" +
                    " order by serviceDate, applicationGeneratorId ASC, genMeterReplaceDate DESC"
            , nativeQuery = true)
    List<Map<String, Object>> find15MinutesGeneratorSettleInfoByServiceDate(Date billDate);

    /** #11 轉直供用戶之各月轉直供資料 帳單起訖年月 KWH 1 3 9 11 用電端(4欄位)
     * 結帳相關 applicationLoadId, SETTLEMENT_ID ENERGY_CHARGE_SECTION_ID, KWH 結帳VIEW serviceDate
     * CUST_ELEC_NO[用電端電號] CONTR_TYPE[用電端契約類型] TIME_PRICE_STG[用電端段別] <- 用電契約類型 與 用電段別(不分段(0)/二段式(2)/三段式(3)) 從 NBS 取得 與 PWDS[轉供直供]
     * @param billStart
     * @param billEnd
     * @return
     */
    @Query(value =
            "Select DISTINCT vBill.SERVICE_DATE as serviceDate, settle.APPLICATION_LOAD_ID as applicationLoadId, settle.SETTLEMENT_ID" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) over (partition by settle.SETTLEMENT_ID, settle.APPLICATION_LOAD_ID, settle.ENERGY_CHARGE_SECTION_ID) as KWH" +
                    ", settle.ENERGY_CHARGE_SECTION_ID, lEntity.NBS_CUSTOMER_NUMBER as CUST_ELEC_NO" +
                    ", lEntity.CONTRACT_STG as CONTR_TYPE, lEntity.TIME_STG as TIME_PRICE_STG, app.TYPE as PWDS" +
                    " from APPLICATION_MONTHLY_CAPACITY_RECORD as settle" +
                    " join VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinLoadEnd +
                    " join APPLICATION as app on app.ID = lap.APPLICATION_ID" +
                    " where vBill.BILL_DATE >= ?1 and vBill.BILL_DATE < ?2" +
                    " order by vBill.SERVICE_DATE ASC, settle.APPLICATION_LOAD_ID ASC, settle.ENERGY_CHARGE_SECTION_ID, settle.SETTLEMENT_ID DESC"
            , nativeQuery = true)
    List<Map<String, Object>> findMonthlyApplicationLoadByBillDateRange(Date billStart, Date billEnd);

    /** #3 #4 每月轉直供度數資料(限定 帳單單月搜尋) serviceDate applicationGeneratorId, appType(提供 PWDS 欄位判斷) ENERGY_CHARGE_SECTION_ID KWH
     * 業務處>再購組 發電報表每月轉直供度數(9欄位) GEN_PW_PERCENT[轉供比率] METER_NO[表號] METER_REPLACED_DATE[換表日]
     * DEVICE_CAPACITY[裝置容量(電業執照)] GEN_ELEC_NO[電號] SERVICE_ID[轉供契約編號] TERMINATE_DATE[契約終止日]
     * CONTRACT_TYPE[契約類別]
     * @param billDate
     * @return
     */
    @Query(value =
            "Select DISTINCT vBill.SERVICE_DATE as serviceDate, settle.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", settle.ENERGY_CHARGE_SECTION_ID" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) over (partition by settle.APPLICATION_GENERATOR_ID, settle.ENERGY_CHARGE_SECTION_ID) as KWH" +
                    ", ap.PMI as GEN_PW_PERCENT, gMeter.METER_NO as METER_NO, IIF(mCR.USE_TO is not null, mCR.USE_TO, gMeterChild.USE_TO) as GEN_METER_CHANGE_DATE" +
                    ", gMeterChild.METER_NO as oldGenMeterNo, gC2.METER_NO as oGMeterNext" +
                    ", gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO, gEntity.CAPACITY_APPLIED as DEVICE_CAPACITY, gEntity.TIME_STG as TIME_PRICE_STG" +
                    ", app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, app.CONTRACTED_END as TERMINATE_DATE, app.TYPE as PWDS" +
                    ", apType.LABEL+'|'+apType.ID as CONTRACT_TYPE" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID" + joinGeneratorEnd +
                    " Left outer join GENERATOR_ENTITY_METER as gMeter on ap.GENERATOR_METER_ID = gMeter.ID" +
                    joinGenEntityChildTheMonthSettleServiceDate +
                    joinGenEntityChildNextMonthSettleServiceDate +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID" +
                    " join APPLICATION_TYPE as apType on app.TYPE = apType.ID" +
                    " where vBill.BILL_DATE = ?1" +
                    "  ORDER BY serviceDate ASC, applicationGeneratorId ASC, settle.ENERGY_CHARGE_SECTION_ID ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findApplicationGeneratorSettleInfo(Date billDate);

    /** 輸入 結帳年月 取得 該月 所有 settlementIdList
     * @param billDate
     * @return
     */
    @Query(value =
            "Select DISTINCT SETTLEMENT_ID from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP where BILL_DATE = ?1"
            , nativeQuery = true)
    List<Long> findAllSettlementId(Date billDate);

    /**
     * 服務[設備運作]日期(限定 單月 搜尋) 生成 applicationGeneratorIds
     * @param startServiceDate
     * @param endServiceDate 隔月首日
     * @return java.util.List
     */
    @Query(value =
            "Select DISTINCT settle.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    " from VIEW_BILL_SETTLEMENT_AND_NON_ERP as vBill" +
                    joinSettleAppGen +
                    " where vBill.SERVICE_DATE >= ?1 and vBill.SERVICE_DATE < ?2"
            , nativeQuery = true)
    List<Long> findApplicationGeneratorIdsByServiceDate(Date startServiceDate, Date endServiceDate);

    /** 單純讓測試 複查使用
     * @param billDate
     * @return
     */
    @Query(value = "Select CAST(APPLICATION_GENERATOR_ID as varchar(max))+'-'+CAST(APPLICATION_LOAD_ID as varchar(max))" +
            " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT" +
            "  join VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill on APPLICATION_MONTHLY_CAPACITY_SETTLEMENT.SETTLEMENT_ID = vBill.SETTLEMENT_ID" +
            "    and vBill.BILL_DATE = ?1", nativeQuery = true)
    List<String> findAllApplicationGeneratorIdApplicationLoadId(Date billDate);

    /**
     * flexible distribution的第一步驟
     * 1. 存媒合度數*裝置容量開票記帳比例
     * 2. 存對應發電端燃料別之費率
     * 
     * @author:  ting
     * @date:    2025/02/19 15:41:04
     * @param:   [settlementId, pwYear]
     * @return:  void
     **/
    @Transactional
    @Modifying
    @Query(value = "BEGIN               " +
            "        DECLARE @year SMALLINT = (SELECT TOP 1 YEAR FROM VIEW_PW_FUEL_RATE " +
            "                              WHERE YEAR = (SELECT YEAR(SERVICE_DATE) FROM SETTLEMENT_CALCULATION " +
            "                                            WHERE SETTLEMENT_ID = :settlementId)) " +
            "   DECLARE @calculateYear SMALLINT = IIF(@year IS NOT NULL , @year, (SELECT TOP 1 YEAR FROM VIEW_PW_FUEL_RATE))       " +
            "       " +
            "   INSERT INTO APPLICATION_MONTHLY_CAPACITY_RECORD(SETTLEMENT_ID       " +
            "          , ENERGY_CHARGE_SECTION_ID       " +
            "          , APPLICATION_GENERATOR_ID       " +
            "          , APPLICATION_LOAD_ID       " +
            "          , CAPACITY_CODE       " +
            "          , PERCENTAGE       " +
            "          , MATCHED_KW       " +
            "          , ADJUSTED_MATCHED_KW       " +
            "          , ANCILLARY_SERVICE_COST       " +
            "          , DISPATCH_SERVICE_COST       " +
            "          , POWER_TRANS_COST       " +
            "          , POWER_DIST_COST       " +
            "          , CALCULATED_AT) " +
            "   SELECT DISTINCT SCC.SETTLEMENT_ID       " +
            "        , AMFGLR.ENERGY_CHARGE_SECTION_ID       " +
            "        , AMFGLR.APPLICATION_GENERATOR_ID       " +
            "        , AMFGLR.APPLICATION_LOAD_ID       " +
            "        , SCC.CAPACITY_CODE       " +
            "        , SCC.PERCENTAGE       " +
            "        , AMFGLR.MATCHED_RM * SCC.PERCENTAGE       " +
            "        , FLOOR(AMFGLR.MATCHED_RM * SCC.PERCENTAGE)       " +
            "        , ANCILLARY       " +
            "        , DISPATCH       " +
            "        , IIF(AGLT.TYPE = 1 OR AGLT.TYPE = 3 , TRANSMISSION, 0)       " +
            "        , IIF(AGLT.TYPE = 2 OR AGLT.TYPE = 3 , DISTRIBUTION, 0)       " +
            "        , GETDATE() " +
            "   FROM SETTLEMENT_CAPACITY_CALCULATION SCC       " +
            "            INNER JOIN APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD AMFGLR       " +
            "                       ON AMFGLR.SETTLEMENT_ID = SCC.SETTLEMENT_ID       " +
            "                           AND AMFGLR.APPLICATION_GENERATOR_ID = SCC.APPLICATION_GENERATOR_ID       " +
            "            INNER JOIN APPLICATION_GENERATOR AS AG       " +
            "                       ON AG.ID = SCC.APPLICATION_GENERATOR_ID       " +
            "            INNER JOIN GENERATOR_ENTITY AS GE       " +
            "                       ON GE.ID = AG.GENERATOR_ID       " +
            "            INNER JOIN FUEL_TYPE AS FT       " +
            "                       ON GE.FUEL_TYPE = FT.ID       " +
            "            INNER JOIN VIEW_PW_FUEL_RATE AS VPFR       " +
            "                       ON VPFR.FUEL_TYPE_ID = FT.PW_FUEL_TYPE_ID       " +
            "            INNER JOIN APPLICATION_GENERATOR_LOAD_TYPE AS AGLT       " +
            "                       ON AGLT.APPLICATION_GENERATOR_ID = AMFGLR.APPLICATION_GENERATOR_ID       " +
            "                           AND AGLT.APPLICATION_LOAD_ID = AMFGLR.APPLICATION_LOAD_ID       " +
            "   WHERE SCC.SETTLEMENT_ID = :settlementId       " +
            "     AND SCC.PERCENTAGE IS NOT NULL "+
            "     AND VPFR.YEAR = @calculateYear       " +
            "END ", nativeQuery = true)
    void saveAllFlexibleDistributionBeforeAdjustMatchedKw(@Param("settlementId") Long settlementId);


    /**
     * flexible distribution的第二步驟
     * after adjust matched_kw (取整)
     * @author:  ting
     * @date:    2025/02/19 16:10:42
     * @param:   [settlementId]
     * @return:  void
     **/
    @Transactional
    @Modifying
    @Query(value = "UPDATE AMCS    " +
            "    SET AMCS.ANCILLARY_SERVICE_COST = ROUND(AMCS.ADJUSTED_MATCHED_KW * AMCS.ANCILLARY_SERVICE_COST, 0)    " +
            "      , AMCS.DISPATCH_SERVICE_COST = ROUND(AMCS.ADJUSTED_MATCHED_KW * AMCS.DISPATCH_SERVICE_COST, 0)    " +
            "      , AMCS.POWER_TRANS_COST = ROUND(AMCS.ADJUSTED_MATCHED_KW * AMCS.POWER_TRANS_COST, 0)    " +
            "      , AMCS.POWER_DIST_COST = ROUND(AMCS.ADJUSTED_MATCHED_KW * AMCS.POWER_DIST_COST, 0)    " +
            "    FROM APPLICATION_MONTHLY_CAPACITY_RECORD AMCS    " +
            "        WHERE SETTLEMENT_ID = :settlementId ", nativeQuery = true)
    void updateAllFlexibleDistributionAfterAdjustMatchedKw(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = "BEGIN    " +
            "    DELETE APPLICATION_MONTHLY_SETTLEMENT    " +
            "        WHERE SETTLEMENT_ID = :settlementId    " +
            "          AND APPLICATION_GENERATOR_ID IN (:appGenIdList)    " +
            "          AND APPLICATION_LOAD_ID IN (:appLoadIdList)    " +
            "    DELETE APPLICATION_MONTHLY_CAPACITY_SETTLEMENT    " +
            "        WHERE SETTLEMENT_ID = :settlementId    " +
            "          AND APPLICATION_GENERATOR_ID IN (:appGenIdList)    " +
            "          AND APPLICATION_LOAD_ID IN (:appLoadIdList)    " +
            "END ", nativeQuery = true)
    void deleteRecordBySettlementId(@Param("settlementId") Long settlementId
            , @Param("appGenIdList") List<Long> appGenIdList, @Param("appLoadIdList") List<Long> appLoadIdList);


    @Query(value = "SELECT LOAD_CUSTOMER_NUMBER, SUM_MATCHED_KW, SUM_ADJUSTED_KW      " +
            "          FROM (SELECT LOAD_CUSTOMER_NUMBER, SUM(MATCHED_KW) AS SUM_MATCHED_KW, SUM(ADJUSTED_MATCHED_KW) AS SUM_ADJUSTED_KW      " +
            "              FROM (SELECT DISTINCT VALM.NBS_CUSTOMER_NUMBER AS LOAD_CUSTOMER_NUMBER, MATCHED_KW, ADJUSTED_MATCHED_KW      " +
            "                  FROM APPLICATION_MONTHLY_CAPACITY_RECORD AMCR      " +
            "                           INNER JOIN VIEW_APPLICATION_LOAD_METER VALM      " +
            "                                      ON VALM.APPLICATION_LOAD_ID = AMCR.APPLICATION_LOAD_ID      " +
            "                  WHERE SETTLEMENT_ID = :settlementId) SUBQUERY      " +
            "                  GROUP BY SUBQUERY.LOAD_CUSTOMER_NUMBER) THIRDQUERY      " +
            "              WHERE FLOOR(SUM_MATCHED_KW) != SUM_ADJUSTED_KW", nativeQuery = true)
    List<Map<String, Object>> findNeedAdjustMatchedKw(@Param("settlementId") Long settlementId);

    @Query(value = "SELECT  * FROM APPLICATION_MONTHLY_CAPACITY_RECORD AMCR       " +
            "      WHERE APPLICATION_LOAD_ID IN (       " +
            "          SELECT APPLICATION_LOAD_ID FROM VIEW_APPLICATION_LOAD_METER VALM       " +
            "           WHERE  NBS_CUSTOMER_NUMBER = :loadCustomer)       " +
            "      AND SETTLEMENT_ID = :settlementId       " +
            "      ORDER BY MATCHED_KW DESC", nativeQuery = true)
    List<ApplicationMonthlyCapacityRecord> findByDateAndSettlementIdAndLoadCustomerNumber(@Param("settlementId") Long settlementId, @Param("loadCustomer") String loadCustomer);

}
