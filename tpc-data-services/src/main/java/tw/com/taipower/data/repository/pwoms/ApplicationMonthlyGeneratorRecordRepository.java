package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorRecord;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorRecordColumnId;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static tw.com.taipower.data.constant.Constants.*;


/**
 * Repository of ApplicationMonthlyGeneratorRecord
 *
 * @class: ApplicationMonthlyGeneratorRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-30 17:27
 * @see:
 **/

public interface ApplicationMonthlyGeneratorRecordRepository extends JpaRepository<ApplicationMonthlyGeneratorRecord, ApplicationMonthlyGeneratorRecordColumnId> {

    /**
     * #9 小額綠電  KWH 1 3 9 11 媒合電量(起迄結帳年月) 搜尋 使用 月初 + 跨月份月底 + SETTLEMENT_ID
     * -- 月結算資料 - 媒合電量資料 serviceDate, applicationGeneratorId, applicationLoadId, energyChangeSectionId
     * -- 組合3表 取出 1,3,9,11 媒合電量 matchedRm APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD(使用ADJUSTED_MATCHED_RM 電量)
     *        + APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD(使用 ADJUSTED_MATCHED_RM 電量)
     * @param billStart 月初
     * @param billEnd 隔月初
     * @return
     */
    @Query(value =
            "Select apMGenLoad.APPLICATION_GENERATOR_ID as applicationGeneratorId, apMGenLoad.APPLICATION_LOAD_ID as applicationLoadId" +
                    ", apMGenLoad.DATE as serviceDate, apMGenLoad.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId" +
                    ", apMGenLoad.ADJUSTED_MATCHED_RM as matchedRm from APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD as apMGenLoad" +
                    " where apMGenLoad.SETTLEMENT_ID in (Select SETTLEMENT_ID from VIEW_SETTLEMENT_APPLICATION_NON_ERP" +
                    "   where BILL_DATE >= ?1 and BILL_DATE < ?2)" +
                    " Union " +
                    "Select apMoDGenLoad.APPLICATION_GENERATOR_ID as applicationGeneratorId, apMoDGenLoad.APPLICATION_LOAD_ID as applicationLoadId" +
                    ", apMoDGenLoad.DATE as serviceDate, apMoDGenLoad.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId" +
                    ", apMoDGenLoad.ADJUSTED_MATCHED_RM as matchedRm from APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD as apMoDGenLoad" +
                    " where apMoDGenLoad.SETTLEMENT_ID in (Select SETTLEMENT_ID from VIEW_SETTLEMENT_APPLICATION_NON_ERP" +
                    "   where BILL_DATE >= ?1 and BILL_DATE < ?2)" +
                    " order by serviceDate ASC, applicationGeneratorId ASC, applicationLoadId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findMatchedByServiceDateRange(Date billStart, Date billEnd);

    /**
     * #3 #4 月發電媒合度數(限定 單月 搜尋) 1 3 9 11 媒合電量 KWH applicationGeneratorId, energyChangeSectionId, matchedRm + SETTLEMENT_ID
     *    + ENERGY_CHARGE 搭配固定 TIME_PRICE_STAGE[ENERGY_CHARGE.TIME_SLOT]= 3 (不需要夏天判斷 ENERGY_CHARGE.IS_SUMMER 判斷)
     * 合併3表 轉供媒合+直供媒合+彈性分配媒合表 APPLICATION_MONTHLY_GENERATOR_RECORD + APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD
     * + APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD
     * 外部報表 - 業務處>再購組 發電業報表 - 月轉直供度數(1欄位)
     * TIME_PRICE_STAGE[時間電價段別 =3 GENERATOR_ENTITY.TIME_STG] 若有值直接呈現, null無資料 則直接指定 = 3, 0:非時間電價 2,3:時間電價時段
     * 發電報表每月轉直供度數 4時段(+4欄位){energyChangeSectionId:matchedRm} = 1[1表(01)] 半尖峰 3[3表(03)] 離峰 9[9表(09)] 尖峰 11[11表(11)] 週六半尖峰
     * 此為 APPLICATION_MONTHLY_GENERATOR_RECORD + APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD + ENERGY_CHARGE 組合 3表
     * @param serviceDate
     * @return java.util.List
     */
    @Query(value =
            "Select apMoGenRecord.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", apMoGenRecord.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId, apMoGenRecord.MATCHED_RM as matchedRm" +
                    ", null as dirMatched, gEntity.TIME_STG as TIME_PRICE_STAGE from APPLICATION_MONTHLY_GENERATOR_RECORD as apMoGenRecord" +
                    " inner join (Select DATE, ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, MAX(SETTLEMENT_ID) as settleId" +
                    "   from APPLICATION_MONTHLY_GENERATOR_RECORD group by DATE, ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID) cc" +
                    "  on apMoGenRecord.DATE=cc.DATE and apMoGenRecord.ENERGY_CHARGE_SECTION_ID=cc.ENERGY_CHARGE_SECTION_ID" +
                    "     and apMoGenRecord.APPLICATION_GENERATOR_ID=cc.APPLICATION_GENERATOR_ID and (apMoGenRecord.SETTLEMENT_ID=cc.settleId or cc.settleId is null)" +
                    " join APPLICATION_GENERATOR as ap on ap.ID = apMoGenRecord.APPLICATION_GENERATOR_ID" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" +
                    " where apMoGenRecord.DATE = ?1 Union " +
                    "Select apMoDGenRecode.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", apMoDGenRecode.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId, null as matchedRm" +
                    ", apMoDGenRecode.MATCHED_RM as dirMatched, gEntity.TIME_STG as TIME_PRICE_STAGE" +
                    " from APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD as apMoDGenRecode" +
                    " inner join (Select DATE, ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, MAX(SETTLEMENT_ID) as settleId" +
                    "   from APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD group by DATE, ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID) cc" +
                    "  on apMoDGenRecode.DATE=cc.DATE and apMoDGenRecode.ENERGY_CHARGE_SECTION_ID=cc.ENERGY_CHARGE_SECTION_ID" +
                    "    and apMoDGenRecode.APPLICATION_GENERATOR_ID=cc.APPLICATION_GENERATOR_ID and (apMoDGenRecode.SETTLEMENT_ID=cc.settleId or cc.settleId is null)" +
                    " join APPLICATION_GENERATOR as ap on ap.ID = apMoDGenRecode.APPLICATION_GENERATOR_ID" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" +
                    " where apMoDGenRecode.DATE = ?1 " +
                    "Union Select apMoFGenLoad.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", apMoFGenLoad.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId" +
                    ", sum(apMoFGenLoad.MATCHED_RM) as matchedRm, null as dirMatched, gEntity.TIME_STG as TIME_PRICE_STAGE" +
                    " from APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD as apMoFGenLoad" +
                    " inner join (Select DATE, ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, MAX(SETTLEMENT_ID) as settleId" +
                    "   from APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD group by DATE, ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID) cc" +
                    "  on apMoFGenLoad.DATE=cc.DATE and apMoFGenLoad.ENERGY_CHARGE_SECTION_ID=cc.ENERGY_CHARGE_SECTION_ID" +
                    "    and apMoFGenLoad.APPLICATION_GENERATOR_ID=cc.APPLICATION_GENERATOR_ID and (apMoFGenLoad.SETTLEMENT_ID=cc.settleId or cc.settleId is null)" +
                    " join APPLICATION_GENERATOR as ap on ap.ID = apMoFGenLoad.APPLICATION_GENERATOR_ID" +
                    " join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID" +
                    " where apMoFGenLoad.DATE = ?1" +
                    " group by apMoFGenLoad.APPLICATION_GENERATOR_ID, apMoFGenLoad.ENERGY_CHARGE_SECTION_ID, gEntity.TIME_STG" +
                    " order by applicationGeneratorId ASC, energyChangeSectionId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findMatchedRmTimeSlotByApplicationGeneratorIdsDate(Date serviceDate);

    /***
     * unmatched_rm 計算剩餘量
     * sql
     *    SELECT APPLICATION_GENERATOR_ID, SUM(UNMATCHED_RM) AS SUM_UNMATCHED_RM, SUM(MATCHED_RM) AS SUM_MATCHED_RM
     *    FROM [pwoms].[dbo].[APPLICATION_MONTHLY_GENERATOR_RECORD]
     *    WHERE DATE BETWEEN '2024-05-01' and '2024-05-03'
     *    GROUP BY APPLICATION_GENERATOR_ID
     *
     * @author:  ting
     * @date:    2024/06/23 12:54:12
     * @param:   [startTime, endTime]
     * @return:  java.util.List<?>
     **/

    @Query("  SELECT new map( generatorId AS " + FIELD_NAME_APPLICATION_GENERATOR_LOAD_ID +
            ", SUM(unmatchedRm) AS " + FIELD_NAME_SUM_UNMATCHED_CAPCAITY +
            ", SUM(matchedRm) AS " + FIELD_NAME_SUM_MATCHED_CAPACITY + ")" +
            "   FROM ApplicationMonthlyGeneratorRecord" +
            "   WHERE date BETWEEN (?1) AND (?2)" +
            "   GROUP BY generatorId")
    List<?> sumByDateGroupByApplicationGeneratorId(Date startTime, Date endTime);

    /***
     *  sql
     *    SELECT APPLICATION_GENERATOR_ID, SUM(UNMATCHED_RM) AS SUM_UNMATCHED_RM, SUM(MATCHED_RM) AS SUM_MATCHED_RM
     *    FROM [pwoms].[dbo].[APPLICATION_MONTHLY_GENERATOR_RECORD]
     *    WHERE DATE BETWEEN '2024-05-01' and '2024-05-03'
     *    GROUP BY APPLICATION_GENERATOR_ID
     *
     * @author:  ting
     * @date:    2024/06/23 13:45:11
     * @param:   [startTime, endTime]
     * @return:  java.util.List<?>
     **/

    @Query("   SELECT new map(generatorId AS " + FIELD_NAME_APPLICATION_GENERATOR_LOAD_ID +
            ", energyChargeSectionId " + FIELD_NAME_ENERGY_CHARGE_SECTION_ID +
            ", SUM(unmatchedRm) AS " + FIELD_NAME_SUM_UNMATCHED_CAPCAITY +
            ", SUM(matchedRm) AS " + FIELD_NAME_SUM_MATCHED_CAPACITY + ")" +
            "   FROM ApplicationMonthlyGeneratorRecord" +
            "   WHERE date BETWEEN (?1) AND (?2)" +
            "   GROUP BY generatorId, energyChargeSectionId " +
            "   ORDER BY generatorId ASC")
    List<?> sumByDateGroupByApplicationGeneratorIdAndEnergyChargeSectionId(Date startTime, Date endTime);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_MONTHLY_GENERATOR_RECORD WHERE DATE = ?1", nativeQuery = true)
    void deleteByDate(Date date);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_MONTHLY_GENERATOR_RECORD   " +
            "WHERE APPLICATION_GENERATOR_ID IN   " +
            "      (SELECT APPLICATION_GENERATOR_ID FROM APPLICATION_MONTHLY_GENERATOR_RECORD AS AMGR   " +
            "            INNER JOIN  APPLICATION_GENERATOR AS AG   " +
            "            ON AMGR.APPLICATION_GENERATOR_ID = AG.ID   " +
            "            INNER JOIN APPLICATION AS A   " +
            "            ON AG.APPLICATION_ID = A.ID   " +
            "       WHERE TYPE IN (:appTypeList)   " +
            "         AND AMGR.DATE = :date )", nativeQuery = true)
    void deleteByDateAndApplicationTypeIn(@Param("date") Date date, @Param("appTypeList") List<String> appTypeList);
}