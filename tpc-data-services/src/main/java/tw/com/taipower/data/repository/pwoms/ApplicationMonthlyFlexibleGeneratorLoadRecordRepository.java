package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyFlexibleGeneratorLoadRecord;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorLoadRecordColumnId;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of ApplicationMonthlyFlexibleGeneratorLoadRecord
 *
 * @class: ApplicationMonthlyFlexibleGeneratorLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-09-10 20:50
 * @see:
 **/

public interface ApplicationMonthlyFlexibleGeneratorLoadRecordRepository extends JpaRepository<ApplicationMonthlyFlexibleGeneratorLoadRecord, ApplicationMonthlyGeneratorLoadRecordColumnId> {

    String innerSettleLoad = " inner join(Select APPLICATION_LOAD_ID, DATE, MAX(SETTLEMENT_ID) as settle" +
            "                    from APPLICATION_MONTHLY_LOAD_RECORD group by APPLICATION_LOAD_ID, DATE) cc" +
            "                   on apLoad.APPLICATION_LOAD_ID = cc.APPLICATION_LOAD_ID and apLoad.DATE = cc.DATE and apLoad.SETTLEMENT_ID = cc.settle";
    String innerSettleDLoad = " inner join(Select APPLICATION_LOAD_ID, DATE, MAX(SETTLEMENT_ID) as settle" +
            "                    from APPLICATION_MONTHLY_DIRECT_LOAD_RECORD group by APPLICATION_LOAD_ID, DATE) cc" +
            "                   on apDLoad.APPLICATION_LOAD_ID = cc.APPLICATION_LOAD_ID and apDLoad.DATE = cc.DATE and apDLoad.SETTLEMENT_ID = cc.settle";
    String innerSettleFLoad = " inner join(Select APPLICATION_LOAD_ID, DATE, MAX(SETTLEMENT_ID) as settle" +
            "                    from APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD group by APPLICATION_LOAD_ID, DATE) cc" +
            "                   on apFLoad.APPLICATION_LOAD_ID = cc.APPLICATION_LOAD_ID and apFLoad.DATE = cc.DATE and apFLoad.SETTLEMENT_ID = cc.settle";

    /**
     * #11 用電端月結算資料(起迄 年月搜尋) 4時段資料 轉供+直供+彈性分配 applicationLoadId, energyChangeSectionId, matchedCn + SETTLEMENT_ID
     *    , serviceDate(服務[設備運作]日期)
     * 合併3表格 APPLICATION_MONTHLY_LOAD_RECORD + APPLICATION_MONTHLY_DIRECT_LOAD_RECORD
     *       + APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD 取出 energyChangeSectionId
     * 4時段(+4欄位){energyChangeSectionId:matchedCn} = 1[1表(01)] 半尖峰 3[3表(03)] 離峰 9[9表(09)] 尖峰 11[11表(11)] 週六半尖峰
     * @param billStart
     * @param billEnd nextMonthDay
     * @return
     */
    @Query(value =
            "Select apLoad.APPLICATION_LOAD_ID as applicationLoadId, apLoad.DATE as serviceDate" +
                    ", apLoad.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId, apLoad.MATCHED_CN as matchedCn" +
                    " from APPLICATION_MONTHLY_LOAD_RECORD as apLoad" + innerSettleLoad +
                    " where apLoad.SETTLEMENT_ID in (Select DISTINCT SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "   where CALCULATION_METHOD in (3,5,6) and EXECUTION_START >= ?1 and EXECUTION_START < ?2) Union " +
                    "Select apDLoad.APPLICATION_LOAD_ID as applicationLoadId, apDLoad.DATE as serviceDate" +
                    ", apDLoad.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId, apDLoad.MATCHED_CN as matchedCn" +
                    " from APPLICATION_MONTHLY_DIRECT_LOAD_RECORD as apDLoad" + innerSettleDLoad +
                    " where apDLoad.SETTLEMENT_ID in (Select DISTINCT SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "   where CALCULATION_METHOD in (3,5,6) and EXECUTION_START >= ?1 and EXECUTION_START < ?2) Union " +
                    "Select apFLoad.APPLICATION_LOAD_ID as applicationLoadId, apFLoad.DATE as serviceDate" +
                    ", apFLoad.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId, apFLoad.MATCHED_RM as matchedCn" +
                    " from APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD as apFLoad" + innerSettleFLoad +
                    " where apFLoad.SETTLEMENT_ID in (Select DISTINCT SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "   where CALCULATION_METHOD in (3,5,6) and EXECUTION_START >= ?1 and EXECUTION_START < ?2)" +
                    " order by serviceDate ASC, applicationLoadId ASC, energyChangeSectionId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findLoadsMatchedRmByApplicationLoadIds(Date billStart, Date billEnd);

    @Transactional
    @Modifying
    @Query(value ="BEGIN " +
            "    INSERT INTO APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD(DATE, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID " +
            "        , ENERGY_CHARGE_SECTION_ID, MATCHED_RM) " +
            "        SELECT FDUC.SERVICE_DATE,  VFAGL.APPLICATION_GENERATOR_ID, VFAGL.APPLICATION_LOAD_ID " +
            "             , FDUC.METER_TYPE, FDUC.KWH " +
            "               FROM  FLEXIBLE_DISTRIBUTION_UPLOAD_CSV AS FDUC " +
            "                   INNER JOIN VIEW_FLEXIBLE_APPLICATION_GENERATOR_LOAD AS VFAGL " +
            "                   ON FDUC.SERVICE_DATE = :date " +
            "                   AND FDUC.SERVICE_ID = VFAGL.CONTRACT_NO_VERSION " +
            "                   AND FDUC.GEN_ELEC_NO = VFAGL.GEN_NBS_CUSTOMER_NUMBER " +
            "                   AND FDUC.CUST_ELEC_NO = VFAGL.LOAD_NBS_CUSTOMER_NUMBER " +
            "        WHERE CONTRACT_NO_VERSION IN (:contractNoVerList) " +
            "            AND CONTRACT_STATUS != -1 " +
            "            AND CONTRACTED_START <= :date " +
            "            AND (CONTRACTED_END >= :date OR (CONTRACTED_END IS NULL)) " +
            "END ", nativeQuery = true)
    void saveAllByDateAndContractNoIn(@Param("date") Date date, @Param("contractNoVerList")List<String> contractNoVerList);

    /**
     * sql :
     * BEGIN
     *     DELETE FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD WHERE DATE = :date
     *
     *     INSERT INTO APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD(DATE, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID
     *         , ENERGY_CHARGE_SECTION_ID, MATCHED_RM)
     *         SELECT FDUC.SERVICE_DATE,  VFAGL.APPLICATION_GENERATOR_ID, VFAGL.APPLICATION_LOAD_ID
     *              , FDUC.METER_TYPE, FDUC.KWH
     *                FROM  FLEXIBLE_DISTRIBUTION_UPLOAD_CSV AS FDUC
     *                    INNER JOIN VIEW_FLEXIBLE_APPLICATION_GENERATOR_LOAD AS VFAGL
     *                    ON FDUC.SERVICE_DATE = :date
     *                    AND FDUC.SERVICE_ID = VFAGL.CONTRACT_NO_VERSION
     *                    AND FDUC.GEN_ELEC_NO = VFAGL.GEN_NBS_CUSTOMER_NUMBER
     *                    AND FDUC.CUST_ELEC_NO = VFAGL.LOAD_NBS_CUSTOMER_NUMBER
     *         WHERE CONTRACT_NO_VERSION IN (:contractNoVerList)
     *             AND CONTRACT_STATUS != -1
     *             AND CONTRACTED_START <= :date
     *             AND (CONTRACTED_END >= :date OR (CONTRACTED_END IS NULL))
     * END
     * @author:  ting
     * @date:    2024/09/16 14:48:03
     * @param:   [date]
     * @return:  void
     **/

    @Transactional
    @Modifying
    @Query(value ="BEGIN " +
            "DELETE FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD WHERE DATE = :date " +
            "    INSERT INTO APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD(DATE, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID " +
            "        , ENERGY_CHARGE_SECTION_ID, MATCHED_RM) " +
            "        SELECT FDUC.SERVICE_DATE,  VFAGL.APPLICATION_GENERATOR_ID, VFAGL.APPLICATION_LOAD_ID " +
            "             , FDUC.METER_TYPE, FDUC.KWH " +
            "               FROM  FLEXIBLE_DISTRIBUTION_UPLOAD_CSV AS FDUC " +
            "                   INNER JOIN VIEW_FLEXIBLE_APPLICATION_GENERATOR_LOAD AS VFAGL " +
            "                   ON FDUC.SERVICE_DATE = :date " +
            "                   AND FDUC.SERVICE_ID = VFAGL.CONTRACT_NO_VERSION " +
            "                   AND FDUC.GEN_ELEC_NO = VFAGL.GEN_NBS_CUSTOMER_NUMBER " +
            "                   AND FDUC.CUST_ELEC_NO = VFAGL.LOAD_NBS_CUSTOMER_NUMBER " +
            "        WHERE CONTRACTED_START <= :date " +
            "            AND CONTRACT_STATUS != -1 " +
            "            AND (CONTRACTED_END >= :date OR (CONTRACTED_END IS NULL)) " +
            "END ", nativeQuery = true)
    void saveAllByDate(@Param("date") Date date);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD WHERE DATE = :date", nativeQuery = true)
    void deleteByDate(@Param("date") Date date);

    @Query(value = "SELECT DISTINCT (AG.APPLICATION_ID) FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD AS AMFGLR " +
            "INNER JOIN dbo.APPLICATION_GENERATOR AG on AMFGLR.APPLICATION_GENERATOR_ID = AG.ID " +
            "WHERE DATE = :date", nativeQuery = true)
    List<Long> findApplicationIdByDate(@Param("date") Date date);

    @Transactional
    @Modifying
    @Query(value =
            "BEGIN " +
            "    INSERT INTO APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_GENERATOR_ID " +
            "        , APPLICATION_LOAD_ID " +
            "        , MATCHED_RM " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_GENERATOR_ID " +
            "         , APPLICATION_LOAD_ID " +
            "         , MATCHED_RM " +
            "    FROM TEMP_APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            " " +
            "    INSERT INTO APPLICATION_MONTHLY_SETTLEMENT( " +
            "                SERVICE_DATE " +
            "              , BILLING_DATE " +
            "              , APPLICATION_GENERATOR_ID " +
            "              , APPLICATION_LOAD_ID " +
            "              , ANCILLARY_SERVICE_COST " +
            "              , DISPATCH_SERVICE_COST " +
            "              , POWER_TRANS_COST " +
            "              , POWER_DIST_COST " +
            "              , CALCULATED_AT " +
            "              , COMBINED_CAPACITY " +
            "              , LICENSE_CAPACITY " +
            "              , MATCHED_KW  " +
            "    ) " +
            "    SELECT " +
            "        SERVICE_DATE " +
            "         , BILLING_DATE " +
            "         , APPLICATION_GENERATOR_ID " +
            "         , APPLICATION_LOAD_ID " +
            "         , ANCILLARY_SERVICE_COST " +
            "         , DISPATCH_SERVICE_COST " +
            "         , POWER_TRANS_COST " +
            "         , POWER_DIST_COST " +
            "         , CALCULATED_AT " +
            "         , COMBINED_CAPACITY " +
            "         , LICENSE_CAPACITY " +
            "         , MATCHED_KW  " +
            "    FROM TEMP_APPLICATION_MONTHLY_SETTLEMENT " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            " " +
            "    INSERT INTO APPLICATION_MONTHLY_BILL( " +
            "          SERVICE_DATE " +
            "        , BILLING_DATE " +
            "        , APPLICATION_GENERATOR_ID " +
            "        , APPLICATION_LOAD_ID " +
            "        , BILLING_RATIO " +
            "        , BILLING_ANCILLARY_SERVICE_COST " +
            "        , BILLING_DISPATCH_SERVICE_COST " +
            "        , BILLING_POWER_TRANS_COST " +
            "        , BILLING_POWER_DIST_COST " +
            "        , CREDIT_RATIO " +
            "        , CREDIT_ANCILLARY_SERVICE_COST " +
            "        , CREDIT_DISPATCH_SERVICE_COST " +
            "        , CREDIT_POWER_TRANS_COST " +
            "        , CREDIT_POWER_DIST_COST " +
            "    ) " +
            "    SELECT " +
            "        SERVICE_DATE " +
            "         , BILLING_DATE " +
            "         , APPLICATION_GENERATOR_ID " +
            "         , APPLICATION_LOAD_ID " +
            "         , BILLING_RATIO " +
            "         , BILLING_ANCILLARY_SERVICE_COST " +
            "         , BILLING_DISPATCH_SERVICE_COST " +
            "         , BILLING_POWER_TRANS_COST " +
            "         , BILLING_POWER_DIST_COST " +
            "         , CREDIT_RATIO " +
            "         , CREDIT_ANCILLARY_SERVICE_COST " +
            "         , CREDIT_DISPATCH_SERVICE_COST " +
            "         , CREDIT_POWER_TRANS_COST " +
            "         , CREDIT_POWER_DIST_COST " +
            "    FROM TEMP_APPLICATION_MONTHLY_BILL " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            "END", nativeQuery = true)
    void saveFromTempTableBySettlementId(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value ="BEGIN " +
            "      INSERT INTO APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD(DATE, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID   " +
            "          , ENERGY_CHARGE_SECTION_ID, MATCHED_RM, SETTLEMENT_ID)   " +
            "          SELECT FDUC.SERVICE_DATE,  VFAGL.APPLICATION_GENERATOR_ID, VFAGL.APPLICATION_LOAD_ID   " +
            "               , FDUC.METER_TYPE, FDUC.KWH,  :settlementId   " +
            "                 FROM  FLEXIBLE_DISTRIBUTION_UPLOAD_CSV AS FDUC   " +
            "                     INNER JOIN VIEW_FLEXIBLE_APPLICATION_GENERATOR_LOAD AS VFAGL   " +
            "                     ON FDUC.SERVICE_DATE = :date   " +
            "                     AND FDUC.SERVICE_ID = VFAGL.CONTRACT_NO_VERSION   " +
            "                     AND FDUC.GEN_ELEC_NO = VFAGL.GEN_NBS_CUSTOMER_NUMBER   " +
            "                     AND FDUC.CUST_ELEC_NO = VFAGL.LOAD_NBS_CUSTOMER_NUMBER   " +
            "          WHERE CONTRACTED_START <= :date   " +
            "              AND CONTRACT_STATUS != -1   " +
            "              AND (CONTRACTED_END >= :date OR (CONTRACTED_END IS NULL))   " +
            "  END", nativeQuery = true)
    void saveAllByDateAndSettlementId(@Param("date") Date date, @Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value ="DELETE FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD                 " +
            "    WHERE SETTLEMENT_ID = :settlementId        " +
            "       AND APPLICATION_GENERATOR_ID IN (        " +
            "       SELECT AG.ID FROM APPLICATION_GENERATOR AG        " +
            "            WHERE APPLICATION_ID IN (:appIdList)        " +
            ") ", nativeQuery = true)
    void deleteBySettlementIdAndApplicationId(@Param("settlementId") Long settlementId
            , @Param("appIdList") List<Long> appIdList);

    @Transactional
    @Modifying
    @Query(value ="BEGIN " +
            "    DELETE FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD " +
            "        WHERE SETTLEMENT_ID = :settlementId " +
            "                AND APPLICATION_GENERATOR_ID IN ( " +
            "                SELECT AG.APPLICATION_ID FROM APPLICATION_GENERATOR AG " +
            "                WHERE APPLICATION_ID IN (:appIdList) " +
            "           ) " +
            " " +
            "    DELETE FROM APPLICATION_MONTHLY_SETTLEMENT " +
            "        WHERE SETTLEMENT_ID = :settlementId " +
            "          AND APPLICATION_GENERATOR_ID IN ( " +
            "                SELECT AG.APPLICATION_ID FROM APPLICATION_GENERATOR AG " +
            "                    WHERE APPLICATION_ID IN (:appIdList) " +
            "            ) " +
            "END", nativeQuery = true)
    void deleteAllMonthlyByDateAndSettlementIdAndApplicationId(@Param("settlementId") Long settlementId
            , @Param("appIdList") List<Long> appIdList);
}