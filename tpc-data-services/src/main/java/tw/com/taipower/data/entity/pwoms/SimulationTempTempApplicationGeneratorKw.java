package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Persistable;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Temp Application Generator Kw
 *
 * @class: SimulationTempTempApplicationGeneratorKw
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-14 21:40
 * @see:
 **/

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Entity
@IdClass(TempTempApplicationGeneratorKwId.class)
@Table(name = "SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW")
public class SimulationTempTempApplicationGeneratorKw implements Persistable<TempTempApplicationGeneratorKwId> {

    @Id
    @Column(name = "DATETIME")
    private Date datetime;

    @Id
    @Column(name = "APPLICATION_GENERATOR_ID")
    private Long appGeneratorId;

    @Id
    @Column(name = "ENERGY_CHARGE_SECTION_ID")
    protected Integer energyChargeSectionId;

    @Column(name = "KW_RATIO")
    private BigDecimal kwRatio;

    @Column(name = "GMI")
    private BigDecimal gmi;

    @Column(name = "KW_UPDATETIME")
    private Date kwUpdateTime;

    @Override
    public TempTempApplicationGeneratorKwId getId() {
        return TempTempApplicationGeneratorKwId.builder()
                .appGeneratorId(appGeneratorId)
                .datetime(datetime)
                .energyChargeSectionId(energyChargeSectionId)
                .build();
    }

    @Override
    public boolean isNew() {
        return true;
    }
}