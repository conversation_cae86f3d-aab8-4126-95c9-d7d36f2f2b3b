package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyDirectLoadRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyDirectLoadRecord;

import java.util.Date;
import java.util.List;


/**
 * Repository of ApplicationMonthlyLoadRecord
 *
 * @class: TempApplicationMonthlyLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationMonthlyDirectLoadRecordRepository extends JpaRepository<SimulationTempApplicationMonthlyDirectLoadRecord, ApplicationMonthlyLoadRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD(          " +
            "                DATE          " +
            "              , ENERGY_CHARGE_SECTION_ID          " +
            "              , APPLICATION_LOAD_ID          " +
            "              , UNMATCHED_CN          " +
            "              , MATCHED_CN          " +
            "              , SETTLEMENT_ID)          " +
            "      SELECT FIRST_DAY          " +
            "           , ENERGY_CHARGE_SECTION_ID          " +
            "           , APPLICATION_LOAD_ID          " +
            "           , SUM_UNMATCHED_CN          " +
            "           , SUM_MATCHED_CN          " +
            "          , SUBQUERY.SETTLEMENT_ID   " +
            "      FROM(   " +
            "            SELECT CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)) AS FIRST_DAY   " +
            "                , ENERGY_CHARGE_SECTION_ID   " +
            "                , APPLICATION_LOAD_ID   " +
            "                , SUM(UNMATCHED_CN) AS SUM_UNMATCHED_CN   " +
            "                , SUM(MATCHED_CN) AS SUM_MATCHED_CN   " +
            "                , SETTLEMENT_ID   " +
            "            FROM SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD TADDLR   " +
            "                 INNER JOIN  SIMULATION_APPLICATION_LOAD AS AL   " +
            "                    ON TADDLR.APPLICATION_LOAD_ID = AL.ID   " +
            "                 INNER JOIN APPLICATION AS A   " +
            "                    ON AL.APPLICATION_ID = A.ID   " +
            "            WHERE TYPE IN (:appTypeList)   " +
            "                AND DATE BETWEEN :startTime AND :endTime   " +
            "                AND SETTLEMENT_ID = :settlementId   " +
            "                GROUP BY CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)), ENERGY_CHARGE_SECTION_ID, APPLICATION_LOAD_ID, SETTLEMENT_ID) AS SUBQUERY", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId, @Param("appTypeList") List<String> appTypeList);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD   " +
            "WHERE DATE = :date   " +
            "  AND SETTLEMENT_ID = :settlementId", nativeQuery = true)
    void deleteByDateAndSettlementId(@Param("date") Date date, @Param("settlementId") Long settlementId);
}