package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyCapacitySettlement;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyCapacitySettlementId;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of ApplicationMonthlyBillByCapacity
 *
 * @class: ApplicationMonthlyBillByCapacityRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-17 11:02
 * @see:
 **/
public interface ApplicationMonthlyCapacitySettlementRepository extends JpaRepository<ApplicationMonthlyCapacitySettlement, ApplicationMonthlyCapacitySettlementId> {

    String innerSettlementBeforeBillDate = " inner join (Select APPLICATION_ID as appId, SERVICE_DATE as service, MAX(BILL_DATE) as billing" +
            " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP where BILL_DATE < ?1 group by APPLICATION_ID, SERVICE_DATE) cc" +
            "      on vBill.SERVICE_DATE = cc.service and vBill.BILL_DATE = cc.billing and vBill.APPLICATION_ID = cc.appId";
    String innerSelectSettleInfo = " inner join (Select ENERGY_CHARGE_SECTION_ID, APPLICATION_LOAD_ID, APPLICATION_GENERATOR_ID, MAX(FORMAT(CALCULATED_AT, 'yyyy-MM')) as calAt" +
            "    from APPLICATION_MONTHLY_CAPACITY_RECORD" +
            "       group by ENERGY_CHARGE_SECTION_ID, APPLICATION_LOAD_ID, APPLICATION_GENERATOR_ID) cc" +
            "     on settle.APPLICATION_LOAD_ID=cc.APPLICATION_LOAD_ID and settle.APPLICATION_GENERATOR_ID=cc.APPLICATION_GENERATOR_ID" +
            "       and settle.ENERGY_CHARGE_SECTION_ID=cc.ENERGY_CHARGE_SECTION_ID and FORMAT(settle.CALCULATED_AT, 'yyyy-MM')=cc.calAt";
    String joinGeneratorEnd = "  join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
            "  join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID";

    String joinLoadEnd = "  join APPLICATION_LOAD as lap on settle.APPLICATION_LOAD_ID = lap.ID" +
            "  join LOAD_ENTITY as lEntity on lap.LOAD_ID = lEntity.ID";
    String joinGenEntityChildTheMonthSettleServiceDate = " Left outer join GENERATOR_ENTITY_METER_CHILD as gMeterChild on gMeter.ID = gMeterChild.GENERATOR_ENTITY_METER_ID" +
            "   and gMeterChild.USE_FROM != gMeterChild.USE_TO and MONTH(gMeterChild.USE_TO) = MONTH(SC.SERVICE_DATE)" +
            "   and YEAR(gMeterChild.USE_TO) = YEAR(SC.SERVICE_DATE)" +
            "  Left outer join METER_CHANGE_RECORD as mCR on ap.ID = mCR.APPLICATION_GENERATOR_ID and mCR.USE_FROM != mCR.USE_TO" +
            "    and MONTH(mCR.USE_TO) = MONTH(SC.SERVICE_DATE) and YEAR(mCR.USE_TO) = YEAR(SC.SERVICE_DATE)" +
            "    and SC.SETTLEMENT_ID = mCR.SETTLEMENT_ID";
    String joinGenEntityChildNextMonthSettleServiceDate = " Left outer join GENERATOR_ENTITY_METER_CHILD as gC2 on gMeter.ID = gC2.GENERATOR_ENTITY_METER_ID and gC2.USE_FROM != gC2.USE_TO" +
            "    and gC2.USE_TO in (Select min(USE_TO) from GENERATOR_ENTITY_METER_CHILD where USE_FROM != USE_TO and USE_TO >= DATEADD(month, 1, SC.SERVICE_DATE)" +
            "    and gC2.GENERATOR_ENTITY_METER_ID = GENERATOR_ENTITY_METER_ID group by CUSTOMER_NO)";
    String joinVoltageLevel = " join VOLTAGE_LEVEL as volLevel on ((gEntity.COMBINE_METHOD = 1 and gEntity.VOLTAGE = volLevel.ID)" +
            "   OR (gEntity.COMBINE_METHOD != 1 and gEntity.RESPONSIBILITY_VOLTAGE = volLevel.ID))" +
            "     and volLevel.VOLTAGE_CLASSIFICATIONS_ID is not null";
    String joinApGenLoadType = " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
            " join APPLICATION_GENERATOR_LOAD_TYPE as aplType on settle.APPLICATION_GENERATOR_ID=aplType.APPLICATION_GENERATOR_ID" +
            "   and settle.APPLICATION_LOAD_ID=aplType.APPLICATION_LOAD_ID";
    String whereAllSettleIds = " where settle.SETTLEMENT_ID in (Select DISTINCT MAX(SETTLEMENT_ID) as SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
            "   where CALCULATION_METHOD in (3,5,6) and FORMAT(EXECUTION_START, 'yyyy-MM') = FORMAT(CAST(?1 as DATE), 'yyyy-MM')" +
            "    group by SERVICE_DATE, CALCULATION_METHOD)";
    String joinAllSettleCal = " join SETTLEMENT_CALCULATION as SC on settle.SETTLEMENT_ID = SC.SETTLEMENT_ID" + whereAllSettleIds;
    String whereErpSettleIds = " where settle.SETTLEMENT_ID in (Select DISTINCT MAX(SETTLEMENT_ID) as SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
            "   where CALCULATION_METHOD in (3,5) and FORMAT(EXECUTION_START, 'yyyy-MM') = FORMAT(CAST(?1 as DATE), 'yyyy-MM')" +
            "    group by SERVICE_DATE, CALCULATION_METHOD)";
    String joinErpSettleCal = " join SETTLEMENT_CALCULATION as SC on settle.SETTLEMENT_ID = SC.SETTLEMENT_ID" + whereErpSettleIds;

    /** 判斷是否送Erp count = 0 表示未送 count >0 表示已送 ERP
     * @param billStart 當月1日
     * @param billEnd 隔月1日
     * @return int
     */
    @Query(value =
            "Select count(*) from VIEW_BILL_SETTLEMENT_APPLICATION where BILL_DATE >= ?1 and BILL_DATE < ?2"
            , nativeQuery = true)
    Integer countCurrentErpMode(Date billStart, Date billEnd);

    /** #2 會計室 勞務收入明細表 單帳單年月 取出帳單當月度數資料
     * noErp - 小額綠電的資料 （只有 12 跟 3 有值)
     * @param billDate 帳單月首日
     * @return java.util.List
     */
    @Query(value =
            "Select sum(settle.ADJUSTED_MATCHED_KW) as kwh, 3 as powerType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinApGenLoadType + joinAllSettleCal +
                    " UNION Select sum(settle.ADJUSTED_MATCHED_KW) as kwh, 12 as powerType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinApGenLoadType + " and aplType.TYPE = 3" +
                    "  join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2'" + joinAllSettleCal +
                    " UNION Select sum(settle.ADJUSTED_MATCHED_KW) as kwh, 2 as powerType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinApGenLoadType + " and aplType.TYPE = 2" +
                    "  join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2'" + joinAllSettleCal +
                    " UNION Select sum(settle.ADJUSTED_MATCHED_KW) as kwh, 1 as powerType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinApGenLoadType + " and aplType.TYPE = 1" +
                    "  join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2'"+ joinAllSettleCal +
                    " order by powerType DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumKwhADSTByBillingDate(Date billDate);

    /** #23 環保處 能源別發電報表(單月服務年月) FUEL_LABEL[發電類型] FUEL_FORM[能源來源] PWDS[直供 DS 轉供 PW 小額綠電 Q]
     *  VOLT_LEVEL_CLASS[發電端電壓別] matchedKw[電量(度)]
     * @param billDate 帳單月首日
     * @return java.util.List
     */
    @Query(value =
            "Select volLevel.VOLTAGE_CLASSIFICATIONS_ID as VOLT_LEVEL_CLASS, fulType.LABEL as FUEL_LABEL, fuelForm.LABEL as FUEL_FORM, 'DS' as PWDS" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinGeneratorEnd +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != 'Q' and (app.TYPE = '2' or app.TYPE = '3')" + joinVoltageLevel +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " left outer join FUEL_FORM as fuelForm on gEntity.FUEL_FORM = fuelForm.ID" + joinAllSettleCal +
                    " group by volLevel.VOLTAGE_CLASSIFICATIONS_ID, fulType.LABEL, fuelForm.LABEL, app.TYPE UNION " +
                    "Select volLevel.VOLTAGE_CLASSIFICATIONS_ID as VOLT_LEVEL_CLASS, fulType.LABEL as FUEL_LABEL, fuelForm.LABEL as FUEL_FORM, 'PW' as PWDS" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinGeneratorEnd +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != 'Q' and app.TYPE != '2' and app.TYPE != '3'" + joinVoltageLevel +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " left outer join FUEL_FORM as fuelForm on gEntity.FUEL_FORM = fuelForm.ID" + joinAllSettleCal +
                    " group by volLevel.VOLTAGE_CLASSIFICATIONS_ID, fulType.LABEL, fuelForm.LABEL, app.TYPE UNION " +
                    "Select volLevel.VOLTAGE_CLASSIFICATIONS_ID as VOLT_LEVEL_CLASS, fulType.LABEL as FUEL_LABEL, fuelForm.LABEL as FUEL_FORM, app.TYPE as PWDS" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinGeneratorEnd +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE = 'Q'" + joinVoltageLevel +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " left outer join FUEL_FORM as fuelForm on gEntity.FUEL_FORM = fuelForm.ID" + joinAllSettleCal +
                    " group by volLevel.VOLTAGE_CLASSIFICATIONS_ID, fulType.LABEL, fuelForm.LABEL, app.TYPE" +
                    " order by PWDS ASC, VOLT_LEVEL_CLASS ASC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwByVoltageClassFuelLabelApplicationType(Date billDate);

    /**
     * #22 企劃室 電能轉直供資訊報表(單月服務年月) - 取得整年度服務年月 依照APPLICATION.ID 加總取整媒合電量 matchedKw
     * @param billDate 帳單月首日
     * @return
     */
    @Query(value =
            "Select ap.APPLICATION_ID as ID, SC.SERVICE_DATE as serviceDate, CAST(?1 as DATE) as billDate" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" +
                    joinGeneratorEnd + joinAllSettleCal +
                    " group by SC.SERVICE_DATE, ap.APPLICATION_ID" +
                    " order by ap.APPLICATION_ID, SC.SERVICE_DATE"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKwByApplicationId(Date billDate);

    /** #13 調度處 每月發轉餘總計 每月發轉餘統計 以年為週期取出每月資料 需排除 彈性分配 APPLICATION.TYPE != 4
     * serviceDate(Date), billDate(Date) MATCHED_KW[計費度數] PWDS[轉供直供 2,3 直供; 其他轉供]
     * @param billDate 帳單月月初
     * @return
     */
    @Query(value =
            "Select DISTINCT SC.SERVICE_DATE as serviceDate, CAST(?1 as DATE) as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'PW' as PWDS" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
                    " join APPLICATION as app on app.ID = ap.APPLICATION_ID and app.TYPE != '4' and app.TYPE != '2' and app.TYPE != '3'" +
                    joinErpSettleCal + "  group by SC.SERVICE_DATE UNION " +
                    "Select DISTINCT SC.SERVICE_DATE as serviceDate, CAST(?1 as DATE) as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'DS' as PWDS" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
                    " join APPLICATION as app on app.ID = ap.APPLICATION_ID and (app.TYPE = '2' or app.TYPE = '3')" +
                    joinErpSettleCal + "  group by SC.SERVICE_DATE UNION " +
                    "Select DISTINCT vBill.SERVICE_DATE as serviceDate, vBill.BILL_DATE as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'PW' as PWDS" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + innerSettlementBeforeBillDate +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on settle.SETTLEMENT_ID = vBill.SETTLEMENT_ID and vBill.ERP_CANCELLATION_DATE is null" +
                    "    and vBill.TYPE != '4' and vBill.TYPE != '2' and vBill.TYPE != '3'" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    "  group by vBill.SERVICE_DATE, vBill.BILL_DATE UNION " +
                    " Select DISTINCT vBill.SERVICE_DATE as serviceDate, vBill.BILL_DATE as billDate" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'DS' as PWDS" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + innerSettlementBeforeBillDate +
                    " join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on settle.SETTLEMENT_ID = vBill.SETTLEMENT_ID and vBill.ERP_CANCELLATION_DATE is null" +
                    "    and (vBill.TYPE = '2' or vBill.TYPE = '3')" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    "  group by vBill.SERVICE_DATE, vBill.BILL_DATE" +
                    " order by serviceDate, billDate DESC, PWDS DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKmByEachApplicationGeneratorId(Date billDate);

    /** #14 調度處 發轉餘報表 電號發電公司訊息 (帳單年月搜尋) 統計 billDate(Date), serviceStart(Date)：起始服務月月初, serviceEndNext(Date): 結束服務隔月月初
     * , applicationGeneratorId(long) gEntityId(long)  需排除 彈性分配 APPLICATION.TYPE != 4
     * MATCHED_KW[計費度數] PWDS_CONTRACT_TYPE[說明: 契約類型] GEN_NAME[發電端名稱] GEN_ELEC_NO[發電端電號] PWDS[轉供直供 2,3 直供 其他轉供]
     * @param billDate 帳單月首日
     * @return
     */
    @Query(value =
            "Select DISTINCT settle.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) over (partition by settle.APPLICATION_GENERATOR_ID) as matchedKw" +
                    ", app.TYPE as PWDS, apType.LABEL as PWDS_CONTRACT_TYPE, gEntity.NAME as GEN_NAME" +
                    ", gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO, gEntity.ID as gEntityId" +
                    ", CAST(?1 as DATE) as billDate, null as serviceStart, null as serviceEndNext" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinGeneratorEnd +
                    "  join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '4'" +
                    "  join APPLICATION_TYPE as apType on app.TYPE = apType.ID" + whereErpSettleIds + " UNION " +
                    "Select DISTINCT settle.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) over (partition by settle.APPLICATION_GENERATOR_ID, vBill.BILL_DATE) as matchedKw" +
                    ", null as PWDS, null as PWDS_CONTRACT_TYPE, null as GEN_NAME, null as GEN_ELEC_NO, null as gEntityId" +
                    ", vBill.BILL_DATE as billDate, null as serviceStart, null as serviceEndNext" +
                    " from VIEW_SETTLEMENT_ALL_BILL_AND_NON_ERP as vBill" + innerSettlementBeforeBillDate +
                    "  join APPLICATION_MONTHLY_CAPACITY_RECORD as settle on vBill.SETTLEMENT_ID = settle.SETTLEMENT_ID and vBill.TYPE != '4'" +
                    "  join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID and vBill.APPLICATION_ID = ap.APPLICATION_ID" +
                    " UNION " +
                    "Select DISTINCT null as applicationGeneratorId, null as matchedKw, null as PWDS, null as PWDS_CONTRACT_TYPE" +
                    ", null as GEN_NAME, null as GEN_ELEC_NO, null as gEntityId, CAST(?1 as DATE) as billDate" +
                    ", (Select MIN(SETTLEMENT_CALCULATION.SERVICE_DATE)" +
                    "    from SETTLEMENT_CALCULATION where SETTLEMENT_CALCULATION.SETTLEMENT_ID in (" +
                    "Select DISTINCT MAX(SETTLEMENT_ID) as SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "    where CALCULATION_METHOD in (3,5) and FORMAT(EXECUTION_START, 'yyyy-MM') = FORMAT(?1, 'yyyy-MM')" +
                    "   group by SERVICE_DATE, CALCULATION_METHOD)) as servicStart" +
                    ", DATEADD(month, 1, (Select MAX(SETTLEMENT_CALCULATION.SERVICE_DATE)" +
                    "    from SETTLEMENT_CALCULATION where SETTLEMENT_CALCULATION.SETTLEMENT_ID in (" +
                    "Select DISTINCT MAX(SETTLEMENT_ID) as SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "    where CALCULATION_METHOD in (3,5) and FORMAT(EXECUTION_START, 'yyyy-MM') = FORMAT(?1, 'yyyy-MM')" +
                    "   group by SERVICE_DATE, CALCULATION_METHOD))) as serviceEndNext" +
                    "  order by billDate DESC, applicationGeneratorId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> sumMatchedKmGeneratorEndInfo(Date billDate);

    /**
     * #14 調度處 發轉餘報表(帳單月查詢) 統計 - 餘電總和(一階餘電減去二階轉供)[再媒合未媒合電量]
     * @param billDate 帳單月首日
     * @return
     */
    @Query(value =
            "Select DISTINCT reGen.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", ROUND(sum(reGen.MATCHED_RM), 0) as matchedRm, ROUND(sum(reGen.UNMATCHED_RM), 0) as unmatchedRm" +
                    " from APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD as reGen" +
                    " where reGen.SETTLEMENT_ID in (" +
                    "Select DISTINCT MAX(SETTLEMENT_ID) as SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "   where CALCULATION_METHOD in (3,5) and FORMAT(EXECUTION_START, 'yyyy-MM') = FORMAT(?1, 'yyyy-MM')" +
                    "  group by SERVICE_DATE, CALCULATION_METHOD)" +
                    " group by reGen.APPLICATION_GENERATOR_ID" +
                    " ORDER BY applicationGeneratorId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> sumGenMatchedRmUnmatchedRmByDate(Date billDate);

    /** #17 調度處 線損計算 用戶 用電端 限定單月帳單年月
     * serviceDate, billDate, applicationGeneratorId, applicationLoadId 提供查找比對
     * 取出 settle.ADJUSTED_MATCHED_KW as A_KWH
     * EQUIP_VOLT_LEVEL[電壓層級 = 用電端責任分界點電壓層級] FUEL_LABEL[能源類別] TP_CODE[用戶地區 又稱電電公司單位代碼]
     * @param billDate 月初
     * @return List
     */
    @Query(value =
            "Select CAST(?1 as DATE) as billDate" +
                    ", settle.APPLICATION_GENERATOR_ID as applicationGeneratorId, settle.APPLICATION_LOAD_ID as applicationLoadId" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as A_KWH, volLevel.LABEL as EQUIP_VOLT_LEVEL" +
                    ", fulType.LABEL as FUEL_LABEL, tpcCom.CODE as TPC_CODE from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinLoadEnd +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpcCom on lEntity.TPC_DEPT_ID = tpcCom.ID" + joinGeneratorEnd +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" +
                    " Left outer join VOLTAGE_LEVEL as volLevel on lEntity.RESPONSIBILITY_VOLTAGE = volLevel.ID" +
                    joinAllSettleCal +
                    "  group by settle.APPLICATION_GENERATOR_ID, settle.APPLICATION_LOAD_ID, volLevel.LABEL, fulType.LABEL, tpcCom.CODE" +
                    " order by tpcCom.CODE ASC, fulType.LABEL ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findLoadFuelTypeTpcCompanyVoltLevel(Date billDate);

    /** #17 調度處 線損計算 電源 發電端 限定單月帳單年月
     *  serviceDate, billDate, applicationGeneratorId, applicationLoadId 提供查找比對
     * 取出 settle.MATCHED_KW as A_KWH
     * EQUIP_VOLT_LEVEL[電壓層級 又稱發電設備併接點電壓層級] FUEL_LABEL[能源類別] TP_CODE[電源地區 又稱電電公司單位代碼]
     * @param billDate 月初
     * @return
     */
    @Query(value =
            "Select CAST(?1 as DATE) as billDate" +
                    ", settle.APPLICATION_GENERATOR_ID as applicationGeneratorId, settle.APPLICATION_LOAD_ID as applicationLoadId" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) as A_KWH, volLevel.LABEL as EQUIP_VOLT_LEVEL" +
                    ", fulType.LABEL as FUEL_LABEL, tpcCom.CODE as TPC_CODE from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinGeneratorEnd +
                    " Left outer join TAIPOWER_COMPANY_UNIT as tpcCom on gEntity.TPC_DEPT_ID = tpcCom.ID" +
                    " join FUEL_TYPE as fulType on gEntity.FUEL_TYPE = fulType.ID" + joinVoltageLevel +
                    joinAllSettleCal +
                    "  group by settle.APPLICATION_GENERATOR_ID, settle.APPLICATION_LOAD_ID, volLevel.LABEL, fulType.LABEL, tpcCom.CODE" +
                    " order by tpcCom.CODE ASC, fulType.LABEL ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findGeneratorFuelTypeTpcCompanyVoltLevel(Date billDate);

    /** #16 #18 #19 調度處 帳單月份 取出 所有服務月列表
     * @param billDate
     * @return List
     */
    @Query(value =
            "Select DISTINCT SETTLEMENT_CALCULATION.SERVICE_DATE as serviceDate from SETTLEMENT_CALCULATION" +
                    " where SETTLEMENT_CALCULATION.SETTLEMENT_ID in (Select DISTINCT MAX(SETTLEMENT_ID) as SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "  where CALCULATION_METHOD in (3,5) and FORMAT(EXECUTION_START, 'yyyy-MM') = FORMAT(CAST(?1 as DATE), 'yyyy-MM')" +
                    " group by SERVICE_DATE, CALCULATION_METHOD)" +
                    " order by SERVICE_DATE"
            , nativeQuery = true)
    List<Map<String, Object>> findSettleServiceDates(Date billDate);

    /** #15 每月轉直供服務各類度數(單月結帳年月) 調度處報表 取出 MATCHED_KW 已經四捨五入到整數的 kwh 搭配 APPLICATION_GENERATOR_LOAD_TYPE
     * 與 APPLICATION.TYPE 分成 非小額綠電類 與 小額綠電類 加總 (度數應進位[四捨五入到整數]至整數再統計加總
     * @param billDate 月初
     * @return
     */
    @Query(value =
            "Select CAST(?1 as DATE) as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, '3' as powerType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
                    " join APPLICATION_GENERATOR_LOAD_TYPE as apGLType on settle.APPLICATION_LOAD_ID = apGLType.APPLICATION_LOAD_ID" +
                    "   and settle.APPLICATION_GENERATOR_ID = apGLType.APPLICATION_GENERATOR_ID" +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != 'Q'" +
                    joinAllSettleCal + " UNION " +
                    "Select CAST(?1 as DATE) as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, '12' as powerType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
                    " join APPLICATION_GENERATOR_LOAD_TYPE as apGLType on settle.APPLICATION_LOAD_ID = apGLType.APPLICATION_LOAD_ID" +
                    "   and settle.APPLICATION_GENERATOR_ID = apGLType.APPLICATION_GENERATOR_ID and apGLType.TYPE = 3" +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2' and app.TYPE != 'Q'" +
                    joinAllSettleCal + " UNION " +
                    "Select CAST(?1 as DATE) as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, '2' as powerType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
                    " join APPLICATION_GENERATOR_LOAD_TYPE as apGLType on settle.APPLICATION_LOAD_ID = apGLType.APPLICATION_LOAD_ID" +
                    "   and settle.APPLICATION_GENERATOR_ID = apGLType.APPLICATION_GENERATOR_ID and apGLType.TYPE = 2" +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2' and app.TYPE != 'Q'" +
                    joinAllSettleCal + " UNION " +
                    "Select CAST(?1 as DATE) as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, '1' as powerType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
                    " join APPLICATION_GENERATOR_LOAD_TYPE as apGLType on settle.APPLICATION_LOAD_ID = apGLType.APPLICATION_LOAD_ID" +
                    "   and settle.APPLICATION_GENERATOR_ID = apGLType.APPLICATION_GENERATOR_ID and apGLType.TYPE = 1" +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '2' and app.TYPE != 'Q'" +
                    joinAllSettleCal + " UNION " +
                    "Select CAST(?1 as DATE) as billDate, sum(settle.ADJUSTED_MATCHED_KW) as matchedKw, 'Q' as powerType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" +
                    " join APPLICATION_GENERATOR as ap on settle.APPLICATION_GENERATOR_ID = ap.ID" +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE = 'Q'" +
                    joinAllSettleCal +
                    " order by powerType ASC"
            , nativeQuery = true)
    List<Map<String, Object>> sumExpsByServiceDateBillDate(Date billDate);

    /** #12 各電壓層級度數報表(帳單限定年月搜尋) 業務處費率組 serviceDate(Date), billDate(Date), applicationGeneratorId(long), applicationLoadId(long)
     * 表格輸出當年份到 輸入月份之 累計 不同電壓層級累積資料 KWH[MATCHED_KW] = kwh EQUIP_VOLT_LEVEL[發電設備併接點電壓層級]
     * @param billDate 帳單月初
     * @return
     */
    @Query(value =
            "Select SC.SERVICE_DATE as serviceDate, sum(settle.ANCILLARY_SERVICE_COST) as KWH, volLevel.LABEL as EQUIP_VOLT_LEVEL, volLevel.ID" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinGeneratorEnd + joinVoltageLevel + joinAllSettleCal +
                    "  group by SC.SERVICE_DATE, volLevel.LABEL, volLevel.ID" +
                    "  order by SC.SERVICE_DATE, volLevel.ID"
            , nativeQuery = true)
    List<Map<String, Object>> findExpVoltInfoByServiceDates(Date billDate);

    /** #7 #14 15分鐘發電端媒合度數(帳單年月 搜尋) TransRelatRaw 資料 SETTLEMENT_ID + applicationGeneratorId + serviceTime + bllDate 需排除 彈性分配 APPLICATION.TYPE != 4
     *    , appType[方便換算 PWDS] 該列 運用 genMeterReplaceDate[發電端換表日] 搭配 oldGenMeterNo[舊發電端表號] 可替換 METER_NO[表號] + oGMeterNext[舊電表換表隔月換表號]
     * 業務處>再購組 每月發電端每15分鐘轉直供度數(3欄位+已併網裝置容量) GEN_METER_NO[表號]] GEN_ELEC_NO[電號] combineCapacity[已併網裝置容量
     * SERVICE_ID[轉供契約編號]
     * @param billDate 帳單月初
     * @return java.util.List
     */
    @Query(value =
            "Select DISTINCT settle.SETTLEMENT_ID, settle.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", SC.SERVICE_DATE as serviceDate, gMeter.METER_NO as GEN_METER_NO" +
                    ", IIF(mCR.USE_TO is not null, mCR.USE_TO, gMeterChild.USE_TO) as genMeterReplaceDate, gMeterChild.METER_NO as oldGenMeterNo" +
                    ", gC2.METER_NO as oGMeterNext, gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO" +
                    ", app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, app.TYPE as appType" +
                    " from APPLICATION_MONTHLY_CAPACITY_SETTLEMENT as settle" + joinGeneratorEnd +
                    " join SETTLEMENT_CALCULATION as SC on settle.SETTLEMENT_ID = SC.SETTLEMENT_ID" +
                    " Left outer join GENERATOR_ENTITY_METER as gMeter on ap.GENERATOR_METER_ID = gMeter.ID" +
                    joinGenEntityChildTheMonthSettleServiceDate + joinGenEntityChildNextMonthSettleServiceDate +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID and app.TYPE != '4'" + whereErpSettleIds +
                    " order by serviceDate, applicationGeneratorId ASC, genMeterReplaceDate DESC"
            , nativeQuery = true)
    List<Map<String, Object>> find15MinutesGeneratorSettleInfoByServiceDate(Date billDate);

    /** #8 每月發電結算第二階段媒合度數 4時段 再媒合(限定 單月 搜尋)TransRelat2TH applicationGeneratorId, serviceDate, monthEndDate
     * applicationGeneratorId 列留下有 METER_REPLACED_DATE 的欄位 + oGMeterNext[舊電表隔月換電表]
     * 業務處>再購組 發電報表每月第二階段媒合度數(8欄位) GEN_PW_PERCENT[轉供比率] METER_NO[表號] METER_REPLACED_DATE[換表日]
     *      DEVICE_CAPACITY[裝置容量(電業執照)] GEN_ELEC_NO[電號] SERVICE_ID[轉供契約編號] TERMINATE_DATE[契約終止日]
     *      CONTRACT_TYPE[PWDS契約類別] METER_REPLACED_DATE[發電端換表日]
     * @param billDate 服務月
     * @return java.util.List
     */
    @Query(value =
            "Select Distinct settle.APPLICATION_GENERATOR_ID as applicationGeneratorId, settle.DATE as serviceDate, settle.SETTLEMENT_ID" +
                    ", settle.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId, settle.MATCHED_RM as matchedRm" +
                    ", ap.PMI as GEN_PW_PERCENT, gMeter.METER_NO as METER_NO" +
                    ", IIF(mCR.USE_TO is not null, mCR.USE_TO, gMeterChild.USE_TO) as METER_REPLACED_DATE, gC2.METER_NO as oGMeterNext" +
                    ", gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO, ap.LICENSE_CAPACITY as DEVICE_CAPACITY" +
                    ", app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, app.CONTRACTED_END as TERMINATE_DATE, app.TYPE as CONTRACT_TYPE" +
                    " from APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD as settle" + joinGeneratorEnd +
                    " join SETTLEMENT_CALCULATION as SC on settle.SETTLEMENT_ID = SC.SETTLEMENT_ID" +
                    " Left outer join GENERATOR_ENTITY_METER as gMeter on ap.GENERATOR_METER_ID = gMeter.ID" +
                    joinGenEntityChildTheMonthSettleServiceDate + joinGenEntityChildNextMonthSettleServiceDate +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID" + whereErpSettleIds +
                    " order by applicationGeneratorId ASC, serviceDate ASC, energyChangeSectionId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findApplicationMonthlyGeneratorsReMatchInfoByBillDate(Date billDate);

    /** #11 轉直供用戶之各月轉直供資料 帳單起訖年月 KWH 1 3 9 11 用電端(4欄位)
     * 結帳相關 applicationLoadId, SETTLEMENT_ID ENERGY_CHARGE_SECTION_ID, KWH 結帳VIEW serviceDate
     * CUST_ELEC_NO[用電端電號] CONTR_TYPE[用電端契約類型] TIME_PRICE_STG[用電端段別] <- 用電契約類型 與 用電段別(不分段(0)/二段式(2)/三段式(3)) 從 NBS 取得 與 PWDS[轉供直供]
     * @param billStart
     * @param billEnd
     * @return
     */
    @Query(value =
            "Select DISTINCT SC.SERVICE_DATE as serviceDate, settle.APPLICATION_LOAD_ID as applicationLoadId, settle.SETTLEMENT_ID" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) over (partition by settle.SETTLEMENT_ID, settle.APPLICATION_LOAD_ID, settle.ENERGY_CHARGE_SECTION_ID) as KWH" +
                    ", settle.ENERGY_CHARGE_SECTION_ID, lEntity.NBS_CUSTOMER_NUMBER as CUST_ELEC_NO" +
                    ", lEntity.CONTRACT_STG as CONTR_TYPE, lEntity.TIME_STG as TIME_PRICE_STG, app.TYPE as PWDS" +
                    " from APPLICATION_MONTHLY_CAPACITY_RECORD as settle" + innerSelectSettleInfo + joinLoadEnd +
                    " join APPLICATION as app on app.ID = lap.APPLICATION_ID" +
                    " join SETTLEMENT_CALCULATION as SC on settle.SETTLEMENT_ID = SC.SETTLEMENT_ID" +
                    " where settle.SETTLEMENT_ID in (Select DISTINCT MAX(SETTLEMENT_ID) as SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "   where CALCULATION_METHOD in (3,5,6) and EXECUTION_START >= ?1 and EXECUTION_START < ?2" +
                    "  group by SERVICE_DATE, CALCULATION_METHOD)" +
                    " order by SC.SERVICE_DATE ASC, settle.APPLICATION_LOAD_ID ASC, settle.ENERGY_CHARGE_SECTION_ID, settle.SETTLEMENT_ID DESC"
            , nativeQuery = true)
    List<Map<String, Object>> findMonthlyApplicationLoadByBillDateRange(Date billStart, Date billEnd);

    /** #3 #4 每月轉直供度數資料(限定 帳單單月搜尋) serviceDate applicationGeneratorId, appType(提供 PWDS 欄位判斷) ENERGY_CHARGE_SECTION_ID KWH
     * 業務處>再購組 發電報表每月轉直供度數(9欄位) GEN_PW_PERCENT[轉供比率] METER_NO[表號] METER_REPLACED_DATE[換表日]
     * DEVICE_CAPACITY[裝置容量(電業執照)] GEN_ELEC_NO[電號] SERVICE_ID[轉供契約編號] TERMINATE_DATE[契約終止日]
     * CONTRACT_TYPE[契約類別]
     * @param billDate
     * @return
     */
    @Query(value =
            "Select DISTINCT SC.SERVICE_DATE as serviceDate, settle.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", settle.ENERGY_CHARGE_SECTION_ID" +
                    ", sum(settle.ADJUSTED_MATCHED_KW) over (partition by settle.APPLICATION_GENERATOR_ID, settle.ENERGY_CHARGE_SECTION_ID) as KWH" +
                    ", ap.PMI as GEN_PW_PERCENT, gMeter.METER_NO as METER_NO, IIF(mCR.USE_TO is not null, mCR.USE_TO, gMeterChild.USE_TO) as GEN_METER_CHANGE_DATE" +
                    ", gMeterChild.METER_NO as oldGenMeterNo, gC2.METER_NO as oGMeterNext" +
                    ", gEntity.NBS_CUSTOMER_NUMBER as GEN_ELEC_NO, gEntity.CAPACITY_APPLIED as DEVICE_CAPACITY, gEntity.TIME_STG as TIME_PRICE_STG" +
                    ", app.CONTRACT_NO+'-'+app.VERSION as SERVICE_ID, app.CONTRACTED_END as TERMINATE_DATE, app.TYPE as PWDS" +
                    ", apType.LABEL+'|'+apType.ID as CONTRACT_TYPE" +
                    " from APPLICATION_MONTHLY_CAPACITY_RECORD as settle" + joinGeneratorEnd +
                    " join SETTLEMENT_CALCULATION as SC on settle.SETTLEMENT_ID = SC.SETTLEMENT_ID" +
                    " Left outer join GENERATOR_ENTITY_METER as gMeter on ap.GENERATOR_METER_ID = gMeter.ID" +
                    joinGenEntityChildTheMonthSettleServiceDate +
                    joinGenEntityChildNextMonthSettleServiceDate +
                    " join APPLICATION as app on ap.APPLICATION_ID = app.ID" +
                    " join APPLICATION_TYPE as apType on app.TYPE = apType.ID" + whereAllSettleIds +
                    "  ORDER BY serviceDate ASC, applicationGeneratorId ASC, settle.ENERGY_CHARGE_SECTION_ID ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findApplicationGeneratorSettleInfo(Date billDate);

    /** 輸入 結算年月 取得 該月 所有 settlementIdList
     * @param billDate
     * @return
     */
    @Query(value =
            "Select DISTINCT MAX(SETTLEMENT_ID) as SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    " where CALCULATION_METHOD in (3,5,6) and FORMAT(EXECUTION_START, 'yyyy-MM') = FORMAT(CAST(?1 as DATE), 'yyyy-MM')" +
                    " group by SERVICE_DATE, CALCULATION_METHOD"
            , nativeQuery = true)
    List<Long> findAllSettlementId(Date billDate);

    @Transactional
    @Modifying
    @Query(value = "BEGIN                  " +
            "  DELETE APPLICATION_MONTHLY_CAPACITY_RECORD                  " +
            "      WHERE SETTLEMENT_ID = :settlementId                  " +
            "        AND APPLICATION_GENERATOR_ID IN (:appGenIdList)                  " +
            "        AND APPLICATION_LOAD_ID IN (:appLoadIdList)                  " +
            "  DELETE APPLICATION_MONTHLY_CAPACITY_SETTLEMENT                  " +
            "      WHERE SETTLEMENT_ID = :settlementId                  " +
            "        AND APPLICATION_GENERATOR_ID IN (:appGenIdList)                  " +
            "        AND APPLICATION_LOAD_ID IN (:appLoadIdList)                  " +
            "END ", nativeQuery = true)
    void deleteRecordBySettlementId(@Param("settlementId") Long settlementId
            , @Param("appGenIdList") List<Long> appGenIdList, @Param("appLoadIdList") List<Long> appLoadIdList);

    @Transactional
    @Modifying
    @Query(value = "BEGIN                   " +
            "  INSERT INTO APPLICATION_MONTHLY_CAPACITY_RECORD(SETTLEMENT_ID       " +
            "        , ENERGY_CHARGE_SECTION_ID       " +
            "        , APPLICATION_GENERATOR_ID       " +
            "        , APPLICATION_LOAD_ID       " +
            "        , CAPACITY_CODE       " +
            "        , PERCENTAGE       " +
            "        , MATCHED_KW       " +
            "        , ADJUSTED_MATCHED_KW       " +
            "        , ANCILLARY_SERVICE_COST       " +
            "        , DISPATCH_SERVICE_COST       " +
            "        , POWER_TRANS_COST       " +
            "        , POWER_DIST_COST       " +
            "        , CALCULATED_AT)       " +
            "      SELECT SETTLEMENT_ID       " +
            "           , ENERGY_CHARGE_SECTION_ID       " +
            "           , APPLICATION_GENERATOR_ID       " +
            "           , APPLICATION_LOAD_ID       " +
            "           , CAPACITY_CODE       " +
            "           , PERCENTAGE       " +
            "           , MATCHED_KW       " +
            "           , ADJUSTED_MATCHED_KW       " +
            "           , ANCILLARY_SERVICE_COST       " +
            "           , DISPATCH_SERVICE_COST       " +
            "           , POWER_TRANS_COST       " +
            "           , POWER_DIST_COST       " +
            "           , CALCULATED_AT       " +
            "      FROM TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD       " +
            "        WHERE SETTLEMENT_ID = :settlementId       " +
            "       " +
            "INSERT INTO APPLICATION_MONTHLY_CAPACITY_SETTLEMENT(       " +
            "             SETTLEMENT_ID       " +
            "           , APPLICATION_GENERATOR_ID       " +
            "           , APPLICATION_LOAD_ID       " +
            "           , CAPACITY_CODE       " +
            "           , ADJUSTED_MATCHED_KW       " +
            "           , ANCILLARY_SERVICE_COST       " +
            "           , DISPATCH_SERVICE_COST       " +
            "           , POWER_TRANS_COST       " +
            "           , POWER_DIST_COST)       " +
            "        SELECT       " +
            "           SETTLEMENT_ID       " +
            "            , APPLICATION_GENERATOR_ID       " +
            "            , APPLICATION_LOAD_ID       " +
            "            , CAPACITY_CODE       " +
            "            , ADJUSTED_MATCHED_KW       " +
            "            , ANCILLARY_SERVICE_COST       " +
            "            , DISPATCH_SERVICE_COST       " +
            "            , POWER_TRANS_COST       " +
            "            , POWER_DIST_COST       " +
            "       FROM TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT       " +
            "        WHERE SETTLEMENT_ID = :settlementId       " +
            "END ", nativeQuery = true)
    void saveFromTempTableBySettlementId(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = "BEGIN       " +
            "   DELETE FROM APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_DAILY_DIRECT_GENERATOR_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_DAILY_DIRECT_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_LOAD_ID IN (:appLoadIdList)  " +
            "  " +
            "   DELETE FROM APPLICATION_DAILY_GENERATOR_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_DAILY_GENERATOR_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_DAILY_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_LOAD_ID IN (:appLoadIdList)  " +
            "  " +
            "   DELETE FROM APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_MONTHLY_DIRECT_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_LOAD_ID IN (:appLoadIdList)  " +
//            "   DELETE FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD       " +
//            "       WHERE SETTLEMENT_ID = :settlementId       " +
            "   DELETE FROM APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_MONTHLY_GENERATOR_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_MONTHLY_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_LOAD_ID IN (:appLoadIdList)  " +
            "  " +
            "   DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD  " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_MONTHLY_REMATCH_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_LOAD_ID IN (:appLoadIdList)  " +
            "  " +
            "   DELETE FROM APPLICATION_MONTHLY_CAPACITY_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_MONTHLY_CAPACITY_SETTLEMENT       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "  " +
            "  " +
            "   DELETE FROM APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD   " +
            "      WHERE SETTLEMENT_ID = :settlementId  " +
            "          AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD  " +
            "   WHERE SETTLEMENT_ID = :settlementId  " +
            "          AND APPLICATION_GENERATOR_ID IN (:appGenIdList)   " +
            "   DELETE FROM APPLICATION_TIMELY_DIRECT_LOAD_RECORD  " +
            "      WHERE SETTLEMENT_ID = :settlementId  " +
            "          AND APPLICATION_LOAD_ID IN (:appLoadIdList)   " +
            "  " +
            "   DELETE FROM APPLICATION_TIMELY_GENERATOR_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_TIMELY_GENERATOR_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_GENERATOR_ID IN (:appGenIdList)  " +
            "   DELETE FROM APPLICATION_TIMELY_LOAD_RECORD       " +
            "       WHERE SETTLEMENT_ID = :settlementId  " +
            "         AND APPLICATION_LOAD_ID IN (:appLoadIdList)  " +
            "END ", nativeQuery = true)
    void deleteAllBySettlementId(@Param("settlementId") Long settlementId
            , @Param("appGenIdList")List<Long> appGenIdList, @Param("appLoadIdList") List<Long> appLoadIdList);

    @Transactional
    @Modifying
    @Query(value = " INSERT INTO APPLICATION_MONTHLY_CAPACITY_SETTLEMENT(     " +
            "    SETTLEMENT_ID     " +
            "    , APPLICATION_GENERATOR_ID     " +
            "    , APPLICATION_LOAD_ID     " +
            "    , CAPACITY_CODE     " +
            "    , ADJUSTED_MATCHED_KW     " +
            "    , ANCILLARY_SERVICE_COST     " +
            "    , DISPATCH_SERVICE_COST     " +
            "    , POWER_TRANS_COST     " +
            "    , POWER_DIST_COST     " +
            ")     " +
            "  SELECT SETTLEMENT_ID     " +
            "     , APPLICATION_GENERATOR_ID     " +
            "     , APPLICATION_LOAD_ID     " +
            "     , CAPACITY_CODE     " +
            "     , SUM(ADJUSTED_MATCHED_KW)     " +
            "     , SUM(ANCILLARY_SERVICE_COST)     " +
            "     , SUM(DISPATCH_SERVICE_COST)     " +
            "     , SUM(POWER_TRANS_COST)     " +
            "     , SUM(POWER_DIST_COST)     " +
            "  FROM APPLICATION_MONTHLY_CAPACITY_RECORD     " +
            "    WHERE SETTLEMENT_ID = :settlementId     " +
            "  GROUP BY SETTLEMENT_ID, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID, CAPACITY_CODE", nativeQuery = true)
    void saveAllRecordBySettlementId(@Param("settlementId") Long settlementId);


    @Transactional
    @Modifying
    @Query(value ="BEGIN       " +
            "    DELETE APPLICATION_MONTHLY_CAPACITY_RECORD       " +
            "        WHERE SETTLEMENT_ID = :settlementId               " +
            "    DELETE APPLICATION_MONTHLY_CAPACITY_SETTLEMENT  " +
            "        WHERE SETTLEMENT_ID = :settlementId       " +
            "END ", nativeQuery = true)
    void deleteAllRecordBySettlementId(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = " BEGIN       " +
            "    DELETE FROM APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD       " +
            "    DELETE FROM APPLICATION_DAILY_DIRECT_GENERATOR_RECORD       " +
            "    DELETE FROM APPLICATION_DAILY_DIRECT_LOAD_RECORD       " +
            "       " +
            "    DELETE FROM APPLICATION_DAILY_GENERATOR_LOAD_RECORD       " +
            "    DELETE FROM APPLICATION_DAILY_GENERATOR_RECORD       " +
            "    DELETE FROM APPLICATION_DAILY_LOAD_RECORD       " +
            "       " +
            "    DELETE FROM APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD       " +
            "    DELETE FROM APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD       " +
            "    DELETE FROM APPLICATION_MONTHLY_DIRECT_LOAD_RECORD       " +
            "       " +
            "    DELETE FROM APPLICATION_MONTHLY_FLEXIBLE_GENERATOR_LOAD_RECORD       " +
            "    DELETE FROM APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD       " +
            "    DELETE FROM APPLICATION_MONTHLY_GENERATOR_RECORD       " +
            "    DELETE FROM APPLICATION_MONTHLY_LOAD_RECORD       " +
            "       " +
            "    DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD       " +
            "    DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD       " +
            "    DELETE FROM APPLICATION_MONTHLY_REMATCH_LOAD_RECORD       " +
            "       " +
            "    DELETE FROM APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD       " +
            "    DELETE FROM APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD       " +
            "    DELETE FROM APPLICATION_TIMELY_DIRECT_LOAD_RECORD       " +
            "       " +
            "    DELETE FROM APPLICATION_TIMELY_GENERATOR_LOAD_RECORD       " +
            "    DELETE FROM APPLICATION_TIMELY_GENERATOR_RECORD       " +
            "    DELETE FROM APPLICATION_TIMELY_LOAD_RECORD       " +
            "       " +
            "    DELETE FROM APPLICATION_MONTHLY_BILL       " +
            "    DELETE FROM APPLICATION_MONTHLY_SETTLEMENT       " +
            "    DELETE FROM APPLICATION_MONTHLY_CAPACITY_RECORD       " +
            "    DELETE FROM APPLICATION_MONTHLY_CAPACITY_SETTLEMENT       " +
            "       " +
            "    BEGIN       " +
            "        DELETE FROM DATE_APPLICATION_METER       " +
            "        DELETE FROM DATE_APPLICATION_COMPUTABLE_SETTLEMENT       " +
            "        DELETE FROM DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT       " +
            "        DELETE FROM DATE_METER_COMPUTABLE_SETTLEMENT       " +
            "        DELETE FROM DATE_APPLICATION_GENERATOR_LOAD       " +
            "    END       " +
            " END ", nativeQuery = true)
    void deleteAll();
}
