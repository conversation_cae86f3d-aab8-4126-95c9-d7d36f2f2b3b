package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Computable Application Settlement
 *
 * @class: TempDateApplicationSelfComputableSettlement
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-06-09 17:11
 * @see:
 **/

@AllArgsConstructor
@SuperBuilder
@Entity
@Table(name = "SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT")
public class SimulationTempDateApplicationSelfComputableSettlement extends DateApplicationComputableSettlementColumn{

}