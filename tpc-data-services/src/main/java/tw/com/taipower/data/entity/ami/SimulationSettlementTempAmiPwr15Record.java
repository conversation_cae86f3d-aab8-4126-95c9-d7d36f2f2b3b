package tw.com.taipower.data.entity.ami;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 試算算電量歷程(與AMI差15分鐘存放，例如：2024-07-01 23:45:00 為對應AMI存放為 2024-07-00 00:00:00)
 *
 * @author:  ting
 * @date:    2024/11/04 18:39:10
 * @param:
 * @return:
 **/

@AllArgsConstructor
@SuperBuilder
@Entity
@Table(name = "SIMULATION_SETTLEMENT_TEMP_AMI_PWR15_RECORD")
public class SimulationSettlementTempAmiPwr15Record extends SettlementAmiPwr15RecordColumn {


}