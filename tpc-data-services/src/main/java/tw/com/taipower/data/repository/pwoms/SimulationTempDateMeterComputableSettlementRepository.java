package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationTempDateMeterComputableSettlement;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * DateMeterComputableSettlement Repository
 *
 * @class: DateMeterComputableSettlementRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-06-09 17:34
 * @see:
 **/
public interface SimulationTempDateMeterComputableSettlementRepository extends JpaRepository<SimulationTempDateMeterComputableSettlement, Long> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN   " +
            "    DECLARE @date DATE = :startDate   " +
            "   " +
            "    WHILE @date <= :endDate   " +
            "        BEGIN   " +
            "            BEGIN   " +
            "                INSERT INTO SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT(   " +
            "                                                                   DATE   " +
            "                                                                 , LOAD_METER_ID   " +
            "                                                                 , GENERATOR_METER_ID   " +
            "                                                                 , NBS_CUSTOMER_NUMBER   " +
            "                                                                 , METER_NO   " +
            "                                                                 , COMPUTABLE   " +
            "                                                                 , SETTLEMENT_ID)   " +
            "                SELECT DISTINCT @date, LOAD_METER_ID, NULL, NBS_CUSTOMER_NUMBER, METER_NO, 0, :settlementId   " +
            "                FROM (SELECT VALM.*   " +
            "                      FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL   " +
            "                               INNER JOIN VIEW_APPLICATION_LOAD_METER AS VALM   " +
            "                                          ON VALM.APPLICATION_LOAD_ID = TDAGL.APPLICATION_LOAD_ID   " +
            "                                              AND (VALM.USE_FROM <= @date)   " +
            "                                              AND ((VALM.USE_TO >= @date) OR (VALM.USE_TO IS NULL))   " +
            "                      WHERE TDAGL.DATE = @date AND TDAGL.SETTLEMENT_ID = :settlementId) AS SUBQUERY   " +
            "   " +
            "                INSERT INTO SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT(   " +
            "                                                                   DATE   " +
            "                                                                 , LOAD_METER_ID   " +
            "                                                                 , GENERATOR_METER_ID   " +
            "                                                                 , NBS_CUSTOMER_NUMBER   " +
            "                                                                 , METER_NO   " +
            "                                                                 , COMPUTABLE   " +
            "                                                                 , SETTLEMENT_ID)   " +
            "                SELECT DISTINCT @date, NULL, GENERATOR_METER_ID, NBS_CUSTOMER_NUMBER, METER_NO, 0, :settlementId   " +
            "                FROM (SELECT VAGM.*   " +
            "                      FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL   " +
            "                               INNER JOIN VIEW_APPLICATION_GENERATOR_METER AS VAGM   " +
            "                                          ON VAGM.APPLICATION_GENERATOR_ID = TDAGL.APPLICATION_GENERATOR_ID   " +
            "                                              AND (VAGM.USE_FROM <= @date)   " +
            "                                              AND ((VAGM.USE_TO >= @date) OR (VAGM.USE_TO IS NULL))   " +
            "                      WHERE TDAGL.DATE = @date AND TDAGL.SETTLEMENT_ID = :settlementId) AS SUBQUERY   " +
            "            END   " +
            "            BEGIN   " +
            "                INSERT INTO SIMULATION_TEMP_DATE_APPLICATION_METER(   " +
            "                                                         DATE_APPLICATION_ID   " +
            "                                                       , DATE_METER_ID)   " +
            "                SELECT DISTINCT TDAGL.ID,TDMCS.ID   " +
            "                FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL   " +
            "                         INNER JOIN VIEW_APPLICATION_LOAD_METER AS VALM   " +
            "                                    ON VALM.APPLICATION_LOAD_ID = TDAGL.APPLICATION_LOAD_ID   " +
            "                         INNER JOIN SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT AS TDMCS   " +
            "                                    ON TDMCS.METER_NO = VALM.METER_NO   " +
            "                                        AND TDMCS.NBS_CUSTOMER_NUMBER = VALM.NBS_CUSTOMER_NUMBER   " +
            "                                        AND TDMCS.SETTLEMENT_ID = :settlementId " +
            "                WHERE TDAGL.DATE = @date AND TDAGL.SETTLEMENT_ID = :settlementId   " +
            "                  AND VALM.LOAD_METER_ID IS NOT NULL   " +
            "   " +
            "                INSERT INTO SIMULATION_TEMP_DATE_APPLICATION_METER(   " +
            "                                                         DATE_APPLICATION_ID   " +
            "                                                       , DATE_METER_ID)   " +
            "                SELECT DISTINCT TDAGL.ID, TDMCS.ID   " +
            "                FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL   " +
            "                         INNER JOIN VIEW_APPLICATION_GENERATOR_METER AS VAGM   " +
            "                                    ON VAGM.APPLICATION_GENERATOR_ID = TDAGL.APPLICATION_GENERATOR_ID   " +
            "                         INNER JOIN SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT AS TDMCS   " +
            "                                    ON TDMCS.METER_NO = VAGM.METER_NO   " +
            "                                        AND TDMCS.NBS_CUSTOMER_NUMBER = VAGM.NBS_CUSTOMER_NUMBER   " +
            "                                        AND TDMCS.SETTLEMENT_ID = :settlementId " +
            "                WHERE TDAGL.DATE = @date AND TDAGL.SETTLEMENT_ID = :settlementId   " +
            "                  AND VAGM.GENERATOR_METER_ID IS NOT NULL   " +
            "            END   " +
            "            SET @date = DATEADD(day, 1, @date)   " +
            "        END   " +
            "END", nativeQuery = true)
    void saveAllByDateIntervalAndSettlementId(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId);


    @Query(value = "SELECT DISTINCT METER_NO " +
            "        FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT " +
            "    WHERE GENERATOR_METER_ID IS NOT NULL " +
            "      AND DATE = :date " +
            "      AND SETTLEMENT_ID = :settlementId", nativeQuery = true)
    List<String> findGeneratorMeterIdBySettlementId(@Param("date") Date date, @Param("settlementId") Long settlementId);

    @Query(value = "SELECT DISTINCT METER_NO " +
            "        FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT " +
            "    WHERE  LOAD_METER_ID IS NOT NULL " +
            "        AND DATE = :date " +
            "        AND SETTLEMENT_ID = :settlementId ", nativeQuery = true)
    List<String> findLoadMeterIdBySettlementId(@Param("date") Date date, @Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = "UPDATE SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT " +
            "    SET COMPUTABLE = :computable " +
            "    WHERE GENERATOR_METER_ID IS null " +
            "       AND METER_NO IN (:meterList) " +
            "       AND NBS_CUSTOMER_NUMBER IN (:nbsCustList)" +
            "       AND DATE = :date " +
            "       AND SETTLEMENT_ID = :settlementId ", nativeQuery = true)
    void updateLoadComputableBySettlementIdAndMeterNoIn(@Param("date") Date date, @Param("settlementId") Long settlementId, @Param("nbsCustList") List<String> nbsCustList, @Param("meterList") List<String> meterList, @Param("computable") boolean computable);


    @Transactional
    @Modifying
    @Query(value = "UPDATE SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT " +
            "    SET COMPUTABLE = :computable " +
            "    WHERE LOAD_METER_ID IS null " +
            "       AND METER_NO IN (:meterList) " +
            "       AND NBS_CUSTOMER_NUMBER IN (:nbsCustList)" +
            "       AND DATE = :date " +
            "       AND SETTLEMENT_ID = :settlementId ", nativeQuery = true)
    void updateGeneratorComputableBySettlementIdAndMeterNoIn(@Param("date") Date date, @Param("settlementId") Long settlementId, @Param("nbsCustList") List<String> nbsCustList, @Param("meterList") List<String> meterList, @Param("computable") boolean computable);

    @Query(value = "SELECT DISTINCT FORMAT (TDMCS.DATE, 'yyyy-MM-dd') AS STRING_DATE, APPLICATION_ID, APPLICATION_GENERATOR_ID FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT TDMCS      " +
            "    INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_METER TDAM      " +
            "            ON TDMCS.ID = TDAM.DATE_METER_ID      " +
            "    INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD TDAGL      " +
            "            ON TDAGL.ID = TDAM.DATE_APPLICATION_ID      " +
            "                AND TDAGL.SETTLEMENT_ID = :settlementId      " +
            "    WHERE TDMCS.SETTLEMENT_ID = :settlementId      " +
            "        AND TDMCS.LOAD_METER_ID IS NULL      " +
            "        AND TDMCS.COMPUTABLE = 0" +
            "        AND TDAGL.APPLICATION_ID IN (:appIdList)", nativeQuery = true)
    List<Map<String, Object>> findInComputableGeneratorBySettlementIdAndApplicationIn(@Param("settlementId") Long settlementId, @Param("appIdList") List<Long> appIdList);

    @Query(value = "SELECT DISTINCT FORMAT (TDMCS.DATE, 'yyyy-MM-dd') AS STRING_DATE, APPLICATION_ID, APPLICATION_LOAD_ID FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT TDMCS      " +
            "    INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_METER TDAM      " +
            "        ON TDMCS.ID = TDAM.DATE_METER_ID      " +
            "    INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD TDAGL      " +
            "        ON TDAGL.ID = TDAM.DATE_APPLICATION_ID      " +
            "        AND TDAGL.SETTLEMENT_ID = :settlementId      " +
            "    WHERE TDMCS.SETTLEMENT_ID = :settlementId      " +
            "        AND TDMCS.GENERATOR_METER_ID IS NULL      " +
            "        AND TDMCS.COMPUTABLE = 0" +
            "        AND TDAGL.APPLICATION_ID IN (:appIdList)", nativeQuery = true)
    List<Map<String, Object>> findInComputableLoadBySettlementIdAndApplicationIn(@Param("settlementId") Long settlementId, @Param("appIdList") List<Long> appIdList);

    @Query(value = "SELECT METER_NO FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT" +
            "    WHERE SETTLEMENT_ID = :settlementId" +
            "      AND DATE = :date" +
            "      AND LOAD_METER_ID IS NOT NULL" +
            "      AND COMPUTABLE = 1  ", nativeQuery = true)
    List<String> findMeterNoBySettlementIdAndDateAndLoadMeterIdIsNotNullAndComputableTrue(@Param("settlementId") Long settlementId, @Param("date") Date date);

    @Query(value = "SELECT METER_NO FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT        " +
            "    WHERE SETTLEMENT_ID = :settlementId        " +
            "      AND DATE = :date        " +
            "      AND GENERATOR_METER_ID IS NOT NULL        " +
            "      AND COMPUTABLE = 1     ", nativeQuery = true)
    List<String> findMeterNoBySettlementIdAndDateAndGeneratorMeterIdIsNotNullAndComputableTrue(@Param("settlementId") Long settlementId, @Param("date") Date date);

    @Query(value = "SELECT DISTINCT AG.APPLICATION_ID, TDAGL.APPLICATION_GENERATOR_ID, NBS_CUSTOMER_NUMBER, METER_NO, PMI * PERCENTAGE * 0.01 AS PMI_EXCEPT_OP, COMPUTABLE_CAPACITY                   " +
            "  FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT TDMCS                   " +
            "      INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_METER TDAM                   " +
            "               ON TDMCS.ID = TDAM.DATE_METER_ID                   " +
            "      INNER JOIN dbo.SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD TDAGL                   " +
            "               ON TDAGL.ID = TDAM.DATE_APPLICATION_ID                   " +
            "      INNER JOIN SIMULATION_APPLICATION_GENERATOR AG                   " +
            "                 ON AG.ID = APPLICATION_GENERATOR_ID                   " +
            "      INNER JOIN SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION SSCC                   " +
            "                 ON SSCC.APPLICATION_GENERATOR_ID = TDAGL.APPLICATION_GENERATOR_ID                   " +
            "                 AND SSCC.SETTLEMENT_ID = :settlementId                   " +
            "  WHERE TDMCS.LOAD_METER_ID IS NULL                   " +
            "        AND TDMCS.DATE = :date                   " +
            "        AND TDMCS.SETTLEMENT_ID = :settlementId                   " +
            "        AND TDMCS.COMPUTABLE = 1                   " +
            "        AND TDAGL.APPLICATION_ID IN (:appIdList)", nativeQuery = true)
    List<Map<String, Object>> findComputableGeneratorBySettlementIdAndApplicationIn(@Param("date") Date date, @Param("settlementId") Long settlementId, @Param("appIdList") List<Long> appIdList);

    @Query(value = "SELECT DISTINCT TDAGL.APPLICATION_ID, APPLICATION_LOAD_ID, TDMCS.NBS_CUSTOMER_NUMBER, METER_NO, TIME_STG, CONTRACT_STG        " +
            "    FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT TDMCS        " +
            "        INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_METER TDAM        " +
            "            ON TDMCS.ID = TDAM.DATE_METER_ID        " +
            "        INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD TDAGL        " +
            "            ON TDAGL.ID = TDAM.DATE_APPLICATION_ID        " +
            "        INNER JOIN SIMULATION_APPLICATION_LOAD AL        " +
            "            ON AL.ID = TDAGL.APPLICATION_LOAD_ID        " +
            "        INNER JOIN SIMULATION_LOAD_ENTITY LE        " +
            "            ON LE.ID = AL.LOAD_ID        " +
            "WHERE TDMCS.GENERATOR_METER_ID IS NULL        " +
            "  AND TDMCS.DATE = :date        " +
            "  AND TDMCS.SETTLEMENT_ID = :settlementId        " +
            "  AND TDMCS.COMPUTABLE = 1        " +
            "  AND TDAGL.APPLICATION_ID IN (:appIdList)", nativeQuery = true)
    List<Map<String, Object>> findComputableLoadBySettlementIdAndApplicationIn(@Param("date") Date date, @Param("settlementId") Long settlementId, @Param("appIdList") List<Long> appIdList);


}