package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyCapacityRecordId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyCapacityRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyCapacityRecord;

import java.util.List;
import java.util.Map;

/**
 * Repository of TempApplicationMonthlyCapacityRecord
 *
 * @class: TempApplicationMonthlyCapacityRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-17 11:02
 * @see:
 **/
public interface SimulationTempApplicationMonthlyCapacityRecordRepository extends JpaRepository<SimulationTempApplicationMonthlyCapacityRecord, ApplicationMonthlyCapacityRecordId> {

    /**
     * 只計算轉供和直供（Flexible Distribution 不算）
     * 步驟一
     *      從TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD.MATCHED_RM
     *      TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD.MATCHED_RM
     *      存入MATCHED_KW, ADJUSTED_MATCHED_KW
     *
     * @author:  ting
     * @date:    2025/03/11 21:40:26
     * @param:   [settlementId]
     * @return:  void
     **/
    @Transactional
    @Modifying
    @Query(value = "BEGIN     " +
            "        DECLARE @year SMALLINT = (SELECT TOP 1 YEAR FROM VIEW_PW_FUEL_RATE " +
            "                              WHERE YEAR = (SELECT YEAR(SERVICE_DATE) FROM SIMULATION_SETTLEMENT_CALCULATION " +
            "                                            WHERE SETTLEMENT_ID = :settlementId)) " +
            "    DECLARE @calculateYear SMALLINT = IIF(@year IS NOT NULL , @year, (SELECT TOP 1 YEAR FROM VIEW_PW_FUEL_RATE))     " +
            "     " +
            "    INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD(SETTLEMENT_ID     " +
            "                                                   , ENERGY_CHARGE_SECTION_ID     " +
            "                                                   , APPLICATION_GENERATOR_ID     " +
            "                                                   , APPLICATION_LOAD_ID     " +
            "                                                   , CAPACITY_CODE     " +
            "                                                   , PERCENTAGE     " +
            "                                                   , MATCHED_KW     " +
            "                                                   , ADJUSTED_MATCHED_KW     " +
            "                                                   , ANCILLARY_SERVICE_COST     " +
            "                                                   , DISPATCH_SERVICE_COST     " +
            "                                                   , POWER_TRANS_COST     " +
            "                                                   , POWER_DIST_COST)     " +
            "    SELECT DISTINCT SCC.SETTLEMENT_ID     " +
            "         , TAMDGLR.ENERGY_CHARGE_SECTION_ID     " +
            "         , TAMDGLR.APPLICATION_GENERATOR_ID     " +
            "         , TAMDGLR.APPLICATION_LOAD_ID     " +
            "         , SCC.CAPACITY_CODE     " +
            "         , SCC.PERCENTAGE     " +
            "         , TAMDGLR.MATCHED_RM * SCC.PERCENTAGE     " +
            "         , FLOOR(TAMDGLR.MATCHED_RM * SCC.PERCENTAGE)     " +
            "         , ANCILLARY     " +
            "         , DISPATCH     " +
            "         , 0     " +
            "         , 0     " +
            "    FROM SIMULATION_SETTLEMENT_CAPACITY_CALCULATION SCC     " +
            "             INNER JOIN SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD TAMDGLR     " +
            "                        ON TAMDGLR.APPLICATION_GENERATOR_ID = SCC.APPLICATION_GENERATOR_ID     " +
            "                            AND TAMDGLR.APPLICATION_GENERATOR_ID = SCC.APPLICATION_GENERATOR_ID     " +
            "             INNER JOIN SIMULATION_APPLICATION_GENERATOR AS AG     " +
            "                        ON AG.ID = SCC.APPLICATION_GENERATOR_ID     " +
            "             INNER JOIN SIMULATION_GENERATOR_ENTITY AS GE     " +
            "                        ON GE.ID = AG.GENERATOR_ID     " +
            "             INNER JOIN FUEL_TYPE AS FT     " +
            "                        ON GE.FUEL_TYPE = FT.ID     " +
            "             INNER JOIN VIEW_PW_FUEL_RATE AS VPFR     " +
            "                        ON VPFR.FUEL_TYPE_ID = FT.PW_FUEL_TYPE_ID     " +
            "             INNER JOIN APPLICATION_GENERATOR_LOAD_TYPE AS AGLT     " +
            "                        ON AGLT.APPLICATION_GENERATOR_ID = TAMDGLR.APPLICATION_GENERATOR_ID     " +
            "                            AND AGLT.APPLICATION_LOAD_ID = TAMDGLR.APPLICATION_LOAD_ID     " +
            "    WHERE SCC.SETTLEMENT_ID = :settlementId     " +
            "      AND SCC.PERCENTAGE IS NOT NULL     " +
            "      AND VPFR.YEAR = @calculateYear     " +
            "     " +
            "    INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD(SETTLEMENT_ID     " +
            "                                                   , ENERGY_CHARGE_SECTION_ID     " +
            "                                                   , APPLICATION_GENERATOR_ID     " +
            "                                                   , APPLICATION_LOAD_ID     " +
            "                                                   , CAPACITY_CODE     " +
            "                                                   , PERCENTAGE     " +
            "                                                   , MATCHED_KW     " +
            "                                                   , ADJUSTED_MATCHED_KW     " +
            "                                                   , ANCILLARY_SERVICE_COST     " +
            "                                                   , DISPATCH_SERVICE_COST     " +
            "                                                   , POWER_TRANS_COST     " +
            "                                                   , POWER_DIST_COST)     " +
            "    SELECT DISTINCT SCC.SETTLEMENT_ID     " +
            "         , TAMGLR.ENERGY_CHARGE_SECTION_ID     " +
            "         , TAMGLR.APPLICATION_GENERATOR_ID     " +
            "         , TAMGLR.APPLICATION_LOAD_ID     " +
            "         , SCC.CAPACITY_CODE     " +
            "         , SCC.PERCENTAGE     " +
            "         , TAMGLR.MATCHED_RM * SCC.PERCENTAGE     " +
            "         , FLOOR(TAMGLR.MATCHED_RM * SCC.PERCENTAGE)     " +
            "         , ANCILLARY     " +
            "         , DISPATCH     " +
            "         , IIF(AGLT.TYPE = 1 OR AGLT.TYPE = 3 , TRANSMISSION, 0)     " +
            "         , IIF(AGLT.TYPE = 2 OR AGLT.TYPE = 3 , DISTRIBUTION, 0)     " +
            "    FROM SIMULATION_SETTLEMENT_CAPACITY_CALCULATION SCC     " +
            "             INNER JOIN SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD TAMGLR     " +
            "                        ON TAMGLR.SETTLEMENT_ID = SCC.SETTLEMENT_ID     " +
            "                            AND TAMGLR.APPLICATION_GENERATOR_ID = SCC.APPLICATION_GENERATOR_ID     " +
            "             INNER JOIN SIMULATION_APPLICATION_GENERATOR AS AG     " +
            "                        ON AG.ID = SCC.APPLICATION_GENERATOR_ID     " +
            "             INNER JOIN SIMULATION_GENERATOR_ENTITY AS GE     " +
            "                        ON GE.ID = AG.GENERATOR_ID     " +
            "             INNER JOIN FUEL_TYPE AS FT     " +
            "                        ON GE.FUEL_TYPE = FT.ID     " +
            "             INNER JOIN VIEW_PW_FUEL_RATE AS VPFR     " +
            "                        ON VPFR.FUEL_TYPE_ID = FT.PW_FUEL_TYPE_ID     " +
            "             INNER JOIN APPLICATION_GENERATOR_LOAD_TYPE AS AGLT     " +
            "                        ON AGLT.APPLICATION_GENERATOR_ID = TAMGLR.APPLICATION_GENERATOR_ID     " +
            "                            AND AGLT.APPLICATION_LOAD_ID = TAMGLR.APPLICATION_LOAD_ID     " +
            "    WHERE SCC.SETTLEMENT_ID = :settlementId     " +
            "      AND SCC.PERCENTAGE IS NOT NULL     " +
            "      AND VPFR.YEAR = @calculateYear     " +
            "END      ", nativeQuery = true)
    void saveAllExceptFlexibleDistributionBeforeAdjustMatchedKw(@Param("settlementId") Long settlementId);


    @Query(value = "SELECT LOAD_CUSTOMER_NUMBER, SUM_MATCHED_KW, SUM_ADJUSTED_KW      " +
            "          FROM (SELECT LOAD_CUSTOMER_NUMBER, SUM(MATCHED_KW) AS SUM_MATCHED_KW, SUM(ADJUSTED_MATCHED_KW) AS SUM_ADJUSTED_KW      " +
            "              FROM (SELECT DISTINCT VALM.NBS_CUSTOMER_NUMBER AS LOAD_CUSTOMER_NUMBER, MATCHED_KW, ADJUSTED_MATCHED_KW      " +
            "                  FROM SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD TAMCR      " +
            "                           INNER JOIN VIEW_APPLICATION_LOAD_METER VALM      " +
            "                                      ON VALM.APPLICATION_LOAD_ID = TAMCR.APPLICATION_LOAD_ID      " +
            "                  WHERE SETTLEMENT_ID = :settlementId) SUBQUERY      " +
            "                  GROUP BY SUBQUERY.LOAD_CUSTOMER_NUMBER) THIRDQUERY      " +
            "              WHERE FLOOR(SUM_MATCHED_KW) != SUM_ADJUSTED_KW", nativeQuery = true)
    List<Map<String, Object>> findNeedAdjustMatchedKw(@Param("settlementId") Long settlementId);

    @Query(value = "SELECT  * FROM SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD TAMCR       " +
            "      WHERE APPLICATION_LOAD_ID IN (       " +
            "          SELECT APPLICATION_LOAD_ID FROM VIEW_APPLICATION_LOAD_METER VALM       " +
            "           WHERE  NBS_CUSTOMER_NUMBER = :loadCustomer)       " +
            "      AND SETTLEMENT_ID = :settlementId       " +
            "      ORDER BY MATCHED_KW DESC", nativeQuery = true)
    List<SimulationTempApplicationMonthlyCapacityRecord> findByDateAndSettlementIdAndLoadCustomerNumber(@Param("settlementId") Long settlementId, @Param("loadCustomer") String loadCustomer);

    /**
     * 只計算轉供和直供（Flexible Distribution 不算）
     * 第二步驟
     * after adjust matched_kw (取整)
     * @author:  ting
     * @date:    2025/02/19 16:10:42
     * @param:   [settlementId]
     * @return:  void
     **/
    @Transactional
    @Modifying
    @Query(value = "UPDATE TAMCS    " +
            "    SET TAMCS.ANCILLARY_SERVICE_COST = ROUND(TAMCS.ADJUSTED_MATCHED_KW * TAMCS.ANCILLARY_SERVICE_COST, 0)    " +
            "      , TAMCS.DISPATCH_SERVICE_COST = ROUND(TAMCS.ADJUSTED_MATCHED_KW * TAMCS.DISPATCH_SERVICE_COST, 0)    " +
            "      , TAMCS.POWER_TRANS_COST = ROUND(TAMCS.ADJUSTED_MATCHED_KW * TAMCS.POWER_TRANS_COST, 0)    " +
            "      , TAMCS.POWER_DIST_COST = ROUND(TAMCS.ADJUSTED_MATCHED_KW * TAMCS.POWER_DIST_COST, 0)    " +
            "    FROM SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD TAMCS    " +
            "    WHERE SETTLEMENT_ID = :settlementId", nativeQuery = true)
    void updateAllExceptFlexibleDistributionAfterAdjustMatchedKw(@Param("settlementId") Long settlementId);

    /**
     * 只計算轉供和直供（Flexible Distribution 不算）
     * 第三步驟
     * ADJUSTED_MATCHED_RM
     * IN TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD
     * TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD
     * @author:  ting
     * @date:    2025/02/19 16:10:42
     * @param:   [settlementId]
     * @return:  void
     **/
    @Transactional
    @Modifying
    @Query(value = "UPDATE TAMDGLR       " +
            "    SET TAMDGLR.ADJUSTED_MATCHED_RM = SUBSQL.SUM_ADJUSTED_MATCHED_KW       " +
            "FROM SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD TAMDGLR INNER JOIN       " +
            "     (SELECT SETTLEMENT_ID, APPLICATION_LOAD_ID, APPLICATION_GENERATOR_ID, ENERGY_CHARGE_SECTION_ID, SUM(ADJUSTED_MATCHED_KW) AS SUM_ADJUSTED_MATCHED_KW       " +
            "        FROM SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD TAMCR       " +
            "            WHERE SETTLEMENT_ID = :settlementId       " +
            "    GROUP BY SETTLEMENT_ID, APPLICATION_LOAD_ID, APPLICATION_GENERATOR_ID, ENERGY_CHARGE_SECTION_ID) AS SUBSQL       " +
            "        ON TAMDGLR.APPLICATION_GENERATOR_ID = SUBSQL.APPLICATION_GENERATOR_ID       " +
            "            AND TAMDGLR.APPLICATION_LOAD_ID = SUBSQL.APPLICATION_LOAD_ID       " +
            "            AND TAMDGLR.SETTLEMENT_ID = SUBSQL.SETTLEMENT_ID       " +
            "            AND TAMDGLR.ENERGY_CHARGE_SECTION_ID = SUBSQL.ENERGY_CHARGE_SECTION_ID       " +
            "       " +
            "UPDATE TAMGLR       " +
            "    SET TAMGLR.ADJUSTED_MATCHED_RM = SUBSQL.SUM_ADJUSTED_MATCHED_KW       " +
            "FROM SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD TAMGLR INNER JOIN       " +
            "     (SELECT SETTLEMENT_ID, APPLICATION_LOAD_ID, APPLICATION_GENERATOR_ID, ENERGY_CHARGE_SECTION_ID, SUM(ADJUSTED_MATCHED_KW) AS SUM_ADJUSTED_MATCHED_KW       " +
            "      FROM SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD TAMCR       " +
            "      WHERE SETTLEMENT_ID = :settlementId       " +
            "      GROUP BY SETTLEMENT_ID, APPLICATION_LOAD_ID, APPLICATION_GENERATOR_ID, ENERGY_CHARGE_SECTION_ID) AS SUBSQL       " +
            "     ON TAMGLR.APPLICATION_GENERATOR_ID = SUBSQL.APPLICATION_GENERATOR_ID       " +
            "         AND TAMGLR.APPLICATION_LOAD_ID = SUBSQL.APPLICATION_LOAD_ID       " +
            "         AND TAMGLR.SETTLEMENT_ID = SUBSQL.SETTLEMENT_ID       " +
            "         AND TAMGLR.ENERGY_CHARGE_SECTION_ID = SUBSQL.ENERGY_CHARGE_SECTION_ID", nativeQuery = true)
    void updateRelatedTableAfterAdjustMatchedKw(@Param("settlementId") Long settlementId);

}
