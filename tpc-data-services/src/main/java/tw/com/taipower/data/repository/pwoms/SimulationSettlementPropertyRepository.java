package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationSettlementProperty;

public interface SimulationSettlementPropertyRepository extends JpaRepository<SimulationSettlementProperty, String> {

    /**
     * 這個function遇到很奇怪問題，update應該不會有insert問題，但是，這裡卻遇到了，單純下sql語法不會有問題，
     * 一單使用此function就會發生。
     * 解決方式，刪除primary key在sql server的tool重建，問題就也解決了，使用intellijs sql plus還是要小心。
     *
     * @author:  ting
     * @date:    2024/11/13 09:38:06
     * @param:   [id, value]
     * @return:  void
     **/
    @Transactional
    @Modifying
    @Query(value = "UPDATE SIMULATION_SETTLEMENT_PROPERTY SET VALUE = :value WHERE ID = :id  ", nativeQuery = true)
    void updateValueById(@Param("id") String id, @Param("value") String value);
}
