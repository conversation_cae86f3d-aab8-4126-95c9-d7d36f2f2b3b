package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationSettlementCalculationFailureRecord;

import java.util.List;
import java.util.Map;

/**
 * Repository of SettlementCalculationFailureRecord
 *
 * @class: SettlementCalculationFailureRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-08 10:34
 * @see:
 **/

public interface SimulationSettlementCalculationFailureRecordRepository extends JpaRepository<SimulationSettlementCalculationFailureRecord, Long> {



}
