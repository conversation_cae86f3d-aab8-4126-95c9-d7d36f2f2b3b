package tw.com.taipower.data.entity.pwoms;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

/**
 * 發電端機組清單
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Entity
@Table(name = "SIMULATION_GENERATOR_ENTITY_COMBINED_CAPACITY")
@EqualsAndHashCode(callSuper = true)
public class SimulationGeneratorEntityCombinedCapacity extends BaseEntity {

    @Schema(description = "ID")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Schema(description = "對應發電端ID")
    @Column(name = "GENERATOR_ENTITY_ID")
    private Long generatorEntityId;

    @Schema(description = "裝置容量")
    @Column(name = "CAPACITY")
    private java.math.BigDecimal capacity;

    @Schema(description = "機組併聯首日（表示裝上電表、開始送電試運轉）")
    @Column(name = "COMBINED_DATE")
    private java.util.Date combinedDate;

    @Schema(description = "機組取得執照日")
    @Column(name = "LICENSE_DATE")
    private java.util.Date licenseDate;

}
