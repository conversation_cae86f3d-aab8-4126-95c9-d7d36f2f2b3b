package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Bill Settlement
 *
 * @class: BillSettlement
 * @author:  ting
 * @version: 0.1.0
 * @since: 2025/02/05 17:14:28
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "SIMULATION_BILL_SETTLEMENT")
@IdClass(BillSettlementId.class)
public class SimulationBillSettlement {

    @Id
    @Column(name = "SETTLEMENT_ID")
    private Long settlementId;

    @Id
    @Column(name = "BILL_ID")
    private Long billId;

    @Id
    @Column(name = "APPLICATION_ID")
    private Long applicationId;

    @Column(name = "BILL_DATE")
    private Date billDate;

    @Column(name = "COST")
    private BigDecimal cost;
}
