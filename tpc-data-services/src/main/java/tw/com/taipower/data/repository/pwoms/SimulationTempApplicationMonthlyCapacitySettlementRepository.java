package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyCapacitySettlementId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyCapacitySettlement;

/**
 * Repository of TempApplicationMonthlyCapacitySettlement
 *
 * @class: TempApplicationMonthlyCapacitySettlementRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-17 11:02
 * @see:
 **/
public interface SimulationTempApplicationMonthlyCapacitySettlementRepository extends JpaRepository<SimulationTempApplicationMonthlyCapacitySettlement, ApplicationMonthlyCapacitySettlementId> {

    @Transactional
    @Modifying
    @Query(value ="BEGIN       " +
            "    DELETE SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD       " +
            "        WHERE SETTLEMENT_ID = :settlementId               " +
            "    DELETE SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT  " +
            "        WHERE SETTLEMENT_ID = :settlementId       " +
            "    DELETE SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT       " +
            "        WHERE SETTLEMENT_ID = :settlementId       " +
            "END ", nativeQuery = true)
    void deleteAllRecordBySettlementId(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = " INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT(     " +
            "    SETTLEMENT_ID     " +
            "    , APPLICATION_GENERATOR_ID     " +
            "    , APPLICATION_LOAD_ID     " +
            "    , CAPACITY_CODE     " +
            "    , ADJUSTED_MATCHED_KW     " +
            "    , ANCILLARY_SERVICE_COST     " +
            "    , DISPATCH_SERVICE_COST     " +
            "    , POWER_TRANS_COST     " +
            "    , POWER_DIST_COST     " +
            ")     " +
            "SELECT SETTLEMENT_ID     " +
            "     , APPLICATION_GENERATOR_ID     " +
            "     , APPLICATION_LOAD_ID     " +
            "     , CAPACITY_CODE     " +
            "     , SUM(ADJUSTED_MATCHED_KW)     " +
            "     , SUM(ANCILLARY_SERVICE_COST)     " +
            "     , SUM(DISPATCH_SERVICE_COST)     " +
            "     , SUM(POWER_TRANS_COST)     " +
            "     , SUM(POWER_DIST_COST)     " +
            "FROM SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD     " +
            "    WHERE SETTLEMENT_ID = :settlementId     " +
            "GROUP BY SETTLEMENT_ID, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID, CAPACITY_CODE", nativeQuery = true)
    void saveAllRecordBySettlementId(@Param("settlementId") Long settlementId);



}
