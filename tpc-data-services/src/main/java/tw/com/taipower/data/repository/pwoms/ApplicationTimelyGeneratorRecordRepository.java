package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import tw.com.taipower.data.entity.pwoms.ApplicationTimelyGeneratorRecord;
import tw.com.taipower.data.entity.pwoms.ApplicationTimelyGeneratorRecordColumnId;
import tw.com.taipower.data.vo.powms.IMatchedRmEcChangeId;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of ApplicationTimelyGeneratorRecord
 *
 * @class: ApplicationTimelyGeneratorRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface ApplicationTimelyGeneratorRecordRepository extends JpaRepository<ApplicationTimelyGeneratorRecord, ApplicationTimelyGeneratorRecordColumnId> {

    /**
     * #3 #4 15分月發電端媒合電量 累加 媒合度數 起迄時間區間 matchedRm(轉供媒合度數), dirMatched(直供媒合度數) ENERGY_CHARGE_SECTION_ID
     * 輸入 SETTLEMENT_ID 列表 + 起訖服務時間[迄=換表時間] + applicationGeneratorId
     * @param settlementId
     * @param applicationGeneratorId
     * @param from 該月使用起始日
     * @param to 結束日=換表日
     * @return
     */
    @Query(value =
            "Select apTGen.ENERGY_CHARGE_SECTION_ID as ecChangeId, ROUND(sum(apTGen.MATCHED_RM), 0) as matchedRm, null as dirMatched" +
                    " from APPLICATION_TIMELY_GENERATOR_RECORD as apTGen" +
                    " where apTGen.SETTLEMENT_ID in ?1 and apTGen.APPLICATION_GENERATOR_ID = ?2 and apTGen.DATETIME >= ?3 and apTGen.DATETIME < ?4" +
                    "  group by apTGen.ENERGY_CHARGE_SECTION_ID Union " +
                    "Select apDTGen.ENERGY_CHARGE_SECTION_ID as ecChangeId, null as matchedRm, ROUND(sum(apDTGen.MATCHED_RM), 0) as dirMatched" +
                    " from APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD as apDTGen" +
                    " where apDTGen.SETTLEMENT_ID in ?1 and apDTGen.APPLICATION_GENERATOR_ID = ?2 and apDTGen.DATETIME >= ?3 and apDTGen.DATETIME < ?4" +
                    "  group by apDTGen.ENERGY_CHARGE_SECTION_ID" +
                    " order by ecChangeId ASC"
            , nativeQuery = true)
    List<IMatchedRmEcChangeId> sumMatchedRm13911ByApplicationGeneratorIdBetweenDateRange(List<Long> settlementId
            , Long applicationGeneratorId, Date from, Date to);

    /** #7 月15分發電端媒合度數(限定 單月 搜尋) - 轉供 applicationGeneratorId, time(運作時間), matchedRm(轉供媒合度數), dirMatched(直供媒合度數) + SETTLEMENT_ID
     * 注意搜集的 applicationGeneratorId 需排除彈性分配 契約編號開頭 4 的資料
     * 業務處>再購組 每月發電端每15分鐘轉直供度數(96欄位) 每日 15分鐘 媒合度數
     * @param settlementId
     * @return
     */
    @Query(value =
            "Select DISTINCT apTGen.DATETIME as time, apTGen.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", sum(apTGen.MATCHED_RM) over (partition by apTGen.DATETIME, apTGen.APPLICATION_GENERATOR_ID) as matchedRm" +
                    ", null as dirMatched" +
                    " from APPLICATION_TIMELY_GENERATOR_RECORD as apTGen" +
                    " where apTGen.SETTLEMENT_ID = ?1" +
                    " UNION Select DISTINCT apDTGen.DATETIME as time, apDTGen.APPLICATION_GENERATOR_ID as applicationGeneratorId" +
                    ", null as matchedRm, sum(apDTGen.MATCHED_RM) over (partition by apDTGen.DATETIME, apDTGen.APPLICATION_GENERATOR_ID) as dirMatched" +
                    " from APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD as apDTGen" +
                    "  where apDTGen.SETTLEMENT_ID = ?1" +
                    " order by applicationGeneratorId ASC, time ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findGen15MinutesMatchedRmByDate(Long settlementId);

    List<ApplicationTimelyGeneratorRecord> findByGeneratorIdIn(List<Long> generatorIdList);

    List<ApplicationTimelyGeneratorRecord> findByDatetimeBetweenOrderByDatetimeAsc(Date startTime, Date endTime);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_TIMELY_GENERATOR_RECORD WHERE DATETIME = ?1", nativeQuery = true)
    void deleteByDatetime(Date datetime);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_TIMELY_GENERATOR_RECORD WHERE DATETIME = ?1 AND APPLICATION_GENERATOR_ID IN (?2)", nativeQuery = true)
    void deleteByDatetimeAndGeneratorIdIn(Date datetime, List<Long> generatorIdList);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_TIMELY_GENERATOR_RECORD WHERE DATETIME BETWEEN ?1 AND ?2", nativeQuery = true)
    void deleteByDatetimeBetween(Date startTime, Date endTime);
}
