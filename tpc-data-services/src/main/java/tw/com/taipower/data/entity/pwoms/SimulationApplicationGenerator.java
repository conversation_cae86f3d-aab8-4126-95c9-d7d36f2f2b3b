package tw.com.taipower.data.entity.pwoms;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import tw.com.taipower.data.converters.ALGKeepEnumConverter;
import tw.com.taipower.data.enums.ALGKeepEnum;

import java.math.BigDecimal;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Entity
@Table(name = "SIMULATION_APPLICATION_GENERATOR")
public class SimulationApplicationGenerator {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "APPLICATION_ID")
    private Long applicationId;

    @Column(name = "GENERATOR_ID")
    private Long generatorId;

    @Schema(description = "（發電或餘電）轉供比例")
    @Column(name = "PMI")
    private BigDecimal pmi;


}
