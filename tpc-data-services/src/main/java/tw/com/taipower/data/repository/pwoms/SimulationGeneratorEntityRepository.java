package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import tw.com.taipower.data.entity.pwoms.GeneratorEntity;
import tw.com.taipower.data.entity.pwoms.SimulationGeneratorEntity;

import java.util.Date;
import java.util.List;

import static tw.com.taipower.data.constant.Constants.*;

public interface SimulationGeneratorEntityRepository extends JpaRepository<SimulationGeneratorEntity, Long>,
        JpaSpecificationExecutor<SimulationGeneratorEntity> {

}
