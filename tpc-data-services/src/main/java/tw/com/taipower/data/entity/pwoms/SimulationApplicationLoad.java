package tw.com.taipower.data.entity.pwoms;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import tw.com.taipower.data.converters.ALGKeepEnumConverter;
import tw.com.taipower.data.enums.ALGKeepEnum;

import java.math.BigDecimal;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Entity
@Table(name = "SIMULATION_APPLICATION_LOAD")
public class SimulationApplicationLoad {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "APPLICATION_ID")
    private Long applicationId;

    @Column(name = "LOAD_ID")
    private Long loadId;

    @Schema(description = "每月轉供度數契約上限")
    @Column(name = "MONTHLY_CONTRACT_CAP")
    private Long monthlyContractCap;

    @Schema(description = "年度轉供度數契約上限")
    @Column(name = "ANNUAL_CONTRACT_CAP")
    private Long annualContractCap;

    @Schema(description = "是否為直供")
    @Column(name = "IS_DIRECT")
    private Boolean isDirect;


}
