package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Calculation Trial SUM Capacity
 *
 * @class: SettlementSumCapacityCalculation
 * @author:  ting
 * @version: 0.1.0
 * @since: 2025/02/05 17:14:28
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION")
@IdClass(SettlementSumCapacityCalculationId.class)
public class SimulationSettlementSumCapacityCalculation {

    @Id
    @Column(name = "SETTLEMENT_ID", nullable = false)
    private Long settlementId;

    @Id
    @Column(name = "APPLICATION_GENERATOR_ID")
    private Long generatorId;

    @Column(name = "CAPACITY")
    private BigDecimal capacity;

    @Column(name = "COMPUTABLE_CAPACITY")
    private BigDecimal cmpCapacity;

    @Column(name = "COMPUTABLE_CAPACITY_PARALLEL_DAY")
    private BigDecimal cmpCapParallelDay;

    @Column(name = "PERCENTAGE")
    private BigDecimal percentage;



}
