package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationRelation;
import tw.com.taipower.data.entity.pwoms.ApplicationRelationId;
import tw.com.taipower.data.entity.pwoms.SimulationApplicationRelation;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of Application Relation
 *
 * @class: ApplicationRelationRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-11 10:04
 * @see:
 **/

public interface SimulationApplicationRelationRepository extends JpaRepository<SimulationApplicationRelation, ApplicationRelationId> {

    @Query(value = "SELECT DISTINCT RELATION_ID FROM SIMULATION_APPLICATION_RELATION          " +
            "   WHERE APPLICATION_ID IN (:appIdList)          " +
            "     AND DATE BETWEEN :startDate AND :endDate " , nativeQuery = true)
    List<Long> findRelationIdByDateIntervalAndApplicationIdIn(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("appIdList") List<Long> appIdList);

    @Query(value = "BEGIN " +
            "    SELECT DISTINCT * FROM SIMULATION_APPLICATION_RELATION " +
            "    WHERE APPLICATION_ID IN (" +
            "        SELECT TDACS.APPLICATION_ID FROM  SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS " +
            "             WHERE COMPUTABLE = 1 " +
            "                AND DATE = :date " +
            "               AND SETTLEMENT_ID = :settlementId)" +
            "     AND  RELATION_ID IN (SELECT TDACS.APPLICATION_ID FROM  SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS " +
            "                         WHERE COMPUTABLE = 1 " +
            "                           AND DATE = :date " +
            "                           AND SETTLEMENT_ID = :settlementId) " +
            "      AND DATE = :date " +
            "END ", nativeQuery = true)
    List<ApplicationRelation> findApplicationRelationByAppDateComputable(@Param("date") Date date, @Param("settlementId") Long settlementId);

    @Query(value = "BEGIN  " +
            "    SELECT DISTINCT APPLICATION_ID, RELATION_ID FROM SIMULATION_APPLICATION_RELATION  " +
            "    WHERE APPLICATION_ID IN (  " +
            "        SELECT TDACS.APPLICATION_ID FROM  SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS  " +
            "        WHERE COMPUTABLE = 1  " +
            "          AND DATE BETWEEN :startDate AND :endDate  " +
            "          AND SETTLEMENT_ID = :settlementId)  " +
            "      AND  RELATION_ID IN (SELECT TDACS.APPLICATION_ID FROM  SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT TDACS  " +
            "                           WHERE COMPUTABLE = 1  " +
            "                             AND DATE BETWEEN :startDate AND :endDate  " +
            "                             AND SETTLEMENT_ID = :settlementId)  " +
            "      AND DATE BETWEEN :startDate AND :endDate  " +
            " END ", nativeQuery = true)
    List<Map<String, Object>> findByComputableDateIntervalAndSettlementId(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId);


}