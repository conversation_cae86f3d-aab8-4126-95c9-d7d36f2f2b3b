package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyGeneratorLoadRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of ApplicationMonthlyGeneratorLoadRecord
 *
 * @class: ApplicationMonthlyGeneratorLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationMonthlyGeneratorLoadRecordRepository extends JpaRepository<SimulationTempApplicationMonthlyGeneratorLoadRecord, ApplicationMonthlyGeneratorLoadRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "END ", nativeQuery = true)
    void deleteAllRecordBySettlementId(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD(  " +
            "                DATE  " +
            "              , ENERGY_CHARGE_SECTION_ID  " +
            "              , APPLICATION_GENERATOR_ID  " +
            "              , APPLICATION_LOAD_ID  " +
            "              , MATCHED_RM  " +
            "              , ADJUSTED_MATCHED_RM  " +
            "              , SETTLEMENT_ID)  " +
            "SELECT FIRST_DAY  " +
            "     , TAMRGLR.ENERGY_CHARGE_SECTION_ID  " +
            "     , TAMRGLR.APPLICATION_GENERATOR_ID  " +
            "     , TAMRGLR.APPLICATION_LOAD_ID  " +
            "     , (MATCHED_RM + SUM_MATCHED_RM)  " +
            "     , FLOOR(MATCHED_RM + SUM_MATCHED_RM)  " +
            "     , TAMRGLR.SETTLEMENT_ID  " +
            "    FROM SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD AS TAMRGLR  " +
            "    LEFT JOIN (  " +
            "        SELECT CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)) AS FIRST_DAY  " +
            "             , ENERGY_CHARGE_SECTION_ID  " +
            "             , APPLICATION_GENERATOR_ID  " +
            "             , APPLICATION_LOAD_ID  " +
            "             , SUM(MATCHED_RM) AS SUM_MATCHED_RM  " +
            "             , SUM(MATCHED_RM) AS SUM_MATCHED_RM2  " +
            "             , SETTLEMENT_ID  " +
            "        FROM SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD  " +
            "        WHERE DATE BETWEEN :startTime AND :endTime  " +
            "          AND SETTLEMENT_ID = :settlementId  " +
            "  " +
            "        GROUP BY CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)), ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID, SETTLEMENT_ID  " +
            "    ) AS TADGLR  " +
            "    ON TADGLR.APPLICATION_GENERATOR_ID = TAMRGLR.APPLICATION_GENERATOR_ID  " +
            "    AND TADGLR.APPLICATION_LOAD_ID = TAMRGLR.APPLICATION_LOAD_ID  " +
            "    AND TADGLR.ENERGY_CHARGE_SECTION_ID = TAMRGLR.ENERGY_CHARGE_SECTION_ID  " +
            "    WHERE TAMRGLR.SETTLEMENT_ID = :settlementId  " +
            "      AND TAMRGLR.APPLICATION_GENERATOR_ID IN (:appGenList)  " +
            "      AND TAMRGLR.APPLICATION_LOAD_ID IN (:appLoadList)  " +
            "      AND TAMRGLR.SETTLEMENT_ID = :settlementId " +
            "      AND TAMRGLR.DATE = FIRST_DAY ", nativeQuery = true)
    void saveByRematchRecordAndDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId
            , @Param("appGenList")List<Long> appGenList, @Param("appLoadList") List<Long> appLoadList);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD(   " +
            "                           DATE   " +
            "                         , ENERGY_CHARGE_SECTION_ID   " +
            "                         , APPLICATION_GENERATOR_ID   " +
            "                         , APPLICATION_LOAD_ID   " +
            "                         , MATCHED_RM   " +
            "                         , ADJUSTED_MATCHED_RM   " +
            "                         , SETTLEMENT_ID)   " +
            "SELECT FIRST_DAY   " +
            "     , ENERGY_CHARGE_SECTION_ID   " +
            "     , APPLICATION_GENERATOR_ID   " +
            "     , APPLICATION_LOAD_ID   " +
            "     , SUM_MATCHED_RM   " +
            "     , ADJUSTED_MATCHED_RM   " +
            "     , SETTLEMENT_ID   " +
            "FROM(SELECT CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)) AS FIRST_DAY   " +
            "          , ENERGY_CHARGE_SECTION_ID   " +
            "          , APPLICATION_GENERATOR_ID   " +
            "          , APPLICATION_LOAD_ID   " +
            "          , SUM(MATCHED_RM) AS SUM_MATCHED_RM   " +
            "          , FLOOR(SUM(MATCHED_RM)) AS ADJUSTED_MATCHED_RM   " +
            "          , SETTLEMENT_ID   " +
            "     FROM SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD   " +
            "     WHERE DATE BETWEEN :startTime AND :endTime   " +
            "       AND SETTLEMENT_ID = :settlementId   " +
            "       AND APPLICATION_GENERATOR_ID IN (:appGenList)   " +
            "       AND APPLICATION_LOAD_ID IN (:appLoadList)   " +
            "     GROUP BY CONVERT(DATE, DATEADD(DAY, - DATEPART(DAY, DATE) + 1, DATE)), ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID, SETTLEMENT_ID) AS SUBQUERY      ", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId
            , @Param("appGenList")List<Long> appGenList, @Param("appLoadList") List<Long> appLoadList);


}