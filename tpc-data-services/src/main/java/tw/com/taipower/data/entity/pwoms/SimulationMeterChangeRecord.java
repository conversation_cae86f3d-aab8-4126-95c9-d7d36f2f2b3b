package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Record Meter Change
 *
 * @class: MeterChangeRecord
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-06 13:54
 * @see:
 **/

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Entity
@IdClass(MeterChangeRecordId.class)
@Table(name = "SIMULATION_METER_CHANGE_RECORD")
public class SimulationMeterChangeRecord {

    @Id
    @Column(name = "SETTLEMENT_ID")
    protected Long settlementId;

    @Id
    @Column(name = "NBS_CUSTOMER_NUMBER")
    protected String customerNumber;

    @Id
    @Column(name = "METER_NO")
    protected String meterNo;

    @Column(name = "APPLICATION_LOAD_ID")
    protected Long loadId;

    @Column(name = "APPLICATION_GENERATOR_ID")
    protected Long generatorId;

    @Column(name = "USE_FROM")
    private Date useFrom;

    @Column(name = "USE_TO")
    private Date useTo;
}