package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * trial settlement
 *
 * @class: TempApplicationMonthlyTrialSettlement
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-10-24 08:44
 * @see:
 **/

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Entity
@IdClass(TempApplicationMonthlyTrialSettlementId.class)
@Table(name = "SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT")
public class SimulationTempApplicationMonthlyTrialSettlement {

    @Id
    @Column(name = "SETTLEMENT_ID")
    private Long settlementId;

    @Id
    @Column(name = "CAPACITY_CODE")
    private String capCode;

    @Id
    @Column(name = "APPLICATION_LOAD_ID")
    private Long loadId;

    @Id
    @Column(name = "APPLICATION_GENERATOR_ID")
    private Long generatorId;

    @Column(name = "CURRENT_KW")
    private BigDecimal currentKW;

    @Column(name = "TRIAL_KW")
    private BigDecimal trialKw;

    @Column(name = "VARIANCE_KW")
    private BigDecimal varianceKw;

    @Column(name = "VARIANCE_KW_PERCENT")
    private BigDecimal varianceKwPercent;

    @Column(name = "CURRENT_COST")
    private BigDecimal currentCost;

    @Column(name = "TRAIL_COST")
    private BigDecimal trialCost;

    @Column(name = "VARIANCE_COST")
    private BigDecimal varianceCost;

    @Column(name = "VARIANCE_COST_PERCENT")
    private BigDecimal varianceCostPercent;

}