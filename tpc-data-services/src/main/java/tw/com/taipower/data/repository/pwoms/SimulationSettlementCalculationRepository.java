package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SettlementCalculation;
import tw.com.taipower.data.entity.pwoms.SimulationSettlementCalculation;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of SettlementCalculation
 *
 * @class: SimulationSettlementCalculationRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-08 10:34
 * @see:
 **/

public interface SimulationSettlementCalculationRepository extends JpaRepository<SimulationSettlementCalculation, Long> {

    @Query(
            value = "SELECT TOP 1 SETTLEMENT_ID FROM SIMULATION_SETTLEMENT_CALCULATION      WHERE CALCULATION_METHOD = :calMethod      ORDER BY EXECUTION_START DESC",
            nativeQuery = true
    )
    Long findTop1CalculationMethod(@Param("calMethod") Short var1);

    @Transactional
    @Modifying
    @Query(value = "BEGIN     " +
            "    DELETE SIMULATION_METER_CHANGE_RECORD " +
            "      WHERE SETTLEMENT_ID = :settlementId " +
            "    DELETE SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION     " +
            "        WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_SETTLEMENT_CAPACITY_CALCULATION     " +
            "        WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD     " +
            "        WHERE SETTLEMENT_RECORD_ID IN (SELECT ID FROM SIMULATION_SETTLEMENT_CALCULATION_RECORD     " +
            "                        WHERE SETTLEMENT_ID = :settlementId     " +
            "    )     " +
            "    DELETE SIMULATION_SETTLEMENT_CALCULATION_RECORD     " +
            "        WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_BILL_SETTLEMENT     " +
            "        WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_SETTLEMENT_CALCULATION     " +
            "        WHERE SETTLEMENT_ID = :settlementId     " +
            "END " , nativeQuery = true)
    void deleteAllRecordBySettlementId(@Param("settlementId") Long settlementId);


    @Transactional
    @Modifying
    @Query(value = "UPDATE SIMULATION_SETTLEMENT_CALCULATION  " +
            "    SET EXECUTION_PROGRESS = :log" +
            "       , EXECUTION_PERCENTAGE = :percentage  " +
            "       , CALCULATION_METHOD = :calMethod  " +
            " WHERE SETTLEMENT_ID = :settlementId" , nativeQuery = true)
    void updateCalculationMethodAndProgressBySettlementId(@Param("settlementId") Long settlementId, @Param("calMethod") Short calMethod, @Param("log") String log, @Param("percentage") BigDecimal percentage);

    @Query(value = "SELECT APPLICATION_ID, SETTLEMENT_ID, ERP_SENT_DATE, ERP_CANCELLATION_DATE           " +
            "    FROM VIEW_BILL_SETTLEMENT VBS           " +
            "        WHERE SERVICE_DATE = :serviceDate           " +
            "          AND APPLICATION_ID IN (:appIdList)           " +
            "          AND ERP_CANCELLATION_DATE IS NULL           " +
            "          AND CALCULATION_METHOD IN (:calMethodList)           " +
            "ORDER BY ERP_SENT_DATE DESC ", nativeQuery = true)
    List<Map<String, Object>> findLatestIdByServiceDateAndApplicationIdInAndErpSentDateIsNotNull(
            @Param("serviceDate") Date serviceDate
            , @Param("appIdList") List<Long> appIdList
            , @Param("calMethodList") List<Short> calMethodList);

    @Query(value = "SELECT APPLICATION_ID, SETTLEMENT_ID, ERP_SENT_DATE, ERP_CANCELLATION_DATE           " +
            "    FROM VIEW_BILL_SETTLEMENT VBS           " +
            "    WHERE SERVICE_DATE = :serviceDate           " +
            "      AND APPLICATION_ID IN (:appIdList)           " +
            "      AND ERP_CANCELLATION_DATE IS NOT NULL           " +
            "      AND CALCULATION_METHOD IN (:calMethodList)           " +
            "ORDER BY ERP_SENT_DATE, ERP_CANCELLATION_DATE DESC", nativeQuery = true)
    List<Map<String, Object>> findLatestIdByServiceDateAndApplicationIdInAndErpCancellationDateIsNotNull(
            @Param("serviceDate") Date serviceDate
            , @Param("appIdList") List<Long> appIdList
            , @Param("calMethodList") List<Short> calMethodList);

    @Query(value = "SELECT SC.SETTLEMENT_ID, SCR.APPLICATION_ID FROM SIMULATION_SETTLEMENT_CALCULATION SC           " +
            "    INNER JOIN SIMULATION_SETTLEMENT_CALCULATION_RECORD SCR           " +
            "            ON SC.SETTLEMENT_ID = SCR.SETTLEMENT_ID           " +
            "            AND SCR.APPLICATION_ID IN (:appIdList)           " +
            "            AND SCR.ID NOT IN (           " +
            "                SELECT SC_RECORD_ID           " +
            "                FROM VIEW_BILL_SETTLEMENT VBS           " +
            "                WHERE SERVICE_DATE = :serviceDate           " +
            "                  AND APPLICATION_ID IN (:appIdList)           " +
            "                  AND CALCULATION_METHOD IN (:calMethodList))           " +
            "    WHERE SERVICE_DATE = :serviceDate           " +
            "      AND CALCULATION_METHOD IN (:calMethodList)           " +
            "      AND SC.SETTLEMENT_ID != (:settlementId)           " +
            "      AND SC.EXECUTION_RESULT = 1 " +
            "      AND SCR.EXECUTION_RESULT = 1 ", nativeQuery = true)
    List<Map<String, Object>> findLatestIdByServiceDateAndApplicationIdInAndNotInErpAndNotSettlementId(
            @Param("settlementId") Long settlementId
            , @Param("serviceDate") Date serviceDate
            , @Param("appIdList") List<Long> appIdList
            , @Param("calMethodList") List<Short> calMethodList);

    @Transactional
    @Modifying
    @Query(value = "" +
            "  UPDATE SIMULATION_SETTLEMENT_CALCULATION_RECORD " +
            "    SET EXECUTION_RESULT = :result " +
            "  WHERE SETTLEMENT_ID = :settlementId " +
            "  AND APPLICATION_ID IN (:appIdList) ", nativeQuery = true)
    void updateResultBySettlementIdAndApplicationIdIn(@Param("settlementId") Long settlementId
            , @Param("appIdList") List<Long> appIdList
            , @Param("result") Boolean result);

    @Transactional
    @Modifying
    @Query(value = "" +
            "  UPDATE SIMULATION_SETTLEMENT_CALCULATION_RECORD " +
            "    SET EXECUTION_RESULT = :result " +
            "  WHERE SETTLEMENT_ID = :settlementId " +
            "  AND EXECUTION_RESULT IS NULL ", nativeQuery = true)
    void updateResultBySettlementIdAndExecutionResultIsNull(@Param("settlementId") Long settlementId
            , @Param("result") Boolean result);

    @Transactional
    @Modifying
    @Query(value = "" +
            "  UPDATE SIMULATION_SETTLEMENT_CALCULATION " +
            "    SET EXECUTION_RESULT = IIF((SELECT COUNT(EXECUTION_RESULT) FROM SIMULATION_SETTLEMENT_CALCULATION_RECORD " +
            "                                WHERE EXECUTION_RESULT = 0) > 0, 0, 1) " +
            "  WHERE SETTLEMENT_ID = :settlementId" , nativeQuery = true)
    void updateResultBySettlementId(@Param("settlementId") Long settlementId);

    SimulationSettlementCalculation findBySettlementId(Long settlementId);

}
