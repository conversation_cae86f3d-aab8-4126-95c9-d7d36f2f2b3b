package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import tw.com.taipower.data.entity.pwoms.ApplicationGenerator;
import tw.com.taipower.data.entity.pwoms.PmiUsage;
import tw.com.taipower.data.entity.pwoms.SimulationApplicationGenerator;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static tw.com.taipower.data.constant.Constants.*;

public interface SimulationApplicationGeneratorRepository extends JpaRepository<SimulationApplicationGenerator, Long> {

    @Query(value = "SELECT ID, APPLICATION_ID FROM SIMULATION_APPLICATION_GENERATOR " +
            "    WHERE APPLICATION_ID IN (:appIdList) " +
            "        AND PMI IS NULL ", nativeQuery = true)
    List<Map<String, Object>> findIdAndApplicationIdByAppIdInAndPmiIsNull(@Param("appIdList") List<Long> appIdList);

    @Query(value = "SELECT AG2.ID, APPLICATION_ID FROM SIMULATION_APPLICATION_GENERATOR AG2 " +
            "    WHERE AG2.ID NOT IN ( " +
            "    SELECT AG.ID FROM SIMULATION_APPLICATION_GENERATOR AG " +
            "      INNER JOIN SIMULATION_GENERATOR_ENTITY_COMBINED_CAPACITY GECC " +
            "        ON AG.GENERATOR_ID = GECC.GENERATOR_ENTITY_ID " +
            "        WHERE AG.APPLICATION_ID IN (:appIdList)) " +
            "      AND AG2.APPLICATION_ID IN (:appIdList)  ", nativeQuery = true)
    List<Map<String, Object>> findIdAndApplicationIdByAppIdInAndCapacityIsNull(@Param("appIdList") List<Long> appIdList);

}