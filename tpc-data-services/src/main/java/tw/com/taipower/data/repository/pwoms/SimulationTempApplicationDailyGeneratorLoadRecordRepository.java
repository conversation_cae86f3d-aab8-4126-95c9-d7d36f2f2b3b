package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationDailyGeneratorLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationDailyGeneratorLoadRecord;

import java.util.Date;
import java.util.List;

/**
 * Repository of TempApplicationDailyGeneratorLoadRecord
 *
 * @class:  TempApplicationDailyGeneratorLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-28 23:40
 * @see:
 **/

public interface SimulationTempApplicationDailyGeneratorLoadRecordRepository extends JpaRepository<SimulationTempApplicationDailyGeneratorLoadRecord, ApplicationDailyGeneratorLoadRecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "END ", nativeQuery = true)
    void deleteAllRecordBySettlementId(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD(   " +
            "             DATE   " +
            "           , ENERGY_CHARGE_SECTION_ID   " +
            "           , APPLICATION_GENERATOR_ID   " +
            "           , APPLICATION_LOAD_ID   " +
            "           , MATCHED_RM   " +
            "           , SETTLEMENT_ID)   " +
            "SELECT CAST(DATETIME AS DATE) AS DATE   " +
            "     , ENERGY_CHARGE_SECTION_ID   " +
            "     , APPLICATION_GENERATOR_ID   " +
            "     , APPLICATION_LOAD_ID   " +
            "     , SUM(MATCHED_RM) AS SUM_MATCHED_RM   " +
            "     , SETTLEMENT_ID   " +
            "FROM SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD   " +
            "WHERE DATETIME BETWEEN :startTime AND :endTime   " +
            "  AND SETTLEMENT_ID = :settlementId   " +
            "GROUP BY CAST(DATETIME AS DATE), ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, APPLICATION_LOAD_ID, SETTLEMENT_ID   ", nativeQuery = true)
    void saveByDateIntervalAndSettlementId(@Param("startTime") Date startTime, @Param("endTime")Date endTime, @Param("settlementId") Long settlementId);


}