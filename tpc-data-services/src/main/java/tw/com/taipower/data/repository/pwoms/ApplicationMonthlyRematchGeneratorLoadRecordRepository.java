package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyGeneratorLoadRecordColumnId;
import tw.com.taipower.data.entity.pwoms.ApplicationMonthlyRematchGeneratorLoadRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * Repository of ApplicationDailyGeneratorLoadRecord
 *
 * @class: ApplicationMonthlyRematchGeneratorLoadRecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-05-30 17:27
 * @see:
 **/

public interface ApplicationMonthlyRematchGeneratorLoadRecordRepository extends JpaRepository<ApplicationMonthlyRematchGeneratorLoadRecord, ApplicationMonthlyGeneratorLoadRecordColumnId> {

    String joinGeneratorEnd = "  join APPLICATION_GENERATOR as ap on reGen.APPLICATION_GENERATOR_ID = ap.ID" +
            "  join GENERATOR_ENTITY as gEntity on ap.GENERATOR_ID = gEntity.ID";
    String joinVirtualGeneratorEnd = " Left outer join VIRTUAL_GENERATOR_COMPANY_DETAIL as virGenDetail on virGenDetail.GENERATOR_ID= gEntity.ID" +
            "  and virGenDetail.VIRTUAL_COMPANY_ID > 0" +
            " Left outer join VIRTUAL_GENERATOR_COMPANY as virGen on virGenDetail.VIRTUAL_COMPANY_ID = virGen.ID";
    String innerRematchGeneratorRecord = " inner join(Select DATE, ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID, MAX(SETTLEMENT_ID) as settleId" +
            "   from APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD" +
            "     group by DATE, ENERGY_CHARGE_SECTION_ID, APPLICATION_GENERATOR_ID) cc" +
            "         on reGen.DATE = cc.DATE and reGen.ENERGY_CHARGE_SECTION_ID = cc.ENERGY_CHARGE_SECTION_ID" +
            "            and reGen.APPLICATION_GENERATOR_ID = cc.APPLICATION_GENERATOR_ID and (reGen.SETTLEMENT_ID = cc.settleId or cc.settleId is null)";

    /**
     * #4 視覺化分析 列表與圖 發電端餘電度數 整年所有單月 + 整年加總 + 虛擬集團
     * @param serviceStart 輸入年1/1
     * @param serviceEnd 隔年1/1
     * @return List
     */
    @Query(value =
            "Select IIF(virGen.NAME is null, gEntity.NAME, virGen.NAME) as gEntityNAME, null as serviceDate, sum(ROUND(reGen.UNMATCHED_RM, 0)) as matchedKw" +
                    "  from APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD as reGen" + innerRematchGeneratorRecord +
                    joinGeneratorEnd + joinVirtualGeneratorEnd +
                    "  where reGen.DATE >= ?1 and reGen.DATE < ?2 group by IIF(virGen.NAME is null, gEntity.NAME, virGen.NAME) UNION " +
                    "Select IIF(virGen.NAME is null, gEntity.NAME, virGen.NAME) as gEntityNAME, reGen.DATE as serviceDate, sum(ROUND(reGen.UNMATCHED_RM, 0)) as matchedKw" +
                    "  from APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD as reGen" + innerRematchGeneratorRecord +
                    joinGeneratorEnd + joinVirtualGeneratorEnd +
                    "  where reGen.DATE >= ?1 and reGen.DATE < ?2 group by reGen.DATE, IIF(virGen.NAME is null, gEntity.NAME, virGen.NAME)" +
                    " order by serviceDate, matchedKw DESC"
            , nativeQuery = true)
    List<Map<String, Object>> sumUnMatchedRmByGeneratorEntityNamesYear(Date serviceStart, Date serviceEnd);

    /**
     * #11 用電端月結算資料(起迄 帳單年月搜尋) 4時段資料 再媒合 applicationLoadId, energyChangeSectionId, matchedCn
     *      , serviceDate(服務[設備運作]日期)
     * 4時段(+4欄位){energyChangeSectionId:matchedCn} = 1[1表(01)] 半尖峰 3[3表(03)] 離峰 9[9表(09)] 尖峰 11[11表(11)] 週六半尖峰
     * @param billStart 月初
     * @param billEnd 隔月初
     * @return
     */
    @Query(value =
            "Select DISTINCT reLoad.APPLICATION_LOAD_ID as applicationLoadId, reLoad.DATE as serviceDate" +
                    ", reLoad.ENERGY_CHARGE_SECTION_ID as energyChangeSectionId, reLoad.MATCHED_CN as matchedCn" +
                    " from APPLICATION_MONTHLY_REMATCH_LOAD_RECORD as reLoad" +
                    "  inner join(Select APPLICATION_LOAD_ID, DATE, MAX(SETTLEMENT_ID) as settle" +
                    "    from APPLICATION_MONTHLY_REMATCH_LOAD_RECORD group by APPLICATION_LOAD_ID, DATE) cc" +
                    "   on reload.APPLICATION_LOAD_ID = cc.APPLICATION_LOAD_ID and reload.DATE = cc.DATE and reload.SETTLEMENT_ID = cc.settle" +
                    " join ENERGY_CHARGE as ec on reLoad.ENERGY_CHARGE_SECTION_ID = ec.ENERGY_CHARGE_SECTION_ID" +
                    " where reLoad.SETTLEMENT_ID in (Select DISTINCT SETTLEMENT_ID from SETTLEMENT_CALCULATION" +
                    "   where CALCULATION_METHOD in (3,5,6) and EXECUTION_START >= ?1 and EXECUTION_START < ?2)" +
                    " order by serviceDate ASC, applicationLoadId ASC, energyChangeSectionId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findLoadMatchedRmTimeSlotByDateRange(Date billStart, Date billEnd);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD WHERE DATE = ?1", nativeQuery = true)
    void deleteByDate(Date date);

    List<ApplicationMonthlyRematchGeneratorLoadRecord> findByDateAndGeneratorIdIn(Date date, List<Long> generatorIdList);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD     " +
            "    WHERE APPLICATION_LOAD_ID IN      " +
            "          (SELECT APPLICATION_LOAD_ID FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD AS AMRGLR      " +
            "            INNER JOIN  APPLICATION_LOAD AS AL     " +
            "                    ON AMRGLR.APPLICATION_LOAD_ID = AL.ID    " +
            "            INNER JOIN APPLICATION AS A      " +
            "                       ON AL.APPLICATION_ID = A.ID      " +
            "           WHERE TYPE IN (:appTypeList)     " +
            "             AND AMRGLR.DATE = :date)", nativeQuery = true)
    void deleteByDateAndApplicationTypeIn(@Param("date") Date date, @Param("appTypeList") List<String> appTypeList);

    @Transactional
    @Modifying
    @Query(value = " BEGIN " +
            "    DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD " +
            "       WHERE DATE BETWEEN :startTime AND :endTime " +
            "    DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD " +
            "       WHERE DATE BETWEEN :startTime AND :endTime " +
            "    DELETE FROM APPLICATION_MONTHLY_REMATCH_LOAD_RECORD " +
            "       WHERE DATE BETWEEN :startTime AND :endTime " +
            "END " +
            " BEGIN " +
            "    INSERT INTO APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_GENERATOR_ID " +
            "        , APPLICATION_LOAD_ID " +
            "        , MATCHED_RM " +
            "        , SETTLEMENT_ID " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_GENERATOR_ID " +
            "         , APPLICATION_LOAD_ID " +
            "         , MATCHED_RM " +
            "        , SETTLEMENT_ID " +
            "    FROM TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            " " +
            "    INSERT INTO APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_GENERATOR_ID " +
            "        , UNMATCHED_RM " +
            "        , MATCHED_RM " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_GENERATOR_ID " +
            "         , UNMATCHED_RM " +
            "         , MATCHED_RM " +
            "    FROM TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            " " +
            "    INSERT INTO APPLICATION_MONTHLY_REMATCH_LOAD_RECORD    ( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_LOAD_ID " +
            "        , UNMATCHED_CN " +
            "        , MATCHED_CN " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_LOAD_ID " +
            "         , UNMATCHED_CN " +
            "         , MATCHED_CN " +
            "    FROM TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            "END ", nativeQuery = true)
    void deleteAndSaveFromTempTableByDateIntervalAndSettlementId(@Param("startTime") Date startTime,  @Param("endTime") Date endTime, @Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = " BEGIN " +
            "    DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD " +
            "       WHERE DATE BETWEEN :startTime AND :endTime " +
            "           AND APPLICATION_GENERATOR_ID IN (:appGenIdList) " +
            "    DELETE FROM APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD " +
            "       WHERE DATE BETWEEN :startTime AND :endTime " +
            "           AND APPLICATION_GENERATOR_ID IN (:appGenIdList) " +
            "    DELETE FROM APPLICATION_MONTHLY_REMATCH_LOAD_RECORD " +
            "       WHERE DATE BETWEEN :startTime AND :endTime " +
            "           AND APPLICATION_LOAD_ID IN (:appLoadIdList) " +
            "END " +
            " BEGIN " +
            "    INSERT INTO APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_GENERATOR_ID " +
            "        , APPLICATION_LOAD_ID " +
            "        , MATCHED_RM " +
            "        , SETTLEMENT_ID " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_GENERATOR_ID " +
            "         , APPLICATION_LOAD_ID " +
            "         , MATCHED_RM " +
            "        , SETTLEMENT_ID " +
            "    FROM TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            " " +
            "    INSERT INTO APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_GENERATOR_ID " +
            "        , UNMATCHED_RM " +
            "        , MATCHED_RM " +
            "        , SETTLEMENT_ID " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_GENERATOR_ID " +
            "         , UNMATCHED_RM " +
            "         , MATCHED_RM " +
            "         , SETTLEMENT_ID " +
            "    FROM TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            " " +
            "    INSERT INTO APPLICATION_MONTHLY_REMATCH_LOAD_RECORD    ( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_LOAD_ID " +
            "        , UNMATCHED_CN " +
            "        , MATCHED_CN " +
            "        , SETTLEMENT_ID " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_LOAD_ID " +
            "         , UNMATCHED_CN " +
            "         , MATCHED_CN " +
            "         , SETTLEMENT_ID " +
            "    FROM TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            "END ", nativeQuery = true)
    void deleteAndSaveFromTempTableByDateIntervalAndSettlementId(@Param("startTime") Date startTime
            , @Param("endTime") Date endTime
            , @Param("settlementId") Long settlementId
            , @Param("appGenIdList") List<Long> appGenIdList
            , @Param("appLoadIdList") List<Long> appLoadIdList);

    @Transactional
    @Modifying
    @Query(value = " BEGIN " +
            "    INSERT INTO APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_GENERATOR_ID " +
            "        , APPLICATION_LOAD_ID " +
            "        , MATCHED_RM " +
            "        , SETTLEMENT_ID " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_GENERATOR_ID " +
            "         , APPLICATION_LOAD_ID " +
            "         , MATCHED_RM " +
            "         , SETTLEMENT_ID " +
            "    FROM TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            " " +
            "    INSERT INTO APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_GENERATOR_ID " +
            "        , UNMATCHED_RM " +
            "        , MATCHED_RM " +
            "        , SETTLEMENT_ID " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_GENERATOR_ID " +
            "         , UNMATCHED_RM " +
            "         , MATCHED_RM " +
            "         , SETTLEMENT_ID " +
            "    FROM TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            " " +
            "    INSERT INTO APPLICATION_MONTHLY_REMATCH_LOAD_RECORD    ( " +
            "        DATE " +
            "        , ENERGY_CHARGE_SECTION_ID " +
            "        , APPLICATION_LOAD_ID " +
            "        , UNMATCHED_CN " +
            "        , MATCHED_CN " +
            "        , SETTLEMENT_ID " +
            "    ) " +
            "    SELECT " +
            "        DATE " +
            "         , ENERGY_CHARGE_SECTION_ID " +
            "         , APPLICATION_LOAD_ID " +
            "         , UNMATCHED_CN " +
            "         , MATCHED_CN " +
            "         , SETTLEMENT_ID " +
            "    FROM TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD " +
            "    WHERE SETTLEMENT_ID = :settlementId " +
            "END ", nativeQuery = true)
    void saveFromTempTableBySettlementId(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = "BEGIN           " +
            "      DELETE APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD   " +
            "         WHERE SETTLEMENT_ID = :settlementId   " +
            "            AND APPLICATION_GENERATOR_ID IN (:appGenIdList)   " +
            "      DELETE APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD   " +
            "         WHERE SETTLEMENT_ID = :settlementId   " +
            "           AND APPLICATION_GENERATOR_ID IN (:appGenIdList)   " +
            "      DELETE APPLICATION_MONTHLY_REMATCH_LOAD_RECORD   " +
            "         WHERE SETTLEMENT_ID = :settlementId   " +
            "           AND APPLICATION_LOAD_ID IN (:appLoadIdList)   " +
            "END", nativeQuery = true)
    void deleteAllRecordBySettlementIdAndAppGeneratorIdAndAppLoadId(@Param("settlementId") Long settlementId
            , @Param("appGenIdList") List<Long> appGenIdList
            , @Param("appLoadIdList") List<Long> appLoadIdList);

    @Transactional
    @Modifying
    @Query(value = "BEGIN     " +
            "    DELETE APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "    DELETE APPLICATION_MONTHLY_REMATCH_LOAD_RECORD     " +
            "       WHERE SETTLEMENT_ID = :settlementId     " +
            "END ", nativeQuery = true)
    void deleteAllRecordBySettlementId(@Param("settlementId") Long settlementId);
}