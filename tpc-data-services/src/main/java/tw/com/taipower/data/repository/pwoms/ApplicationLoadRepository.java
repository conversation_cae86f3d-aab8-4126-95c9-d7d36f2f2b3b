package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import tw.com.taipower.data.entity.pwoms.ApplicationLoad;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static tw.com.taipower.data.constant.Constants.*;

public interface ApplicationLoadRepository extends JpaRepository<ApplicationLoad, Long> {

    @Query(value = "select MAX( al.order ) FROM ApplicationLoad al where al.applicationId = ?1")
    Integer findMaxOrderByApplication(Long applicationId);

    /**
     * #12 用電端月結算資料(起迄 年月搜尋) - 相關用電資料 applicationLoadId(long)[申請契約用電ID]
     * @param applicationLoadIds
     * @return
     */
    @Query(value =
            "Select Distinct lap.ID as applicationLoadId" +
                    ", lEntity.NBS_CUSTOMER_NUMBER as CUST_ELEC_NO, lEntity.CONTRACT_STG as CONTR_TYPE" +
                    ", lEntity.TIME_STG as TIME_PRICE_STG, app.TYPE as PWDS" +
                    " from APPLICATION_LOAD as lap" +
                    " join LOAD_ENTITY as lEntity on lap.LOAD_ID = lEntity.ID" +
                    " join APPLICATION as app on app.ID = lap.APPLICATION_ID where lap.ID in ?1" +
                    " order by CUST_ELEC_NO ASC, applicationLoadId ASC"
            , nativeQuery = true)
    List<Map<String, Object>> findApplicationLoadsInfoByIds(List<Long> applicationLoadIds);

    @Query(" SELECT new map(appLoad AS " + FIELD_NAME_APPLICATION_LOAD + ")" +
            " FROM ApplicationLoad appLoad " +
            " INNER JOIN LoadEntityMeter loadMeter " +
            " ON loadMeter.loadEntityId = appLoad.loadId " +
            " WHERE loadMeter.meterNo IN (?1) " +
            " ORDER BY appLoad.applicationId ASC")
    List<?> findByMeterNoInOrderByApplicationIdAsc(List<String> meterList);

    @Query(" SELECT DISTINCT new map(appLoad.loadId AS " + FIELD_NAME_APPLICATION_LOAD_ID + ")" +
            " FROM ApplicationLoad appLoad " +
            " INNER JOIN LoadEntityMeter loadMeter " +
            " ON loadMeter.loadEntityId = appLoad.loadId " +
            " WHERE loadMeter.meterNo IN (?1) " +
            " ORDER BY appLoad.loadId ASC")
    List<?> findDistinctByMeterNoInOrderByLoadIdAsc(List<String> meterList);

    @Query(" SELECT new map(appLoad.id AS " + FIELD_NAME_APPLICATION_LOAD_ID +
            " , loadEntity.nbsCustomerNumber AS " + FIELD_NAME_ENTITY_NBS_CUSTOMER_NUMBER +
            " , loadEntity.id AS " + FIELD_NAME_LOAD_ENTITY_ID + ")" +
            " FROM ApplicationLoad appLoad " +
            " INNER JOIN LoadEntity loadEntity " +
            " ON loadEntity.id = appLoad.loadId " +
            " WHERE appLoad.id In (?1) " +
            " ORDER BY appLoad.id ASC")
    List<?> findByLoadEntityByApplicationLoadIdInOrderByLoadIdAsc(List<Long> appLoadIdList);

    @Query(" SELECT DISTINCT new map(appLoad.id AS " + FIELD_NAME_APPLICATION_LOAD_ID +
            " , loadEntity.id AS " + FIELD_NAME_LOAD_ENTITY_ID + ")" +
            " FROM ApplicationLoad appLoad " +
            " INNER JOIN LoadEntity loadEntity " +
            " ON loadEntity.id = appLoad.loadId " +
            " WHERE appLoad.id In (?1) " +
            " ORDER BY loadEntity.id ASC")
    List<?> findDistinctLoadEntityByApplicationLoadIdInOrderByLoadIdAsc(List<Long> appLoadIdList);

    @Query(" SELECT new map(appLoad.id AS " + FIELD_NAME_APPLICATION_LOAD_ID +
            " , appLoad.monthlyContractCap AS " + FIELD_NAME_APPLICATION_LOAD_MONTHLY_CONTRACT_CAPACITY +
            " , appLoad.annualContractCap AS " + FIELD_NAME_APPLICATION_LOAD_ANNUAL_CONTRACT_CAPACITY + ")" +
            " FROM ApplicationLoad appLoad " +
            " WHERE appLoad.id In (?1) " +
            " ORDER BY appLoad.id ASC")
    List<?> findByIdInOrderByIdAsc(List<Long> appLoadIdList);

    @Query(value = " SELECT id FROM APPLICATION_LOAD WHERE APPLICATION_ID = ?1 AND IS_DIRECT = 1", nativeQuery = true)
    List<Long> findIdByApplicationIdAndDirectTrue(Long id);


    @Query(value = " SELECT id FROM APPLICATION_LOAD WHERE APPLICATION_ID = ?1 AND (IS_DIRECT = 0 OR IS_DIRECT IS NULL)",
            nativeQuery = true)
    List<Long> findIdByApplicationIdAndDirectFalseOrDirectNull(Long id);

    List<ApplicationLoad> findAllByApplicationId(Long applicationId);

    List<ApplicationLoad> findAllByApplicationIdIn(List<Long> applicationIdList);

    List<ApplicationLoad> findAllByLoadId(Long loadEntityId);

    ApplicationLoad findOneByApplicationIdAndLoadId(Long applicationId, Long loadId);

    @Query(value = " SELECT ID FROM APPLICATION_LOAD WHERE APPLICATION_ID = ?1", nativeQuery = true)
    List<Long> findIdByApplicationId(Long id);

    /**
     * 根據快速通關需求，尋找用電端已經在轉供中，且沒有在修約的
     * stage3Status 99的條件我還在猶豫
     *
     * @param loadIds
     * @param today
     * @return
     */
    @Query(" SELECT al from ApplicationLoad al WHERE al.loadId in ?1 and al.loadId not in ( " +
            "select al1.loadId FROM ApplicationLoad al1 where al1.isInContractChange = true" +
            " ) and al.applicationId in ( SELECT ap.id FROM Application ap WHERE ( 1 = 1 OR ap.stage3Status = 99 ) " +
            "AND (" +
            " ap" +
            ".contractedStart <= ?2 and ( ap.contractedEnd is null or ?2 <= ap.contractedEnd ) " +
            ")) order by al.id desc")
    List<ApplicationLoad> findLoadIsInContractAndNotChange(List<Long> loadIds, Date today);

    @Transactional
    @Modifying
    @Query("update ApplicationLoad al set al.isInContractChange = false where al.applicationId = ?1")
    void updateIsInContractChange(Long applicationId, Long modifiedBy);

    public Long countByApplicationId(Long applicationId);
    
    @Query(value = "SELECT ID FROM APPLICATION_LOAD " +
            "    WHERE ID IN (:idList) " +
            "        AND IS_DIRECT IS NOT NULL " +
            "        AND IS_DIRECT = :isDirect" , nativeQuery = true)
    List<Long> findIdByIdInAndIsDirect(@Param("idList") List<Long> idList, @Param("isDirect") boolean isDirect);

    @Query(value = "SELECT DISTINCT AG.ID FROM APPLICATION_GENERATOR AG " +
            "    WHERE APPLICATION_ID IN ( SELECT APPLICATION_ID FROM APPLICATION_LOAD " +
            "        WHERE ID IN (:idList)) ", nativeQuery = true)
    List<Long> findApplicationGeneratorIdByIdIn(@Param("idList") List<Long> idList);

    @Query(value = " SELECT ID FROM APPLICATION_LOAD WHERE APPLICATION_ID IN (?1)", nativeQuery = true)
    List<Long> findIdByApplicationIdIn(List<Long> appIdList);

    @Query(value = "SELECT * FROM APPLICATION_LOAD   " +
            "    WHERE APPLICATION_ID IN (:appIdList)   " +
            "        AND MONTHLY_CONTRACT_CAP IS NULL OR ANNUAL_CONTRACT_CAP IS NULL ", nativeQuery = true)
    List<ApplicationLoad> findByAppIdInAndContractCapIsNull(@Param("appIdList") List<Long> appIdList);

    /**
     * 查詢相關契約對應用電端電號
     * @param applicationId
     * @return
     */
    @Query(value=" select le.id,le.nbsCustomerNumber from ApplicationLoad al join LoadEntity le on al.loadId = le.id " +
            "where al" +
            ".applicationId = ?1 ")
    List<Object[]> findAllSimpleInfoByApplicationId(Long applicationId);
}
