package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Settlement Calculation Record
 *
 * @class: SettlementCalculationRecord
 * @author:  ting
 * @version: 0.1.0
 * @since: 2025/02/05 17:14:28
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "SIMULATION_SETTLEMENT_CALCULATION_RECORD")
//@IdClass(SettlementCalculationRecordId.class)
public class SimulationSettlementCalculationRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "SETTLEMENT_ID", nullable = false)
    private Long settlementId;

    @Column(name = "APPLICATION_ID")
    private Long applicationId;

    @Column(name = "RECALCULATION_REASON")
    private String reCalReason;

    @Column(name = "EXECUTION_RESULT")
    private Boolean executionResult;

    @Column(name = "REASON_OF_FAILURE")
    private String reasonOfFailure;
}
