package tw.com.taipower.data.repository.ami;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.ami.SettlementAmiPwr15RecordColumnId;
import tw.com.taipower.data.entity.ami.SettlementTempAmiPwr15Record;
import tw.com.taipower.data.entity.ami.SimulationSettlementTempAmiPwr15Record;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * SettlementTempAmiPwr15Record Repository
 *
 * @class: SettlementTempAmiPwr15RecordRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-09-03 09:53
 * @see:
 **/
public interface SimulationSettlementTempAmiPwr15RecordRepository extends JpaRepository<SimulationSettlementTempAmiPwr15Record, SettlementAmiPwr15RecordColumnId> {

    @Transactional
    @Modifying
    @Query(value = "DELETE SimulationSettlementTempAmiPwr15Record " +
            " WHERE settlementId = :settlementId", nativeQuery = true)
    void deleteBySettlementId(@Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = " BEGIN" +
            " INSERT INTO SimulationSettlementTempAmiPwr15Record(pwrTime, custId, meterId, channel, kw, ratio, updateTime, settlementId)   " +
            "        SELECT ap15m.pwrTime, ap15m.custId, ap15m.meterId, ap15m.channel, ap15m.kw, ap15m.ratio, ap15m.updateTime" +
            "            FROM AmiPwr15Mdes AS ap15m" +
            "                 INNER JOIN dbo.AmiCmDateStat acds" +
            "                        ON  ap15m.meterId = acds.meterId" +
            "                        AND ap15m.custId = acds.custId" +
            "                        AND ap15m.channel = acds.channel" +
            "                        AND acds.statPwrValCnt = 96" +
            "                        AND acds.pwrDate BETWEEN :pwrStartDate AND :pwrEndDate" +
            "                        AND ap15m.pwrTime BETWEEN :pwrStartTime AND :pwrEndTime" +
            "        WHERE NOT EXISTS(" +
            "            SELECT pwrTime, custId, meterId, channel, updateTime, settlementId FROM SimulationSettlementTempAmiPwr15Record AS sap15r" +
            "                     WHERE ap15m.meterId = sap15r.meterId" +
            "                       AND ap15m.custId = sap15r.custId" +
            "                       AND ap15m.channel = sap15r.channel" +
            "                       AND ap15m.updateTime = sap15r.updateTime" +
            "                       AND ap15m.pwrTime = sap15r.pwrTime" +
            "                       AND :settlementId = sap15r.settlementId)" +
            " END ", nativeQuery = true)
    void saveAllFromAmiPwr15Mdes(@Param("pwrStartDate")Date pwrStartDate
            , @Param("pwrEndDate")Date pwrEndDate
            , @Param("pwrStartTime")Date pwrStartTime
            , @Param("pwrEndTime")Date pwrEndTime
            , @Param("settlementId") Long settlementId);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO SimulationSettlementTempAmiPwr15Record(pwrTime, custId, meterId, channel, kw, ratio, updateTime, settlementId)         " +
            "    SELECT dateadd(minute, - 15, pwrTime) as pwrTime, custId, meterId, channel, kw, ratio, updateTime, :settlementId         " +
            "        FROM AmiPwrFixed         " +
            "            WHERE channel = :channel         " +
            "                AND pwrDate = :date         " +
            "                AND meterId IN (SELECT splitMeter.value FROM STRING_SPLIT(:meterIdListInAStr, ',') splitMeter) ", nativeQuery = true)
    void saveAllFromAmiPwr15Mdes(@Param("date")Date date
            , @Param("settlementId") Long settlementId
            , @Param("channel") Integer channel
            , @Param("meterIdListInAStr") String meterIdListInAStr);

    @Query(value = "SELECT * FROM SimulationSettlementTempAmiPwr15Record  " +
            "    WHERE settlementId = :settlementId " +
            "      AND channel = :channel  " +
            "      AND custId IN (:customerIdList)  " +
            "      AND meterId IN (:meterIdList)  " +
            "      AND pwrTime BETWEEN :pwrStartTime AND :pwrEndTime " +
            "      ORDER BY custId ASC, pwrTime ASC", nativeQuery = true)
    List<SettlementTempAmiPwr15Record> findByMeterIdInAndChannelAndPwrTimeBetweenOrderByPwrTime(@Param("settlementId") Long settlementId
            , @Param("pwrStartTime")Date pwrStartTime
            , @Param("pwrEndTime")Date pwrEndTime
            , @Param("customerIdList")List<String> customerIdList
            , @Param("meterIdList")List<String> meterIdList
            , @Param("channel") Integer channel);

    @Query(value = "WITH PARTITION_SQL AS     " +
            "         (SELECT meterId, pwrTime, ROW_NUMBER() OVER (PARTITION BY meterId ORDER BY pwrTime ASC) AS ROW_NUMBER     " +
            "          FROM SimulationSettlementTempAmiPwr15Record STAP15R     " +
            "          WHERE settlementId = :settlementId     " +
            "            AND pwrTime BETWEEN :pwrStartTime AND :pwrEndTime     " +
            "            AND meterId IN (:meterIdList)     " +
            "            AND channel = :channel     " +
            "            AND kw > 0     " +
            "            AND ratio > 0)     " +
            "SELECT meterId, pwrTime     " +
            "    FROM PARTITION_SQL     " +
            "    WHERE ROW_NUMBER = 1     " +
            "ORDER BY pwrTime, meterId ASC", nativeQuery = true)
    List<Map<String, Object>> findPwrTimeAndMeterIdBySettlementIdAndMeterIdInAndChannelAndTimeInterval(@Param("settlementId") Long settlementId
            , @Param("pwrStartTime")Date pwrStartTime
            , @Param("pwrEndTime")Date pwrEndTime
            , @Param("meterIdList")List<String> meterIdList
            , @Param("channel") Integer channel);


}