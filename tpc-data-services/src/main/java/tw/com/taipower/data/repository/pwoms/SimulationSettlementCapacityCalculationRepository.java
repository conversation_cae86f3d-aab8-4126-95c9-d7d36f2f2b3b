package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationSettlementCapacityCalculation;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Repository of SettlementCapacityCalculation
 *
 * @class: SettlementCapacityCalculation
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-08 10:34
 * @see:
 **/

public interface SimulationSettlementCapacityCalculationRepository extends JpaRepository<SimulationSettlementCapacityCalculation, Long> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN   " +
            "    DECLARE @pccCode VARCHAR(2) = (SELECT TOP 1 CODE FROM PARALLEL_CAPACITY_CODE   " +
            "                                   WHERE YEAR = DATEPART(YEAR, :startDate)   " +
            "                                   ORDER BY MONTH ASC)   " +
            "   " +
            "    DECLARE @dateDiff TINYINT = (SELECT DATEDIFF(day, :startDate, EOMONTH(:startDate)) + 1)   " +
            "   " +
            "    INSERT INTO SIMULATION_SETTLEMENT_CAPACITY_CALCULATION(   " +
            "            SETTLEMENT_ID   " +
            "            , APPLICATION_GENERATOR_ID   " +
            "            , CAPACITY_CODE   " +
            "            , CAPACITY   " +
            "            , CAPACITY_PARALLEL_DAY   " +
            "            , SALE_IN_TRIAL_OP   " +
            "            , PARALLEL_DAY)   " +
            "    SELECT SETTLEMENT_ID   " +
            "         , APPLICATION_GENERATOR_ID   " +
            "         , PCC_CODE   " +
            "         , SUM(CAPACITY)   " +
            "         , SUM(CAPACITY * PARALLEL_DAY)   " +
            "         , SALE_IN_TRIAL_OP   " +
            "         , PARALLEL_DAY   " +
            "    FROM (SELECT :settlementId  AS SETTLEMENT_ID   " +
            "             , AG.ID AS APPLICATION_GENERATOR_ID   " +
            "             , PCC.CODE  AS PCC_CODE   " +
            "             , CAPACITY   " +
            "             , GE.IS_SALE_IN_TRIAL_RUN AS SALE_IN_TRIAL_OP   " +
            "             , IIF((DATEDIFF(day, GECC.COMBINED_DATE, EOMONTH(:startDate)) + 1) > @dateDiff   " +
            "                , @dateDiff   " +
            "                , (DATEDIFF(day, GECC.COMBINED_DATE, EOMONTH(:startDate)) + 1)) AS PARALLEL_DAY   " +
            "        FROM  SIMULATION_GENERATOR_ENTITY_COMBINED_CAPACITY GECC   " +
            "              INNER JOIN SIMULATION_APPLICATION_GENERATOR AG   " +
            "                         ON AG.GENERATOR_ID = GECC.GENERATOR_ENTITY_ID   " +
            "              INNER JOIN SIMULATION_GENERATOR_ENTITY GE   " +
            "                         ON AG.GENERATOR_ID = GE.ID   " +
            "              INNER JOIN PARALLEL_CAPACITY_CODE PCC   " +
            "                         ON DATEPART(YEAR, COMBINED_DATE) = PCC.YEAR   " +
            "                             AND DATEPART(MONTH, COMBINED_DATE) = PCC.MONTH   " +
            "          WHERE  (LICENSE_DATE IS NULL OR CAST(LICENSE_DATE AS Date) > EOMONTH(:startDate))   " +
            "            AND COMBINED_DATE <= EOMONTH(:startDate)   " +
            "            AND AG.APPLICATION_ID IN (:appIdList))  AS SUBSQL   " +
            "    GROUP BY SETTLEMENT_ID, APPLICATION_GENERATOR_ID, PCC_CODE, SALE_IN_TRIAL_OP, PARALLEL_DAY   " +
            "   " +
            "    INSERT INTO SIMULATION_SETTLEMENT_CAPACITY_CALCULATION(   " +
            "                         SETTLEMENT_ID   " +
            "                       , APPLICATION_GENERATOR_ID   " +
            "                       , CAPACITY_CODE   " +
            "                       , CAPACITY   " +
            "                       , CAPACITY_PARALLEL_DAY   " +
            "                       , SALE_IN_TRIAL_OP   " +
            "                       , PARALLEL_DAY)   " +
            "    SELECT SETTLEMENT_ID   " +
            "         , APPLICATION_GENERATOR_ID   " +
            "         , PCC_CODE   " +
            "         , SUM(CAPACITY)   " +
            "         , SUM(CAPACITY * PARALLEL_DAY)   " +
            "         , SALE_IN_TRIAL_OP   " +
            "         , PARALLEL_DAY   " +
            "    FROM (SELECT :settlementId AS SETTLEMENT_ID   " +
            "               , AG.ID AS APPLICATION_GENERATOR_ID   " +
            "               , @pccCode  AS PCC_CODE   " +
            "               , CAPACITY   " +
            "               , GE.IS_SALE_IN_TRIAL_RUN AS SALE_IN_TRIAL_OP   " +
            "               , IIF((DATEDIFF(day, GECC.COMBINED_DATE, EOMONTH(:startDate)) + 1) > @dateDiff   " +
            "            , @dateDiff   " +
            "            , (DATEDIFF(day, GECC.COMBINED_DATE, EOMONTH(:startDate)) + 1)) AS PARALLEL_DAY   " +
            "          FROM SIMULATION_GENERATOR_ENTITY_COMBINED_CAPACITY GECC   " +
            "                   INNER JOIN SIMULATION_APPLICATION_GENERATOR AG   " +
            "                              ON AG.GENERATOR_ID = GECC.GENERATOR_ENTITY_ID   " +
            "                   INNER JOIN SIMULATION_GENERATOR_ENTITY GE   " +
            "                              ON AG.GENERATOR_ID = GE.ID   " +
            "          WHERE CAST(LICENSE_DATE AS Date) <= EOMONTH(:startDate)   " +
            "            AND AG.APPLICATION_ID IN (:appIdList))  AS SUBSQL   " +
            "    GROUP BY SETTLEMENT_ID, APPLICATION_GENERATOR_ID, PCC_CODE, SALE_IN_TRIAL_OP, PARALLEL_DAY   " +
            "END ", nativeQuery = true)
    void saveAllByDateAndSettlementIdAndAppIdIn(@Param("startDate") Date startDate
            , @Param("settlementId") Long settlementId
            , @Param("appIdList") List<Long> appIdList);


}
