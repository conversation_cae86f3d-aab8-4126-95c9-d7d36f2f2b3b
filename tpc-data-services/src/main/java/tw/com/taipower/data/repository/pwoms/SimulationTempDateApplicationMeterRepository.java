package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.DateApplicationMeterColumnId;
import tw.com.taipower.data.entity.pwoms.SimulationTempDateApplicationMeter;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * TempDateApplicationMeter Repository
 *
 * @class: TempDateApplicationMeterRepository
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-06-09 18:04
 * @see:
 **/

public interface SimulationTempDateApplicationMeterRepository extends JpaRepository<SimulationTempDateApplicationMeter, DateApplicationMeterColumnId> {

    @Transactional
    @Modifying
    @Query(value = "BEGIN           " +
            " DELETE SIMULATION_TEMP_DATE_APPLICATION_METER               " +
            "          WHERE DATE_METER_ID IN (SELECT ID    " +
            "              FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT    " +
            "              WHERE SETTLEMENT_ID = :settlementId)    " +
            "       DELETE FROM SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT          " +
            "           WHERE SETTLEMENT_ID = :settlementId         " +
            "       DELETE FROM SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT         " +
            "           WHERE SETTLEMENT_ID = :settlementId         " +
            "       DELETE FROM SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT          " +
            "           WHERE SETTLEMENT_ID = :settlementId         " +
            "       DELETE FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD          " +
            "           WHERE SETTLEMENT_ID = :settlementId   " +
            "END ", nativeQuery = true)
    void deleteAllRecordBySettlementId(@Param("settlementId") Long settlementId);

    @Query(value = "SELECT DISTINCT TDAGL.APPLICATION_ID, TDAGL.APPLICATION_LOAD_ID, METER_NO FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL   " +
            "   INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_METER AS TDAM   " +
            "       ON TDAM.DATE_APPLICATION_ID = TDAGL.ID   " +
            "   INNER JOIN SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT AS TDMCS   " +
            "      ON TDMCS.ID = TDAM.DATE_METER_ID   " +
            "          AND TDMCS.LOAD_METER_ID IS NOT NULL   " +
            "          AND TDMCS.SETTLEMENT_ID = :settlementId   " +
            "    WHERE TDAGL.APPLICATION_LOAD_ID IN (:appLoadIdList)   " +
            "      AND TDAGL.SETTLEMENT_ID = :settlementId   " +
            "            AND TDAGL.DATE = :date", nativeQuery = true)
    List<Map<String, Object>> findLoadIdAndMeterIdByDateAndSettlementIdAndApplicationLoadIdIn(@Param("date") Date date, @Param("settlementId") Long settlementId, @Param("appLoadIdList") List<Long> appLoadIdList);

    @Query(value = "SELECT DISTINCT TDAGL.APPLICATION_ID, TDAGL.APPLICATION_LOAD_ID, METER_NO FROM SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD AS TDAGL   " +
            "   INNER JOIN SIMULATION_TEMP_DATE_APPLICATION_METER AS TDAM   " +
            "       ON TDAM.DATE_APPLICATION_ID = TDAGL.ID   " +
            "   INNER JOIN SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT AS TDMCS   " +
            "      ON TDMCS.ID = TDAM.DATE_METER_ID   " +
            "          AND TDMCS.LOAD_METER_ID IS NOT NULL   " +
            "          AND TDMCS.SETTLEMENT_ID = :settlementId   " +
            "    WHERE TDAGL.APPLICATION_LOAD_ID IN (:appLoadIdList)   " +
            "      AND TDAGL.SETTLEMENT_ID = :settlementId   " +
            "            AND TDAGL.DATE BETWEEN :startDate AND :endDate", nativeQuery = true)
    List<Map<String, Object>> findLoadIdAndMeterIdByDateIntervalAndSettlementIdAndApplicationLoadIdIn(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("settlementId") Long settlementId, @Param("appLoadIdList") List<Long> appLoadIdList);


}