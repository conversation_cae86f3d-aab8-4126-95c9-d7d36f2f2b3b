package tw.com.taipower.data.repository.pwoms;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tw.com.taipower.data.entity.pwoms.SimulationSettlementSumCapacityCalculation;
import tw.com.taipower.data.entity.pwoms.SettlementSumCapacityCalculationId;

import java.util.Date;

/**
 * Repository of SettlementCapacityCalculation
 *
 * @class: SettlementCapacityCalculation
 * @author: ting
 * @version: 0.1.0
 * @since: 2025-02-08 10:34
 * @see:
 **/

public interface SimulationSettlementSumCapacityCalculationRepository extends JpaRepository<SimulationSettlementSumCapacityCalculation, SettlementSumCapacityCalculationId> {

    @Transactional
    @Modifying
    @Query(value = " BEGIN                  " +
            "   DECLARE @pccCode VARCHAR(2) = (SELECT TOP 1 CODE FROM PARALLEL_CAPACITY_CODE        " +
            "                          WHERE YEAR = DATEPART(YEAR, :startDate)        " +
            "                          ORDER BY MONTH ASC)        " +
            "        " +
            "   INSERT INTO SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION(        " +
            "           SETTLEMENT_ID        " +
            "           , APPLICATION_GENERATOR_ID        " +
            "           , COMPUTABLE_CAPACITY        " +
            "           , COMPUTABLE_CAPACITY_PARALLEL_DAY        " +
            "       )        " +
            "    SELECT SETTLEMENT_ID, APPLICATION_GENERATOR_ID, SUM(CAPACITY), SUM(CAPACITY * PARALLEL_DAY) AS SUM_CAPACITY FROM SIMULATION_SETTLEMENT_CAPACITY_CALCULATION        " +
            "       WHERE SETTLEMENT_ID = :settlementId        " +
            "           AND (SALE_IN_TRIAL_OP = 1        " +
            "           OR (SALE_IN_TRIAL_OP = 0 AND CAPACITY_CODE = @pccCode))        " +
            "    GROUP BY SETTLEMENT_ID, APPLICATION_GENERATOR_ID        " +
            "        " +
            "    UPDATE SSCC        " +
            "       SET CAPACITY = SUBSQL.SUM_CAPACITY        " +
            "       FROM SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION SSCC INNER JOIN        " +
            "           (SELECT SETTLEMENT_ID, APPLICATION_GENERATOR_ID, SUM(CAPACITY) AS SUM_CAPACITY FROM SIMULATION_SETTLEMENT_CAPACITY_CALCULATION        " +
            "               WHERE SETTLEMENT_ID = :settlementId        " +
            "           GROUP BY SETTLEMENT_ID, APPLICATION_GENERATOR_ID) AS SUBSQL        " +
            "       ON SSCC.APPLICATION_GENERATOR_ID = SUBSQL.APPLICATION_GENERATOR_ID        " +
            "       AND SSCC.SETTLEMENT_ID = SUBSQL.SETTLEMENT_ID        " +
            "        " +
            "    UPDATE SSCC        " +
            "       SET PERCENTAGE = COMPUTABLE_CAPACITY/CAPACITY        " +
            "    FROM SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION SSCC        " +
            "       WHERE SETTLEMENT_ID = :settlementId        " +
            "        " +
            "    UPDATE SCC        " +
            "       SET SCC.PERCENTAGE = CAPACITY_PARALLEL_DAY / COMPUTABLE_CAPACITY_PARALLEL_DAY        " +
            "       FROM SIMULATION_SETTLEMENT_CAPACITY_CALCULATION SCC INNER JOIN        " +
            "            (SELECT SETTLEMENT_ID, APPLICATION_GENERATOR_ID, COMPUTABLE_CAPACITY_PARALLEL_DAY FROM SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION        " +
            "               WHERE SETTLEMENT_ID = :settlementId        " +
            "               ) AS SUBSQL        " +
            "       ON SCC.APPLICATION_GENERATOR_ID = SUBSQL.APPLICATION_GENERATOR_ID        " +
            "       AND SCC.SETTLEMENT_ID = SUBSQL.SETTLEMENT_ID        " +
            "           AND (SALE_IN_TRIAL_OP = 1        " +
            "               OR (SALE_IN_TRIAL_OP = 0 AND CAPACITY_CODE = @pccCode))        " +
            "        " +
            "   INSERT INTO SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION(        " +
            "                                                    SETTLEMENT_ID        " +
            "                                                  , APPLICATION_GENERATOR_ID        " +
            "                                                  , CAPACITY        " +
            "                                                  , COMPUTABLE_CAPACITY        " +
            "                                                  , COMPUTABLE_CAPACITY_PARALLEL_DAY        " +
            "                                                  , PERCENTAGE        " +
            "   )        " +
            "     SELECT SETTLEMENT_ID, APPLICATION_GENERATOR_ID, CAPACITY, 0, 0, 1 FROM SIMULATION_SETTLEMENT_CAPACITY_CALCULATION        " +
            "        WHERE APPLICATION_GENERATOR_ID NOT IN (        " +
            "            SELECT APPLICATION_GENERATOR_ID FROM SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION        " +
            "                WHERE SETTLEMENT_ID = :settlementId        " +
            "            )        " +
            "        AND SETTLEMENT_ID = :settlementId" +
            " END ", nativeQuery = true)
    void saveAllByDateAndSettlementIdAndUpdatePercentage(@Param("startDate") Date startDate, @Param("settlementId") Long settlementId);

}
