package tw.com.taipower.data.entity.pwoms;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Settlement Calculation Failure Record
 *
 * @class: SettlementCalculationFailureRecord
 * @author:  ting
 * @version: 0.1.0
 * @since: 2025/02/05 17:14:28
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD")
//@IdClass(SettlementCalculationFailureRecordId.class)
public class SimulationSettlementCalculationFailureRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "SETTLEMENT_RECORD_ID", nullable = false)
    private Long settlementRecordId;

    @Column(name = "APPLICATION_GENERATOR_ID")
    private Long generatorId;

    @Column(name = "APPLICATION_LOAD_ID")
    private Long loadId;

    @Column(name = "REASON_OF_FAILURE")
    private String reasonOfFailure;
}
