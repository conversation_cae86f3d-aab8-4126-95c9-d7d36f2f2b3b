package tw.com.taipower.data.repository.pwoms;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import tw.com.taipower.data.entity.pwoms.LoadEntity;
import tw.com.taipower.data.entity.pwoms.SimulationLoadEntity;

import java.util.Date;
import java.util.List;

import static tw.com.taipower.data.constant.Constants.*;

public interface SimulationLoadEntityRepository extends JpaRepository<SimulationLoadEntity, Long>, JpaSpecificationExecutor<SimulationLoadEntity> {

}
