
CREATE TABLE [dbo].[SIMULATION_GENERATOR_ENTITY_COMBINED_CAPACITY](
    [GENERATOR_ENTITY_ID] [bigint] NULL,
    [CAPACITY] [decimal](18, 2) NULL,
    [ID] [bigint] IDENTITY(0,1) NOT NULL,
    [COMBINED_DATE] [datetime] NULL,
    [LICENSE_DATE] [datetime] NULL,
    CONSTRAINT [SIMULATION_GENERATOR_ENTITY_COMBINED_CAPACITY_PK] PRIMARY KEY CLUSTERED
(
[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]

GO

CREATE TABLE [dbo].[SIMULATION_APPLICATION_GENERATOR](
    [ID] [bigint] IDENTITY(1,1) NOT NULL,
    [APPLICATION_ID] [bigint] NOT NULL,
    [GENERATOR_ID] [bigint] NOT NULL,
    [PMI] [decimal](5, 2) NOT NULL,
    CONSTRAINT [PK_SIMULATION_APPLICATION_GENERATOR] PRIMARY KEY CLUSTERED
(
[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]

GO


CREATE TABLE [dbo].[SIMULATION_GENERATOR_ENTITY](
    [ID] [bigint] NOT NULL,
    [IS_SALE_IN_TRIAL_RUN] [bit] NULL,
    [RESPONSIBILITY_VOLTAGE] [int] NULL,
     CONSTRAINT [PK_SIMULATION_GENERATOR_ENTITIES] PRIMARY KEY CLUSTERED
    (
[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]

GO


CREATE TABLE [dbo].[SIMULATION_APPLICATION_LOAD](
    [ID] [bigint] IDENTITY(1,1) NOT NULL,
    [APPLICATION_ID] [bigint] NOT NULL,
    [LOAD_ID] [bigint] NOT NULL,
    [MONTHLY_CONTRACT_CAP] [decimal](15, 0) NULL,
    [ANNUAL_CONTRACT_CAP] [decimal](15, 0) NULL,
    [IS_DIRECT] [bit] NULL,
    CONSTRAINT [PK_SIMULATION_APPLICATION_LOADS] PRIMARY KEY CLUSTERED
(
[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]

GO

CREATE TABLE [dbo].[SIMULATION_LOAD_ENTITY](
    [ID] [bigint] NOT NULL,
    [RESPONSIBILITY_VOLTAGE] [int] NULL,
     CONSTRAINT [PK_SIMULATION_LOAD_ENTITIES] PRIMARY KEY CLUSTERED
    (
[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]

GO

CREATE TABLE [dbo].[SIMULATION_APPLICATION_RELATION](
    [DATE] [date] NOT NULL,
    [APPLICATION_ID] [bigint] NOT NULL,
    [RELATION_ID] [bigint] NOT NULL,
     CONSTRAINT [PK_SIMULATION_APPLICATION_RELATION] PRIMARY KEY CLUSTERED
    (
    [DATE] ASC,
    [APPLICATION_ID] ASC,
[RELATION_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]

GO