CREATE TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION](
	[SETTLEMENT_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[SERVICE_DATE] [date] NOT NULL,
	[CALCULATION_METHOD] [tinyint] NOT NULL,
	[EXECUTION_START] [datetime2](0) NULL,
	[EXECUTION_END] [datetime2](0) NULL,
	[EXECUTION_PROGRESS] [varchar](max) NULL,
	[EXECUTION_RESULT] [bit] NULL,
	[EXECUTION_PERCENTAGE] [decimal](5, 2) NULL,
	[USER_ID] [int] NOT NULL,
 CONSTRAINT [PK_SIMULATION_SETTLEMENT_CALCULATION] PRIMARY KEY CLUSTERED 
(
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ADD  CONSTRAINT [DF_SIMULATION_SETTLEMENT_CALCULATION_CALCULATION_METHOD]  DEFAULT ((1)) FOR [CALCULATION_METHOD]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ADD  CONSTRAINT [DF_SIMULATION_SETTLEMENT_CALCULATION_EXECUTION_START]  DEFAULT (getdate()) FOR [EXECUTION_START]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_SETTLEMENT_CALCULATION_ACCOUNT] FOREIGN KEY([USER_ID])
REFERENCES [dbo].[ACCOUNT] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION] CHECK CONSTRAINT [FK_SIMULATION_SETTLEMENT_CALCULATION_ACCOUNT]
GO


CREATE TRIGGER [dbo].[SIMULATION_SETTLEMENT_CALCULATION_TRIGGER]
    ON  [dbo].[SIMULATION_SETTLEMENT_CALCULATION]
    AFTER INSERT
      AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON;
UPDATE [SIMULATION_SETTLEMENT_CALCULATION]
SET [EXECUTION_START] = GETDATE()
FROM INSERTED i
WHERE i.SETTLEMENT_ID = [SIMULATION_SETTLEMENT_CALCULATION].SETTLEMENT_ID
  AND i.SERVICE_DATE = [SIMULATION_SETTLEMENT_CALCULATION].SERVICE_DATE
  AND i.CALCULATION_METHOD = [SIMULATION_SETTLEMENT_CALCULATION].CALCULATION_METHOD
-- Insert statements for trigger here
END
  
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ENABLE TRIGGER [SIMULATION_SETTLEMENT_CALCULATION_TRIGGER]
GO

CREATE TABLE [dbo].[SIMULATION_BILL_SETTLEMENT](
	[BILL_ID] [bigint] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[APPLICATION_ID] [bigint] NOT NULL,
	[BILL_DATE] [datetime2](0) NULL,
	[COST] [decimal](14, 0) NULL,
 CONSTRAINT [PK_SIMULATION_BILL_SETTLEMENT] PRIMARY KEY CLUSTERED 
(
	[BILL_ID] ASC,
	[SETTLEMENT_ID] ASC,
	[APPLICATION_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_BILL_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_BILL_SETTLEMENT_APPLICATION] FOREIGN KEY([APPLICATION_ID])
REFERENCES [dbo].[APPLICATION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_BILL_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_BILL_SETTLEMENT_APPLICATION]
GO

ALTER TABLE [dbo].[SIMULATION_BILL_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_BILL_SETTLEMENT_BILL] FOREIGN KEY([BILL_ID])
REFERENCES [dbo].[BILL] ([BILL_ID])
GO

ALTER TABLE [dbo].[SIMULATION_BILL_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_BILL_SETTLEMENT_BILL]
GO

ALTER TABLE [dbo].[SIMULATION_BILL_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_BILL_SETTLEMENT_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_BILL_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_BILL_SETTLEMENT_SETTLEMENT_CALCULATION]
GO

CREATE TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_RECORD](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[APPLICATION_ID] [bigint] NOT NULL,
	[RECALCULATION_REASON] [varchar](max) NULL,
	[EXECUTION_RESULT] [bit] NULL,
	[REASON_OF_FAILURE] [varchar](max) NULL,
 CONSTRAINT [PK_SIMULATION_SETTLEMENT_CALCULATION_RECORD] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_RECORD]  WITH CHECK ADD  CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_RECORD_APPLICATION_ID_FK] FOREIGN KEY([APPLICATION_ID])
REFERENCES [dbo].[APPLICATION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_RECORD] CHECK CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_RECORD_APPLICATION_ID_FK]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_RECORD]  WITH CHECK ADD  CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_RECORD_SETTLEMENT_ID_FK] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_RECORD] CHECK CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_RECORD_SETTLEMENT_ID_FK]
GO

CREATE TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[SETTLEMENT_RECORD_ID] [bigint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NULL,
	[APPLICATION_LOAD_ID] [bigint] NULL,
	[REASON_OF_FAILURE] [nvarchar](max) NULL,
 CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD_ID_PK] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD]  WITH CHECK ADD  CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD_APPLICATION_GENERATOR_ID] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD] CHECK CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD_APPLICATION_GENERATOR_ID]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD]  WITH CHECK ADD  CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD_APPLICATION_LOAD_ID] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD] CHECK CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD_APPLICATION_LOAD_ID]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD]  WITH CHECK ADD  CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_RECORD_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION_RECORD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD] CHECK CONSTRAINT [SIMULATION_SETTLEMENT_CALCULATION_FAILURE_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_SETTLEMENT_CAPACITY_CALCULATION](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[CAPACITY_CODE] [varchar](2) NOT NULL,
	[CAPACITY] [decimal](18, 2) NOT NULL,
	[CAPACITY_PARALLEL_DAY] [decimal](18, 2) NOT NULL,
	[SALE_IN_TRIAL_OP] [bit] NOT NULL,
	[PARALLEL_DAY] [tinyint] NOT NULL,
	[PERCENTAGE] [decimal](6, 5) NULL,
 CONSTRAINT [PK_SIMULATION_SETTLEMENT_CAPACITY_CALCULATION] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CAPACITY_CALCULATION]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_APPLICATION_MONTHLY_TRIAL_CAPACITY_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CAPACITY_CALCULATION] CHECK CONSTRAINT [FK_SIMULATION_APPLICATION_MONTHLY_TRIAL_CAPACITY_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CAPACITY_CALCULATION]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_APPLICATION_MONTHLY_TRIAL_CAPACITY_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CAPACITY_CALCULATION] CHECK CONSTRAINT [FK_SIMULATION_APPLICATION_MONTHLY_TRIAL_CAPACITY_SETTLEMENT_CALCULATION]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CAPACITY_CALCULATION]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_SETTLEMENT_CAPACITY_CALCULATION_PARALLEL_CAPACITY_CODE] FOREIGN KEY([CAPACITY_CODE])
REFERENCES [dbo].[PARALLEL_CAPACITY_CODE] ([CODE])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_CAPACITY_CALCULATION] CHECK CONSTRAINT [FK_SIMULATION_SETTLEMENT_CAPACITY_CALCULATION_PARALLEL_CAPACITY_CODE]
GO

CREATE TABLE [dbo].[SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION](
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[CAPACITY] [decimal](18, 2) NULL,
	[COMPUTABLE_CAPACITY] [decimal](18, 2) NOT NULL,
	[COMPUTABLE_CAPACITY_PARALLEL_DAY] [decimal](18, 2) NOT NULL,
	[PERCENTAGE] [decimal](6, 5) NULL,
 CONSTRAINT [PK_SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION] PRIMARY KEY CLUSTERED 
(
	[SETTLEMENT_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION] CHECK CONSTRAINT [FK_SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION] CHECK CONSTRAINT [FK_SIMULATION_SETTLEMENT_SUM_CAPACITY_CALCULATION_SETTLEMENT_CALCULATION]
GO

CREATE TABLE [dbo].[SIMULATION_METER_CHANGE_RECORD](
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[NBS_CUSTOMER_NUMBER] [varchar](100) NOT NULL,
	[METER_NO] [varchar](100) NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NULL,
	[USE_FROM] [datetime] NOT NULL,
	[USE_TO] [datetime] NULL,
 CONSTRAINT [PK_SIMULATION_METER_CHANGE_RECORD_1] PRIMARY KEY CLUSTERED 
(
	[SETTLEMENT_ID] ASC,
	[NBS_CUSTOMER_NUMBER] ASC,
	[METER_NO] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_METER_CHANGE_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_METER_CHANGE_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_METER_CHANGE_RECORD] CHECK CONSTRAINT [FK_SIMULATION_METER_CHANGE_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_METER_CHANGE_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_METER_CHANGE_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_METER_CHANGE_RECORD] CHECK CONSTRAINT [FK_SIMULATION_METER_CHANGE_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_METER_CHANGE_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_METER_CHANGE_RECORD_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_METER_CHANGE_RECORD] CHECK CONSTRAINT [FK_SIMULATION_METER_CHANGE_RECORD_SETTLEMENT_CALCULATION]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[APPLICATION_ID] [bigint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_GENERATOR_LOAD_DATE] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_GENERATOR_LOAD_DATE_APPLICATION] FOREIGN KEY([APPLICATION_ID])
REFERENCES [dbo].[APPLICATION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_GENERATOR_LOAD_DATE_APPLICATION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_GENERATOR_LOAD_DATE_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_GENERATOR_LOAD_DATE_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_GENERATOR_LOAD_DATE_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_GENERATOR_LOAD_DATE_APPLICATION_LOAD]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[LOAD_METER_ID] [bigint] NULL,
	[GENERATOR_METER_ID] [bigint] NULL,
	[NBS_CUSTOMER_NUMBER] [nvarchar](50) NOT NULL,
	[METER_NO] [varchar](100) NOT NULL,
	[COMPUTABLE] [bit] NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_METER_DAILY_COMPUTABLE_SETTLEMENT] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT_GENERATOR_ENTITY] FOREIGN KEY([GENERATOR_METER_ID])
REFERENCES [dbo].[GENERATOR_ENTITY_METER] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT_GENERATOR_ENTITY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT_LOAD_ENTITY] FOREIGN KEY([LOAD_METER_ID])
REFERENCES [dbo].[LOAD_ENTITY_METER] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT_LOAD_ENTITY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[APPLICATION_ID] [bigint] NOT NULL,
	[COMPUTABLE] [bit] NOT NULL,
	[CALCULATED_AT] [datetime2](7) NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[APPLICATION_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT] ADD  CONSTRAINT [DF__SIMULATION_TEMP_DATE__CALCU__7E8CC4B1]  DEFAULT (getdate()) FOR [CALCULATED_AT]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT_APPLICATION] FOREIGN KEY([APPLICATION_ID])
REFERENCES [dbo].[APPLICATION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT_APPLICATION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_SELF_COMPUTABLE_SETTLEMENT_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[APPLICATION_ID] [bigint] NOT NULL,
	[COMPUTABLE] [bit] NOT NULL,
	[CALCULATED_AT] [datetime2](7) NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPICATION_DAILY_COMPUTABLE_SETTLEMENT] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[APPLICATION_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT] ADD  CONSTRAINT [DF__SIMULATION_TEMP_DATE__CALCU__7226EDCC]  DEFAULT (getdate()) FOR [CALCULATED_AT]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPICATION_DAILY_COMPUTABLE_SETTLEMENT_APPLICATION] FOREIGN KEY([APPLICATION_ID])
REFERENCES [dbo].[APPLICATION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPICATION_DAILY_COMPUTABLE_SETTLEMENT_APPLICATION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_COMPUTABLE_SETTLEMENT_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_METER](
	[DATE_APPLICATION_ID] [bigint] NOT NULL,
	[DATE_METER_ID] [bigint] NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_DATE_APPLICATION_METER] PRIMARY KEY CLUSTERED 
(
	[DATE_APPLICATION_ID] ASC,
	[DATE_METER_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_METER]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_METER_DATE_APPLICATION_GENERATOR_LOAD] FOREIGN KEY([DATE_APPLICATION_ID])
REFERENCES [dbo].[SIMULATION_TEMP_DATE_APPLICATION_GENERATOR_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_METER] CHECK CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_METER_DATE_APPLICATION_GENERATOR_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_METER]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_METER_DATE_METER_COMPUTABLE_SETTLEMENT] FOREIGN KEY([DATE_METER_ID])
REFERENCES [dbo].[SIMULATION_TEMP_DATE_METER_COMPUTABLE_SETTLEMENT] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_DATE_APPLICATION_METER] CHECK CONSTRAINT [FK_SIMULATION_TEMP_DATE_APPLICATION_METER_DATE_METER_COMPUTABLE_SETTLEMENT]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT](
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[CAPACITY_CODE] [varchar](2) NOT NULL,
	[CURRENT_KW] [decimal](18, 0) NULL,
	[TRIAL_KW] [decimal](18, 0) NOT NULL,
	[VARIANCE_KW] [decimal](18, 4) NULL,
	[VARIANCE_KW_PERCENT] [decimal](18, 2) NULL,
	[CURRENT_COST] [decimal](18, 4) NULL,
	[TRAIL_COST] [decimal](18, 4) NOT NULL,
	[VARIANCE_COST] [decimal](18, 4) NULL,
	[VARIANCE_COST_PERCENT] [decimal](18, 2) NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT] PRIMARY KEY CLUSTERED 
(
	[SETTLEMENT_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[CAPACITY_CODE] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT_PARALLEL_CAPACITY_CODE] FOREIGN KEY([CAPACITY_CODE])
REFERENCES [dbo].[PARALLEL_CAPACITY_CODE] ([CODE])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT_PARALLEL_CAPACITY_CODE]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_TRIAL_SETTLEMENT_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT](
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[CAPACITY_CODE] [varchar](2) NOT NULL,
	[ADJUSTED_MATCHED_KW] [decimal](18, 0) NULL,
	[ANCILLARY_SERVICE_COST] [decimal](18, 0) NULL,
	[DISPATCH_SERVICE_COST] [decimal](18, 0) NULL,
	[POWER_TRANS_COST] [decimal](18, 0) NULL,
	[POWER_DIST_COST] [decimal](18, 0) NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT] PRIMARY KEY CLUSTERED 
(
	[SETTLEMENT_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[CAPACITY_CODE] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT_PARALLEL_CAPACITY_CODE] FOREIGN KEY([CAPACITY_CODE])
REFERENCES [dbo].[PARALLEL_CAPACITY_CODE] ([CODE])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT_PARALLEL_CAPACITY_CODE]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT_SETTLEMENT_CALCULATION]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD](
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[CAPACITY_CODE] [varchar](2) NOT NULL,
	[PERCENTAGE] [decimal](6, 5) NOT NULL,
	[MATCHED_KW] [decimal](18, 4) NOT NULL,
	[ADJUSTED_MATCHED_KW] [decimal](18, 0) NULL,
	[ANCILLARY_SERVICE_COST] [decimal](18, 4) NULL,
	[DISPATCH_SERVICE_COST] [decimal](18, 4) NULL,
	[POWER_TRANS_COST] [decimal](18, 4) NULL,
	[POWER_DIST_COST] [decimal](18, 4) NULL,
	[CALCULATED_AT] [datetime] NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD] PRIMARY KEY CLUSTERED 
(
	[SETTLEMENT_ID] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[CAPACITY_CODE] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD] ADD  CONSTRAINT [DF_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_CALCULATED_AT]  DEFAULT (getdate()) FOR [CALCULATED_AT]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_PARALLEL_CAPACITY_CODE] FOREIGN KEY([CAPACITY_CODE])
REFERENCES [dbo].[PARALLEL_CAPACITY_CODE] ([CODE])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_PARALLEL_CAPACITY_CODE]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_CAPACITY_RECORD_SETTLEMENT_CALCULATION]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD](
	[DATETIME] [datetime] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[UMI] [decimal](18, 4) NOT NULL,
	[UNMATCHED_CN] [decimal](18, 4) NOT NULL,
	[MATCHED_CN] [decimal](18, 4) NOT NULL,
	[ACTUAL_LOAD] [decimal](18, 4) NOT NULL,
	[KW_UPDATETIME] [datetime] NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[APPLICATION_LOAD_ID] ASC,
	[DATETIME] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_LOAD_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD](
	[DATETIME] [datetime] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[GMI] [decimal](18, 4) NOT NULL,
	[UNMATCHED_RM] [decimal](18, 4) NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
	[ACTUAL_GEN] [decimal](18, 4) NOT NULL,
	[KW_UPDATETIME] [datetime] NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD] PRIMARY KEY CLUSTERED 
(
	[APPLICATION_GENERATOR_ID] ASC,
	[DATETIME] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD](
	[DATETIME] [datetime] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[APPLICATION_LOAD_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[DATETIME] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_GENERATOR_LOAD_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[UNMATCHED_CN] [decimal](18, 4) NOT NULL,
	[MATCHED_CN] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[SETTLEMENT_ID] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_LOAD_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_LOAD_RECORD_ENERGY_CHARGE_SECTION]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[UNMATCHED_RM] [decimal](18, 4) NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[SETTLEMENT_ID] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_RECORD_ENERGY_CHARGE_SECTION]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
	[CALCULATED_AT] [datetime2](7) NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD] ADD  CONSTRAINT [DF__SIMULATION_TEMP_APPL__CALCU__10E07F16]  DEFAULT (getdate()) FOR [CALCULATED_AT]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD_SETTLEMENT_CALCULATION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION_ID] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [SIMULATION_TEMP_APPLICATION_DAILY_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[UNMATCHED_CN] [decimal](18, 4) NOT NULL,
	[MATCHED_CN] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_LOAD_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[UNMATCHED_RM] [decimal](18, 4) NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
	[ADJUSTED_MATCHED_RM] [decimal](14, 0) NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION1] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION1]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_GENERATOR_LOAD_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[UNMATCHED_CN] [decimal](18, 4) NOT NULL,
	[MATCHED_CN] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_LOAD_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[UNMATCHED_RM] [decimal](18, 4) NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION1] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION1]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_REMATCH_GENERATOR_LOAD_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD](
	[DATETIME] [datetime] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[UMI] [decimal](18, 4) NOT NULL,
	[UNMATCHED_CN] [decimal](18, 4) NOT NULL,
	[MATCHED_CN] [decimal](18, 4) NOT NULL,
	[ACTUAL_LOAD] [decimal](18, 4) NOT NULL,
	[KW_UPDATETIME] [datetime] NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[APPLICATION_LOAD_ID] ASC,
	[DATETIME] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_LOAD_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD](
	[DATETIME] [datetime] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[GMI] [decimal](18, 4) NOT NULL,
	[UNMATCHED_RM] [decimal](18, 4) NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
	[ACTUAL_GEN] [decimal](18, 4) NOT NULL,
	[KW_UPDATETIME] [datetime] NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD] PRIMARY KEY CLUSTERED 
(
	[APPLICATION_GENERATOR_ID] ASC,
	[DATETIME] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD](
	[DATETIME] [datetime] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[APPLICATION_LOAD_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[DATETIME] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION1] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION1]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_TIMELY_DIRECT_GENERATOR_LOAD_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[UNMATCHED_CN] [decimal](18, 4) NOT NULL,
	[MATCHED_CN] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_LOAD_RECORD_SETTLEMENT_CALCULATION]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[UNMATCHED_RM] [decimal](18, 4) NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_RECORD_SETTLEMENT_CALCULATION]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
	[CALCULATED_AT] [datetime2](7) NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD] ADD  CONSTRAINT [DF__SIMULATION_TEMP_APPL__CALCU__7869D707]  DEFAULT (getdate()) FOR [CALCULATED_AT]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD_SETTLEMENT_CALCULATION] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_DAILY_DIRECT_GENERATOR_LOAD_RECORD_SETTLEMENT_CALCULATION]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[UNMATCHED_CN] [decimal](18, 4) NOT NULL,
	[MATCHED_CN] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_LOAD_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_LOAD_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[UNMATCHED_RM] [decimal](18, 4) NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD_1] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[SETTLEMENT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD_ENERGY_CHARGE_SECTION] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD_ENERGY_CHARGE_SECTION]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD_SETTLEMENT_ID] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_RECORD_SETTLEMENT_ID]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD](
	[DATE] [date] NOT NULL,
	[SETTLEMENT_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[MATCHED_RM] [decimal](18, 4) NOT NULL,
	[ADJUSTED_MATCHED_RM] [decimal](14, 0) NULL,
 CONSTRAINT [PK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD] PRIMARY KEY CLUSTERED 
(
	[DATE] ASC,
	[SETTLEMENT_ID] ASC,
	[ENERGY_CHARGE_SECTION_ID] ASC,
	[APPLICATION_GENERATOR_ID] ASC,
	[APPLICATION_LOAD_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_GENERATOR]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_LOAD] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD_APPLICATION_LOAD]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION1] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD_ENERGY_CHARGE_SECTION1]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD]  WITH CHECK ADD  CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD] FOREIGN KEY([SETTLEMENT_ID])
REFERENCES [dbo].[SIMULATION_SETTLEMENT_CALCULATION] ([SETTLEMENT_ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD] CHECK CONSTRAINT [FK_SIMULATION_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD_TEMP_APPLICATION_MONTHLY_DIRECT_GENERATOR_LOAD_RECORD]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW](
	[DATETIME] [datetime] NOT NULL,
	[APPLICATION_LOAD_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NULL,
	[KW_RATIO] [decimal](18, 4) NOT NULL,
	[KW_UPDATETIME] [datetime] NOT NULL,
 CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW_PK] PRIMARY KEY CLUSTERED 
(
	[DATETIME] ASC,
	[APPLICATION_LOAD_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW]  WITH CHECK ADD  CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW_ENERGY_CHARGE_SECTION_ID_FK] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW] CHECK CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW_ENERGY_CHARGE_SECTION_ID_FK]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW]  WITH CHECK ADD  CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW_FK] FOREIGN KEY([APPLICATION_LOAD_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_LOAD] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW] CHECK CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_LOAD_KW_FK]
GO

CREATE TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW](
	[DATETIME] [datetime] NOT NULL,
	[APPLICATION_GENERATOR_ID] [bigint] NOT NULL,
	[ENERGY_CHARGE_SECTION_ID] [tinyint] NOT NULL,
	[KW_RATIO] [decimal](18, 4) NOT NULL,
	[GMI] [decimal](18, 4) NOT NULL,
	[KW_UPDATETIME] [datetime] NOT NULL,
 CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW_PK] PRIMARY KEY CLUSTERED 
(
	[DATETIME] ASC,
	[APPLICATION_GENERATOR_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW]  WITH CHECK ADD  CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW_ENERGY_CHARGE_SECTION_ID_FK] FOREIGN KEY([ENERGY_CHARGE_SECTION_ID])
REFERENCES [dbo].[ENERGY_CHARGE_SECTION] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW] CHECK CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW_ENERGY_CHARGE_SECTION_ID_FK]
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW]  WITH CHECK ADD  CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW_FK] FOREIGN KEY([APPLICATION_GENERATOR_ID])
REFERENCES [dbo].[SIMULATION_APPLICATION_GENERATOR] ([ID])
GO

ALTER TABLE [dbo].[SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW] CHECK CONSTRAINT [SIMULATION_TEMP_TEMP_APPLICATION_GENERATOR_KW_FK]
GO

CREATE TABLE [dbo].[SIMULATION_SETTLEMENT_PROPERTY](
	[ID] [varchar](200) NOT NULL,
	[VALUE] [varchar](60) NULL,
	[UPDATE_TIME] [smalldatetime] NULL,
	[VALUE_TYPE] [varchar](50) NOT NULL,
 CONSTRAINT [PK_SIMULATION_SETTLEMENT_PROPERTY] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_PROPERTY] ADD  CONSTRAINT [DF__SIMULATION_SETTLEMEN__UPDAT__62AFA012]  DEFAULT (getdate()) FOR [UPDATE_TIME]
GO

CREATE TRIGGER [dbo].[SIMULATION_SETTLEMENT_PROPERTY_TRIGGER]
    ON  [dbo].[SIMULATION_SETTLEMENT_PROPERTY]
    AFTER INSERT, UPDATE
                      AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON;
UPDATE SIMULATION_SETTLEMENT_PROPERTY
SET UPDATE_TIME = GETDATE()
    FROM INSERTED i
WHERE i.ID = SIMULATION_SETTLEMENT_PROPERTY.ID
-- Insert statements for trigger here
END
GO

ALTER TABLE [dbo].[SIMULATION_SETTLEMENT_PROPERTY] ENABLE TRIGGER [SIMULATION_SETTLEMENT_PROPERTY_TRIGGER]
GO