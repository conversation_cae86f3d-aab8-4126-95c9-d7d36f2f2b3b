package tw.com.taipower.pwoms.controller.system;

import static tw.com.taipower.pwoms.constant.ApiUrl.API_SYSTEM;
import static tw.com.taipower.pwoms.constant.ApiUrl.API_SYSTEM_SKIPERPAPPLICATION;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.log4j.Log4j2;
import tw.com.taipower.pwoms.controller.AbstractController;
import tw.com.taipower.pwoms.controller.vo.response.DataResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.PageResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.ResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.api.BadRequestException;
import tw.com.taipower.pwoms.filter.AuthenticationFilter;
import tw.com.taipower.pwoms.services.account.TaipowerCompanyUnitService;
import tw.com.taipower.pwoms.services.filter.AmiMeterModelFilter;
import tw.com.taipower.pwoms.services.filter.ApplicationFilter;
import tw.com.taipower.pwoms.services.flowcontrol.ApplicationService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.account.RoleVo;
import tw.com.taipower.pwoms.services.vo.account.TaipowerCompanyUnitVo;
import tw.com.taipower.pwoms.services.vo.generated.AmiMeterModelVO;
import tw.com.taipower.pwoms.services.vo.generated.ApplicationExampleDocumentVO;
import tw.com.taipower.pwoms.services.vo.generated.ApplicationVO;

@Log4j2
@RestController
@RequestMapping(API_SYSTEM + API_SYSTEM_SKIPERPAPPLICATION)
@Tag(name = "系統管理模組")
public class SkipERPApplicationController extends AbstractController {
	
	@Autowired
	ApplicationService applicationService;
	
	@Autowired
    AuthenticationFilter sessionAuthFilter;
	
	@Operation(summary = "根據免送ERPFlag取得契約", description = "")
    @PostMapping("/list")
    public PageResponseMessage<ApplicationVO> findBySkipERP(@RequestBody ApplicationFilter filter) {
        var list = applicationService.findAllByPage(filter);
        // TODO data == null 噴出異常
        return new PageResponseMessage<>(list);
    }
	
	@Operation(summary = "更新免送ERP契約", description = "")
    @PutMapping("/{contractNo}")
    public ResponseEntity<ResponseMessage> updateSkipERPAapplication(@PathVariable(value = "contractNo") String contractNo,
												            @RequestParam("isSkipERP") Boolean isSkipERP,HttpServletRequest request) throws IOException, BadRequestException {

		Long accountId = null;
        if (sessionAuthFilter != null)
            accountId = sessionAuthFilter.passSessionAuthority(request);
        if(accountId != null) {
        	
        	Integer version = applicationService.findMaxUseVersionWithContractNo(contractNo);
        	
        	if(version == null) {
        		throw new BadRequestException("契約不存在，請重新輸入");
        	}
        	var today = DateUtils.getTruncatedDate(new Date());
        	List<Long> applicationIds = applicationService.findAllByContractNoAndToday(contractNo+"-"+version, today);
        	
        	if(!applicationIds.isEmpty()) {

				List<ApplicationVO> applications = applicationService.findByContractNo(contractNo);
        		
        		for(ApplicationVO application : applications) {
        			applicationService.updateSkipERPStatus(application.getId(), isSkipERP, accountId);
        		}
        		
        		
        	}else {
        		throw new BadRequestException("契約已終止，無法修改狀態");
        	}
        	
        	
        	
    		
        }else {
        	return this.resourceResponse(400);
        }
		
		
        // TODO data == null 噴出異常 + @RequestBody @Valid LoadEntityVo loadEntity 效驗欄位
        return this.resourceResponse(200);
    }

}
