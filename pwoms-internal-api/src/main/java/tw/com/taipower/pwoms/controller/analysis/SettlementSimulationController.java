package tw.com.taipower.pwoms.controller.analysis;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.pwoms.controller.AbstractController;
import tw.com.taipower.pwoms.controller.vo.response.DataResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.api.BadRequestException;
import tw.com.taipower.pwoms.enumclass.PartialQueryCategoryEnum;
import tw.com.taipower.pwoms.service.ApiSettlementService;
import tw.com.taipower.pwoms.services.analysis.SettlementSimulationService;
import tw.com.taipower.pwoms.services.flowcontrol.ApplicationService;
import tw.com.taipower.pwoms.services.system.EnergyChargeService;
import tw.com.taipower.pwoms.services.system.VoltageLevelService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.analysis.*;
import tw.com.taipower.pwoms.services.vo.generated.*;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

import static tw.com.taipower.pwoms.constant.ApiUrl.API_SETTLEMENT_REPORT_PROCESS_MENU;
import static tw.com.taipower.pwoms.constant.ApiUrl.API_SETTLEMENT_SIMULATION;

@Log4j2
@RestController
@RequestMapping(API_SETTLEMENT_SIMULATION)
@Tag(name = "結算模擬分析")
public class SettlementSimulationController extends AbstractController {

    @Autowired
    private ApiSettlementService apiSettlementService;

    @Autowired
    VoltageLevelService voltageLevelService;

    @Autowired
    ApplicationService applicationService;

    @Autowired
    EnergyChargeService energyChargeService;

    @Autowired
    SettlementSimulationService settlementSimulationService;


    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> handleFileUpload(
            @Parameter(description = "上傳4個JSON檔案", content = @io.swagger.v3.oas.annotations.media.Content(
                    mediaType = "application/json"))
            @RequestPart("jsonFiles") MultipartFile[] jsonFiles,

            @Parameter(description = "上傳2個Excel檔案", content = @io.swagger.v3.oas.annotations.media.Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
            @RequestPart("excelFiles") MultipartFile[] excelFiles,

            @Parameter(description = "年份")
            @RequestPart("year") int year,

            @Parameter(description = "月份")
            @RequestPart("month") int month
    ) {

        if (jsonFiles.length != 4 || excelFiles.length != 2) {
            return ResponseEntity.badRequest().body("需要4個JSON檔案和2個Excel檔案");
        }

        try {
            Map<String, Object> jsonResults = processJsonFiles(jsonFiles);
            Map<String, List<OriginalPowerVo>> excelResults = processExcelFiles(excelFiles);

            // 1. 取出 JSON 物件
            List<AssociatedContractsVo> associatedContracts =
                    (List<AssociatedContractsVo>) jsonResults.get("associatedContracts");
            List<GenerationsVo> generations =
                    (List<GenerationsVo>) jsonResults.get("generations");
            List<LoadsVo> loads =
                    (List<LoadsVo>) jsonResults.get("loads");
            ConfigurableTOUsVo configurableTous =
                    (ConfigurableTOUsVo) jsonResults.get("configurableTous");

            // 2. 取出 Excel 物件
            List<OriginalPowerVo> loadRawList = excelResults.get("loadRaw");
            List<OriginalPowerVo> generationRawList = excelResults.get("generationRaw");

            //繼續模擬結算_儲存Json
            settlementSimulationService.saveSimulationInfo(year,month,associatedContracts,generations,loads);

            //開始結算



            return ResponseEntity.ok("處理完成\nJSON結果: " + jsonResults + "\nExcel結果: " + excelResults);

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("文件處理錯誤: " + e.getMessage());
        }
    }

    @GetMapping(value = "/associated-contracts")
    public ResponseEntity<?> getAssociatedContracts (@RequestParam(name = "year") int year
            , @RequestParam(name = "month") int month, @RequestParam(name = "contractNo") String contractNo, @RequestParam(name = "version") String version) throws Exception {

        PartialQueryCategoryEnum categoryEnum = PartialQueryCategoryEnum.get("CONTRACT_NO");

        LocalDateTime ldt = LocalDateTime.of(year, month, 1, 0, 0, 0);

        // 轉為 UTC 毫秒數
        long tDate = ldt.toInstant(ZoneOffset.UTC).toEpochMilli();

        List<String> contratNoList = new ArrayList<>();

        contratNoList.add(contractNo+"-"+version);

        if(null == categoryEnum) {
            return resourceResponse(HttpStatus.BAD_REQUEST, "category 種類有誤，category只有CONTRACT_NO或CUSTOMER_NUMBER");
        }

        List<String> associatedContractNos = apiSettlementService.getRelationContractByContractOrNcn(categoryEnum, tDate , contratNoList);

        List<AssociatedContractsVo> associatedContracts = new ArrayList<>();

        if(!associatedContractNos.isEmpty()) {
            for(String associatedContractNo : associatedContractNos) {
                var today = DateUtils.getTruncatedDate(new Date());
                List<Long> applicationIds = applicationService.findAllByContractNoAndToday(associatedContractNo, today);
                if(!applicationIds.isEmpty()) {
                    ApplicationVO applicationVO = applicationService.findById(applicationIds.getFirst());

                    AssociatedContractsVo  associatedContractVo = new AssociatedContractsVo();
                    associatedContractVo.setContractNo(associatedContractNo);
                    associatedContractVo.setApplicationId(applicationVO.getId());


                    associatedContracts.add(associatedContractVo);

                }


            }
        }

        // 轉成 JSON
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(associatedContracts);
        byte[] jsonBytes = jsonString.getBytes(StandardCharsets.UTF_8);

        String fileName = contractNo+"-"+version+"_Associated_Contracts.json";

        // 回傳檔案下載
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename="+fileName)
                .contentType(MediaType.APPLICATION_JSON)
                .contentLength(jsonBytes.length)
                .body(jsonBytes);

    }

    @GetMapping(value = "/generations")
    public ResponseEntity<?> getGenerations (@RequestParam(name = "year") int year
            , @RequestParam(name = "month") int month,@RequestParam(name = "contractNo") String contractNo, @RequestParam(name = "version") String version) throws Exception {


        var today = DateUtils.getTruncatedDate(new Date());
//        List<Long> applicationIds = applicationService.findAllByContractNoAndToday(contractNo+"-"+version, today);



//        if(!applicationIds.isEmpty()) {
//
//            ApplicationVO applicationVO = applicationService.findById(applicationIds.getFirst());
//
//            if(!applicationVO.getApplicationGeneratorList().isEmpty()){
//
//                for(ApplicationGeneratorVO applicationGeneratorVO : applicationVO.getApplicationGeneratorList()){
//
//                    GenerationsVo  generationVo = new GenerationsVo();
//
//                    generationVo.setPmi(applicationGeneratorVO.getPmi());
//                    generationVo.setId(applicationGeneratorVO.getId());
//                    generationVo.setApplicationId(applicationGeneratorVO.getApplicationId());
//                    generationVo.setGeneratorId(applicationGeneratorVO.getGeneratorId());
//                    String responsibilityVoltage = voltageLevelService.findById(applicationGeneratorVO.getGeneratorEntity().getResponsibilityVoltage()).getLabel();
//                    generationVo.setResponsibilityVoltage(responsibilityVoltage);
//
//                    List<GeneratorEntityMeterVo> generatorEntityMeterList = new ArrayList<>();
//
//                    if(!applicationGeneratorVO.getGeneratorEntity().getGeneratorEntityCombinedCapacities().isEmpty()){
//
//                        for(GeneratorEntityCombinedCapacityVO generatorEntityCombinedCapacityVO : applicationGeneratorVO.getGeneratorEntity().getGeneratorEntityCombinedCapacities() ){
//                            GeneratorEntityMeterVo  generatorEntityMeterVo = new GeneratorEntityMeterVo();
//                            generatorEntityMeterVo.setCapacity(generatorEntityCombinedCapacityVO.getCapacity());
//                            generatorEntityMeterVo.setCombinedDate(generatorEntityCombinedCapacityVO.getCombinedDate());
//                            generatorEntityMeterVo.setLicenseDate(generatorEntityCombinedCapacityVO.getLicenseDate());
//
//                            generatorEntityMeterList.add(generatorEntityMeterVo);
//                        }
//                    }
//
//                    generationVo.setGeneratorEntityMeterList(generatorEntityMeterList);
//
//                    generations.add(generationVo);
//
//                }
//
//            }
//
//        }else {
//            throw new BadRequestException("契約已終止，無法下載資訊");
//        }

        PartialQueryCategoryEnum categoryEnum = PartialQueryCategoryEnum.get("CONTRACT_NO");

        LocalDateTime ldt = LocalDateTime.of(year, month, 1, 0, 0, 0);

        // 轉為 UTC 毫秒數
        long tDate = ldt.toInstant(ZoneOffset.UTC).toEpochMilli();

        List<String> contratNoList = new ArrayList<>();

        contratNoList.add(contractNo+"-"+version);

        if(null == categoryEnum) {
            return resourceResponse(HttpStatus.BAD_REQUEST, "category 種類有誤，category只有CONTRACT_NO或CUSTOMER_NUMBER");
        }

        List<String> associatedContractNos = apiSettlementService.getRelationContractByContractOrNcn(categoryEnum, tDate , contratNoList);

        List<GenerationsVo> generations =  new ArrayList<>();

        if(!associatedContractNos.isEmpty()) {

            for(String associatedContractNo : associatedContractNos) {
                List<Long> applicationIds_relation = applicationService.findAllByContractNoAndToday(associatedContractNo, today);

                if(!applicationIds_relation.isEmpty()) {

                    ApplicationVO applicationVO = applicationService.findById(applicationIds_relation.getFirst());

                    if(!applicationVO.getApplicationGeneratorList().isEmpty()){

                        for(ApplicationGeneratorVO applicationGeneratorVO : applicationVO.getApplicationGeneratorList()){

                            GenerationsVo  generationVo = new GenerationsVo();

                            generationVo.setPmi(applicationGeneratorVO.getPmi());
                            generationVo.setId(applicationGeneratorVO.getId());
                            generationVo.setApplicationId(applicationGeneratorVO.getApplicationId());
                            generationVo.setGeneratorId(applicationGeneratorVO.getGeneratorId());

                            String responsibilityVoltage = voltageLevelService.findById(applicationGeneratorVO.getGeneratorEntity().getResponsibilityVoltage()).getLabel();
                            generationVo.setResponsibilityVoltage(responsibilityVoltage);

                            List<GeneratorEntityMeterVo> generatorEntityMeterList = new ArrayList<>();

                            if(!applicationGeneratorVO.getGeneratorEntity().getGeneratorEntityCombinedCapacities().isEmpty()){

                                for(GeneratorEntityCombinedCapacityVO generatorEntityCombinedCapacityVO : applicationGeneratorVO.getGeneratorEntity().getGeneratorEntityCombinedCapacities() ){
                                    GeneratorEntityMeterVo  generatorEntityMeterVo = new GeneratorEntityMeterVo();
                                    generatorEntityMeterVo.setCapacity(generatorEntityCombinedCapacityVO.getCapacity());
                                    generatorEntityMeterVo.setCombinedDate(generatorEntityCombinedCapacityVO.getCombinedDate());
                                    generatorEntityMeterVo.setLicenseDate(generatorEntityCombinedCapacityVO.getLicenseDate());

                                    generatorEntityMeterList.add(generatorEntityMeterVo);
                                }
                            }

                            generationVo.setGeneratorEntityMeterList(generatorEntityMeterList);

                            generations.add(generationVo);

                        }

                    }

                }

            }

        }





        // 轉成 JSON
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(generations);
        byte[] jsonBytes = jsonString.getBytes(StandardCharsets.UTF_8);

        String fileName = contractNo+"-"+version+"_Generations.json";

        // 回傳檔案下載
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename="+fileName)
                .contentType(MediaType.APPLICATION_JSON)
                .contentLength(jsonBytes.length)
                .body(jsonBytes);

    }

    @GetMapping(value = "/loads")
    public ResponseEntity<?> getLoads (@RequestParam(name = "year") int year
            , @RequestParam(name = "month") int month,@RequestParam(name = "contractNo") String contractNo, @RequestParam(name = "version") String version) throws Exception {

        var today = DateUtils.getTruncatedDate(new Date());
//        List<Long> applicationIds = applicationService.findAllByContractNoAndToday(contractNo+"-"+version, today);

//        if(!applicationIds.isEmpty()) {
//
//            ApplicationVO applicationVO = applicationService.findById(applicationIds.getFirst());
//
//            if(!applicationVO.getApplicationLoadList().isEmpty()){
//
//                for(ApplicationLoadVO applicationLoadVO : applicationVO.getApplicationLoadList()){
//
//                    LoadsVo  loadVo = new LoadsVo();
//
//
//                    loadVo.setId(applicationLoadVO.getId());
//                    loadVo.setApplicationId(applicationLoadVO.getApplicationId());
//                    loadVo.setLoadId(applicationLoadVO.getLoadId());
//                    loadVo.setAnnualContractCap(BigDecimal.valueOf(applicationLoadVO.getAnnualContractCap()));
//                    String responsibilityVoltage = voltageLevelService.findById(applicationLoadVO.getLoadEntity().getResponsibilityVoltage()).getLabel();
//                    loadVo.setResponsibilityVoltage(responsibilityVoltage);
//                    loadVo.setMonthlyContractCap(BigDecimal.valueOf(applicationLoadVO.getMonthlyContractCap()));
//
//
//                    loads.add(loadVo);
//
//                }
//
//            }
//
//        }else {
//            throw new BadRequestException("契約已終止，無法下載資訊");
//        }

        PartialQueryCategoryEnum categoryEnum = PartialQueryCategoryEnum.get("CONTRACT_NO");

        LocalDateTime ldt = LocalDateTime.of(year, month, 1, 0, 0, 0);

        // 轉為 UTC 毫秒數
        long tDate = ldt.toInstant(ZoneOffset.UTC).toEpochMilli();

        List<String> contratNoList = new ArrayList<>();

        contratNoList.add(contractNo+"-"+version);

        if(null == categoryEnum) {
            return resourceResponse(HttpStatus.BAD_REQUEST, "category 種類有誤，category只有CONTRACT_NO或CUSTOMER_NUMBER");
        }

        List<String> associatedContractNos = apiSettlementService.getRelationContractByContractOrNcn(categoryEnum, tDate , contratNoList);

        List<LoadsVo> loads =  new ArrayList<>();

        if(!associatedContractNos.isEmpty()) {

            for(String associatedContractNo : associatedContractNos) {
                List<Long> applicationIds_relation = applicationService.findAllByContractNoAndToday(associatedContractNo, today);

                if(!applicationIds_relation.isEmpty()) {

                    ApplicationVO applicationVO = applicationService.findById(applicationIds_relation.getFirst());

                    if(!applicationVO.getApplicationLoadList().isEmpty()){

                        for(ApplicationLoadVO applicationLoadVO : applicationVO.getApplicationLoadList()){

                            LoadsVo  loadVo = new LoadsVo();

                            loadVo.setId(applicationLoadVO.getId());
                            loadVo.setApplicationId(applicationLoadVO.getApplicationId());
                            loadVo.setLoadId(applicationLoadVO.getLoadId());
                            loadVo.setAnnualContractCap(BigDecimal.valueOf(applicationLoadVO.getAnnualContractCap()));
                            String responsibilityVoltage = voltageLevelService.findById(applicationLoadVO.getLoadEntity().getResponsibilityVoltage()).getLabel();
                            loadVo.setResponsibilityVoltage(responsibilityVoltage);
                            loadVo.setMonthlyContractCap(BigDecimal.valueOf(applicationLoadVO.getMonthlyContractCap()));


                            loads.add(loadVo);

                        }

                    }

                }

            }

        }



        // 轉成 JSON
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(loads);
        byte[] jsonBytes = jsonString.getBytes(StandardCharsets.UTF_8);

        String fileName = contractNo+"-"+version+"_Loads.json";

        // 回傳檔案下載
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename="+fileName)
                .contentType(MediaType.APPLICATION_JSON)
                .contentLength(jsonBytes.length)
                .body(jsonBytes);

    }

    @GetMapping(value = "/configurable")
    public ResponseEntity<?> getConfigurable (@RequestParam(name = "year") int year
            , @RequestParam(name = "month") int month,@RequestParam(name = "contractNo") String contractNo, @RequestParam(name = "version") String version) throws Exception {

        var today = DateUtils.getTruncatedDate(new Date());
        List<Long> applicationIds = applicationService.findAllByContractNoAndToday(contractNo+"-"+version, today);

        ConfigurableTOUsVo configurableTOUsVo = new ConfigurableTOUsVo();

        if(!applicationIds.isEmpty()) {

            ApplicationVO applicationVO = applicationService.findById(applicationIds.getFirst());

            if(!applicationVO.getApplicationLoadList().isEmpty()){

                ApplicationLoadVO applicationLoadVO = applicationVO.getApplicationLoadList().getFirst();

                String contractStg = applicationLoadVO.getLoadEntity().getContractStg();
                Integer timeSlot = applicationLoadVO.getLoadEntity().getTimeStg();

                List<ContractStgVO> contractStgVOS = energyChargeService.getContractStgListByContractStg(contractStg);

                if(!contractStgVOS.isEmpty()){
                    List<EnergyChargeTableVO>  energyChargeTableVOS = energyChargeService.getEnergyChargeTableListByTimeSlotAndContractId((short)timeSlot.intValue(),contractStgVOS.getFirst().getId());

                    if(!energyChargeTableVOS.isEmpty()){

                        for(EnergyChargeTableVO energyChargeTableVO : energyChargeTableVOS){

                            if(isYearMonthInRange(energyChargeTableVO.getFrom(), energyChargeTableVO.getTo(), year, month)){

                                configurableTOUsVo.setSummerStartStr(energyChargeTableVOS.getFirst().getSummerStartStr());
                                configurableTOUsVo.setSummerEndStr(energyChargeTableVOS.getFirst().getSummerEndStr());

                                break;
                            }

                        }




                    }


                }


            }



        }else {
            throw new BadRequestException("契約已終止，無法下載資訊");
        }

        // 轉成 JSON
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(configurableTOUsVo);
        byte[] jsonBytes = jsonString.getBytes(StandardCharsets.UTF_8);

        String fileName = "Configurable_TOUs.json";

        // 回傳檔案下載
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename="+fileName)
                .contentType(MediaType.APPLICATION_JSON)
                .contentLength(jsonBytes.length)
                .body(jsonBytes);

    }

    private Map<String, Object> processJsonFiles(MultipartFile[] jsonFiles) throws IOException {
        Map<String, Object> result = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();

        for (MultipartFile file : jsonFiles) {
            String fileName = file.getOriginalFilename();

            assert fileName != null;
            if (fileName.endsWith("_Associated_Contracts.json")) {
                List<AssociatedContractsVo> associatedContractsVolist = Arrays.asList(
                        mapper.readValue(file.getInputStream(), AssociatedContractsVo[].class)
                );
                result.put("associatedContracts", associatedContractsVolist);
            } else if (fileName.endsWith("_Generations.json")) {
                List<GenerationsVo> generationsVolist = Arrays.asList(
                        mapper.readValue(file.getInputStream(), GenerationsVo[].class)
                );
                result.put("generations", generationsVolist);
            } else if (fileName.endsWith("_Loads.json")) {
                List<LoadsVo> loadsVolist = Arrays.asList(
                        mapper.readValue(file.getInputStream(), LoadsVo[].class)
                );
                result.put("loads", loadsVolist);
            } else if (fileName.equals("Configurable_TOUs.json")) {
                result.put("configurableTous", mapper.readValue(file.getInputStream(), ConfigurableTOUsVo.class));
            } else {
                throw new IllegalArgumentException("未知的JSON檔案名稱: " + fileName);
            }
        }
        // 檢查是否四種都齊全
        if (result.size() != 4) {
            throw new IllegalArgumentException("JSON檔案類型不齊全");
        }
        return result;
    }

    private Map<String, List<OriginalPowerVo>> processExcelFiles(MultipartFile[] excelFiles) throws IOException {
        Map<String, List<OriginalPowerVo>> result = new HashMap<>();

        for (MultipartFile file : excelFiles) {
            String fileName = file.getOriginalFilename();

            assert fileName != null;
            if (fileName.contains("原始用電量")) {
                result.put("loadRaw", parseExcel(file));
            } else if (fileName.contains("原始發電量")) {
                result.put("generationRaw", parseExcel(file));
            } else {
                throw new IllegalArgumentException("未知的Excel檔案名稱: " + fileName);
            }
        }
        if (result.size() != 2) {
            throw new IllegalArgumentException("Excel檔案類型不齊全");
        }
        return result;
    }

    // 共用Excel解析方法，第四行是表頭，第五行開始才是內容
    private List<OriginalPowerVo> parseExcel(MultipartFile file) throws IOException {
        List<OriginalPowerVo> list = new ArrayList<>();
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (int i = 4; i <= sheet.getLastRowNum(); i++) { // index 4 = 第五行
                Row row = sheet.getRow(i);
                if (row == null) continue;
                OriginalPowerVo vo = new OriginalPowerVo();
                vo.setMeterNo(row.getCell(0).getStringCellValue());
                // 日期欄位（假設Excel日期格式正確）
                Cell dateCell = row.getCell(1);
                if (dateCell != null && dateCell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(dateCell)) {
                    vo.setDate(dateCell.getDateCellValue()); // 直接取得java.util.Date
                } else if (dateCell != null && dateCell.getCellType() == CellType.STRING) {
                    // 若日期是字串，需自行解析
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    vo.setDate(sdf.parse(dateCell.getStringCellValue()));
                }

                double[] periods = new double[96];
                for (int j = 0; j < 96; j++) {
                    periods[j] = row.getCell(j + 2).getNumericCellValue();
                }
                vo.setPeriods(periods);

                list.add(vo);
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return list;
    }

    public static boolean isYearMonthInRange(Date from, Date to, int year, int month) {
        // month: 1~12
        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.set(year, month - 1, 1); // Calendar 的 month 從 0 開始
        Date targetDate = cal.getTime();

        if (from == null) {
            // 如果 from 也可能為 null，可依需求調整
            return false;
        }
        if (to == null) {
            // 無結束日，只需大於等於 from
            return !targetDate.before(from);
        } else {
            // 有結束日，需介於 from ~ to 之間（含端點）
            return !targetDate.before(from) && !targetDate.after(to);
        }
    }

}
