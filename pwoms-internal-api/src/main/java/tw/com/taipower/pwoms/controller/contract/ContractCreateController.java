package tw.com.taipower.pwoms.controller.contract;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.pwoms.controller.AbstractController;
import tw.com.taipower.pwoms.controller.vo.request.contract.ServiceNoRequest;
import tw.com.taipower.pwoms.controller.vo.response.DataResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.ResponseMessage;
import tw.com.taipower.pwoms.service.vo.MyUserPrincipal;
import tw.com.taipower.pwoms.services.contract.ContractService;
import tw.com.taipower.pwoms.services.flowcontrol.ApplicationService;
import tw.com.taipower.pwoms.services.flowcontrol.ApplicationStage2TxService;
import tw.com.taipower.pwoms.services.vo.generated.ApplicationEntityDocumentRequiredVO;
import tw.com.taipower.pwoms.services.vo.generated.ApplicationVO;
import tw.com.taipower.pwoms.services.vo.generated.EntityDocumentRequiredVO;
import tw.com.taipower.pwoms.services.vo.generated.GeneratorEntityDocumentVO;
import tw.com.taipower.pwoms.utils.CommonUtil;

import java.io.IOException;
import java.util.List;

import static tw.com.taipower.pwoms.constant.ApiUrl.API_CONTRACT_CREATE;

/**
 * Contract Resource
 *
 * @class: ApplicantResource
 * @author:
 * @version: 0.1.0
 * @since: 2024-04-25 14:27
 * @see:
 **/

@Log4j2
@RestController
@RequestMapping(API_CONTRACT_CREATE)
@Tag(name = "契約管理模組")
public class ContractCreateController extends AbstractController {

        @Autowired
        ContractService contractService;

        @Autowired
        ApplicationService applicationService;

        @Autowired
        ApplicationStage2TxService applicationStage2TxService;

        @Operation(summary = "利用遞件編號查詢對應計畫書，完整比對", description = "")
        @GetMapping("/serviceNo/{serviceNo}")
        public DataResponseMessage<ApplicationVO> saveApplicationContact(
                        @PathVariable(value = "serviceNo") String serviceNo) {
                var applicationId = contractService.findApplicatonByNo(serviceNo, "");
                var vo = applicationService.findById(applicationId);
                return new DataResponseMessage(vo);
        }

        @Operation(summary = "利用遞件編號以及同意函公文文號查詢對應計畫書，完整比對", description = "")
        @PostMapping("/serviceNo/{serviceNo}")
        public DataResponseMessage<ApplicationVO> saveApplicationContact(
                        @PathVariable(value = "serviceNo") String serviceNo,
                        @RequestBody ServiceNoRequest request) {
                var applicationId =
                                contractService.findApplicatonByNo(serviceNo, request.getComment());
                var vo = applicationService.findById(applicationId);
                return new DataResponseMessage(vo);
        }

        @Operation(summary = "取得當前計畫書資訊且產生契約編號", description = "")
        @GetMapping("/application/single/{applicationId}")
        public DataResponseMessage<ApplicationVO> saveApplicationContact(
                        @PathVariable(value = "applicationId") Long applicationId) {
                applicationStage2TxService.giveApplicationContractNo(applicationId);
                var vo = applicationService.findById(applicationId);
                return new DataResponseMessage(vo);
        }

        @Operation(summary = "步驟一、輸入遞件編號及同意函\u200B -> 同意函有沒有上傳, 是否字號日期有效(?)", description = "")
        @PostMapping("/application/step1check/{applicationId}")
        public DataResponseMessage<ApplicationVO> step1check(
                        @PathVariable(value = "applicationId") Long applicationId,
                        @AuthenticationPrincipal MyUserPrincipal principal) {
                var vo = ApplicationVO.builder().id(applicationId).build();
                contractService.checkStep1(vo);
                return new DataResponseMessage(null);
        }

        @Operation(summary = "步驟二、輸入發電端轉供比例\u200B -> 比例是否正確", description = "")
        @PostMapping("/application/step2check/{applicationId}")
        public DataResponseMessage<ApplicationVO> step2check(
                        @RequestBody ApplicationVO applicationVO,
                        @PathVariable(value = "applicationId") Long applicationId,
                        @AuthenticationPrincipal MyUserPrincipal principal) {

                contractService.checkStep2(applicationVO, principal.getUserId());
                return new DataResponseMessage(null);
        }

        @Operation(summary = "步驟三、輸入用電端轉供上限\u200B -> 不確定要不要檢查", description = "")
        @PostMapping("/application/step3check/{applicationId}")
        public DataResponseMessage<ApplicationVO> step3check(
                        @RequestBody ApplicationVO applicationVO,
                        @PathVariable(value = "applicationId") Long applicationId,
                        @AuthenticationPrincipal MyUserPrincipal principal) {

                contractService.checkStep3(applicationVO, principal.getUserId());
                return new DataResponseMessage(null);
        }

        @Operation(summary = "步驟四、上傳契約相關文件\u200B -> 是否有未用印契約檔案, 同意函是否字號日期有效(?)\n" + "\n",
                        description = "")
        @PostMapping("/application/step4check/{applicationId}")
        public DataResponseMessage<ApplicationVO> step4check(
                        @PathVariable(value = "applicationId") Long applicationId,
                        @AuthenticationPrincipal MyUserPrincipal principal) {
                var vo = ApplicationVO.builder().id(applicationId).build();
                contractService.checkStep4(vo);
                return new DataResponseMessage(null);
        }

        @Operation(summary = "步驟五、上傳發電端相關文件 -> 裡面所有的輸入的字號是否有效(?)", description = "")
        @PostMapping("/application/step5check/{applicationId}")
        public DataResponseMessage<ApplicationVO> step5check(
                        @PathVariable(value = "applicationId") Long applicationId,
                        @AuthenticationPrincipal MyUserPrincipal principal) {

                var vo = ApplicationVO.builder().id(applicationId).build();
                contractService.checkStep5(vo);

                return new DataResponseMessage(null);
        }

        @Operation(summary = "取得單一計畫書已上傳文件紀錄", description = "")
        @GetMapping("/application/{id}/document/all")
        public DataResponseMessage<List<GeneratorEntityDocumentVO>> getVmAll(
                        @PathVariable(value = "id") Long id) {
                var list = applicationService.findUploadedDocumentsByApplicationId(id);
                return new DataResponseMessage(list);
        }

        @Operation(summary = "下載計畫書已經上完的檔案", description = "")
        @GetMapping("/application/{id}/document/{fileId}/download")
        public ResponseEntity<byte[]> getUploadedDocumentByEntityIdAndId(
                        @PathVariable(value = "id") Long id,
                        @PathVariable(value = "fileId") Long fileId

        ) {

                var result = applicationService.getUploadedDocumentById(fileId);
                return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                                "attachment; filename=\"" + "document" + result.getDocumentId()
                                                + "." + result.getExt() + "\"")
                                .body(result.getContent());
        }

        @Operation(summary = "下載計畫書發電端已經上完的檔案", description = "")
        @GetMapping("/application/generator/document/download/{entityId}/{source}/{fileId}")
        public ResponseEntity<byte[]> getUploadedDocumentByEntityIdAndId(
                        @PathVariable(value = "entityId") Long entityId,
                        @PathVariable(value = "source") String source,
                        @PathVariable(value = "fileId") Long fileId

        ) {

                var result = applicationService.getGeneratorEntityDocumentFile(entityId, source,
                                fileId);
                return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                                "attachment; filename=\"" + "document" + result.getDocumentId()
                                                + "." + result.getExt() + "\"")
                                .body(result.getContent());
        }

        @Operation(summary = "上傳計畫書應附文件，上傳完成提供檔案ＩＤ", description = "")
        @PostMapping("/application/{id}/document/{documentId}/upload")
        public DataResponseMessage<Long> addApplicationDocument(@PathVariable(value = "id") Long id,
                        @PathVariable(value = "documentId") Integer documentId,
                        @RequestParam(value = "file", required = false) MultipartFile file,
                        @RequestParam(value = "unit", required = false) String unit,
                        @RequestParam(value = "serialNumber", required = false) String serialNumber,
                        @RequestParam(value = "operationMode",
                                        required = false) String operationMode,
                        @RequestParam(value = "validDate", required = false) String validDate,
                        @RequestParam(value = "issueDate", required = false) String issueDate,
                        @RequestParam(value = "comment", required = false) String comment,
                        @AuthenticationPrincipal MyUserPrincipal principal) throws Exception {
                var userId = principal.getUserId();
                var vmId = applicationService.uploadDocumentByApplicationIdAndDocumentId(id,
                                documentId, file, unit, serialNumber, operationMode,
                                CommonUtil.foramtDateTimeUtcString(validDate),
                                CommonUtil.foramtDateTimeUtcString(issueDate), comment, userId);
                return new DataResponseMessage<Long>(vmId);
        }


        @Operation(summary = "上傳發電端應附文件，上傳完成提供檔案ＩＤ",
                        description = "有些檔案可能沒有額外敘述，所以上傳時可以為空，日期也是同理，如果檔案為空，卻有註釋代表要調整註釋")
        @PostMapping("/application/generator/{entityId}/document/{documentId}/upload")
        public DataResponseMessage<Long> addNweMeterVm(
                        @PathVariable(value = "entityId") Long entityId,
                        @PathVariable(value = "documentId") Integer documentId,
                        @RequestParam(value = "file", required = false) MultipartFile file,
                        @RequestParam(value = "unit", required = false) String unit,
                        @RequestParam(value = "serialNumber", required = false) String serialNumber,
                        @RequestParam(value = "operationMode",
                                        required = false) String operationMode,
                        @RequestParam(value = "validDate", required = false) String validDate,
                        @RequestParam(value = "comment", required = false) String comment,
                        @RequestParam(value = "issueDate", required = false) String issueDate,
                        @AuthenticationPrincipal MyUserPrincipal principal) throws Exception {
                var userId = principal.getUserId();
                var vmId = applicationService.uploadDocumentByEntityIdAndDocumentId(entityId,
                                documentId, file, unit, serialNumber, operationMode,
                                CommonUtil.foramtDateTimeUtcString(validDate), comment,
                                CommonUtil.foramtDateTimeUtcString(issueDate), userId);
                return new DataResponseMessage<Long>(vmId);
        }

        @Operation(summary = "依據計畫書類型取得發電端已上傳文件紀錄", description = "")
        @GetMapping("/application/generator/document/uploaded/{applicationType}/{entityId}")
        public DataResponseMessage<List<GeneratorEntityDocumentVO>> getVmAll(
                        @PathVariable(value = "applicationType") String applicationType,
                        @PathVariable(value = "entityId") Long entityId)
                        throws JsonProcessingException {
                var list = applicationService.getGeneratorEntityDocument(applicationType, entityId);
                return new DataResponseMessage(list);
        }

        @Operation(summary = "取得計畫書所有應附文件", description = "")
        @GetMapping("/application/required/document")
        public DataResponseMessage<List<EntityDocumentRequiredVO>> findAllRequiredDocuments() {


                var list = applicationService.findPlanAllRequiredDocuments();
                return new DataResponseMessage(list);
        }

        @Operation(summary = "取得計畫書發電端所有應附文件", description = "")
        @GetMapping("/application/generator/required/document/{applicationType}")
        public DataResponseMessage<List<ApplicationEntityDocumentRequiredVO>> getGeneratorRequiredDocumentsByApplicationType(
                        @PathVariable(value = "applicationType") String applicationType) {
                var list = applicationService
                                .getRequiredDocumentsByApplicationType(applicationType);
                return new DataResponseMessage(list);
        }

        @Operation(summary = "產生未用印契約文件", description = "")
        @GetMapping("/application/{applicationId}/document21/create")
        public ResponseEntity<ResponseMessage> createContractNotSignDocument(
                        @PathVariable(value = "applicationId") Long applicationId,
                        @AuthenticationPrincipal MyUserPrincipal principal) throws IOException {
                var userId = principal.getUserId();
                contractService.createContractNotSignDocument(applicationId, userId);
                return this.resourceSuccessfulResponse();
        }

        @Operation(summary = "儲存契約草稿", description = "")
        @PostMapping("/application/{applicationId}/savedraft")
        public ResponseEntity<ResponseMessage> saveDraft(@RequestBody ApplicationVO applicationVO,
                        @PathVariable(value = "applicationId") Long applicationId,
                        @AuthenticationPrincipal MyUserPrincipal principal) {
                var userId = principal.getUserId();
                contractService.saveDraft(applicationVO, userId);
                return this.resourceSuccessfulResponse();
        }

        @Operation(summary = "送出新增契約", description = "")
        @PostMapping("/application/{applicationId}/submit")
        public ResponseEntity<ResponseMessage> submit(@RequestBody ApplicationVO applicationVO,
                        @PathVariable(value = "applicationId") Long applicationId,
                        @AuthenticationPrincipal MyUserPrincipal principal)
                        throws JsonProcessingException {
                var userId = principal.getUserId();
                contractService.submit(applicationVO, userId);
                return this.resourceSuccessfulResponse();
        }
}
