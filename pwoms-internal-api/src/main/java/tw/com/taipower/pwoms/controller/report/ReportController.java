package tw.com.taipower.pwoms.controller.report;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.core.io.InputStreamResource;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tw.com.taipower.pwoms.controller.AbstractController;
import tw.com.taipower.pwoms.controller.vo.response.api.NotFoundException;
import tw.com.taipower.pwoms.service.ExcelExporter;
import tw.com.taipower.pwoms.services.account.TaipowerCompanyUnitService;
import tw.com.taipower.pwoms.services.report.FileService;
import tw.com.taipower.pwoms.services.report.FlexibleReportRequest;
import tw.com.taipower.pwoms.services.report.FlexibleReportService;
import tw.com.taipower.pwoms.services.report.ReportService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.report.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import static tw.com.taipower.pwoms.constant.ApiMessage.NO_TABLE_CONTENT;
import static tw.com.taipower.pwoms.constant.ApiUrl.API_REPORT;

/**
 * @class: ReportResource
 * @author:
 * @version: 0.1.0
 * @since: 2024-04-25 14:27
 * @see:
 **/

@Log4j2
@RestController
@RequestMapping(API_REPORT)
@Tag(name = "報表模組")
public class ReportController extends AbstractController {

    @Autowired
    ReportService service;

    @Autowired
    TaipowerCompanyUnitService companyUnitService;

    @Autowired
    FileService fileService;

    @Autowired
    ExcelExporter excelExporter;

    @Autowired
    FlexibleReportService flexibleReportService;

    @Operation(summary = "#1 原始資料報表.xlsx(前端提供 帳單 起訖 年 月 讓客人選擇 回傳起迄日期 請填固定1日的 date string) + 彈性分配"
            , description = "路徑參數 useFrom 起始時間 ex. 2024-03-01, useTo 結束時間 ex. 2024-09-01 只提供 .xlsx Excel 檔案下載" +
                       ", 產出檔名範例: 原始資料_11301-11309.xlsx")
    @GetMapping("/download/{useFrom}/{useTo}/original-source")
    public ResponseEntity<?> downloadFile(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "帳單起始日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-03-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string", example = "2024-09-01") String useTo)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        Date end = DateUtils.passStringToDate(useTo);
        List<ReportVo> oriReport = service.getOriginalBillingReport(start, end);
        if (oriReport == null || oriReport.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String billStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String billEndDate = DateUtils.passYearMonthToString(end, "twYMm");
        String name = "原始資料";
        String tailName = ".xlsx";
        String oName = name +"_" +billStartDate +"-"+billEndDate+tailName;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(oriReport, oName, tailName, "originalSource", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#3 #4 外部業務處再購組 每月轉直供度數 (前端提供 發電設備運作月報表 年 月供用戶選擇 [回傳西元年月日(1日)] + 彈性分配" +
            ", 副檔名 可填寫 .xlsx 或 .csv) - 月發電端媒合4時段度數報表", description = "monthlyGenEnergyCharge" +
            " csv 報表檔前前綴 TransRelat11306_1130910.csv; excel 則是 ex.每月轉供度數_11306.xlsx")
    @GetMapping("/download/external/month/{useFrom}/{tailName}/generator-match")
    public ResponseEntity<?> downloadMonthlyEnergyRecharge(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom,
            @PathVariable(value = "tailName") @Parameter(name = "tailName", description = "副檔名，String"
                    , example = "xlsx or csv") String tailName) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGenPowerVo> monthlyGenEnergy = service.getMonthlyGeneratorMatchedRM(start);
        if (null == monthlyGenEnergy || monthlyGenEnergy.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String dtwYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String tailStr = tailName.equals("csv") ? ".csv": ".xlsx";
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String oName = tailStr.equals(".xlsx") ? "每月轉直供度數_" +dtwYMm+tailStr : "TransRelat" +dtwYMm+"_"+today+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(monthlyGenEnergy, oName, tailStr, "TransRelat", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        if (tailStr.equals(".xlsx")) {
            return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
        } else {
            return this.resourceFileResponse(urlName, "application/csv", file);
        }
    }

    @Operation(summary = "#7 外部 業務處再購組 每月發電端每15分鐘轉直供度數.csv (前端提供 發電設備運作月報表 年 月供用戶選擇 [回傳西元年月日(1日)])"
            , description = "monthly15MinGenEnergyCharge csv 報表檔前前綴 TransRelatRaw11306_1130910.csv")
    @GetMapping("/download/external/month/{useFrom}/15-min-generator-match")
    public ResponseEntity<?> downloadMonthly15MinEnergyRecharge(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGen15powerVo> monthly15GenEnergy = service.get15MinuteMonthlyGeneratorMatchedRM(start);
        //log.info("get db info"+monthly15GenEnergy);
        if (null == monthly15GenEnergy || monthly15GenEnergy.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String dtwYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String oName = "TransRelatRaw" +dtwYMm+"_"+today+".csv";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(monthly15GenEnergy, oName, ".csv", "TransRelatRaw", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/csv", file);
    }

    @Operation(summary = "#8 外部 業務處再購組 每月發電端4時段轉供度數.csv (前端提供 發電設備運作月報表 年 月供用戶選擇 [回傳西元年月日(1日)])" +
            ", 月發電端4時段再媒合度數 只有轉供", description = "monthly2ThRematchEnergyRecharge csv 報表檔前前綴 TransRelat2TH11306_1130912.csv")
    @GetMapping("/download/external/month/{useFrom}/generator-rematch")
    public ResponseEntity<?> downloadMonthly2ThRematchEnergyRecharge(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGenPowerVo> monthly2ThGenEnergy = service.get2ThMonthlyGeneratorReMatchedRM(start);
        //log.info("get db info"+monthly15GenEnergy);
        if (null == monthly2ThGenEnergy || monthly2ThGenEnergy.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String dtwYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String oName = "TransRelat2TH" +dtwYMm+"_"+today+".csv";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(monthly2ThGenEnergy, oName, ".csv", "TransRelat2TH", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/csv", file);
    }

    @Operation(summary = "#11 轉直供用戶之各月轉直供資料.xlsx(前端提供 用電端運作起訖 年 月 選擇 回傳起迄日期 請填固定1日的 date string) 業務處營收組 + 彈性分配"
            , description = "路徑參數 useFrom 起始時間 ex. 2024-03-01, useTo 結束時間 ex. 2024-09-01 只提供 .xlsx Excel 檔案下載" +
            ", 產出檔名範例: 轉直供用戶之各月轉直供資料_11303-11309.xlsx")
    @GetMapping("/download/external/month/{useFrom}/{useTo}/load-match")
    public ResponseEntity<?> downloadApplicationLoadMatchedRmByMonthRangeFile(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "起始日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-03-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "結束日期，String" +
                    " 請填固定1日的 結束 date string", example = "2024-09-01") String useTo)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        Date end = DateUtils.passStringToDate(useTo);
        List<ReportLoadVo> report = service.getMonthsApplicationLoadMatchedCn(start, end);
        if (report == null || report.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String billStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String billEndDate = DateUtils.passYearMonthToString(end, "twYMm");
        String name = "轉直供用戶之各月轉直供資料";
        String tailName = ".xlsx";
        String oName = name +"_" +billStartDate +"-"+billEndDate+tailName;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName, tailName, "loads4TimeSlot", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#5 每月發電端購電(Rch3表)每15分鐘讀值(含轉直供平台補值).csv" +
            ", 業務處再購組資料(前端提供 發電設備運作月報表 年 月供用戶選擇 [回傳西元年月日(1日)])"
            , description = "monthlyAmiSettlement15MinValue csv 報表檔前前綴 Surp11306_1130920.csv")
    @GetMapping("/download/external/month/{useFrom}/15-min-value")
    public ResponseEntity<?> downloadMonthlyAmiSettlement15MinValue(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGen15valueVo> monthly15AmiGenSettle = service.getMonthsAmiSettlement15Record(start);
        //log.info("get db info"+monthly15AmiGenSettle);
        if (null == monthly15AmiGenSettle || monthly15AmiGenSettle.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String dtwYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String oName = "Surp" +dtwYMm+"_"+today+".csv";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(monthly15AmiGenSettle, oName, ".csv", "Surp", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/csv", file);
    }

    @Operation(summary = "#6 每月發電端購電(Rch3表)每15分鐘讀值(含轉直供平台補值)_上限裝置容量.csv" +
            ", 業務處再購組資料(前端提供 發電設備運作月報表 年 月供用戶選擇 [回傳西元年月日(1日)])"
            , description = "monthlyAmiSettlement15RecordLimitCapacity csv 報表檔前前綴 SurpLim11306_1130920.csv")
    @GetMapping("/download/external/month/{useFrom}/15-min-value-limit")
    public ResponseEntity<?> downloadMonthlyAmiSettlement15MinLimitCapacity(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGen15valueVo> monthly15AmiGenSettle = service.getMonthsAmiSettlement15RecordLimitCapacity(start);
        //log.info("get db info"+monthly15AmiGenSettle);
        if (null == monthly15AmiGenSettle || monthly15AmiGenSettle.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String dtwYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String oName = "SurpLim" +dtwYMm+"_"+today+".csv";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(monthly15AmiGenSettle, oName, ".csv", "SurpLim", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/csv", file);
    }

    @Operation(summary = "#9 外部 業務處再購組 小額綠電契約4時段轉供度數(前端提供 發電設備服務運作月報表 起迄服務年月供用戶選擇 [起迄西元年月日(1日)]" +
            ", 副檔名 .xlsx) - 小額綠電契約4時段轉供度數", description = "smallGreenSettleServiceDateMatched" +
            " ex. 小額綠電契約4時段轉供度數11305_11310.xlsx")
    @GetMapping("/download/external/{useFrom}/{useTo}/green-match")
    public ResponseEntity<?> downloadGreenMatched(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務起始日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-03-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "服務結束日期，String" +
                    " 請填固定1日的 結束 date string", example = "2024-10-01") String useTo)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        Date end = DateUtils.passStringToDate(useTo);
        List<ReportGreenVo> greenMatchedInfo = service.getSmallGreenSettleServiceDateMatched(start, end, "Q");
        if (null == greenMatchedInfo || greenMatchedInfo.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String serviceEndDate = DateUtils.passYearMonthToString(end, "twYMm");
        String tailStr = ".xlsx";
        String oName = "小額綠電契約4時段轉供度數_" +serviceStartDate+"-"+serviceEndDate+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(greenMatchedInfo, oName, tailStr, "green4TimeSlot", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#10 外部 業務處再購組 小額綠電契約報表(前端提供 發電設備服務運作月報表 起迄服務年月供用戶選擇 [起迄西元年月日(1日)]" +
            ", 副檔名 .xlsx) - 小額綠電契約報表", description = "greenSmallSettleServiceDateMatched" +
            " ex. 小額綠電契約報表11305_11310.xlsx")
    @GetMapping("/download/external/{useFrom}/{useTo}/green-contract")
    public ResponseEntity<?> downloadGreenContracts(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務起始日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-03-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "服務結束日期，String" +
                    " 請填固定1日的 結束 date string", example = "2024-10-01") String useTo)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        Date end = DateUtils.passStringToDate(useTo);
        List<ReportGreenContractVo> greenContractInfo = service.getSmallGreenContractsInfo(start, end, "Q");
        if (null == greenContractInfo || greenContractInfo.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String serviceEndDate = DateUtils.passYearMonthToString(end, "twYMm");
        String tailStr = ".xlsx";
        String oName = "小額綠電契約報表_" +serviceStartDate+"-"+serviceEndDate+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(greenContractInfo, oName, tailStr, "greenContract", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#12 外部 業務處費率組 各電壓層級度數報表(前端提供 發電設備服務運作統計報表 服務統計截止年月供用戶選擇 [起迄西元年月日(1日)]" +
            ", 副檔名 .xlsx) - 各電壓層級度數報表", description = "totalYearVoltKwh" +
            " ex. 各電壓層級度數報表_11310.xlsx")
    @GetMapping("/download/external/{useFrom}/volt-level")
    public ResponseEntity<?> downloadYearVoltLevel(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務統計截止日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-12-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportVoltVo> voltageLevel = service.getTotalYearVoltKwh(start);
        if (null == voltageLevel || voltageLevel.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String tailStr = ".xlsx";
        String oName = "各電壓層級度數報表_" +serviceStartDate+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(voltageLevel, oName, tailStr, "voltageLevel", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#15 外部 調度處 每月轉直供服務各類度數 (前端提供 發電設備服務運作統計報表 服務運作年月供用戶選擇 [起迄西元年月日(1日)]" +
            ", 副檔名 .xlsx) - 轉直供服務各類度數", description = "monthExpRate" +
            " ex. 轉直供服務各類度數_11305.xlsx")
    @GetMapping("/download/external/month/{useFrom}/exp-rate")
    public ResponseEntity<?> downloadMonthExpRate(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務運作統計日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-05-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportInfoRateVo> expRate = service.getMonthExpRate(start);
        if (null == expRate || expRate.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String stringHeaderDate = DateUtils.passYearMonthToString(start, "TW");
        String tailStr = ".xlsx";
        String oName = "轉直供服務各類度數_" +serviceStartDate+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(expRate, oName, tailStr, "expRate~"+stringHeaderDate, null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#16 外部 調度處 當月發電月報 (前端提供畫面 不提供日期選擇 但輸入 [西元年月日(1日)]本月 1日 ex. 2024-10-01傳給後端" +
            ", 副檔名 .xlsx) - 當月發電月報", description = "monthExpRate" +
            " ex. 當月發電月報_11310.xlsx")
    @GetMapping("/download/external/month/{useFrom}/generator-com")
    public ResponseEntity<?> downloadGeneratorCompany(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務運作統計日期，String" +
            " 請填固定1日的 起始 date string", example = "2024-05-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGenComVo> expRate = service.getGeneratorCompanies(start);
        if (null == expRate || expRate.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String tailStr = ".xlsx";
        String oName = "當月發電月報_" +serviceStartDate+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(expRate, oName, tailStr, "genCom~發電月報", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#17 外部 調度處 線損計算 (前端提供 線損小組報告 帳單年月供用戶選擇 [起迄西元年月日(1日)]" +
            ", 副檔名 .xlsx) - 線損計算", description = "tpcLocationFuelVoltKwh" +
            " ex. 線損計算_11310.xlsx, 內有有三個 tab - 電源 用戶 對照表")
    @GetMapping("/download/external/month/{useFrom}/gen-load-tpc")
    public ResponseEntity<?> downloadGeneratorLoadTpcLocation(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "帳單統計日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-10-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportLocFuelVoltVo> genFuelVolt = service.getTpcLocationFuelVoltKwh(start, true);
        if (null == genFuelVolt || genFuelVolt.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        List<ReportLocFuelVoltVo> loadFuelVolt = service.getTpcLocationFuelVoltKwh(start, false);
        List<ReportTpcCodeUnitVo> tpcCom = companyUnitService.findTpcCodeUnitName();

        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String tailStr = ".xlsx";
        String oName = "線損計算_" +serviceStartDate+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(genFuelVolt, oName, tailStr, "genLoadTpcCom~線損計算"
                , loadFuelVolt, tpcCom));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#18 外部 調度處高雄報表 (前端提供日期選擇 但輸入 [西元年月日(1日)]本月 1日 ex. 2024-10-01傳給後端" +
            ", 副檔名 .xlsx) - 轉直供報表(高雄) 發電業者設備裝置容量資料", description = "genDeviceCapacities" +
            " ex. 轉直供報表(高雄)_11306.xlsx")
    @GetMapping("/download/external/month/{useFrom}/gen-device-capacity")
    public ResponseEntity<?> downloadGeneratorCompanyCapacity(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "契約有效日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGenCapacityVo> expRate = service.getGenDeviceCapacities(start);
        if (null == expRate || expRate.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String tailStr = ".xlsx";
        String oName = "轉直供報表(高雄)_" +serviceStartDate+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(expRate, oName, tailStr, "genDeviceComCapacities", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#19 外部 調度處 15分鐘發用電量報表 (前端提供 15分鐘發用電量報表 服務年月供用戶選擇 [起迄西元年月日(1日)]" +
            ", 副檔名 .xlsx) - 15分鐘發用電量報表", description = "ami15EndLoads" +
            " ex. 15分鐘發用電量報表_11306.xlsx, 內有有2個 tab - 發電量 用電量")
    @GetMapping("/download/external/month/{useFrom}/15-min-powers")
    public ResponseEntity<?> downloadAmi15EndGenLoadPower(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務統計日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGenLoad15powerVo> genPower = service.getAmi15GeneratorLoadEndPowers(start, true);
        if (null == genPower || genPower.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        List<ReportGenLoad15powerVo> loadPower = service.getAmi15GeneratorLoadEndPowers(start, false);

        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String tailStr = ".xlsx";
        String oName = "15分鐘發用電量報表_" +serviceStartDate+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(genPower, oName, tailStr, "ami15GenLoadPowers"
                , loadPower, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "彈性查詢.xlsx"
            , description = "產出檔名範例: 彈性查詢.xlsx")
    @PostMapping("/download/report/flexible-report")
    public ResponseEntity<?> downloadFlexibleReport(@RequestBody FlexibleReportRequest req)
            throws Exception {

        LocalDate sDate, eDate;
        if ((req.getServiceYear() == null && req.getServiceMonth() == null ) && (req.getBillYear() == null && req.getBillMonth() == null )) {
            return this.responseBadRequest("日期為空白！") ;
        }
        if (req.getPrintCol() == null ||  req.getPrintCol().length == 0 ) {
            return this.responseBadRequest("沒有列印欄位！") ;
        }
        if ( req.getServiceYear()  != null && req.getServiceMonth() != null ) {
            sDate =LocalDate.of(req.getServiceYear(),req.getServiceMonth(),1);
            eDate = LocalDate.of(sDate.getYear(), req.getServiceMonth(),1);
        } else if ( req.getServiceYear()  != null){
            sDate =LocalDate.of(req.getServiceYear(),1,1);
            eDate = LocalDate.of(sDate.getYear(), 12,1);
        } else if( req.getBillYear()  != null){
            sDate =LocalDate.of(req.getBillYear(),1,1);
            eDate = LocalDate.of(sDate.getYear(), 12,1);
        } else {
            sDate =LocalDate.of(LocalDate.now().getYear(), 1,1);
            eDate = LocalDate.of(sDate.getYear(), 12,1);
        }
        req.setUseFrom(Date.from(sDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        req.setUseTo(Date.from(eDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));

        List<?> oriReport = flexibleReportService.getFlexibleReport(req);
        if (oriReport == null || oriReport.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String startDate = DateUtils.passYearMonthToString(req.getUseFrom(), "twYMm");
        String endDate = DateUtils.passYearMonthToString(req.getUseTo(), "twYMm");
        String name = "彈性查詢"; // 彈性查詢下載報表
        String tailName = ".xlsx";
        String oName = name +"_" +startDate +"-"+endDate+tailName;

        InputStreamResource file = new InputStreamResource( excelExporter.exportToExcel(oriReport,req.getPrintCol())) ;
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#14 外部 調度處 每月發轉餘電表 (前端提供 發轉餘電表 服務年月供用戶選擇 [起迄西元年月日(1日)]" +
            ", 副檔名 .xlsx) - 發轉餘電表", description = "ami15PowerContractSummary" +
            " ex. 發轉餘電表_11306.xlsx, 內有有2個 tab - 統計 發電量")
    @GetMapping("/download/external/month/{useFrom}/15-min-power-contract")
    public ResponseEntity<?> downloadAmi15PowerContract(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務統計日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGen15TranReVo> genPower = service.getAmi15PowerContractNo(start); // 發電量
        if (null == genPower || genPower.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        List<ReportGen15TranReSummaryVo> genPowerSummary = service.getAmi15PowerContractNoSummary(start); // 統計
        if (null == genPowerSummary || genPowerSummary.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String tailStr = ".xlsx";
        String oName = "發轉餘電表_" +serviceStartDate+tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(genPowerSummary, oName, tailStr, "ami15PowerContractSummary"
                , genPower, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#13 外部 調度處 每月發轉餘總計 (前端提供 每月發轉餘總計 服務年供用戶選擇 [西元年(1月1日)]" +
            ", 副檔名 .xlsx) - 每月發轉餘總計", description = "yearPwDsPower" +
            " ex. 每月發轉餘總計.xlsx")
    @GetMapping("/download/external/{useFrom}/year-pw-ds-power")
    public ResponseEntity<?> downloadYearPwDsPower(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務統計日期，String" +
                    " 請填選定年+固定1月1日的 起始 date string", example = "2024-01-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportGenTransReYearVo> pwDsPower = service.getYearPwDsPowerInfo(start); // 發電餘電總計
        if (null == pwDsPower || pwDsPower.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String tailStr = ".xlsx";
        String oName = "每月發轉餘總計" +tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(pwDsPower, oName, tailStr, "yearPwDsPower"
                , null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#22 外部 企劃處 電能轉直供資訊報表 (前端提供 電能轉直供資訊報表 服務年供用戶選擇 [西元年(1月1日)]" +
            ", 副檔名 .xlsx) - 電能轉直供資訊報表_11306", description = "monthYearGenLoadPowerInfo" +
            " ex. 電能轉直供資訊報表_11306.xlsx")
    @GetMapping("/download/external/{useFrom}/month-year-gen-load-power")
    public ResponseEntity<?> downloadMonthYearGenLoadPower(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務統計日期，String" +
                    " 請填選定年+固定1月1日的 起始 date string", example = "2024-06-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportPowerGenLoadVo> genLoPower = service.getMonthYearGenLoadPowerInfo(start); // 電能轉直供資訊總計
        if (null == genLoPower || genLoPower.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String headerRunDate = DateUtils.passYearMonthToString(start, null);
        String tailStr = ".xlsx";
        String oName = "電能轉直供資訊報表_" + serviceStartDate +tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(genLoPower, oName, tailStr
                , "yearGenLoadRunPower~"+headerRunDate, null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#24 RFP2.7 系統管理報表 每月案件狀態統計報表 (前端提供 每月案件狀態統計報表 服務年供用戶選擇 [西元年(1月1日)]" +
            ", 副檔名 .xlsx) - 每月案件狀態統計報表_11306", description = "appStatusGenLoadElecNoCount" +
            " ex. 電能轉直供資訊報表_11306.xlsx")
    @GetMapping("/download/month/{useFrom}/app-gen-load-count")
    public ResponseEntity<?> downloadAppStatusGenLoadElecNoCount(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務統計日期，String" +
                    " 請填選定年+固定1月1日的 起始 date string", example = "2024-06-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportAppStatusGenLoadVo> genLoCount = service.getAppStatusGenLoadElecNoCounts(start); // 電能轉直供資訊總計
        if (null == genLoCount || genLoCount.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String headerRunDate = DateUtils.passYearMonthToString(start, null).replace("/","年");
        String tailStr = ".xlsx";
        String oName = "每月案件狀態統計報表_" + serviceStartDate +tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(genLoCount, oName, tailStr
                , "appGenLoadCount~"+headerRunDate, null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#25 RFP3.4 系統管理報表 每月發用電端統計報表 (前端選擇服務年月資料 副檔名 .xlsx)" +
            " - 每月發用電端統計報表_11308", description = "genLoadElecNoAppKwhInfo" +
            " ex. 每月發用電端統計報表_11308.xlsx")
    @GetMapping("/download/month/{useFrom}/15-min-kwh-gen-load-app")
    public ResponseEntity<?> downloadGenLoadElecNoAppKwhInfo(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "服務統計日期，String" +
                    " 請填選定年+固定1月1日 與 取得每用發用電統計 年月相同", example = "2024-06-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        ReportAppKwGenLoadVo genLoAppKwh = service.getGenLoadElecNoAppKwhInfo(start); // 每月發用電端統計報表
        if (null == genLoAppKwh) throw new NotFoundException(NO_TABLE_CONTENT);
        List<ReportGenElecNoAppKwVo> genSide = genLoAppKwh.getGenSide();
        List<ReportLoadElecNoAppKwVo> LoadSide = genLoAppKwh.getLoadSide();
        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String tailStr = ".xlsx";
        String oName = "每月發用電端統計報表_" + serviceStartDate +tailStr;
        InputStreamResource file = new InputStreamResource(fileService.loadFile(genSide, oName, tailStr
                , "genLoadAppKwh", LoadSide, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#23 外部 環保處 能源別發電報表.csv (前端提供 能源別發電報表 服務年月 供用戶選擇 [回傳西元年月日(1日)])"
            , description = "能源別發電報表 csv 報表檔前前綴 能源別發電報表_11306.csv")
    @GetMapping("/download/external/month/{useFrom}/fuel-volt-kwh")
    public ResponseEntity<?> downloadMonthlyFuelTypeVoltKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportFuelLabelAppTypeVoltClassVo> fuelVoltKwh = service.getFuelLabelPwDsVoltClassKwh(start);
        if (null == fuelVoltKwh || fuelVoltKwh.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);

        String dtwYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String oName = "能源別發電報表_" +dtwYMm+".csv";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(fuelVoltKwh, oName, ".csv", "FuelVoltKwh", null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/csv", file);
    }

    @Operation(summary = "#2 外部 會計處 勞務收入轉直供度數 (前端選擇帳單年月資料 副檔名 .xlsx)" +
            " - 勞務收入轉直供度數_11308", description = "ADSTKwhIncome" +
            " ex. 勞務收入轉直供度數_11308.xlsx")
    @GetMapping("/download/external/month/{useFrom}/adst-kwh-income")
    public ResponseEntity<?> downloadADSTKwhIncome(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "帳單日期，String" +
                    " 請填選定年月+1日", example = "2024-09-01") String useFrom)
            throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<ReportADSTKwhVo> adstKwhIncome = service.getADSTKwh(start);
        if (null == adstKwhIncome || adstKwhIncome.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        String serviceStartDate = DateUtils.passYearMonthToString(start, "twYMm");
        String tailStr = ".xlsx";
        String oName = "勞務收入轉直供度數_" + serviceStartDate +tailStr;
        serviceStartDate = DateUtils.passYearMonthToString(start, "TW");
        InputStreamResource file = new InputStreamResource(fileService.loadFile(adstKwhIncome, oName, tailStr
                , "adstKwhIncome"+"~"+serviceStartDate, null, null));
        String urlName = URLEncoder.encode(oName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }
}