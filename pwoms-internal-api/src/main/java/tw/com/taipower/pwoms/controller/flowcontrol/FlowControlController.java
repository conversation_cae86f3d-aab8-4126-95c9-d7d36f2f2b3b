package tw.com.taipower.pwoms.controller.flowcontrol;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.pwoms.controller.AbstractController;
import tw.com.taipower.pwoms.controller.vo.request.flowcontrol.TempEntitySearchRequest;
import tw.com.taipower.pwoms.controller.vo.request.flowcontrol.WorkflowSearchRequest;
import tw.com.taipower.pwoms.controller.vo.response.DataResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.PageResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.ResponseMessage;
import tw.com.taipower.pwoms.service.vo.MyUserPrincipal;
import tw.com.taipower.pwoms.services.entitymanage.ApplicantService;
import tw.com.taipower.pwoms.services.entitymanage.GeneratorService;
import tw.com.taipower.pwoms.services.entitymanage.LoadService;
import tw.com.taipower.pwoms.services.filter.*;
import tw.com.taipower.pwoms.services.flowcontrol.ApplicationService;
import tw.com.taipower.pwoms.services.flowcontrol.ApplicationStage2Service;
import tw.com.taipower.pwoms.services.flowcontrol.ApplicationWorkflowProgress;
import tw.com.taipower.pwoms.services.vo.flowcontrol.ApplicationGeneratorOrderVo;
import tw.com.taipower.pwoms.services.vo.flowcontrol.ApplicationLoadOrderVo;
import tw.com.taipower.pwoms.services.vo.generated.*;
import tw.com.taipower.pwoms.utils.CommonUtil;

import java.io.IOException;
import java.util.List;

import static tw.com.taipower.pwoms.constant.ApiUrl.API_FLOW_CONTROL;

/**
 * ERP Resource
 *
 * @class: ErpResource
 * @author:
 * @version: 0.1.0
 * @since: 2024-04-25 14:27
 * @see:
 **/

@Log4j2
@RestController
@RequestMapping(API_FLOW_CONTROL)
@Tag(name = "申請流程模組")
public class FlowControlController extends AbstractController {

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private ApplicantService applicantService;

    @Autowired
    private ApplicationStage2Service applicationStage2Service;

    @Autowired
    private LoadService loadService;
    @Autowired
    private GeneratorService generatorService;

    @Operation(summary = "取得案件契約資訊", description = "")
    @PostMapping("/application/list")
    public PageResponseMessage<ApplicationVO> getApplications(
            @RequestBody ApplicationFilter filter) {
        var data = applicationService.findAllByPage(filter);
        return new PageResponseMessage(data);
    }

    @Operation(summary = "取得案件契約資訊FULL", description = "")
    @PostMapping("/application/fulllist")
    public PageResponseMessage<ApplicationVO> getFullApplications(
            @RequestBody ViewApplicationWithEntityDetailFilter filter) {
        var data = applicationService.findFullAllByPage(filter);
        return new PageResponseMessage(data);
    }


    @Operation(summary = "選擇申請人及聯絡人以後，儲存案件基本資訊，並回傳案件對應編號", description = "")
    @PostMapping("/application/applicant/draft")
    public DataResponseMessage<Long> saveApplicationContact(@RequestBody ApplicationVO vo,
            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        var applicationId = applicationService.saveDraft(vo, userId);
        return new DataResponseMessage(applicationId);
    }

    @Operation(summary = "取得當前案件資訊", description = "")
    @GetMapping("/application/single/{applicationId}")
    public DataResponseMessage<ApplicationVO> saveApplicationContact(
            @PathVariable(value = "applicationId") Long applicationId) {

        var vo = applicationService.findById(applicationId);
        return new DataResponseMessage(vo);
    }

    @Operation(summary = "透過filter加入taxId相似，取得申請人(售電業、發電業)資訊", description = "")
    @PostMapping("/applicants/list")
    public PageResponseMessage<ApplicantEntityVO> getApplicants(
            @RequestBody ApplicantEntityFilter filter) {
        var data = applicantService.findAllByPage(filter);
        return new PageResponseMessage(data);
    }


    @Operation(summary = "取得單一申請人(含聯絡人)資訊", description = "")
    @GetMapping("/applicantfull/{id}")
    public DataResponseMessage<ApplicantEntityVO> getFullById(@PathVariable(value = "id") Long id) {
        var data = applicantService.findFullVOById(id);
        return new DataResponseMessage(data);
    }

    @Operation(summary = "取得當前案件所有發電端資訊", description = "")
    @GetMapping("/application/single/{applicationId}/all/generator")
    public DataResponseMessage<ApplicationGeneratorVO> findAllApplicationGeneratorsByApplicationId(
            @PathVariable(value = "applicationId") Long applicationId) {

        var vo = applicationService.findAllApplicationGeneratorsByApplicationId(applicationId);
        return new DataResponseMessage(vo);
    }

    @Operation(summary = "取得當前案件所有用電端資訊", description = "")
    @GetMapping("/application/single/{applicationId}/all/load")
    public DataResponseMessage<ApplicationLoadVO> findAllApplicationLoadsByApplicationId(
            @PathVariable(value = "applicationId") Long applicationId) {

        var vo = applicationService.findAllApplicationLoadsByApplicationId(applicationId);
        return new DataResponseMessage(vo);
    }


    @Operation(summary = "取得當前案件單一用電端暫存資訊", description = "")
    @GetMapping("/application/single/{applicationId}/temp/load/{loadId}")
    public DataResponseMessage<ApplicationLoadVO> searchTempLoadByApplicationLoadVo(
            @PathVariable(value = "applicationId") Long applicationId,
            @PathVariable(value = "loadId") Long loadId) {
        var vo = new ApplicationLoadVO();
        vo.setApplicationId(applicationId);
        vo.setLoadId(loadId);
        var res = applicationService.searchTempLoadByApplicationLoadVo(vo);
        return new DataResponseMessage(res);
    }

    @Operation(summary = "儲存當前案件單一用電端暫存資訊", description = "")
    @PostMapping("/application/single/{applicationId}/temp/load/{loadId}")
    public ResponseEntity<ResponseMessage> saveTempLoadByApplicationLoadVo(
            @PathVariable(value = "applicationId") Long applicationId,
            @PathVariable(value = "loadId") Long loadId, @RequestBody ApplicationLoadVO vo) {
        // TODO
        throw new RuntimeException("Not implemented");
    }

    @Operation(summary = "取得當前案件單一發電端暫存資訊", description = "")
    @GetMapping("/application/single/{applicationId}/temp/generator/{generatorId}")
    public DataResponseMessage<ApplicationGeneratorVO> searchTempGeneratorByApplicationGeneratorVo(
            @PathVariable(value = "applicationId") Long applicationId,
            @PathVariable(value = "generatorId") Long generatorId) {
        var vo = new ApplicationGeneratorVO();
        vo.setApplicationId(applicationId);
        vo.setGeneratorId(generatorId);
        var res = applicationService.searchTempGeneratorByApplicationGeneratorVo(vo);
        return new DataResponseMessage(res);
    }

    @Operation(summary = "儲存當前案件單一發電端暫存資訊", description = "")
    @PostMapping("/application/single/{applicationId}/temp/generator/{generatorId}")
    public ResponseEntity<ResponseMessage> saveTempGeneratorByApplicationLoadVo(
            @PathVariable(value = "applicationId") Long applicationId,
            @PathVariable(value = "generatorId") Long generatorId,
            @RequestBody ApplicationGeneratorVO vo) {
        // TODO
        throw new RuntimeException("Not implemented");
    }

    @Operation(summary = "發電端大量查詢", description = "")
    @PostMapping("/application/generator/search")
    public DataResponseMessage<List<ApplicationGeneratorOrderVo>> searchGeneratorByUploadFile(
            @RequestBody List<ApplicationGeneratorOrderVo> vos) {

        var vo = applicationService.searchGeneratorByUploadFile(vos);
        return new DataResponseMessage(vo);
    }

    @Operation(summary = "用電端大量查詢", description = "")
    @PostMapping("/application/load/search")
    public DataResponseMessage<List<ApplicationLoadOrderVo>> searchLoadByUploadFile(
            @RequestBody List<ApplicationLoadOrderVo> vos) {

        var vo = applicationService.searchLoadByUploadFile(vos);
        return new DataResponseMessage(vo);
    }

    @Operation(summary = "發電端大量查詢", description = "")
    @PostMapping("/application/generator/searchfromexcel")
    public DataResponseMessage<List<ApplicationGeneratorVO>> searchGeneratorFromExcel(
            @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal MyUserPrincipal principal) throws IOException {
        var userId = principal.getUserId();
        var vo = applicationService.searchGeneratorFromExcel(file.getInputStream(), userId);
        return new DataResponseMessage(vo);
    }

    @Operation(summary = "用電端大量查詢", description = "")
    @PostMapping("/application/load/searchfromexcel")
    public DataResponseMessage<List<ApplicationLoadVO>> searchLoadsFromExcel(
            @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal MyUserPrincipal principal) throws IOException {
        var userId = principal.getUserId();
        var vo = applicationService.searchLoadsFromExcel(file.getInputStream(), userId);
        return new DataResponseMessage(vo);
    }

    @Operation(summary = "對當前案件新增發電端", description = "")
    @PostMapping("/application/single/{applicationId}/add/generator")
    public ResponseEntity<ResponseMessage> addGeneratorToApplication(
            @PathVariable(value = "applicationId") Long applicationId,
            @RequestBody List<ApplicationGeneratorOrderVo> vos,
            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        applicationService.addGeneratorToApplication(applicationId, vos, userId);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "對當前案件新增用電端", description = "")
    @PostMapping("/application/single/{applicationId}/add/load")
    public ResponseEntity<ResponseMessage> addLoadToApplication(
            @PathVariable(value = "applicationId") Long applicationId,
            @RequestBody List<ApplicationLoadOrderVo> vos,
            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        applicationService.addLoadToApplication(applicationId, vos, userId);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "對當前案件草稿儲存", description = "")
    @PostMapping("/application/single/{applicationId}/draft")
    public DataResponseMessage<Long> saveApplicationDraft(
            @PathVariable(value = "applicationId") Long applicationId,
            @RequestBody ApplicationVO vo, @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        applicationService.saveDraft(vo, userId);
        return new DataResponseMessage(applicationId);
    }


    @Operation(summary = "對當前案件草稿送出審查", description = "")
    @PostMapping("/application/single/{applicationId}/submit")
    public ResponseEntity<ResponseMessage> saveApplicationSubmit(
            @PathVariable(value = "applicationId") Long applicationId,
            @RequestBody ApplicationVO vo, @AuthenticationPrincipal MyUserPrincipal principal)
            throws JsonProcessingException {
        var userId = principal.getUserId();
        applicationService.submitApplication(applicationId, vo, userId);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "上傳檔案至案件儲存", description = "")
    @PostMapping("/application/single/{applicationId}/file/{fileId}/upload")
    public ResponseEntity<ResponseMessage> uploadApplicationFile(
            @PathVariable(value = "applicationId") Long applicationId,
            @PathVariable(value = "fileId") Long fileId, @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        // TODO 檔案上傳還未處理
        throw new RuntimeException("Not implemented");
    }

    @Operation(summary = "上傳檔案至相關發電端", description = "")
    @PostMapping("/application/single/{applicationId}/generator/{generatorId}/file/{fileId}/upload")
    public ResponseEntity<ResponseMessage> uploadApplicationGeneratorFile(
            @PathVariable(value = "applicationId") Long applicationId,
            @PathVariable(value = "fileId") Long fileId,
            @PathVariable(value = "generatorId") Long generatorId,
            @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        // TODO 檔案上傳還未處理
        throw new RuntimeException("Not implemented");
    }

    @Operation(summary = "上傳檔案至相關用電端", description = "")
    @PostMapping("/application/single/{applicationId}/load/{loadId}/file/{fileId}/upload")
    public ResponseEntity<ResponseMessage> uploadApplicationLoadFile(
            @PathVariable(value = "applicationId") Long applicationId,
            @PathVariable(value = "fileId") Long fileId,
            @PathVariable(value = "loadId") Long loadId, @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        // TODO 檔案上傳還未處理
        throw new RuntimeException("Not implemented");
    }

    @Operation(summary = "根據ID查詢大量發電端（優先從暫存區取出）", description = "")
    @PostMapping("/application/temp/generators")
    public DataResponseMessage<List<GeneratorEntityVO>> getTempGenerators(
            @RequestBody TempEntitySearchRequest request) {
        var data = generatorService.getTempGeneratorEntitys(request.getIds());
        return new DataResponseMessage(data);
    }

    @Operation(summary = "根據ID查詢大量用電端（優先從暫存區取出）", description = "")
    @PostMapping("/application/temp/loads")
    public DataResponseMessage<List<LoadEntityVO>> getTempLoads(
            @RequestBody TempEntitySearchRequest request) {
        var data = loadService.getTempLoadEntitys(request.getIds());
        return new DataResponseMessage(data);
    }

    @Operation(summary = "根據Filter查詢大量發電端（優先從暫存區取出）", description = "")
    @PostMapping("/application/temp/generators/byfilter")
    public PageResponseMessage<GeneratorEntityVO> getTempGeneratorEntitysByFilter(
            @RequestBody GeneratorEntityFilter filter,
            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        var data = generatorService.getTempGeneratorEntitysByFilter(filter, userId);
        return new PageResponseMessage(data);
    }

    @Operation(summary = "根據Filter查詢大量發電端", description = "")
    @PostMapping("/application/generators/byfilter")
    public PageResponseMessage<GeneratorEntityVO> getGeneratorEntitysByFilter(
            @RequestBody GeneratorEntityFilter filter) {
        var data = generatorService.findAllByPage(filter);
        return new PageResponseMessage(data);
    }

    @Operation(summary = "根據Filter查詢大量用電端（優先從暫存區取出）", description = "")
    @PostMapping("/application/temp/loads/byfilter")
    public PageResponseMessage<LoadEntityVO> getTempGenerators(@RequestBody LoadEntityFilter filter,
            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        var data = loadService.getTempLoadEntitysByFilter(filter, userId);
        return new PageResponseMessage(data);
    }

    @Operation(summary = "根據Filter查詢大量用電端", description = "")
    @PostMapping("/application/loads/byfilter")
    public PageResponseMessage<LoadEntityVO> getLoads(@RequestBody LoadEntityFilter filter) {
        var data = loadService.findAllByPage(filter);
        return new PageResponseMessage(data);
    }

    @Operation(summary = "取得計畫書所有應附文件", description = "")
    @GetMapping("/application/required/document")
    public DataResponseMessage<List<EntityDocumentRequiredVO>> findAllRequiredDocuments() {


        var list = applicationService.findPlanAllRequiredDocuments();
        return new DataResponseMessage(list);
    }

    @Operation(summary = "取得計畫書發電端所有應附文件", description = "")
    @GetMapping("/application/generator/required/document/{applicationType}")
    public DataResponseMessage<List<ApplicationEntityDocumentRequiredVO>> getGeneratorRequiredDocumentsByApplicationType(
            @PathVariable(value = "applicationType") String applicationType) {
        var list = applicationService.getRequiredDocumentsByApplicationType(applicationType);
        return new DataResponseMessage(list);
    }


    @Operation(summary = "取得單一計畫書已上傳文件紀錄", description = "")
    @GetMapping("/application/{id}/document/all")
    public DataResponseMessage<List<GeneratorEntityDocumentVO>> getVmAll(
            @PathVariable(value = "id") Long id) {
        var list = applicationService.findUploadedDocumentsByApplicationId(id);
        return new DataResponseMessage(list);
    }

    @Operation(summary = "依據計畫書類型取得發電端已上傳文件紀錄", description = "")
    @GetMapping("/application/generator/document/uploaded/{applicationType}/{entityId}")
    public DataResponseMessage<List<GeneratorEntityDocumentVO>> getVmAll(
            @PathVariable(value = "applicationType") String applicationType,
            @PathVariable(value = "entityId") Long entityId) throws JsonProcessingException {
        var list = applicationService.getGeneratorEntityDocument(applicationType, entityId);
        return new DataResponseMessage(list);
    }

    @Operation(summary = "下載計畫書已經上完的檔案", description = "")
    @GetMapping("/application/{id}/document/{fileId}/download")
    public ResponseEntity<byte[]> getUploadedDocumentByEntityIdAndId(
            @PathVariable(value = "id") Long id, @PathVariable(value = "fileId") Long fileId

    ) {

        var result = applicationService.getUploadedDocumentById(fileId);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + "document"
                        + result.getDocumentId() + "." + result.getExt() + "\"")
                .body(result.getContent());
    }

    @Operation(summary = "下載計畫書發電端已經上完的檔案", description = "")
    @GetMapping("/application/generator/document/download/{entityId}/{source}/{fileId}")
    public ResponseEntity<byte[]> getUploadedDocumentByEntityIdAndId(
            @PathVariable(value = "entityId") Long entityId,
            @PathVariable(value = "source") String source,
            @PathVariable(value = "fileId") Long fileId

    ) {

        var result = applicationService.getGeneratorEntityDocumentFile(entityId, source, fileId);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + "document"
                        + result.getDocumentId() + "." + result.getExt() + "\"")
                .body(result.getContent());
    }

    @Operation(summary = "上傳計畫書應附文件，上傳完成提供檔案ＩＤ", description = "")
    @PostMapping("/application/{id}/document/{documentId}/upload")
    public DataResponseMessage<Long> addApplicationDocument(@PathVariable(value = "id") Long id,
            @PathVariable(value = "documentId") Integer documentId,
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "unit", required = false) String unit,
            @RequestParam(value = "serialNumber", required = false) String serialNumber,
            @RequestParam(value = "operationMode", required = false) String operationMode,
            @RequestParam(value = "validDate", required = false) String validDate,
            @RequestParam(value = "issueDate", required = false) String issueDate,
            @RequestParam(value = "comment", required = false) String comment,
            @AuthenticationPrincipal MyUserPrincipal principal) throws Exception {
        var userId = principal.getUserId();
        var vmId = applicationService.uploadDocumentByApplicationIdAndDocumentId(id, documentId,
                file, unit, serialNumber, operationMode,
                CommonUtil.foramtDateTimeUtcString(validDate),
                CommonUtil.foramtDateTimeUtcString(issueDate), comment, userId);
        return new DataResponseMessage<Long>(vmId);
    }

    @Operation(summary = "上傳發電端應附文件，上傳完成提供檔案ＩＤ",
            description = "有些檔案可能沒有額外敘述，所以上傳時可以為空，日期也是同理，如果檔案為空，卻有註釋代表要調整註釋")
    @PostMapping("/application/generator/{entityId}/document/{documentId}/upload")
    public DataResponseMessage<Long> addNweMeterVm(@PathVariable(value = "entityId") Long entityId,
            @PathVariable(value = "documentId") Integer documentId,
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "unit", required = false) String unit,
            @RequestParam(value = "serialNumber", required = false) String serialNumber,
            @RequestParam(value = "operationMode", required = false) String operationMode,
            @RequestParam(value = "validDate", required = false) String validDate,
            @RequestParam(value = "comment", required = false) String comment,
            @RequestParam(value = "issueDate", required = false) String issueDate,
            @AuthenticationPrincipal MyUserPrincipal principal) throws Exception {
        var userId = principal.getUserId();
        var vmId = applicationService.uploadDocumentByEntityIdAndDocumentId(entityId, documentId,
                file, unit, serialNumber, operationMode,
                CommonUtil.foramtDateTimeUtcString(validDate), comment,
                CommonUtil.foramtDateTimeUtcString(issueDate), userId);
        return new DataResponseMessage<Long>(vmId);
    }

    @Operation(summary = "檢查當前計畫書是否發電端有任何缺漏", description = "")
    @PostMapping("/application/generators/fullycheck")
    public ResponseEntity<ResponseMessage> applicationGeneratorFullycheck(
            @RequestBody ApplicationVO vo) throws IOException {
        // M362，計畫書階段不檢查
        // applicationService.fullyCheckApplicationGeneratorValid(vo, false);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "檢查當前計畫書是否用電端有任何缺漏", description = "")
    @PostMapping("/application/loads/fullycheck")
    public ResponseEntity<ResponseMessage> applicationLoadFullycheck(@RequestBody ApplicationVO vo)
            throws IOException {
        // M362，計畫書階段不檢查
        // applicationService.fullyCheckApplicationLoadValid(vo);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "取得所有審查流程對應步驟", description = "")
    @PostMapping("/full/workflow")
    DataResponseMessage<ApplicationWorkflowProgress> getAllWorkflowStepMapping(
            @RequestBody WorkflowSearchRequest request) {
        var vo = applicationStage2Service.getApplicationWorkflowProgress(request.getNo());
        return new DataResponseMessage<>(vo);
    }


    @Operation(summary = "取得所有審查流程對應步驟", description = "")
    @DeleteMapping("/application/abandon/{applicationId}")
    ResponseEntity<ResponseMessage> abandon(
            @PathVariable(value = "applicationId") Long applicationId,
            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        applicationService.abandon(applicationId, userId);
        return this.resourceSuccessfulResponse();
    }
}
