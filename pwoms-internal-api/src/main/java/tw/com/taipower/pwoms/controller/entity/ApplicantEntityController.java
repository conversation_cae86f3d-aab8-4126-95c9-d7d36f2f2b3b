package tw.com.taipower.pwoms.controller.entity;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.pwoms.controller.AbstractController;
import tw.com.taipower.pwoms.controller.vo.response.DataResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.PageResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.ResponseMessage;
import tw.com.taipower.pwoms.service.vo.MyUserPrincipal;
import tw.com.taipower.pwoms.services.entitymanage.ApplicantService;
import tw.com.taipower.pwoms.services.filter.ApplicantEntityFilter;
import tw.com.taipower.pwoms.services.vo.generated.ApplicantEntityContactVO;
import tw.com.taipower.pwoms.services.vo.generated.ApplicantEntityVO;
import tw.com.taipower.pwoms.services.vo.generated.EntityDocumentRequiredVO;
import tw.com.taipower.pwoms.services.vo.generated.GeneratorEntityDocumentVO;
import tw.com.taipower.pwoms.utils.CommonUtil;

import java.io.IOException;
import java.util.List;

import static tw.com.taipower.pwoms.constant.ApiUrl.API_ENTITY;
import static tw.com.taipower.pwoms.constant.ApiUrl.API_ENTITY_APPLICANT;

/**
 * Settlement Resource
 *
 * @class: SettlementResource
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-04-25 14:27
 * @see:
 **/

@Log4j2
@RestController
@RequestMapping(API_ENTITY + API_ENTITY_APPLICANT)
@Tag(name = "用戶模組")
public class ApplicantEntityController extends AbstractController {


    @Autowired
    private ApplicantService service;

    @Operation(summary = "取得申請人(售電業、發電業)資訊", description = "")
    @PostMapping("/list")
    public PageResponseMessage<ApplicantEntityVO> getLoads(@RequestBody ApplicantEntityFilter filter
    ) {
        var data = service.findAllByPage(filter);
        return new PageResponseMessage(data);
    }

    @Operation(summary = "取得單一申請人", description = "")
    @GetMapping("/single/{id}")
    public DataResponseMessage<ApplicantEntityVO> getById(@PathVariable(value = "id") Long id) {
        var data = service.findVOById(id);
        return new DataResponseMessage(data);
    }

    @Operation(summary = "取得單一申請人(含聯絡人)", description = "")
    @GetMapping("/singlefull/{id}")
    public DataResponseMessage<ApplicantEntityVO> getFullById(@PathVariable(value = "id") Long id) {
        var data = service.findFullVOById(id);
        return new DataResponseMessage(data);
    }

    @Operation(summary = "新增單一申請人", description = "")
    @PostMapping("/single-add")
    public DataResponseMessage<Long> addnew(@RequestBody ApplicantEntityVO entity,
                                            @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        var id = service.save(entity, userId);
        return new DataResponseMessage(id);
    }

    @Operation(summary = "更新單一申請人", description = "")
    @PostMapping("/single/{id}")
    public Object updateById(@PathVariable(value = "id") Long id, @RequestBody ApplicantEntityVO entity,
                             @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
//        entity.setModifiedBy(userId);
        service.save(entity, userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "取得單一申請人所有聯絡人", description = "")
    @GetMapping("/single/{id}/contacts")
    public DataResponseMessage<List<ApplicantEntityContactVO>> getContactsById(@PathVariable(value = "id") Long id) {
        var data = service.findAllContactsByApplicantId(id);
        return new DataResponseMessage(data);
    }

    @Operation(summary = "更新單一申請人指定聯絡人", description = "")
    @PostMapping("/single/{id}/contact/{contactId}")
    public ResponseEntity<ResponseMessage> updateContactsById(@PathVariable(value = "id") Long id,
                                                              @PathVariable(value = "contactId") Long contactId,
                                                              @RequestBody ApplicantEntityContactVO vo,
                                                              @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.updateApplicantContact(vo, contactId, userId);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "新增單一申請人指定聯絡人", description = "")
    @PostMapping("/single/{id}/contact-add")
    public ResponseEntity<ResponseMessage> addContacts(@PathVariable(value = "id") Long id,
                                                       @RequestBody ApplicantEntityContactVO vo,
                                                       @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.addApplicantContact(vo, id, userId);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "取得申請人所有應附文件", description = "")
    @GetMapping("/required/document")
    public DataResponseMessage<List<EntityDocumentRequiredVO>> findAllRequiredDocuments() {


        var list = service.findAllRequiredDocuments();
        return new DataResponseMessage(list);
    }

    @Operation(summary = "取得單一申請人已上傳文件紀錄", description = "")
    @GetMapping("/single/{id}/document/all")
    public DataResponseMessage<List<GeneratorEntityDocumentVO>> getVmAll(@PathVariable(value = "id") Long id
    ) {


        var list = service.findUploadedDocumentsByEntityId(id);
        return new DataResponseMessage(list);
    }

    @Operation(summary = "下載已經上完的檔案", description = "")
    @GetMapping("/single/{id}/document/{fileId}/download")
    public ResponseEntity<byte[]> getUploadedDocumentByEntityIdAndId(@PathVariable(value = "id") Long id,
                                                                     @PathVariable(value = "fileId") Long fileId

    ) {

        var result = service.getUploadedDocumentByEntityIdAndId(fileId);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + "document" + result.getDocumentId() + "." + result.getExt() +
                                "\"")
                .body(result.getContent());
    }

    @Operation(summary = "上傳應附文件，上傳完成提供檔案ＩＤ", description = "")
    @PostMapping("/single/{id}/document/{documentId}/upload")
    public DataResponseMessage<Long> addNweMeterVm(@PathVariable(value = "id") Long id,
                                                   @PathVariable(value = "documentId") Integer documentId,
                                                   @RequestParam(value = "file", required = false) MultipartFile file,
                                                   @RequestParam(value = "unit",
                                                           required = false) String unit,
                                                   @RequestParam(value = "serialNumber",
                                                           required = false) String serialNumber,
                                                   @RequestParam(value = "operationMode",
                                                           required = false) String operationMode,
                                                   @RequestParam(value = "comment",
                                                           required = false) String comment,

                                                   @RequestParam("validDate") String validDate,
                                                   @RequestParam(value = "issueDate",
                                                           required = false) String issueDate,
                                                   @AuthenticationPrincipal MyUserPrincipal principal) throws Exception {
        var userId = principal.getUserId();
        var vmId =
                service.uploadDocumentByEntityIdAndDocumentId(id, documentId, file, unit, serialNumber, operationMode,
                        CommonUtil.foramtDateTimeUtcString(validDate), comment,
                        CommonUtil.foramtDateTimeUtcString(issueDate), userId);
        return new DataResponseMessage<Long>(vmId);
    }
}