package tw.com.taipower.pwoms.controller.entity;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.pwoms.controller.AbstractController;
import tw.com.taipower.pwoms.controller.vo.request.entity.EntityMeterVmUpdateVO;
import tw.com.taipower.pwoms.controller.vo.request.entity.GeneratorEntityMeterChildUpdateVO;
import tw.com.taipower.pwoms.controller.vo.request.entity.GeneratorEntityMeterDisplayUpdateVO;
import tw.com.taipower.pwoms.controller.vo.response.DataResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.PageResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.ResponseMessage;
import tw.com.taipower.pwoms.service.vo.MyUserPrincipal;
import tw.com.taipower.pwoms.services.entitymanage.EntityService;
import tw.com.taipower.pwoms.services.entitymanage.GeneratorService;
import tw.com.taipower.pwoms.services.filter.GeneratorEntityFilter;
import tw.com.taipower.pwoms.services.tpc.RemsService;
import tw.com.taipower.pwoms.services.vo.entity.MdesMeterVO;
import tw.com.taipower.pwoms.services.vo.entity.SimpleMeterSpVO;
import tw.com.taipower.pwoms.services.vo.entity.load.REMSNbReqest;
import tw.com.taipower.pwoms.services.vo.generated.*;
import tw.com.taipower.pwoms.utils.CommonUtil;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import static tw.com.taipower.pwoms.constant.ApiUrl.API_ENTITY;
import static tw.com.taipower.pwoms.constant.ApiUrl.API_ENTITY_GENERATOR;
import static tw.com.taipower.pwoms.services.enumclass.EntityType.GENERATOR;

/**
 * Settlement Resource
 *
 * @class: SettlementResource
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-04-25 14:27
 * @see:
 **/

@Log4j2
@RestController
@RequestMapping(API_ENTITY + API_ENTITY_GENERATOR)
@Tag(name = "用戶模組")
public class GeneratorEntityController extends AbstractController {


    @Autowired
    private GeneratorService service;

    @Autowired
    private RemsService remsService;

    @Autowired
    private EntityService entityService;
    @Autowired
    private GeneratorService generatorService;

    @Operation(summary = "取得發電業資訊", description = "")
    @PostMapping("/list")
    public PageResponseMessage<GeneratorEntityVO> getLoads(@RequestBody GeneratorEntityFilter filter) {
        var data = service.findAllByPage(filter);
        return new PageResponseMessage<>(data);
    }

    @Operation(summary = "取得單一發電業", description = "")
    @GetMapping("/single/{id}")
    public DataResponseMessage<GeneratorEntityVO> getById(@PathVariable(value = "id") Long id) {
        var data = service.findOneById(id);
        if (data.isPresent()) {
            data.get().setGeneratorEntityMeterList(service.findMeterByGeneratorEntityId(id));
            data.get().setGeneratorEntityCombinedCapacities(service.getCombinedCapacityList(id));
        }
        return new DataResponseMessage(data);
    }

    @Operation(summary = "取得單一發電業特殊電表", description = "")
    @GetMapping("/single/{id}/spmeters")
    public DataResponseMessage<GeneratorEntityVO> findSpecialMeterByGeneratorEntityId(
            @PathVariable(value = "id") Long id) {
        var data = service.findSpecialMeterByGeneratorEntityId(id);
        return new DataResponseMessage(data);
    }

    @Operation(summary = "取得單一發電業實際電表", description = "")
    @GetMapping("/single/{id}/actualmeters")
    public DataResponseMessage<GeneratorEntityVO> findNoneSpecialMeterByGeneratorEntityId(
            @PathVariable(value = "id") Long id) throws JsonProcessingException {
        var data = service.findNoneSpecialMeterByGeneratorEntityId(id);
        return new DataResponseMessage(data);
    }


    @Operation(summary = "更新單一發電業", description = "")
    @PostMapping("/single/{id}")
    public ResponseEntity<ResponseMessage> updateById(@PathVariable(value = "id") Long id,
                                                      @RequestBody GeneratorEntityVO entity,
                                                      @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        entity.setId(id);
        service.save(entity, userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "更新單一發電端電表及其關聯", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/relation")
    public ResponseEntity<ResponseMessage> updateMeterAndChild(@PathVariable(value = "id") Long id,
                                                               @PathVariable(value = "meterId") Long meterId,
                                                               @RequestBody GeneratorEntityMeterVO vo,
                                                               @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.updateFullMeter(vo, userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "刪除單一發電端電表及其關聯", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/relation-delete")
    public ResponseEntity<ResponseMessage> removeMeterChildsWithIds(@PathVariable(value = "id") Long id,
                                                                    @PathVariable(value = "meterId") Long meterId,
                                                                    @RequestBody GeneratorEntityMeterChildUpdateVO vo,
                                                                    @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.removeMeterChildsWithIds(vo.getSourceIds(), userId);
        return this.resourceResponse(200);
    }


    @Operation(summary = "更新單一發電端電表關聯，因為有刪除的情形，所以需要加傳原本操作的對象Id", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/relationonly")
    public ResponseEntity<ResponseMessage> updateMeterChildsOnly(@PathVariable(value = "id") Long id,
                                                                 @PathVariable(value = "meterId") Long meterId,
                                                                 @RequestBody GeneratorEntityMeterChildUpdateVO vo,
                                                                 @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.updateMeterChildsWithIds(meterId, vo.getSourceIds(), vo.getChildren(), vo.getVmId(), userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "更新特殊發電端電表關聯，因為有刪除的情形，所以需要加傳原本操作的對象Id", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/sp/relationonly")
    public ResponseEntity<ResponseMessage> updateSpMeterChildsOnly(@PathVariable(value = "id") Long id,
                                                                   @PathVariable(value = "meterId") Long meterId,
                                                                   @RequestBody GeneratorEntityMeterChildUpdateVO vo,
                                                                   @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.updateSPMeterChildsWithIds(meterId, vo.getSourceIds(), vo.getSpChildren(), vo.getImage(), userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "刪除特殊發電端電表及其關聯", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/sp/relation-delete")
    public ResponseEntity<ResponseMessage> removeSPMeterChildsWithIds(@PathVariable(value = "id") Long id,
                                                                      @PathVariable(value = "meterId") Long meterId,
                                                                      @RequestBody GeneratorEntityMeterChildUpdateVO vo,
                                                                      @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        entityService.removeEntityMeterSp(vo.getSourceIds(), userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "更新單一發電端電表表示關聯，因為有刪除的情形，所以需要加傳原本操作的對象Id", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/displayonly")
    public ResponseEntity<ResponseMessage> updateMeterChildsOnly(@PathVariable(value = "id") Long id,
                                                                 @PathVariable(value = "meterId") Long meterId,
                                                                 @RequestBody GeneratorEntityMeterDisplayUpdateVO vo,
                                                                 @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.updateMeterDisplaysWithIds(vo.getSourceIds(), vo.getDisplays(), userId);
        return this.resourceResponse(200);
    }


    @Operation(summary = "更新單一發電端電表", description = "")
    @PostMapping("/single/{id}/meter-only/{meterId}")
    public ResponseEntity<ResponseMessage> updateMeter(@PathVariable(value = "id") Long id,
                                                       @PathVariable(value = "meterId") Long meterId,
                                                       @RequestBody GeneratorEntityMeterVO vo,
                                                       @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.updateMeterOnly(vo, userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "刪除單一發電端電表", description = "")
    @DeleteMapping("/single/{id}/meter-only/{meterId}")
    public ResponseEntity<ResponseMessage> deleteMeter(@PathVariable(value = "id") Long id,
                                                       @PathVariable(value = "meterId") Long meterId,
                                                       @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.deleteMeterOnly(meterId, userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "新增單一發電端電表", description = "")
    @PostMapping("/single/{id}/meter-add")
    public ResponseEntity<ResponseMessage> addMeter(@PathVariable(value = "id") Long id,
                                                    @RequestBody GeneratorEntityMeterVO vo,
                                                    @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.addMeterOnly(vo, id, userId);
        return this.resourceResponse(200);
    }


    @Operation(summary = "新增單一發電端特殊電表使用的關聯圖檔", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/vm")
    public DataResponseMessage<Long> addNweMeterVm(@PathVariable(value = "id") Long id,
                                                   @PathVariable(value = "meterId") Long meterId,
                                                   @RequestParam("file") MultipartFile file,
//                                                         @RequestParam(value = "originStartDate",
//                                                                 required = false) String originStartDate,
//                                                         @RequestParam(value = "originEndDate",
//                                                                 required = false) String originEndDate,
                                                   @RequestParam("startDate") String startDate,
                                                   @RequestParam(value = "endDate",
                                                           required = false) String endDate,
                                                   @AuthenticationPrincipal MyUserPrincipal principal) throws Exception {
        var userId = principal.getUserId();
        var vmId = service.uploadMeterVm(meterId, CommonUtil.foramtDateTimeUtcString(startDate),
                CommonUtil.foramtDateTimeUtcString(endDate), file, userId);
        return new DataResponseMessage<Long>(vmId);
    }

    @Operation(summary = "取得單一發電端特殊電表使用的關聯圖檔", description = "")
    @GetMapping(value = "/single/{id}/meter/{meterId}/vm/{useFrom}/{useTo}/", produces = "application/octet-stream")
    public ResponseEntity<byte[]> getFile(@PathVariable(value = "id") Long id,
                                          @PathVariable(value = "meterId") Long meterId,
                                          @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description =
                                                  "日期，格式採用Number", example = "20240301") Long useFrom,
                                          @PathVariable(value = "useTo") @Parameter(name = "useTo", description =
                                                  "日期，格式採用Number", example = "20240301") Long useTo) {

        var result = service.getMeterVm(meterId, new Date(useFrom), new Date(useTo));
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"" + "meter" + meterId + ".png" + "\"").body(result.getContent());
    }

    @Operation(summary = "取得單一發電端特殊電表使用的關聯圖檔", description = "")
    @GetMapping("/single/{id}/meter/{meterId}/vm/{useFrom}/")
    public ResponseEntity<byte[]> getFile(@PathVariable(value = "id") Long id,
                                          @PathVariable(value = "meterId") Long meterId,
                                          @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description =
                                                  "日期，格式採用Number", example = "20240301") Long useFrom
    ) {

        var result = service.getMeterVm(meterId, new Date(useFrom), null);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + "meter" + meterId + ".png" + "\"")
                .body(result.getContent());
    }


    @Operation(summary = "取得單一發電端特殊電表使用的關聯圖檔", description = "")
    @GetMapping("/single/{id}/meter/{meterId}/vm/byId/{vmId}/")
    public ResponseEntity<byte[]> getFileById(@PathVariable(value = "id") Long id,
                                              @PathVariable(value = "meterId") Long meterId,
                                              @PathVariable(value = "vmId") Long vmId
    ) {

        var result = service.getMeterVmById(meterId, vmId);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + "meter" + meterId + ".png" + "\"")
                .body(result.getContent());
    }

    @Operation(summary = "修正單一發電端特殊電表使用的關聯圖檔的使用區間", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/vm/useRangeFix/")
    public ResponseEntity<ResponseMessage> getFile(@PathVariable(value = "id") Long id,
                                                   @PathVariable(value = "meterId") Long meterId,
                                                   @RequestBody EntityMeterVmUpdateVO vo,
                                                   @AuthenticationPrincipal MyUserPrincipal principal

    ) {

        var userId = principal.getUserId();
        service.updateMeterVmUseRangeOnly(meterId, vo.getOriginStartDate(), vo.getOriginEndDate(), vo.getStartDate(),
                vo.getEndDate(), userId);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "依據電號查詢相關電表", description = "")
    @GetMapping("/findcustomer/{customerNo}/distinct/meter")
    public DataResponseMessage<List<ViewAllEntityMeterVO>> findEntityMeterChildByCustomerNo(
            @PathVariable(value = "customerNo") String customerNo) {

        var list = service.findEntityMeterChildByCustomerNo(customerNo);

        return new DataResponseMessage(list);
    }

    @Operation(summary = "依據電號查詢相關電表", description = "")
    @GetMapping("/findcustomer/{customerNo}/distinct/entitymeter")
    public DataResponseMessage<List<SimpleMeterSpVO>> findEntityMeterByCustomerNo(
            @PathVariable(value = "customerNo") String customerNo
    ) {

        var list = service.findEntityMeterByCustomerNo(customerNo);

        return new DataResponseMessage(list);
    }

    @Operation(summary = "取得單一發電端特殊電表使用的關聯圖檔的所有紀錄", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/vm/all/")
    public DataResponseMessage<List<GeneratorEntityMeterVmVO>> getVmAll(@PathVariable(value = "id") Long id,
                                                                        @PathVariable(value = "meterId") Long meterId
    ) {


        var list = service.getMeterVmList(meterId);
        return new DataResponseMessage(list);
    }

    @Operation(summary = "取得發電端所有應附文件", description = "目前版本不需要透過發電端資訊過濾文件種類")
    @GetMapping("single/{id}/required/document")
    public DataResponseMessage<List<EntityDocumentRequiredVO>> findAllRequiredDocuments(
            @PathVariable(value = "id") Long id) {


        var list = service.findAllRequiredDocumentsWithoutGeneratorInfo();
        return new DataResponseMessage(list);
    }

    @Operation(summary = "取得單一發電端已上傳文件紀錄", description = "")
    @GetMapping("/single/{id}/document/all")
    public DataResponseMessage<List<GeneratorEntityDocumentVO>> getVmAll(@PathVariable(value = "id") Long id
    ) {


        var list = service.findUploadedDocumentsByEntityId(id);
        return new DataResponseMessage(list);
    }

    @Operation(summary = "下載已經上完的檔案", description = "")
    @GetMapping("/single/{id}/document/{fileId}/download")
    public ResponseEntity<byte[]> getUploadedDocumentByEntityIdAndId(@PathVariable(value = "id") Long id,
                                                                     @PathVariable(value = "fileId") Long fileId

    ) {

        var result = service.getUploadedDocumentByEntityIdAndId(fileId);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + "document" + result.getDocumentId() + "." + result.getExt() +
                                "\"")
                .body(result.getContent());
    }

    @Operation(summary = "上傳應附文件，上傳完成提供檔案ＩＤ",
            description = "有些檔案可能沒有額外敘述，所以上傳時可以為空，日期也是同理，如果檔案為空，卻有註釋代表要調整註釋")
    @PostMapping("/single/{id}/document/{documentId}/upload")
    public DataResponseMessage<Long> addNweMeterVm(@PathVariable(value = "id") Long id,
                                                   @PathVariable(value = "documentId") Integer documentId,
                                                   @RequestParam(value = "file", required = false) MultipartFile file,
                                                   @RequestParam(value = "unit",
                                                           required = false) String unit,
                                                   @RequestParam(value = "serialNumber",
                                                           required = false) String serialNumber,
                                                   @RequestParam(value = "operationMode",
                                                           required = false) String operationMode,
                                                   @RequestParam(value = "validDate",
                                                           required = false) String validDate,
                                                   @RequestParam(value = "comment",
                                                           required = false) String comment,
                                                   @RequestParam(value = "issueDate",
                                                           required = false) String issueDate,
                                                   @AuthenticationPrincipal MyUserPrincipal principal) throws Exception {
        var userId = principal.getUserId();
        var vmId = service.uploadDocumentByEntityIdAndDocumentId(id, documentId, file,
                unit,
                serialNumber,
                operationMode,
                CommonUtil.foramtDateTimeUtcString(validDate),
                comment, CommonUtil.foramtDateTimeUtcString(issueDate), userId);
        return new DataResponseMessage<Long>(vmId);
    }

    @Operation(summary = "同步發電端資訊，以電號資訊同步", description = "")
    @PostMapping("/syncfromrems")
    public ResponseEntity<ResponseMessage> syncFromREMS(@RequestBody REMSNbReqest reqest,
                                                        @AuthenticationPrincipal MyUserPrincipal principal) throws IOException {
        var userId = principal.getUserId();
        remsService.syncRNISWithCustomNumbers(reqest.getNbsCustomerNos(), userId);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "下載發電端變更紀錄", description = "")
    @GetMapping("/single/{id}/changes")
    public ResponseEntity<byte[]> getGeneratorChangeRecords(
            @PathVariable(value = "id") Long id
    ) {

        var result = generatorService.getGeneratorChangeRecords(id.intValue());
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + result.getNo() + ".pdf" +
                                "\"")
                .body(result.getContent());
    }

    @Operation(summary = "取得特殊表計使用所有關聯電表相關狀態", description = "")
    @GetMapping("/single/{id}/special-meter-infos")
    public DataResponseMessage<List<MdesMeterVO>> findEntitySpRelations(
            @PathVariable(value = "id") Long id
    ) {
        var infos = entityService.findEntitySpRelations(GENERATOR.getValue(), id);
        return new DataResponseMessage<List<MdesMeterVO>>(infos);
    }


}