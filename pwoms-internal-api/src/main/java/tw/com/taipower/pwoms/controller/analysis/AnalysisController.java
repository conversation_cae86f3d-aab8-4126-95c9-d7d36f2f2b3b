package tw.com.taipower.pwoms.controller.analysis;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tw.com.taipower.pwoms.controller.AbstractController;
import tw.com.taipower.pwoms.controller.vo.request.analysis.ElecNoListRequest;
import tw.com.taipower.pwoms.controller.vo.response.DataResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.analysis.*;
import tw.com.taipower.pwoms.controller.vo.response.api.BadRequestException;
import tw.com.taipower.pwoms.controller.vo.response.api.NotFoundException;
import tw.com.taipower.pwoms.service.ExcelExporter;
import tw.com.taipower.pwoms.services.report.FileService;
import tw.com.taipower.pwoms.services.report.GraphReportService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.analysis.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static tw.com.taipower.pwoms.constant.ApiMessage.NO_TABLE_CONTENT;
import static tw.com.taipower.pwoms.constant.ApiUrl.API_ANALYSIS;

/**
 * Analysis Resource
 *
 * @class: AnalysisResource
 * @author: jingfungchen
 * @version:
 * @since: 2025-03-16 13:36
 * @see:
 **/

@Log4j2
@RestController
@RequestMapping(API_ANALYSIS)
@Tag(name = "資料分析模組")
public class AnalysisController extends AbstractController {

    @Autowired
    GraphReportService service;

    @Autowired
    FileService fileService;

    @Autowired
    ExcelExporter excelExporter;

    @Operation(summary = "#1 申請者轉直供度數 Top 10 排名 - [產生圖表] 鍵, 根據 年下拉選單、月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 勾選年統計checkbox 隱藏月份選擇, [產生圖表] 回傳年度排名表" +
            ". 2. 取消 年統計 指定年月 2024 6 請輸入 2024-06-01, [產生圖表] 回傳該月的排名表")
    @GetMapping("/{useFrom}/applicant-kwh")
    public DataResponseMessage<List<NameKwhVo>> getTopApplicantNameKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
            " 請填固定1日的 起始 date string, 若只需要輸入年 路徑參數 請輸入 00月00日 ex. 2023-00-00"
                    , example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        boolean yrMark = useFrom.contains("-00-");
        GraphTopYearMonKwhVo tops = service.getTopApplicantKwh(start);
        if (null == tops) return new DataResponseMessage<>(new ArrayList<>());
        if (yrMark) return new DataResponseMessage<>(tops.getYearNameKwh());
        return new DataResponseMessage<>(tops.getMonthNameKwh());
    }

    @Operation(summary = "#1 申請者轉直供度數 Top 10 排名 - [匯出檔案]鍵, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 勾選年統計checkbox 隱藏月份選擇, ex. 選擇 2024 年 可下載 113年資料" +
            ". 2. 取消 年統計 指定年月 2024 6 請輸入 2024-06-01 可下載 11306與年度資料")
    @GetMapping("/{useFrom}/applicant-kwh-report")
    public ResponseEntity<?> getTopApplicantNameKwhReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入年 路徑參數 請輸入 00月00日 ex. 2023-00-00"
                    , example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        GraphReportMonthNameKwhVo report = service.getTopApplicantKwhReport(start, useFrom.contains("-00-"));
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String yr = twYMm.substring(0, twYMm.length()-2)+"年度";
        String oName = report.getGraphTop().getMonthNameKwh().isEmpty()? yr+"排名申請者轉直供度數": twYMm+"與年度排名申請者轉直供度數";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report.getYearNameKwhVo(), oName+tailName
                , tailName, "GRAPHCompanyKwh"+"_"+yr, report.getGraphTop().getYearNameKwh(), report.getGraphTop().getMonthNameKwh()));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#2 發電端轉直供度數 Top 10 排名 - [產生圖表] 鍵, 根據 年下拉選單、月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 勾選年統計checkbox 隱藏月份選擇, [產生圖表] 回傳年度排名表" +
            ". 2. 取消 年統計 指定年月 2024 6 請輸入 2024-06-01, [產生圖表] 回傳該月的排名表")
    @GetMapping("/{useFrom}/generator-name-kwh")
    public DataResponseMessage<List<NameKwhVo>> getTopGenNameKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入年 路徑參數 請輸入 00月00日 ex. 2023-00-00"
                    , example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        boolean yrMark = useFrom.contains("-00-");
        GraphTopYearMonKwhVo tops = service.getTopGenNameKwh(start);
        if (null == tops) return new DataResponseMessage<>(new ArrayList<>());
        if (yrMark) return new DataResponseMessage<>(tops.getYearNameKwh());
        return new DataResponseMessage<>(tops.getMonthNameKwh());
    }

    @Operation(summary = "#2 發電端轉直供度數 Top 10 排名 - [匯出檔案]鍵, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 勾選年統計checkbox 隱藏月份選擇, ex. 選擇 2024 年 可下載 113年資料" +
            ". 2. 取消 年統計 指定年月 2024 6 請輸入 2024-06-01 可下載 11306與年度資料")
    @GetMapping("/{useFrom}/generator-name-kwh-report")
    public ResponseEntity<?> getTopGenNameKwhReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入年 路徑參數 請輸入 00月00日 ex. 2023-00-00"
                    , example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        GraphReportMonthNameKwhVo report = service.getTopGenNameKwhReport(start, useFrom.contains("-00-"));
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String yr = twYMm.substring(0, twYMm.length()-2)+"年度";
        String oName = report.getGraphTop().getMonthNameKwh().isEmpty()? yr+"排名發電端轉直供度數": twYMm+"與年度排名發電端轉直供度數";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report.getYearNameKwhVo(), oName+tailName
                , tailName, "GRAPHCompanyKwh"+"_"+yr, report.getGraphTop().getYearNameKwh(), report.getGraphTop().getMonthNameKwh()));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#3 用電端轉直供度數 Top 10 排名 - [產生圖表] 鍵, 根據 年下拉選單、月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 勾選年統計checkbox 隱藏月份選擇, [產生圖表] 回傳年度排名表" +
            ". 2. 取消 年統計 指定年月 2024 6 請輸入 2024-06-01, [產生圖表] 回傳該月的排名表")
    @GetMapping("/{useFrom}/load-name-kwh")
    public DataResponseMessage<List<NameKwhVo>> getTopLoadNameKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入年 路徑參數 請輸入 00月00日 ex. 2023-00-00"
                    , example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        boolean yrMark = useFrom.contains("-00-");
        GraphTopYearMonKwhVo tops = service.getTopLoadNameKwh(start);
        if (null == tops) return new DataResponseMessage<>(new ArrayList<>());
        if (yrMark) return new DataResponseMessage<>(tops.getYearNameKwh());
        return new DataResponseMessage<>(tops.getMonthNameKwh());
    }

    @Operation(summary = "#3 用電端轉直供度數 Top 10 排名 - [匯出檔案]鍵, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 勾選年統計checkbox 隱藏月份選擇, ex. 選擇 2024 年 可下載 113年資料" +
            ". 2. 取消 年統計 指定年月 2024 6 請輸入 2024-06-01 可下載 11306與年度資料")
    @GetMapping("/{useFrom}/load-name-kwh-report")
    public ResponseEntity<?> getTopLoadNameKwhReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入年 路徑參數 請輸入 00月00日 ex. 2023-00-00"
                    , example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        GraphReportMonthNameKwhVo report = service.getTopLoadNameKwhReport(start, useFrom.contains("-00-"));
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String yr = twYMm.substring(0, twYMm.length()-2)+"年度";
        String oName = report.getGraphTop().getMonthNameKwh().isEmpty()? yr+"排名用電端轉直供度數": twYMm+"與年度排名用電端轉直供度數";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report.getYearNameKwhVo(), oName+tailName
                , tailName, "GRAPHCompanyKwh"+"_"+yr, report.getGraphTop().getYearNameKwh(), report.getGraphTop().getMonthNameKwh()));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#4 發電端餘電度數 Top 10 排名 - [產生圖表] 鍵, 根據 年下拉選單、月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 勾選年統計checkbox 隱藏月份選擇, [產生圖表] 回傳年度排名表" +
            ". 2. 取消 年統計 指定年月 2024 6 請輸入 2024-06-01, [產生圖表] 回傳該月的排名表")
    @GetMapping("/{useFrom}/generator-unmatched-kwh")
    public DataResponseMessage<List<NameKwhVo>> getTopGenUnmatchedKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入年 路徑參數 請輸入 00月00日 ex. 2023-00-00"
                    , example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        boolean yrMark = useFrom.contains("-00-");
        GraphTopYearMonKwhVo tops = service.getTopGenNameUnmatchedKwh(start);
        if (null == tops) return new DataResponseMessage<>(new ArrayList<>());
        if (yrMark) return new DataResponseMessage<>(tops.getYearNameKwh());
        return new DataResponseMessage<>(tops.getMonthNameKwh());
    }

    @Operation(summary = "#4 發電端餘電度數 Top 10 排名 - [匯出檔案]鍵, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 勾選年統計checkbox 隱藏月份選擇, ex. 選擇 2024 年 可下載 113年資料" +
            ". 2. 取消 年統計 指定年月 2024 6 請輸入 2024-06-01 可下載 11306與年度資料")
    @GetMapping("/{useFrom}/generator-unmatched-kwh-report")
    public ResponseEntity<?> getTopGenUnmatchedKwhReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入年 路徑參數 請輸入 00月00日 ex. 2023-00-00"
                    , example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        GraphReportMonthNameKwhVo report = service.getTopGenNameUnmatchedKwhReport(start, useFrom.contains("-00-"));
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String yr = twYMm.substring(0, twYMm.length()-2)+"年度";
        String oName = report.getGraphTop().getMonthNameKwh().isEmpty()? yr+"排名發電端餘電度數": twYMm+"與年度排名發電端餘電度數";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report.getYearNameKwhVo(), oName+tailName
                , tailName, "GRAPHCompanyKwh"+"_"+yr, report.getGraphTop().getYearNameKwh(), report.getGraphTop().getMonthNameKwh()));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#5 轉直供申請業者類別統計 - 甜甜圈圖 [產生圖表] 鍵, 可選擇起訖年月, 預設採用虛擬集團統計 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 選擇虛擬集團, 不加 query string 或指定 query string: total=0" +
            ". 2. 選擇 統編註記, query string: total=1" +
            ". 3. 台電月報日期 2023-12 ~ 2025-01, 因此 含2025-02與之後月份 才能顯示 虛擬集團 與 統編 類別統計之差異")
    @GetMapping("/{useTo}/applicant-type-count")
    public DataResponseMessage<List<NameKwhVo>> getApplicantTypeCount(
            @RequestParam(defaultValue="") @Parameter(name = "total", description="統編註記") String total,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "查找日期，String" +
                    " 請填固定1日的 結束 date string", example = "2024-05-01") String useTo) throws Exception {
        boolean mark = null == total || total.isEmpty() || total.equals("0");
        String uTo = useTo.contains("-00-")? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        List<NameKwhVo> tops = service.getApplicantTypesCount(end, mark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#5 轉直供申請業者類別數量統計 - [匯出檔案]鍵, 根據 選擇年月, 預設採用虛擬集團統計 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 選擇虛擬集團, 不加 query string 或指定 query string: total=0" +
            ". 2. 選擇 統編註記, query string: total=1" +
            ". 3. 台電月報日期 2023-12 ~ 2025-01, 因此 含2025-02與之後月份 才能顯示 虛擬集團 與 統編 類別統計之差異")
    @GetMapping("/{useTo}/applicant-type-count-report")
    public ResponseEntity<?> getApplicantTypeCountReport(
            @RequestParam(defaultValue="") @Parameter(name = "total", description="統編註記") String total,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "查找日期，String" +
                    " 請填固定1日的 結束 date string", example = "2024-05-01") String useTo) throws Exception {
        boolean mark = null == total || total.isEmpty() || total.equals("0");
        String uTo = useTo.contains("-00-")? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        GraphTopYearMonKwhVo report = service.getApplicantTypesCountReportPie(end, mark);
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMm = DateUtils.passYearMonthToString(end, "twYMm");
        String oName = mark? twYMm+"轉直供申請業者虛擬集團類別統計":twYMm+"轉直供申請業者統編類別統計";
        oName += "_" + DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report.getMonthNameKwh(), oName+tailName
                , tailName, "GRAPHPie", report.getYearNameKwh(), null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#6 轉直供發電端數量統計 - 折線圖 [產生圖表] 鍵, 可選擇起訖年下拉選單、起訖月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 選擇虛擬集團, 不加 query string 或指定 query string: total=0" +
            ". 2. 選擇 電號註記, query string: total=1" +
            ". 3. 台電月報日期 2023-12 ~ 2025-01, 因此 含2025-02與之後月份 才能顯示 虛擬集團 與 統編 類別統計之差異")
    @GetMapping("/{useFrom}/{useTo}/generator-end-value")
    public DataResponseMessage<List<DateValueKwhVo>> getGeneratorEndValue(
            @RequestParam(defaultValue="") @Parameter(name = "total", description="電號註記") String total,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2023-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2023-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = useTo.contains("-00-")? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        int diff = DateUtils.computeDiffMonth(start, end);
        if (diff > 24) return new DataResponseMessage<>(new ArrayList<>());
        boolean mark = null == total || total.isEmpty() || total.equals("0");
        List<DateValueKwhVo> tops = service.getGeneratorEnd(start, end, mark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#6 轉直供發電端數量統計 - [匯出檔案]鍵, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 選擇虛擬集團, 不加 query string 或指定 query string: total=0" +
            ". 2. 選擇 電號註記, query string: total=1" +
            ". 3. 台電月報日期 2023-12 ~ 2025-01, 因此 含2025-02與之後月份 才能顯示 虛擬集團 與 統編 類別統計之差異")
    @GetMapping("/{useFrom}/{useTo}/generator-end-value-report")
    public ResponseEntity<?> getGeneratorEndValueReport(
            @RequestParam(defaultValue="") @Parameter(name = "total", description="電號註記") String total,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2023-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = useFrom.contains("-00-")? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        int diff = DateUtils.computeDiffMonth(start, end);
        if (diff > 24) throw new NotFoundException(NO_TABLE_CONTENT);
        boolean mark = null == total || total.isEmpty() || total.equals("0");
        List<DateValueKwhVo> report = service.getGeneratorEndReport(start, end, mark);
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMmS = DateUtils.passYearMonthToString(start, "twYMm");
        String twYMmE = DateUtils.passYearMonthToString(end, "twYMm");
        String oName = mark? twYMmS+"_"+twYMmE+"轉直供發電端虛擬集團數量統計": twYMmS+"_"+twYMmE+"轉直供發電端電號數量統計";
        oName += "_" + DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHLineChart", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#7 轉直供用電端數量統計 - 折線圖 [產生圖表] 鍵, 可選擇起訖年下拉選單、起訖月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 選擇虛擬集團, 不加 query string 或指定 query string: total=0" +
            ". 2. 選擇 電號註記, query string: total=1" +
            ". 3. 台電月報日期 2023-12 ~ 2025-01, 因此 含2025-02與之後月份 才能顯示 虛擬集團 與 統編 類別統計之差異")
    @GetMapping("/{useFrom}/{useTo}/load-end-value")
    public DataResponseMessage<List<DateValueKwhVo>> getLoadEndValue(
            @RequestParam(defaultValue="") @Parameter(name = "total", description="電號註記") String total,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2023-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2023-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = useTo.contains("-00-")? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (DateUtils.computeDiffMonth(start, end) > 24) return new DataResponseMessage<>(new ArrayList<>());
        boolean mark = null == total || total.isEmpty() || total.equals("0");
        List<DateValueKwhVo> tops = service.getLoadEnd(start, end, mark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#7 轉直供用電端數量統計 - [匯出檔案]鍵, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 選擇虛擬集團, 不加 query string 或指定 query string: total=0" +
            ". 2. 選擇 電號註記, query string: total=1" +
            ". 3. 台電月報日期 2023-12 ~ 2025-01, 因此 含2025-02與之後月份 才能顯示 虛擬集團 與 統編 類別統計之差異")
    @GetMapping("/{useFrom}/{useTo}/load-end-value-report")
    public ResponseEntity<?> getLoadEndValueReport(
            @RequestParam(defaultValue="") @Parameter(name = "total", description="電號註記") String total,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2023-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = useFrom.contains("-00-")? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (DateUtils.computeDiffMonth(start, end) > 24) throw new NotFoundException(NO_TABLE_CONTENT);
        boolean mark = null == total || total.isEmpty() || total.equals("0");
        List<DateValueKwhVo> report = service.getLoadEndReport(start, end, mark);
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMmS = DateUtils.passYearMonthToString(start, "twYMm");
        String twYMmE = DateUtils.passYearMonthToString(end, "twYMm");
        String oName = mark? twYMmS+"_"+twYMmE+"轉直供用電端虛擬集團數量統計": twYMmS+"_"+twYMmE + "轉直供用電端電號數量統計";
        oName += "_" + DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHLineChart", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#8 轉直供契約數量統計 - 堆疊柱狀圖 [產生圖表] 鍵, 可選擇起訖年下拉選單、起訖月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 年統計, 輸入 起始年 與 結束年" +
            ". 2. 台電月報日期 2024-06 ~ 2025-01, 因此 含2025-02與之後月份 才能顯示 統計之差異")
    @GetMapping("/{useFrom}/{useTo}/application-piece")
    public DataResponseMessage<List<AddExistChangeResponse>> getApplicationPiece(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) return new DataResponseMessage<>(new ArrayList<>());
        List<ExistAddChangeVo> tops = service.getApplicationByMonthRange(start, end, mark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());

        List<AddExistChangeResponse> out = new ArrayList<>();
        for (ExistAddChangeVo vo : tops)
            out.add(new AddExistChangeResponse(vo.getDate(), vo.getAlNum(), vo.getEfNum(), vo.getCwNum()));

        return new DataResponseMessage<>(out);
    }

    @Operation(summary = "#8 轉直供契約數量統計 - [匯出檔案]鍵, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 年統計, 輸入 起始年 與 結束年" +
            ". 2. 台電月報日期 2024-06 ~ 2025-01, 因此 含2025-02與之後月份 才能顯示 統計之差異")
    @GetMapping("/{useFrom}/{useTo}/application-piece-report")
    public ResponseEntity<?> getApplicationPieceReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) throw new NotFoundException(NO_TABLE_CONTENT);
        List<ExistAddChangeVo> report = service.getApplicationByMonthRangeReport(start, end, mark);
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMmS = DateUtils.passYearMonthToString(start, "twYMm");
        String twYMmE = DateUtils.passYearMonthToString(end, "twYMm");
        String yS = twYMmS.substring(0,3);
        String yE = twYMmE.substring(0,3);
        String oName = mark && yS.equals(yE)? yS+"年度轉直供契約數量統計" : mark? yS+"_"+yE+"年度轉直供契約數量統計"
                : twYMmS+"_"+twYMmE + "轉直供契約數量統計";
        oName += "_" + DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHStackBarChart", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#9 轉供度數統計 - 折線圖 [產生圖表] 鍵, 可選擇起訖年下拉選單、起訖月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00" +
            ". 2. 台電月報日期 2020-05 ~ 2025-01")
    @GetMapping("/{useFrom}/{useTo}/trans-power-kwh")
    public DataResponseMessage<List<NameKwhVo>> getTransPowerKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) return new DataResponseMessage<>(new ArrayList<>());
        List<NameKwhVo> tops = service.getSettleTransPowerMatchKw(start, end, mark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#9 轉供度數統計 - [匯出檔案]鍵 折線圖, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00" +
            ". 2. 台電月報日期 2020-05 ~ 2025-01")
    @GetMapping("/{useFrom}/{useTo}/trans-power-kwh-report")
    public ResponseEntity<?> getTransPowerKwhReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) throw new NotFoundException(NO_TABLE_CONTENT);
        List<NameKwhVo> report = service.getSettleTransPowerMatchKwReport(start, end, mark);
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMmS = DateUtils.passYearMonthToString(start, "twYMm");
        String twYMmE = DateUtils.passYearMonthToString(end, "twYMm");
        String yS = twYMmS.substring(0,3);
        String yE = twYMmE.substring(0,3);
        String oName = mark && yS.equals(yE)? yS+"年度轉供度數統計" : mark? yS+"_"+yE+"年度轉供度數統計"
                : twYMmS+"_"+twYMmE + "轉供度數統計";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHLineChartONE", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#10 直供度數統計 - 折線圖 [產生圖表] 鍵, 可選擇起訖年下拉選單、起訖月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00" +
            ". 2. 台電月報日期 2023-03 ~ 2025-01")
    @GetMapping("/{useFrom}/{useTo}/direct-power-kwh")
    public DataResponseMessage<List<NameKwhVo>> getDirectPowerKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) return new DataResponseMessage<>(new ArrayList<>());
        List<NameKwhVo> tops = service.getSettleDirectPowerMatchKw(start, end, mark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#10 直供度數統計 - [匯出檔案]鍵 折線圖, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00" +
            ". 2. 台電月報日期 2023-03 ~ 2025-01")
    @GetMapping("/{useFrom}/{useTo}/direct-power-kwh-report")
    public ResponseEntity<?> getDirectPowerKwhReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) throw new NotFoundException(NO_TABLE_CONTENT);
        List<NameKwhVo> report = service.getSettleDirectPowerMatchKwReport(start, end, mark);
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMmS = DateUtils.passYearMonthToString(start, "twYMm");
        String twYMmE = DateUtils.passYearMonthToString(end, "twYMm");
        String yS = twYMmS.substring(0,3);
        String yE = twYMmE.substring(0,3);
        String oName = mark && yS.equals(yE)? yS+"年度直供度數統計" : mark? yS+"_"+yE+"年度直供度數統計"
                : twYMmS+"_"+twYMmE + "直供度數統計";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHLineChartONE", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#11 轉直供度數以費用類別統計 - 柱狀圖 [產生圖表] 鍵, 可選擇起訖年下拉選單、起訖月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年5~12月 ~ 2025年1月 期間 2021年~2024年 為完整一年份的資料" +
            ". 2. 台電月報日期 2024/01月 ~ 2025/01月")
    @GetMapping("/{useFrom}/{useTo}/adst-type-kwh")
    public DataResponseMessage<List<DateCostTypeKwhVo>> getADSTTypeKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) return new DataResponseMessage<>(new ArrayList<>());
        List<DateCostTypeKwhVo> tops = service.getCostTypeKwh(start, end, mark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        List<DateCostTypeKwhVo> out = new ArrayList<>();
        for (int i =0; i< tops.size()-1;i++)
            out.add(tops.get(i));
        return new DataResponseMessage<>(out);
    }

    @Operation(summary = "#11 轉直供度數以費用類別統計 - [匯出檔案]鍵 柱狀圖, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年5~12月 ~ 2025年1月 期間 2021年~2024年 為完整一年份的資料" +
            ". 2. 台電月報日期 2024/01月 ~ 2025/01月" +
            ". 3. 欄位說明: aSKwh - 輔助/調度度數, tKwh - 輸電度數, dKwh - 配電度數")
    @GetMapping("/{useFrom}/{useTo}/adst-type-kwh-report")
    public ResponseEntity<?> getADSTTypeKwhReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) throw new NotFoundException(NO_TABLE_CONTENT);
        List<DateCostTypeKwhVo> report = service.getCostTypeKwhReport(start, end, mark);
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMmS = DateUtils.passYearMonthToString(start, "twYMm");
        String twYMmE = DateUtils.passYearMonthToString(end, "twYMm");
        String yS = twYMmS.substring(0,3);
        String yE = twYMmE.substring(0,3);
        String oName = mark && yS.equals(yE)? yS+"年度轉直供度數以費用類別統計": mark? yS+"_"+yE+"年度轉直供度數以費用類別統計"
                : twYMmS+"_"+twYMmE + "轉直供度數以費用類別統計";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHStackBarChartDouble", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#12 轉直供費用統計 - 堆疊柱狀圖 [產生圖表] 鍵, 可選擇起訖年下拉選單、起訖月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年5~12月 ~ 2025年1月 期間 2021年~2024年 為完整一年份的資料" +
            ". 2. 台電月報日期 2024/01月 ~ 2025/01月" +
            ". 3. 欄位說明: aCost - 輔助服務, sCost - 電力調度, tCost - 轉供輸電, dCost - 轉供配電")
    @GetMapping("/{useFrom}/{useTo}/adst-costs")
    public DataResponseMessage<List<ADSTCostResponse>> getADSTCosts(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) return new DataResponseMessage<>(new ArrayList<>());
        List<DateADSTExpTotalVo> tops = service.getADSTExpCost(start, end, mark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());

        List<ADSTCostResponse> out = new ArrayList<>();
        for (int i =0 ; i < tops.size()-1; i++) {
            DateADSTExpTotalVo vo = tops.get(i);
            out.add(new ADSTCostResponse(vo.getDate(), vo.getACost(), vo.getSCost(), vo.getTCost(), vo.getDCost()));
        }
        return new DataResponseMessage<>(out);
    }

    @Operation(summary = "#12 轉直供費用統計 - [匯出檔案]鍵 堆疊柱狀圖, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年5~12月 ~ 2025年1月 期間 2021年~2024年 為完整一年份的資料" +
            ". 2. 台電月報日期 2024/01月 ~ 2025/01月")
    @GetMapping("/{useFrom}/{useTo}/adst-costs-report")
    public ResponseEntity<?> getADSTCostsReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) throw new NotFoundException(NO_TABLE_CONTENT);
        List<DateADSTExpTotalVo> report = service.getADSTExpCostReport(start, end, mark);
        if (null == report) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMmS = DateUtils.passYearMonthToString(start, "twYMm");
        String twYMmE = DateUtils.passYearMonthToString(end, "twYMm");
        String yS = twYMmS.substring(0,3);
        String yE = twYMmE.substring(0,3);
        String oName = mark && yS.equals(yE)? yS+"年度轉直供費用統計": mark? yS+"_"+yE+"年度轉直供費用統計"
                : twYMmS+"_"+twYMmE + "轉直供費用統計";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHStackBarChartDoubleStack4", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#13 轉直供度數以能源別統計 - 堆疊柱狀圖 [產生圖表] 鍵, 可選擇起訖年下拉選單、起訖月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年5~12月 ~ 2025年1月 期間 2021年~2024年 為完整一年份的資料" +
            ". 2. 非台電發電端 ?other=1, 不加 query string 為取得台電發電端資料" +
            ". 3. 台電月報日期 2024/01月 ~ 2025/01月" +
            ". 4. 欄位說明: 台電發電端 groundKwh - 地面型, roofKwh - 屋頂型, waterKw - 水上型" +
            "; 非台電發電端 windGroundKwh - 風力, sunRoofKwh - 太陽, waterKwh - 水力")
    @GetMapping("/{useFrom}/{useTo}/fuel-type-kwh")
    public DataResponseMessage<List<?>> getFuelTypeKwh(
            @RequestParam(defaultValue="") @Parameter(name = "other", description="非台電發電端") String other,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean tpcMark = null == other || other.isEmpty() || other.equals("0");
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) return new DataResponseMessage<>(new ArrayList<>());
        List<DateFuelTypeVo> tops = service.getFuelTypesKwh(start, end, tpcMark, mark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        if (!tpcMark) return new DataResponseMessage<>(tops);
        else {
            List<DateFuelResponse> tpc = new ArrayList<>();
            for (DateFuelTypeVo m: tops) {
                tpc.add(new DateFuelResponse(m.getDate(), m.getWindGroundKwh(), m.getSunRoofKwh(), m.getWaterKwh()));
            }
            return new DataResponseMessage<>(tpc);
        }
    }

    @Operation(summary = "#13 轉直供度數以能源別統計 - [匯出檔案]鍵 堆疊柱狀圖, 根據 年下拉選單、月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年5~12月 ~ 2025年1月 期間 2021年~2024年 為完整一年份的資料" +
            ". 2. 非台電發電端 ?other=1, 不加 query string 為取得台電發電端資料" +
            ". 3. 台電月報日期 2024/01月 ~ 2025/01月" +
            ". 4. 欄位說明: 台電發電端 groundKwh - 地面型, roofKwh - 屋頂型, waterKw - 水上型" +
            "; 非台電發電端 windGroundKwh - 風力, sunRoofKwh - 太陽, waterKwh - 水力")
    @GetMapping("/{useFrom}/{useTo}/fuel-type-kwh-report")
    public ResponseEntity<?> getFuelTypeKwhReport(
            @RequestParam(defaultValue="") @Parameter(name = "other", description="非台電發電端") String other,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        boolean tpcMark = null == other || other.isEmpty() || other.equals("0");
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) throw new NotFoundException(NO_TABLE_CONTENT);
        DateFuelTypesReportVo vo = service.getFuelTypesKwhReport(start, end, tpcMark, mark);
        if (null == vo ||  null == vo.getGroundWindRoofSunWater() || vo.getGroundWindRoofSunWater().isEmpty())
            throw new NotFoundException(NO_TABLE_CONTENT);
        List<DateFuelWSWpcKwhVo> report = vo.getReportFuelKwh();
        List<DateFuelTypeVo> fuels = vo.getGroundWindRoofSunWater();
        String twYMmS = DateUtils.passYearMonthToString(start, "twYMm");
        String twYMmE = DateUtils.passYearMonthToString(end, "twYMm");
        String yS = twYMmS.substring(0,3);
        String yE = twYMmE.substring(0,3);
        String oName = tpcMark && mark && yS.equals(yE)? yS+"年度台電轉直供度數以能源別統計": mark && yS.equals(yE)?
                yS+"年度轉直供度數以能源別統計": tpcMark && mark? yS+"_"+yE+"年度台電轉直供度數以能源別統計": mark
                ? yS+"_"+yE+"年度轉直供度數以能源別統計": tpcMark? twYMmS+"_"+twYMmE + "台電轉直供度數以能源別統計"
                :twYMmS+"_"+twYMmE + "轉直供度數以能源別統計";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHStackBarChartDoubleStack3", fuels, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#14 發電端裝置容量以能源別統計 - 堆疊柱狀圖 [產生圖表] 鍵, 可選擇起訖年下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年 ~ 2025年1月 只有年度的資料" +
            ". 2. 發電端裝置容量 - 執照容量 ?device=1, 發電端容量 - 試運轉容量 ?device=2, 不加 query string 為取得全部容量" +
            ". 3. 欄位說明: 能源別容量 windKw - 風力, windOffshoreKw - 風力(離岸), sunKw - 太陽, waterKw - 水力")
    @GetMapping("/{useFrom}/{useTo}/fuel-capacity")
    public DataResponseMessage<List<DeviceFuelsResponse>> getFuelCapacity(
            @RequestParam(defaultValue="") @Parameter(name = "device", description="裝置容量") String device,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-00-00") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2025-00-00"
                    , example = "2026-00-00") String useTo) throws Exception {
        Boolean capMark = null == device || device.isEmpty() || device.equals("0")? null: device.equals("1");
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) return new DataResponseMessage<>(new ArrayList<>());
        List<ExistAddChangeVo> tops = service.getFuelCapacities(start, end, capMark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        List<DeviceFuelsResponse> res = new ArrayList<>();
        for (ExistAddChangeVo m:tops) {
            res.add(new DeviceFuelsResponse(m.getDate(), m.getAlNum(), m.getEfNum(), m.getCwNum(), m.getTotal()));
        }
        return new DataResponseMessage<>(res);
    }

    @Operation(summary = "#14 發電端裝置容量以能源別統計 - [匯出檔案]鍵 堆疊柱狀圖, 根據 年下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年 ~ 2025年1月 只有年度的資料" +
            ". 2. 發電端裝置容量 - 執照容量 ?device=1, 發電端容量 - 試運轉容量 ?device=2, 不加 query string 為取得全部容量" +
            ". 3. 欄位說明: 能源別容量 alNum - 風力, efNum - 風力(離岸), cwNum - 太陽, total - 水力")
    @GetMapping("/{useFrom}/{useTo}/fuel-capacity-report")
    public ResponseEntity<?> getFuelCapacityReport(
            @RequestParam(defaultValue="") @Parameter(name = "device", description="裝置容量") String device,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-05-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-09-01") String useTo) throws Exception {
        Boolean capMark = null == device || device.isEmpty() || device.equals("0")? null: device.equals("1");
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        String uTo = mark? DateUtils.backYearDate(useTo): useTo;
        Date end = DateUtils.passStringToDate(uTo);
        if (checkMonthYearRange(mark, start, end)) throw new NotFoundException(NO_TABLE_CONTENT);
        DateFuelTypesReportVo vo = service.getFuelCapacitiesReport(start, end, capMark);
        if (null == vo ||  null == vo.getWOffSWCapKw() || vo.getWOffSWCapKw().isEmpty())
            throw new NotFoundException(NO_TABLE_CONTENT);
        List<DateADSTExpTotalVo> report = vo.getReportFuelCapKw();
        List<ExistAddChangeVo> graph = vo.getWOffSWCapKw();
        String yS = DateUtils.passYearMonthToString(start, "twYMm").substring(0,3);
        String yE = DateUtils.passYearMonthToString(end, "twYMm").substring(0,3);
        String oName = null == capMark && yS.equals(yE)? yS+"年度發電端全部容量以能源別統計": null == capMark?
                yS+"_"+yE+"年度發電端全部容量以能源別統計": capMark && yS.equals(yE)? yS+"年度發電端執照容量以能源別統計"
                : capMark?yS+"_"+yE+"年度發電端執照容量以能源別統計": yS.equals(yE)? yS+"年度發電端試運轉容量以能源別統計"
                : yS+"_"+yE+"年度發電端試運轉容量以能源別統計"; // !capMark?
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHStackBarChartDoubleStack4", graph, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#15 轉直供發電端數量以能源別統計 家數 - 柱狀圖 [產生圖表] 鍵, 可選擇起訖年下拉選單、起訖月下拉選單 - 壓下 [產生圖表] 鍵"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年5~12月 ~ 2025年1月 期間 2021年~2024年 為完整一年份的資料" +
            ". 2. 台電月報日期 2020 ~ 2025/01月" +
            ". 3. 欄位說明: windGroundKwh - 風力, sunRoofKwh - 太陽, waterKwh - 水力")
    @GetMapping("/{useFrom}/{useTo}/fuel-com-count")
    public DataResponseMessage<List<DateFuelTypeVo>> getFuelComCount(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-00-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 00月00日 ex: 2025-00-00"
                    , example = "2024-00-01") String useTo) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        Date end = DateUtils.passStringToDate(DateUtils.backYearDate(useTo));
        if (DateUtils.computeDiffYear(start, end) > 23) return new DataResponseMessage<>(new ArrayList<>());
        List<DateFuelTypeVo> tops = service.getFuelCompanyNumber(start, end);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#15 轉直供發電端數量以能源別統計 家數 - 柱狀圖 [匯出檔案]鍵 , 根據 年下拉選單 - [匯出檔案]按鍵 - 回傳excel檔"
            , description = "注意: 1. 年統計, 輸入 起始年-00-00 與 結束年-00-00 從 2020年 ~ 2025年1月 只有年度的資料" +
            ". 2. 台電月報日期 2020 ~ 2025/01月" +
            ". 3. 欄位說明: windGroundKwh - 風力, sunRoofKwh - 太陽, waterKwh - 水力")
    @GetMapping("/{useFrom}/{useTo}/fuel-com-count-report")
    public ResponseEntity<?> getFuelComCountReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-00-01") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 請填固定1日的 結束 date string, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2025-00-01") String useTo) throws Exception {
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        Date end = DateUtils.passStringToDate(DateUtils.backYearDate(useTo));
        if (checkMonthYearRange(mark, start, end)) throw new NotFoundException(NO_TABLE_CONTENT);
        DateFuelTypesReportVo vo = service.getFuelCompanyNumberReport(start, end);
        if (null == vo ||  null == vo.getGroundWindRoofSunWater() || vo.getGroundWindRoofSunWater().isEmpty())
            throw new NotFoundException(NO_TABLE_CONTENT);
        List<ExistAddChangeVo> report = vo.getWOffSWCapKw();
        List<DateFuelTypeVo> graph = vo.getGroundWindRoofSunWater();
        String yS = DateUtils.passYearMonthToString(start, "twYMm").substring(0,3);
        String yE = DateUtils.passYearMonthToString(end, "twYMm").substring(0,3);
        String oName = yS.equals(yE)? yS+"年度發電端數量以能源別統計": yS+"_"+yE+"年度發電端數量以能源別統計";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHStackBarChartDouble", graph, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#18 視覺化分析 計劃書審查流程進度統計 件數 - 柱狀圖 [產生圖表] 鍵 壓下 [產生圖表] 鍵")
    @GetMapping("/application-apply-count")
    public DataResponseMessage<List<AppApplyNameCountVo>> getAppApplyCount() throws Exception {
        List<AppApplyNameCountVo> tops = service.countApplicationStatus();
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#18 視覺化分析 計劃書審查流程進度統計 件數 - 柱狀圖 [匯出檔案]鍵 - 回傳excel檔")
    @GetMapping("/application-apply-count-report")
    public ResponseEntity<?> getAppApplyCountReport() throws Exception {
        List<AppApplyNameCountVo> report = service.countApplicationStatusReport();
        if (null == report ||  report.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        String oName = "計劃書審查流程進度統計_" + DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHBarChart", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#19 視覺化分析 契約流程進度統計 件數 - 柱狀圖 [產生圖表] 鍵 壓下 [產生圖表] 鍵")
    @GetMapping("/contract-flow-count")
    public DataResponseMessage<List<AppApplyNameCountVo>> getContractFlowCount() throws Exception {
        List<AppApplyNameCountVo> tops = service.countContractStatus();
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#19 視覺化分析 契約流程進度統計 件數 - 柱狀圖 [匯出檔案]鍵 - 回傳excel檔")
    @GetMapping("/contract-flow-count-report")
    public ResponseEntity<?> getContractFlowCountReport() throws Exception {
        List<AppApplyNameCountVo> report = service.countContractStatusReport();
        if (null == report ||  report.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        String oName = "契約流程進度統計_" + DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHBarChart", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#21 全省區處轄區內轉直供發電量 Top 百萬度 排名 - 柱狀圖 [產生圖表] 鍵, 根據 年月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名")
    @GetMapping("/{useFrom}/tpc-generator-kwh")
    public DataResponseMessage<List<NameKwhVo>> getTopTpcNameGenKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<NameKwhVo> tops = service.topTpcUnitNameGenKwh(start);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#21 全省區處轄區內轉直供發電量 Top 百萬度 排名 - 柱狀圖 [匯出檔案]鍵, 根據 年月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔")
    @GetMapping("/{useFrom}/tpc-generator-kwh-report")
    public ResponseEntity<?> getTopTpcNameGenKwhReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<NameKwhVo> report = service.topTpcUnitNameGenKwhReport(start);
        if (null == report || report.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String oName = twYMm+"全省區處轄區內轉直供發電量";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHBarChart", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#22 全省區處轄區內轉直供用電量 Top 百萬度 排名 - 柱狀圖 [產生圖表] 鍵, 根據 年月下拉選單 - 壓下 [產生圖表] 鍵 - 回傳 輸出排名")
    @GetMapping("/{useFrom}/tpc-load-kwh")
    public DataResponseMessage<List<NameKwhVo>> getTopTpcNameLoadKwh(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<NameKwhVo> tops = service.topTpcUnitNameLoadKwh(start);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#22 全省區處轄區內轉直供用電量 Top 百萬度 排名 - 柱狀圖 [匯出檔案]鍵, 根據 年月下拉選單 - [匯出檔案]按鍵 - 回傳excel檔")
    @GetMapping("/{useFrom}/tpc-load-kwh-report")
    public ResponseEntity<?> getTopTpcNameLoadKwhReport(
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 請填固定1日的 起始 date string", example = "2024-06-01") String useFrom) throws Exception {
        Date start = DateUtils.passStringToDate(useFrom);
        List<NameKwhVo> report = service.topTpcUnitNameLoadKwhReport(start);
        if (null == report || report.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        String twYMm = DateUtils.passYearMonthToString(start, "twYMm");
        String oName = twYMm+"全省區處轄區內轉直供用電量";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHBarChart", report, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#17 ami電號列表 提供電號給前端選擇", description = "注意: 1. 年統計: ?mode=0" +
            ", 輸入 起始年-00-00 與 結束年-00-00 從 2020年 ~ 2025年1月 只有年度的資料" +
            ". 2. 月統計: ?mode=1, 輸入 起始年-起始月-01 與 結束年-結束月-01 從 2024年1月 ~ 2025年1月 每月的資料" +
            ". 3. 日統計: 輸入 起始年-起始月-起始日 與 結束年-結束月-結束日 從 2024年1月2日 ~ 2025年2月2日 每日的資料")
    @PostMapping("/{useFrom}/{useTo}/ami-electric-number")
    public PageStringVoResponse getAmiElecNoList(
            @RequestParam(defaultValue="") @Parameter(name = "mode", description="年月日識別") String mode,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 指定起始年月日 date string ex. 2024-05-04" +
                    ", 若填固定1日的 起始月 ex. 2024-05-01, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-07-03") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 指定結束年月日 date string ex. 2024-06-04" +
                    ", 若填固定1日的 結束月 ex. 2024-09-01, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-08-03") String useTo,
            @RequestBody @Valid ElecNoListRequest vo) throws Exception {
        if (null != vo.getPartElecNO() && !vo.getPartElecNO().isEmpty() && !vo.getPartElecNO().matches("\\d*"))
            throw new BadRequestException("電號格式錯誤");

        String partElec = null == vo.getPartElecNO()? "": vo.getPartElecNO();
        Boolean bMark = mode.isEmpty()? null: mode.equals("1");
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        Date end = DateUtils.passStringToDate(useTo);

        if ((null == bMark && checkDateRange(useFrom, useTo))
            || (null != bMark && checkMonthYearRange(mark, start, end)))
            return new PageStringVoResponse(PageStringVo.builder().contents(new ArrayList<>()).build());

        PageStringVo info = service.getAmiElecNos(start, end, partElec, vo.getPage(), vo.getPageSize(), bMark);
        if (null == info || info.getContents().isEmpty())
            return new PageStringVoResponse(PageStringVo.builder().contents(new ArrayList<>()).build());;
        return new PageStringVoResponse(info);
    }

    @Operation(summary = "#17 發電量與用電量分析 度 - 折線圖 [產生圖表] 鍵, 根據 年統計 月統計(年月區間) 日統計（年月日區間) 預設日加總 - 壓下 [產生圖表] 鍵"
            , description = "注意: 1. 年統計: ?mode=0, 輸入 起始年-00-00 與 結束年-00-00 從 2020年 ~ 2025年1月 只有年度的資料" +
            ". 2. 月統計: ?mode=1, 輸入 起始年-起始月-01 與 結束年-結束月-01 從 2024年1月 ~ 2025年1月 每月的資料" +
            ". 3. 日統計: 輸入 起始年-起始月-起始日 與 結束年-結束月-結束日 從 2024年1月2日 ~ 2025年2月2日 每日的資料")
    @PostMapping("/{useFrom}/{useTo}/ami-gen-load-kwh")
    public DataResponseMessage<List<DateValueKwhVo>> getAmiGenLoadKwh(
            @RequestParam(defaultValue="") @Parameter(name = "mode", description="年月日識別") String mode,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 指定起始年月日 date string ex. 2024-05-04" +
                    ", 若填固定1日的 起始月 ex. 2024-05-01, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-07-03") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 指定結束年月日 date string ex. 2024-06-04" +
                    ", 若填固定1日的 結束月 ex. 2024-09-01, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-08-03") String useTo, @RequestBody List<String> elecNos) throws Exception {
        if (null == elecNos || elecNos.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        else if (elecNos.size() > 10) throw new BadRequestException("電號數量錯誤");
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        Date end = DateUtils.passStringToDate(useTo);
        Boolean bMark = mode.isEmpty()? null: mode.equals("1");

        if ((null == bMark && checkDateRange(useFrom, useTo))
                || (null != bMark && checkMonthYearRange(mark, start, end))) return new DataResponseMessage<>(new ArrayList<>());

        List<DateValueKwhVo> tops = service.getAmiKwh(start, end, elecNos, bMark);
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#17 發電量與用電量分析 度 - 折線圖 [匯出檔案] 鍵, 根據 年統計 月統計(年月區間) 日統計（年月日區間) 預設日加總 - 壓下 [匯出檔案] 鍵"
            , description = "注意: 1. 年統計: ?mode=0, 輸入 起始年-00-00 與 結束年-00-00 從 2020年 ~ 2025年1月 只有年度的資料" +
            ". 2. 月統計: ?mode=1, 輸入 起始年-起始月-01 與 結束年-結束月-01 從 2024年1月 ~ 2025年1月 每月的資料" +
            ". 3. 日統計: 輸入 起始年-起始月-起始日 與 結束年-結束月-結束日 從 2024年1月2日 ~ 2025年2月2日 每日的資料")
    @PostMapping("/{useFrom}/{useTo}/ami-gen-load-kwh-report")
    public ResponseEntity<?> getAmiGenLoadKwhReport(
            @RequestParam(defaultValue="") @Parameter(name = "mode", description="年月日識別") String mode,
            @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description = "查找日期，String" +
                    " 指定起始年月日 date string ex. 2024-05-04" +
                    ", 若填固定1日的 起始月 ex. 2024-05-01, 若只需要輸入起始年 路徑參數 請輸入 0月0日 ex: 2024-00-00"
                    , example = "2024-07-03") String useFrom,
            @PathVariable(value = "useTo") @Parameter(name = "useTo", description = "帳單結束日期，String" +
                    " 指定結束年月日 date string ex. 2024-06-04" +
                    ", 若填固定1日的 結束月 ex. 2024-09-01, 若只需要輸入結束年 路徑參數 請輸入 0月0日 ex: 2025-00-00"
                    , example = "2024-08-03") String useTo, @RequestBody List<String> elecNos) throws Exception {
        if (null == elecNos || elecNos.isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        else if (elecNos.size() > 10) throw new BadRequestException("電號數量錯誤");
        boolean mark = useFrom.contains("-00-") && useTo.contains(("-00-"));
        Date start = DateUtils.passStringToDate(useFrom);
        Date end = DateUtils.passStringToDate(useTo);
        Boolean bMark = mode.isEmpty()? null: mode.equals("1");

        if ((null == bMark && checkDateRange(useFrom, useTo))
                || (null != bMark && checkMonthYearRange(mark, start, end))) throw new NotFoundException(NO_TABLE_CONTENT);
        DateFuelTypesReportVo reportGraph = service.getAmiKwhReport(start, end, elecNos, bMark);
        if (null == reportGraph || reportGraph.getDateValueKw().isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        List<ElecNoDateGenLoadVo> report = reportGraph.getElecNoDateGenLoadKw();
        List<DateValueKwhVo> graph = reportGraph.getDateValueKw();
        String tmpS = DateUtils.passYearMonthToString(start, "twYMm");
        String tmpD = DateUtils.passYearMonthToString(end, "twYMm");
        String twYMmS = null == bMark? DateUtils.passYearMonthToString(start, "twYMmDd"): mark? tmpS.substring(0, tmpS.length()-2): tmpS;
        String twYMmD = null == bMark? DateUtils.passYearMonthToString(end, "twYMmDd"): mark? tmpD.substring(0, tmpS.length()-2): tmpD;
        String oName = twYMmS+"_"+twYMmD + (null == bMark? "": mark? "年度": "月份") + "發電量與用電量分析";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHLineChart", graph, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    @Operation(summary = "#23 全省區處轉供中契約數量 區處個數 排名 - 柱狀圖 [產生圖表] 鍵 - 回傳 輸出排名")
    @GetMapping("/tpc-application-count")
    public DataResponseMessage<List<NameKwhVo>> topTpcApplicationCount() throws Exception {
        List<NameKwhVo> tops = service.getCountAppFromTpcName();
        if (null == tops || tops.isEmpty()) return new DataResponseMessage<>(new ArrayList<>());
        return new DataResponseMessage<>(tops);
    }

    @Operation(summary = "#23 全省區處轉供中契約數量 區處個數 排名 - 柱狀圖 [匯出檔案]鍵- 回傳excel檔")
    @GetMapping("/tpc-application-count-report")
    public ResponseEntity<?> topTpcApplicationCountReport() throws Exception {
        DateFuelTypesReportVo info = service.getCountAppFromTpcNameReport();
        if (null == info || info.getNameKwh().isEmpty()) throw new NotFoundException(NO_TABLE_CONTENT);
        List<DateValueKwhVo> report = info.getDateValueKw();
        List<NameKwhVo> graph = info.getNameKwh();
        String oName = "全省區處轉供中契約數量";
        String tailName = ".xlsx";
        InputStreamResource file = new InputStreamResource(fileService.loadFile(report, oName+tailName
                , tailName, "GRAPHBarChart", graph, null));
        String urlName = URLEncoder.encode(oName+tailName, StandardCharsets.UTF_8);
        return this.resourceFileResponse(urlName, "application/vnd.ms-excel", file);
    }

    private boolean checkMonthYearRange(boolean mark, Date start, Date end) {
        return ((!mark && DateUtils.computeDiffMonth(start, end) > 24)
                || (mark && DateUtils.computeDiffYear(start, end) > 23));
    }

    private boolean checkDateRange(String useFrom, String useTo) {
        String[] sD = useFrom.split("-");
        String[] eD = useTo.split("-");
        int sD1 = Integer.parseInt(sD[1]);
        int eD1 = Integer.parseInt(eD[1]);
        int sD2 = Integer.parseInt(sD[2]);
        int eD2 = Integer.parseInt(eD[2]);
        return (sD1+1 < eD1 || !sD[0].equals(eD[0]) || !((sD1+1 == eD1 && eD2 <= sD2) || (sD1 == eD1 && sD2 != eD2)));
    }
}