package tw.com.taipower.pwoms.controller.entity;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.pwoms.controller.AbstractController;
import tw.com.taipower.pwoms.controller.vo.request.entity.EntityMeterVmUpdateVO;
import tw.com.taipower.pwoms.controller.vo.request.entity.GeneratorEntityMeterChildUpdateVO;
import tw.com.taipower.pwoms.controller.vo.request.entity.LoadEntityMeterChildUpdateVO;
import tw.com.taipower.pwoms.controller.vo.response.DataResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.PageResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.ResponseMessage;
import tw.com.taipower.pwoms.service.vo.MyUserPrincipal;
import tw.com.taipower.pwoms.services.entitymanage.EntityService;
import tw.com.taipower.pwoms.services.entitymanage.LoadService;
import tw.com.taipower.pwoms.services.filter.LoadEntityFilter;
import tw.com.taipower.pwoms.services.tpc.RemsService;
import tw.com.taipower.pwoms.services.vo.entity.MdesMeterVO;
import tw.com.taipower.pwoms.services.vo.entity.SimpleMeterSpVO;
import tw.com.taipower.pwoms.services.vo.entity.load.REMSNbReqest;
import tw.com.taipower.pwoms.services.vo.generated.*;
import tw.com.taipower.pwoms.utils.CommonUtil;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import static tw.com.taipower.pwoms.constant.ApiUrl.API_ENTITY;
import static tw.com.taipower.pwoms.constant.ApiUrl.API_ENTITY_LOAD;
import static tw.com.taipower.pwoms.services.enumclass.EntityType.LOAD;

/**
 * Settlement Resource
 *
 * @class: SettlementResource
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-04-25 14:27
 * @see:
 **/

@Log4j2
@RestController
@RequestMapping(API_ENTITY + API_ENTITY_LOAD)
@Tag(name = "用戶模組")
public class LoadEntityController extends AbstractController {

    @Autowired
    private LoadService service;

    @Autowired
    private RemsService remsService;

    @Autowired
    private EntityService entityService;

    @Operation(summary = "取得用電端資訊", description = "")
    @PostMapping("/list")
    public PageResponseMessage<LoadEntityVO> getLoads(@RequestBody LoadEntityFilter filter) {
        var data = service.findAllByPage(filter);
        return new PageResponseMessage<>(data);
    }

    @Operation(summary = "取得單一用電端", description = "")
    @GetMapping("/single/{id}")
    public DataResponseMessage<LoadEntityVO> getById(@PathVariable(value = "id") Long id) {
        var data = service.findOneById(id);
        if (data.isPresent()) {
            data.get().setLoadEntityMeterList(service.findMeterByLoadEntityId(id));
            return new DataResponseMessage<>(data.get());
        }
        throw new RuntimeException("data not found");
    }

    @Operation(summary = "更新單一用電端", description = "")
    @PostMapping("/single/{id}")
    public ResponseEntity<ResponseMessage> updateById(@PathVariable(value = "id") Long id,
                                                      @RequestBody LoadEntityVO loadEntity,
                                                      @AuthenticationPrincipal MyUserPrincipal principal) {

        Long userId = principal.getUserId();
        loadEntity.setId(id);
        service.save(loadEntity, userId);
        // TODO data == null 噴出異常 + @RequestBody @Valid LoadEntityVo loadEntity 效驗欄位
        return this.resourceResponse(200);
    }

    @Operation(summary = "更新單一用電端電表及其關聯", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/relation")
    public ResponseEntity<ResponseMessage> updateMeterAndChild(@PathVariable(value = "id") Long id,
                                                               @PathVariable(value = "meterId") Long meterId,
                                                               @RequestBody LoadEntityMeterVO vo,
                                                               @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.updateFullMeter(vo, userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "新增單一發電端電表", description = "")
    @PostMapping("/single/{id}/meter-add")
    public ResponseEntity<ResponseMessage> addMeter(@PathVariable(value = "id") Long id,
                                                    @RequestBody LoadEntityMeterVO vo,
                                                    @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.addMeterOnly(vo, id, userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "更新單一發電端電表", description = "")
    @PostMapping("/single/{id}/meter-only/{meterId}")
    public ResponseEntity<ResponseMessage> updateMeter(@PathVariable(value = "id") Long id,
                                                       @PathVariable(value = "meterId") Long meterId,
                                                       @RequestBody LoadEntityMeterVO vo,
                                                       @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        vo.setId(meterId);
        service.updateMeterOnly(vo, userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "新增單一用電端特殊電表使用的關聯圖檔", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/vm")
    public DataResponseMessage<Long> addNweMeterVm(@PathVariable(value = "id") Long id,
                                                   @PathVariable(value = "meterId") Long meterId,
//                                                         @RequestParam(value = "originStartDate",
//                                                                 required = false) String originStartDate,
//                                                         @RequestParam(value = "originEndDate",
//                                                                 required = false) String originEndDate,
                                                   @RequestParam("startDate") String startDate,
                                                   @RequestParam(value = "endDate",
                                                           required = false) String endDate,
                                                   @RequestParam("file") MultipartFile file,
                                                   @AuthenticationPrincipal MyUserPrincipal principal) throws Exception {
        var userId = principal.getUserId();
        var vmId = service.uploadMeterVm(meterId, CommonUtil.foramtDateTimeUtcString(startDate),
                CommonUtil.foramtDateTimeUtcString(endDate), file, userId);
        return new DataResponseMessage<Long>(vmId);
    }

    @Operation(summary = "取得單一用電端特殊電表使用的關聯圖檔", description = "")
    @GetMapping("/single/{id}/meter/{meterId}/vm/{useFrom}/{useTo}/")
    public ResponseEntity<byte[]> getFile(@PathVariable(value = "id") Long id,
                                          @PathVariable(value = "meterId") Long meterId,
                                          @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description =
                                                  "日期，格式採用Number", example = "20240301") Long useFrom,
                                          @PathVariable(value = "useTo") @Parameter(name = "useTo", description =
                                                  "日期，格式採用Number", example = "20240301") Long useTo) {

        var result = service.getMeterVm(meterId, new Date(useFrom), new Date(useTo));
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + "meter" + meterId + ".png" + "\"")
                .body(result.getContent());
    }

    @Operation(summary = "取得單一用電端特殊電表使用的關聯圖檔", description = "")
    @GetMapping("/single/{id}/meter/{meterId}/vm/{useFrom}/")
    public ResponseEntity<byte[]> getFile(@PathVariable(value = "id") Long id,
                                          @PathVariable(value = "meterId") Long meterId,
                                          @PathVariable(value = "useFrom") @Parameter(name = "useFrom", description =
                                                  "日期，格式採用Number", example = "20240301") Long useFrom
    ) {

        var result = service.getMeterVm(meterId, new Date(useFrom), null);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + "meter" + meterId + ".png" + "\"")
                .body(result.getContent());
    }

    @Operation(summary = "取得單一用電端特殊電表使用的關聯圖檔", description = "")
    @GetMapping("/single/{id}/meter/{meterId}/vm/byId/{vmId}/")
    public ResponseEntity<byte[]> getFileById(@PathVariable(value = "id") Long id,
                                              @PathVariable(value = "meterId") Long meterId,
                                              @PathVariable(value = "vmId") Long vmId
    ) {

        var result = service.getMeterVmById(meterId, vmId);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + "meter" + meterId + ".png" + "\"")
                .body(result.getContent());
    }

    @Operation(summary = "修正單一用電端特殊電表使用的關聯圖檔的使用區間", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/vm/useRangeFix/")
    public ResponseEntity<ResponseMessage> getFile(@PathVariable(value = "id") Long id,
                                                   @PathVariable(value = "meterId") Long meterId,
                                                   @RequestBody EntityMeterVmUpdateVO vo,
                                                   @AuthenticationPrincipal MyUserPrincipal principal

    ) {

        var userId = principal.getUserId();
        service.updateMeterVmUseRangeOnly(meterId, vo.getOriginStartDate(), vo.getOriginEndDate(), vo.getStartDate(),
                vo.getEndDate(), userId);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "依據電號查詢相關電表", description = "")
    @GetMapping("/findcustomer/{customerNo}/distinct/meter")
    public DataResponseMessage<List<ViewAllEntityMeterVO>> findEntityMeterChildByCustomerNo(
            @PathVariable(value = "customerNo") String customerNo
    ) {

        var list = service.findEntityMeterChildByCustomerNo(customerNo);

        return new DataResponseMessage(list);
    }

    @Operation(summary = "依據電號查詢相關電表", description = "")
    @GetMapping("/findcustomer/{customerNo}/distinct/entitymeter")
    public DataResponseMessage<List<SimpleMeterSpVO>> findEntityMeterByCustomerNo(
            @PathVariable(value = "customerNo") String customerNo
    ) {

        var list = service.findEntityMeterByCustomerNo(customerNo);

        return new DataResponseMessage(list);
    }

    @Operation(summary = "取得單一用電端特殊電表使用的關聯圖檔的所有紀錄", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/vm/all/")
    public DataResponseMessage<List<GeneratorEntityMeterVmVO>> getVmAll(@PathVariable(value = "id") Long id,
                                                                        @PathVariable(value = "meterId") Long meterId
    ) {


        var list = service.getMeterVmList(meterId);
        return new DataResponseMessage(list);
    }

    @Operation(summary = "更新單一用電端電表關聯，因為有刪除的情形，所以需要加傳原本操作的對象Id", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/relationonly")
    public ResponseEntity<ResponseMessage> updateMeterChildsOnly(@PathVariable(value = "id") Long id,
                                                                 @PathVariable(value = "meterId") Long meterId,
                                                                 @RequestBody LoadEntityMeterChildUpdateVO vo,
                                                                 @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.updateMeterChildsWithIds(meterId, vo.getSourceIds(), vo.getChildren(), vo.getVmId(), userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "刪除單一用電端電表及其關聯", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/relation-delete")
    public ResponseEntity<ResponseMessage> removeMeterChildsWithIds(@PathVariable(value = "id") Long id,
                                                                    @PathVariable(value = "meterId") Long meterId,
                                                                    @RequestBody LoadEntityMeterChildUpdateVO vo,
                                                                    @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.removeMeterChildsWithIds(vo.getSourceIds(), userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "更新特殊發電端電表關聯，因為有刪除的情形，所以需要加傳原本操作的對象Id", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/sp/relationonly")
    public ResponseEntity<ResponseMessage> updateSpMeterChildsOnly(@PathVariable(value = "id") Long id,
                                                                   @PathVariable(value = "meterId") Long meterId,
                                                                   @RequestBody LoadEntityMeterChildUpdateVO vo,
                                                                   @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        service.updateSPMeterChildsWithIds(meterId, vo.getSourceIds(), vo.getSpChildren(), vo.getImage(), userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "刪除特殊發電端電表及其關聯", description = "")
    @PostMapping("/single/{id}/meter/{meterId}/sp/relation-delete")
    public ResponseEntity<ResponseMessage> removeSPMeterChildsWithIds(@PathVariable(value = "id") Long id,
                                                                      @PathVariable(value = "meterId") Long meterId,
                                                                      @RequestBody GeneratorEntityMeterChildUpdateVO vo,
                                                                      @AuthenticationPrincipal MyUserPrincipal principal) {
        var userId = principal.getUserId();
        entityService.removeEntityMeterSp(vo.getSourceIds(), userId);
        return this.resourceResponse(200);
    }

    @Operation(summary = "取得用電端所有應附文件", description = "")
    @GetMapping("/required/document")
    public DataResponseMessage<List<EntityDocumentRequiredVO>> findAllRequiredDocuments() {


        var list = service.findAllRequiredDocuments();
        return new DataResponseMessage(list);
    }

    @Operation(summary = "取得單一用電端已上傳文件紀錄", description = "")
    @GetMapping("/single/{id}/document/all")
    public DataResponseMessage<List<GeneratorEntityDocumentVO>> getVmAll(@PathVariable(value = "id") Long id
    ) {


        var list = service.findUploadedDocumentsByEntityId(id);
        return new DataResponseMessage(list);
    }

    @Operation(summary = "下載已經上完的檔案", description = "")
    @GetMapping("/single/{id}/document/{fileId}/download")
    public ResponseEntity<byte[]> getUploadedDocumentByEntityIdAndId(@PathVariable(value = "id") Long id,
                                                                     @PathVariable(value = "fileId") Long fileId

    ) {

        var result = service.getUploadedDocumentByEntityIdAndId(fileId);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + "document" + result.getDocumentId() + ".pdf" +
                                "\"")
                .body(result.getContent());
    }

    @Operation(summary = "上傳應附文件，上傳完成提供檔案ＩＤ", description = "")
    @PostMapping("/single/{id}/document/{documentId}/upload")
    public DataResponseMessage<Long> addNweMeterVm(@PathVariable(value = "id") Long id,
                                                   @PathVariable(value = "documentId") Integer documentId,
                                                   @RequestParam("file") MultipartFile file,
                                                   @AuthenticationPrincipal MyUserPrincipal principal) throws Exception {
        var userId = principal.getUserId();
        var vmId = service.uploadDocumentByEntityIdAndDocumentId(id, documentId, file, userId);
        return new DataResponseMessage<Long>(vmId);
    }

    @Operation(summary = "同步用電端資訊，以電號資訊同步", description = "")
    @PostMapping("/syncfromrems")
    public ResponseEntity<ResponseMessage> syncFromREMS(@RequestBody REMSNbReqest reqest,
                                                        @AuthenticationPrincipal MyUserPrincipal principal) throws IOException {
        var userId = principal.getUserId();
        remsService.syncNBSWithCustomNumbers(reqest.getNbsCustomerNos(), userId);
        return this.resourceSuccessfulResponse();
    }

    @Operation(summary = "取得特殊表計使用所有關聯電表相關狀態", description = "")
    @GetMapping("/single/{id}/special-meter-infos")
    public DataResponseMessage<List<MdesMeterVO>> findEntitySpRelations(
            @PathVariable(value = "id") Long id
    ) {
        var infos = entityService.findEntitySpRelations(LOAD.getValue(), id);
        return new DataResponseMessage<List<MdesMeterVO>>(infos);
    }
}