package tw.com.taipower.pwoms.controller.re;

import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import tw.com.taipower.pwoms.service.vo.MyUserPrincipal;
import tw.com.taipower.pwoms.services.re.ReProgress;
import tw.com.taipower.pwoms.services.re.ReProgressListener;
import tw.com.taipower.pwoms.services.re.ReProgressService;

@Log4j2
@Controller
public class ReProgressWebSocketController implements ReProgressListener {
    @Value("${test.user.id:2}")
    private Long TEST_USER_ID ;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    @Autowired
    private ReProgressService reProgressService;

    @Override
    public void onProgress(ReProgress reProcess) {
        if (reProcess.isSave()) {
            Long userId = TEST_USER_ID ;
//            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
//
//            if (auth != null && auth.isAuthenticated() && auth.getPrincipal() instanceof MyUserPrincipal) {
//                MyUserPrincipal myUserPrincipal = (MyUserPrincipal) auth.getPrincipal();
//                userId = myUserPrincipal.getUserId();
//                log.info("== onProgress.user =" + myUserPrincipal);
//            } else {
//                userId = TEST_USER_ID;
//                log.info("== onProgress.fallbackToTestUser = " + TEST_USER_ID);
//            }

            reProgressService.saveReSettlementStep(reProcess, userId);
        }

        log.info("== websocker.onProgress. =" + reProcess);
        messagingTemplate.convertAndSend("/topic/reDistributionProgress", reProcess);
    }


}


