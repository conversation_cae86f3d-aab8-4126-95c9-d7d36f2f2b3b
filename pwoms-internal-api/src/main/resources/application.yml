tpc:
  mail:
    abortToSendApplicant: true
  sso:
    apUid: AP_UID
    apPassword: AP_PASSWORD
    checkEndpointURL:
    unlockEndpointURL: 
spring:
  flyway:
    enabled: false
    locations: classpath:db/migration
#    locations: classpath:db/migration/mydb
    baseline-on-migrate: true  # 初次建置時初始化版本
    clean-disabled: true       # 禁止不小心執行 clean
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  profiles:
    active:
#      ae-dev
#      mte-test-s1
#      mte-dev
#      tpc-prod
#      tpc-dev
#      ae-s1
#      mte-test-MY
#      ae-dev-local25
#      ae-dev-local06
      mte-t2-tpc-My
#      mte-t1-test-s1
#      mte-t2-tpc
#      mte-t2-tpc-MY

server:
  port: 25002
  servlet:
    context-path: /pwoms
    session:
      cookie:
        name: INTERNAL_SITE
springdoc:
  swagger-ui:
    #custom swagger-ui path (default: http://server:port/context-path/v3/api-docs)
    path: /sw/api
    #For sorting endpoints alphabetically
    operationsSorter: alpha
    #For sorting tags alphabetically
    tagsSorter: alpha
    enabled: true
  api-docs:
    path: /sw/docs
    enabled: true
  writer-with-default-pretty-printer: true