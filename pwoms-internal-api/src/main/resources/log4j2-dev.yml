Configuration:
  Appenders:
    Console:  #輸出到控制台
      name: CONSOLE #Appender命名
      target: SYSTEM_OUT
      PatternLayout:
        pattern: "%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm}} %clr{${LOG_LEVEL_PATTERN:-%5p}}{green} ${PID:- } ---%clr{ [%t]}{yellow} %clr{%c{1.}-%F＠（%M:%L）}{cyan} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
    RollingFile: # 輸出到文件，超過256MB歸檔
      - name: ROLLING_FILE
        ignoreExceptions: false
        fileName: ./logs/pwoms-internal-api.log
        filePattern: "./logs/$${date:yyyy-MM}/pwoms-internal-api -%d{yyyy-MM-dd}-%i.log.gz"
        PatternLayout:
          pattern: "%d{yyyy-MM-dd HH:mm:ss} %5p --- [%t] %c{1.}-%F＠(%M:%L): %m%n%wEx"
        Policies:
          TimeBasedTriggeringPolicy:  # 按天分類
            modulate: true
            interval: 1
          SizeBasedTriggeringPolicy:
            size: "10 MB"
        DefaultRolloverStrategy:
          max: 1000
  Loggers:
    Root:
      level: info
      AppenderRef:
        - ref: CONSOLE
        - ref: ROLLING_FILE
    Logger: #單獨設置某些包的輸出級別
      - name: tw.com.taipower
        additivity: false #去除重複的log
        level: ${LOG_LEVEL:trace}
        AppenderRef:
          - ref: CONSOLE
          - ref: ROLLING_FILE
      - name: org.springframework
        additivity: false #去除重複的log
        level: error
        AppenderRef:
          - ref: CONSOLE
          - ref: ROLLING_FILE
      - name: org.hibernate
        additivity: false #去除重複的log
        level: error
        AppenderRef:
          - ref: CONSOLE
          - ref: ROLLING_FILE
              #        - name: com.zaxxer.hikari
              #          additivity: false #去除重複的log
              #          level: error
              #          AppenderRef:
              #            - ref: CONSOLE
              #            - ref: ROLLING_FILE
