custom:
  useGraylog: true
logging:
  config: classpath:log4j2-tpc.yml
chtami:
  baseUrl: ${CHTAMI_BASE_URL:http://*************:8883/pwoms}
  accessToken: ${CHTAMI_ACCESS_TOKEN:1234567890}
# 開發用config
develop:
  version: ${VERSION:unknown}
  skipSSO: ${SKIP_SSO:false}
  skip2FA: ${SKIP_2FA:false}
  skipAuthority: ${SKIP_AUTHORITY:false}
  giveDefaultPassword: ${GIVE_DEFAULT_PASSWORD:false}
  skipLock: ${SKIP_LOCK:false}
  pwoms-scheduler: ${SCHEDULER_URL:http://**************:25003/pwoms-scheduler}
  pwoms-settlementer: ${SETTLEMENTER_URL:http://localhost:25005/pwoms-settlementer}

tpc:
  mail:
    abortToSendApplicant: ${abortToSendApplicant:true}
  rems:
    nbs1URL: ${NBS1URL}
    nbs2URL: ${NBS2URL}
    nbs3URL: ${NBS3URL}
    rnis1URL: ${RNIS1URL}
    rnis2URL: ${RNIS2URL}
    rnis3URL: ${RNIS3URL}
  file-attach:
    download: ${FILE_ATTACH_DOWNLOAD:/pwoms/filesdownload/}
    faq: ${FILE_ATTACH_FAQ:/pwoms/files/faq/}
    news: ${FILE_ATTACH_NEWWS:/pwoms/files/news/}
    video: ${FILE_ATTACH_VIDEO:/pwoms/files/video/}
    sys-info: ${FILE_ATTACH_SYS_INFO:/pwoms/files/sys-info/}
    theme-graphic: ${FILE_THEME_GRAPHIC:/pwoms/files/theme-graphic}
    filename-strategy: ${FILE_ATTACH_FILENAME_STRATEGY:monthly}
  datasource:
    pwoms:
      url: *******************************************************************************
      username: pwomsadm
      password: 1qaz@WSX#EDC
    ami:
      url: ${AMI_DB_URI:*****************************************************************************}
      username: ${AMI_DB_USER:pwomsadm}
      password: ${AMI_DB_PASSWORD:1qaz@WSX#EDC}
  jpa:
    pwoms:
      properties:
        hibernate:
          format_sql: false
          show_sql: false
    ami:
      properties:
        hibernate:
          format_sql: false
          show_sql: false
  selector:
    url: ${TPC_SELECTOR_URL:https://**********:8008/native}
    username: ${TPC_SELECTOR_USERNAME:api}
    password: ${TPC_SELECTOR_PASSWORD:taipoweer1qaz@WSX}
    client-id: ${TPC_SELECTOR_CLIENT_ID:your_clientID}
    skip: ${TPC_SELECTOR_SKIP:true}

springdoc:
  swagger-ui:
    enabled: true
  api-docs:
    enabled: true
  mail-header: "[測試用]"
spring:
  kafka:
    # 是否啟用雙中心，如為啟用，則將資料往雙邊Kafka server送
    enable-dual-center: false
    # 如果為未啟用雙中心，僅將資料往主中心的Kafka server送
    main-bootstrap-server: pwoms
    bootstrap-servers: ${KAFKA_SERVER:**********:9092}
    producer:
      # 重試次數
      retries: 3
      # 批量發送的消息數量
      batch-size: 16384
      # 32MB的批處理緩衝區
      buffer-memory: 33554432
      # message壓縮格式
      compression-type: gzip
    consumer:
      group-id: pwoms-consumer-tpc-dev
      enable-auto-commit: false
    listener:
      ack-mode: manual_immediate
  data:
    redis:
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          max-wait: -1ms
          min-idle: 0
      host: ***********
      port: 6379
      username: pwoms
      password: ${REDIS_PASSWORD:1!2@3#4$5%6^7&8*9}
  session:
    store-type: redis
    timeout: 36000
    redis:
      namespace: "spring:session:internal"
  mail:
    host: smtp.taipower.com.tw
    username: <EMAIL>
    password:
    protocol: smtp
    properties:
      mail:
        smtp:
          timeout: 20000
          connection timeout: 20000
          write timeout: 20000
          auth: false
          starttls:
            enable: true
    default-encoding: UTF-8