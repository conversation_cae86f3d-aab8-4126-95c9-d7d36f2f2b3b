# 開發用config
develop:
  version: local
  skipSSO: true
  skip2FA: true
  skipAuthority: true
  skipLock: true
  ipLockTime: 3  #min
#  pwoms-scheduler: ${SCHEDULER_URL:http://**************:25003/pwoms-scheduler}
  pwoms-scheduler: ${SCHEDULER_URL:http://localhost:25003/pwoms-scheduler} # lien
  pwoms-settlementer: ${SETTLEMENTER_URL:http://localhost:25005/pwoms-settlementer}


tpc:
  mail:
    abortToSendApplicant: true
  datasource:
    pwoms:
      url: ******************************************************************************
#      url: *******************************************************************************
      username: sa
      password: Msdb!234
    ami:
      url: ******************************************************************************
#      url: *******************************************************************************
      username: sa
      password: Msdb!234

  jpa:
    pwoms:
      properties:
        hibernate:
          format_sql: false
          show_sql: true
          enable_lazy_load_no_trans: true  # charlene for lazy load
#          dialect: org.hibernate.dialect.SQLServerDialect
    ami:
      properties:
        hibernate:
          format_sql: false
          show_sql: false
#          dialect: org.hibernate.dialect.SQLServerDialect

springdoc:
  swagger-ui:
    enabled: true
  api-docs:
    enabled: true
  mail-header: "[測試用]"
spring:
#  kafka:
#    # 是否啟用雙中心，如為啟用，則將資料往雙邊Kafka server送
#    enable-dual-center: false
#    # 如果為未啟用雙中心，僅將資料往主中心的Kafka server送
#    main-bootstrap-server: pwoms
#    bootstrap-servers: **********:9092
#    producer:
#      # 重試次數
#      retries: 3
#      # 批量發送的消息數量
#      batch-size: 16384
#      # 32MB的批處理緩衝區
#      buffer-memory: 33554432
#      # message壓縮格式
#      compression-type: gzip
#    consumer:
#      group-instance-id: pwoms-consumer-instance-1
#      partition-num: 0
#    listener:
#      ack-mode: manual_immediate
  data:
    redis:
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          max-wait: -1ms
          min-idle: 0
#      cluster:
#        max-redirects: 3
#        nodes: *************:6379,*************:6380,*************:6381,*************:6382,*************:6383,*************:6384
  session:
    store-type: redis
    timeout: 12000
    redis:
      namespace: "spring:session:internal"
  mail:
    host: smtp.gmail.com
    username: <EMAIL>
    password: iarobnyrtozbhpsf
    protocol: smtp
    properties:
      mail:
        smtp:
          timeout: 20000
          connection timeout: 20000
          write timeout: 20000
          auth: true
          port: 465
          ssl:
            enable: true
    default-encoding: UTF-8

  flyway:
    enabled: false

# working
logging:
  pattern:
  level:
    org.hibernate.orm.jdbc.bind: trace


## log working
#logging:
#  level:
#    org.hibernate: DEBUG

# Debugging Configuration
#Add explicit logging to trace context lifecycle:
logging.level.org.springframework.boot.web: DEBUG
logging.level.org.springframework.context: DEBUG
logging.level.org.springframework.boot.devtools: DEBUG
