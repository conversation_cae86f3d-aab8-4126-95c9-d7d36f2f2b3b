custom:
  useGraylog: true
logging:
  config: classpath:log4j2-tpc.yml
chtami:
  baseUrl: ${CHTAMI_BASE_URL:http://*************:8883/pwoms}
  accessToken: ${CHTAMI_ACCESS_TOKEN:1234567890}
  fileDownloadUrl: ${CHTAMI_BASE_FILE_URL:http://*************/}
develop:
  skipSSO: true
  skip2FA: true
  skipAuthority: ${SKIP_AUTHORITY:true}
  giveDefaultPassword: true
  pwoms-scheduler: ${SCHEDULER_URL:http://**************:25003/pwoms-scheduler}
  resetPwUrl: http://**********/
  pwoms-settlementer: ${SETTLEMENTER_URL:http://localhost:25005/pwoms-settlementer}

erp:
  #url: https://stpcs4qap.taipower.com.tw:54300/sap/bc/srt/wsdl/flv_10002A111AD1/bndg_url/sap/bc/srt/rfc/sap/Z_FI_EI_CUSTOMER_IRT/600/Z_FI_EI_CUSTOMER_IRT/Z_FI_EI_CUSTOMER_IRT?sap-client=600
  url: https://localhost:8443/ws/employees.wsdl
  username: RFCUSER
  password: Stpcs4qap@
  skip: true
tpc:
  mail:
    abortToSendApplicant: true
  erd:
    sFTPKnownHost: ${SFTP_KNOWN_HOST:/known_hosts}
    sFTPRemoteDir: ${ERD_SFTP_REMOTE_DIR:/pwomsuser/upload/}
    sFTPHost: ${ERD_SFTP_HOST:***************}
    sFTPUsername: ${ERD_SFTP_USERNAME:pwomsuser}
    sFTPPassword: ${ERD_SFTP_PASSWORD:3478voijm;4g;..j}
    notifyRoleId: ${ERD_NOTIFY_ROLE:2}
  datasource:
    pwoms:
      url: *********************************************************************************
      username: pwomsadm
      password: pdF0dN9U3y4PJatfucvV
      maximum-pool-size: 20
    ami:
      url: *******************************************************************************
      username: pwomsadm
      password: pdF0dN9U3y4PJatfucvV
  jpa:
    pwoms:
      properties:
        hibernate:
          format_sql: false
          show_sql: false
  file-attach:
    download: /pwoms/files/download/
    faq: /pwoms/files/faq/
    news: /pwoms/files/news/
    video: /pwoms/files/video/
    sys-info: /pwoms/files/sys-info/
    theme-graphic: /pwoms/files/theme-graphic/
    filename-strategy: monthly

springdoc:
  swagger-ui:
    enabled: true
  api-docs:
    enabled: true
  mail-header: "[測試用]"
spring:
  kafka:
    # 是否啟用雙中心，如為啟用，則將資料往雙邊Kafka server送
    enable-dual-center: false
    # 如果為未啟用雙中心，僅將資料往主中心的Kafka server送
    main-bootstrap-server: pwoms
    bootstrap-servers: localhost:9092
    producer:
      # 重試次數
      retries: 3
      # 批量發送的消息數量
      batch-size: 16384
      # 32MB的批處理緩衝區
      buffer-memory: 33554432
      # message壓縮格式
      compression-type: gzip
    consumer:
      group-id: pwoms-consumer-ae-kevin
      enable-auto-commit: false
    listener:
      ack-mode: manual_immediate
  data:
    redis:
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          max-wait: -1ms
          min-idle: 0
      host: redis
      port: 6379

  session:
    store-type: redis
    timeout: 1200
    redis:
      namespace: "spring:session:internal"
  mail:
    host: smtp.gmail.com
    username: <EMAIL>
    password: iarobnyrtozbhpsf
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: true
          port: 465
          ssl:
            enable: true
    default-encoding: UTF-8
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB