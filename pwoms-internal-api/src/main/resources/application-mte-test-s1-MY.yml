# 開發用config
develop:
  version: local
  skipSSO: true
  skip2FA: true
  skipAuthority: false
  skipLock: true
  ipLockTime: 3  #min
  pwoms-scheduler: ${SCHEDULER_URL:http://**************:25003/pwoms-scheduler}
  pwoms-settlementer: ${SETTLEMENTER_URL:http://localhost:25005/pwoms-settlementer}

erp:
  #url: https://stpcs4qap.taipower.com.tw:54300/sap/bc/srt/wsdl/flv_10002A111AD1/bndg_url/sap/bc/srt/rfc/sap/Z_FI_EI_CUSTOMER_IRT/600/Z_FI_EI_CUSTOMER_IRT/Z_FI_EI_CUSTOMER_IRT?sap-client=600
  url: https://localhost:8443/ws/employees.wsdl
  username: RFCUSER
  password: Stpcs4qap@

tpc:
  mail:
    abortToSendApplicant: true
  datasource:
    pwoms:
#      url: ******************************************************************************
#      username: sa
#      password: Msdb!234
      url: **********************************************************************************************
      username: pwomsdeveloper
      password: d0401*#aM
    ami:
#      url: ******************************************************************************
#      username: sa
#      password: Msdb!234
      url: ********************************************************************************************
      username: pwomsdeveloper
      password: d0401*#aM

  jpa:
    pwoms:
      properties:
        hibernate:
          format_sql: true
          show_sql: true
          jdbc:
            batch_size: 200
            order_inserts: true
    ami:
      properties:
        hibernate:
          format_sql: false
          show_sql: false
          jdbc:
            batch_size: 200
            order_inserts: true

springdoc:
  swagger-ui:
    enabled: true
  api-docs:
    enabled: true
  mail-header: "[測試用]"
spring:
  kafka:
    # 是否啟用雙中心，如為啟用，則將資料往雙邊Kafka server送
    enable-dual-center: false
    # 如果為未啟用雙中心，僅將資料往主中心的Kafka server送
    main-bootstrap-server: pwoms
    bootstrap-servers: **********:9092
    producer:
      # 重試次數
      retries: 3
      # 批量發送的消息數量
      batch-size: 16384
      # 32MB的批處理緩衝區
      buffer-memory: 33554432
      # message壓縮格式
      compression-type: gzip
    consumer:
      group-instance-id: pwoms-consumer-instance-1
      partition-num: 0
    listener:
      ack-mode: manual_immediate
  data:
    redis:
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          max-wait: -1ms
          min-idle: 0
      host: localhost
      #      host: ***********
      port: 6379
  session:
    store-type: redis
    timeout: 12000
    redis:
      namespace: "spring:session:internal"
  mail:
    host: smtp.gmail.com
    username: <EMAIL>
    password: iarobnyrtozbhpsf
    protocol: smtp
    properties:
      mail:
        smtp:
          timeout: 20000
          connection timeout: 20000
          write timeout: 20000
          auth: true
          port: 465
          ssl:
            enable: true
    default-encoding: UTF-8

## log working
#logging:
#  level:
#    org.hibernate: DEBUG

# Debugging Configuration
#Add explicit logging to trace context lifecycle:
logging.level.org.springframework.boot.web: DEBUG
logging.level.org.springframework.context: DEBUG
logging.lev