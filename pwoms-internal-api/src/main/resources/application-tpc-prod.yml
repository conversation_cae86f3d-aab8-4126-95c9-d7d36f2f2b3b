custom:
  useGraylog: true
logging:
  config: classpath:log4j2-tpc.yml
  level:
    org.springframework.ws.client.MessageTracing.sent: ${WEBSERVICE_TRACE:OFF}
    org.springframework.ws.client.MessageTracing.received: ${WEBSERVICE_TRACE:OFF}
    org.springframework.ws.server.MessageTracing: ${WEBSERVICE_TRACE:OFF}
    org.springframework.ws.client.core.WebServiceTemplate: ${WEBSERVICE_TRACE:OFF}
    org.springframework.oxm.jaxb.Jaxb2Marshaller: ${WEBSERVICE_TRACE:OFF}
    tw.com.taipower.pwoms.services.utils.VoUtils: OFF
chtami:
  baseUrl: ${CHTAMI_BASE_URL:http://*************:8883/pwoms}
  accessToken: ${CHTAMI_ACCESS_TOKEN:1234567890}
  fileDownloadUrl: ${CHTAMI_BASE_FILE_URL:http://*************/}
  responseTimeout: ${CHTAMI_RESPONSE_TIMEOUT:1800}
# 開發用config
develop:
  version: ${VERSION:unknown}
  skipSSO: ${SKIP_SSO:false}
  skip2FA: ${SKIP_2FA:false}
  skipAuthority: ${SK IP_AUTHORITY:false}
  giveDefaultPassword: ${GIVE_DEFAULT_PASSWORD:false}
  skipLock: ${SKIP_LOCK:false}
  pwoms-scheduler: ${SCHEDULER_URL:http://**************:25003/pwoms-scheduler}
  resetPwUrl: ${resetPwUrl:http://unknown}
  pwoms-settlementer: ${SETTLEMENTER_URL:http://localhost:25005/pwoms-settlementer}

erp:
  url: ${ERP_URL:https://stpcs4qap.taipower.com.tw:54300/sap/bc/srt/rfc/sap/z_fi_ei_customer_irt/600/z_fi_ei_customer_irt/z_fi_ei_customer_irt}
  username: ${ERP_USERNAME:RFCUSER}
  password: ${ERP_PASSWORD:Stpcs4qap@}
  skip: ${ERP_SKIP:false}
tpc:
  mail:
    abortToSendApplicant: ${abortToSendApplicant:true}
  erd:
    sFTPKnownHost: ${SFTP_KNOWN_HOST:/known_hosts}
    sFTPRemoteDir: ${ERD_SFTP_REMOTE_DIR:/pwomsuser/upload/}
    sFTPHost: ${ERD_SFTP_HOST:***************}
    sFTPUsername: ${ERD_SFTP_USERNAME:pwomsuser}
    sFTPPassword: ${ERD_SFTP_PASSWORD:3478voijm;4g;..j}
    notifyRoleId: ${ERD_NOTIFY_ROLE:2}
  rems:
    nbs1URL: ${NBS1URL}
    nbs2URL: ${NBS2URL}
    nbs3URL: ${NBS3URL}
    rnis1URL: ${RNIS1URL}
    rnis2URL: ${RNIS2URL}
    rnis3URL: ${RNIS3URL}
    SFTPKnownHost: ${SFTP_KNOWN_HOST}
    rnisSFTPRemoteDir: ${RNIS_SFTP_REMOTE_DIR}
    rnisSFTPHost: ${RNIS_SFTP_HOST}
    rnisSFTPUsername: ${RNIS_SFTP_USERNAME}
    rnisSFTPPassword: ${RNIS_SFTP_PASSWORD}
    nbsSFTPRemoteDir: ${NBS_SFTP_REMOTE_DIR}
    nbsSFTPHost: ${NBS_SFTP_HOST}
    nbsSFTPUsername: ${NBS_SFTP_USERNAME}
    nbsSFTPPassword: ${NBS_SFTP_PASSWORD}
    rnisFTPNotifyRole: ${RNIS_FTP_NOTIFY_ROLE:2}
  datasource:
    pwoms:
      url: ${PWOMS_DB_URI:*******************************************************************************}
      username: ${PWOMS_DB_USER:pwomsadm}
      password: ${PWOMS_DB_PASSWORD:1qaz@WSX#EDC}
    ami:
      url: ${AMI_DB_URI:****************************************************************************}
      username: ${AMI_DB_USER:pwomsdba}
      password: ${AMI_DB_PASSWORD:1qaz@WSX#EDC}
  jpa:
    pwoms:
      properties:
        hibernate:
          format_sql: false
          show_sql: false
    ami:
      properties:
        hibernate:
          format_sql: false
          show_sql: false
  file-attach:
    download: ${FILE_ATTACH_DOWNLOAD:/pwoms/filesdownload/}
    faq: ${FILE_ATTACH_FAQ:/pwoms/files/faq/}
    news: ${FILE_ATTACH_NEWWS:/pwoms/files/news/}
    video: ${FILE_ATTACH_VIDEO:/pwoms/files/video/}
    sys-info: ${FILE_ATTACH_SYS_INFO:/pwoms/files/sys-info/}
    theme-graphic: ${FILE_THEME_GRAPHIC:/pwoms/files/theme-graphic}
    filename-strategy: ${FILE_ATTACH_FILENAME_STRATEGY:monthly}
  selector:
    url: ${TPC_SELECTOR_URL:https://**********:8008/native}
    username: ${TPC_SELECTOR_USERNAME:api}
    password: ${TPC_SELECTOR_PASSWORD:taipoweer1qaz@WSX}
    client-id: ${TPC_SELECTOR_CLIENT_ID:your_clientID}
    skip: ${TPC_SELECTOR_SKIP:true}

springdoc:
  swagger-ui:
    enabled: true
  api-docs:
    enabled: true
  mail-header: "[測試用]"
spring:
  kafka:
    # 是否啟用雙中心，如為啟用，則將資料往雙邊Kafka server送
    enable-dual-center: false
    # 如果為未啟用雙中心，僅將資料往主中心的Kafka server送
    main-bootstrap-server: pwoms
    bootstrap-servers: ${KAFKA_SERVER:**********:9092}
    producer:
      # 重試次數
      retries: 3
      # 批量發送的消息數量
      batch-size: 16384
      # 32MB的批處理緩衝區
      buffer-memory: 33554432
      # message壓縮格式
      compression-type: gzip
    consumer:
      group-id: ${KAFKA_GROUP_ID:pwoms-tpc-default}
      enable-auto-commit: false
    listener:
      ack-mode: manual_immediate
  data:
    redis:
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          max-wait: -1ms
          min-idle: 0
      cluster:
        max-redirects: 3
        nodes:
          - **********:6379
          - **********:6380
          - **********:6379
          - **********:6380
          - **********:6379
          - **********:6380
          - **********:6379
          - **********:6380
          - **********:6379
          - **********:6380
      password: ${REDIS_PASSWORD:1qaz@WSX#EDC}
  session:
    store-type: redis
    timeout: 36000
    redis:
      namespace: "spring:session:internal"
  mail:
    host: smtp.taipower.com.tw
    username: <EMAIL>
    password:
    protocol: smtp
    properties:
      mail:
        smtp:
          timeout: 20000
          connection timeout: 20000
          write timeout: 20000
          auth: false
          port: 25
          starttls:
            enable: true
    default-encoding: UTF-8
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB