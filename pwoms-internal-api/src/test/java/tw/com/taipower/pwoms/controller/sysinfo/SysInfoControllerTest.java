package tw.com.taipower.pwoms.controller.sysinfo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.hamcrest.CoreMatchers;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tw.com.taipower.pwoms.controller.vo.request.FileAttachRequest;
import tw.com.taipower.pwoms.controller.vo.request.sysinfo.EditSysInfoRequest;
import tw.com.taipower.pwoms.controller.vo.request.sysinfo.FindSysInfoListRequest;
import tw.com.taipower.pwoms.controller.vo.request.sysinfo.FindSysInfoPageRequest;
import tw.com.taipower.pwoms.controller.vo.request.sysinfo.SysInfoRequest;
import tw.com.taipower.pwoms.controller.vo.response.FileAttachResponseMessage;
import tw.com.taipower.pwoms.controller.vo.response.sysinfo.ListSysAttachResponse;
import tw.com.taipower.pwoms.controller.vo.response.sysinfo.ListSysInfoResponse;
import tw.com.taipower.pwoms.controller.vo.response.sysinfo.PageSysInfoResponse;
import tw.com.taipower.pwoms.controller.vo.response.sysinfo.SysInfoResponseMessage;
import tw.com.taipower.pwoms.services.filter.SysInfoFilter;
import tw.com.taipower.pwoms.services.sysinfo.SysAttachService;
import tw.com.taipower.pwoms.services.sysinfo.SysInfoService;
import tw.com.taipower.pwoms.services.utils.VoUtils;
import tw.com.taipower.pwoms.services.vo.sysinfo.SysAttachVO;
import tw.com.taipower.pwoms.services.vo.sysinfo.SysInfoVO;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @class: SysInfoControllerTest
 * @author: JimmyHuang
 * @version:
 * @since: 2025-01-16 09:00
 * @see:
 */
@Log4j2
@Transactional
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
class SysInfoControllerTest {
    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;

    @Resource
    private SysAttachService sysAttachService;
    @Resource
    private SysInfoService sysInfoService;

    private final List<String> links = new ArrayList<>();
    private final List<SysInfoVO> sysInfos = new ArrayList<>();

    @Test
    void addFileTest() {
        addFiles();

        //file is empty
        try {
            MockMultipartFile file = new MockMultipartFile("file", "單元測試主題背景3.jpg", "image/jpg", "".getBytes());
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.multipart("/sys-info/add-file").file(file))
                            .andExpect(MockMvcResultMatchers.status().isBadRequest())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("檔案不可為空")));

            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void deleteFileTest() {
        addFiles();

        //link為必填欄位
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.delete("/sys-info/delete-file")
                                    .param("link", " "))
                            .andExpect(MockMvcResultMatchers.status().isBadRequest())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("link為必填欄位")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //檔案仍被使用
        SysAttachVO attach1 = new SysAttachVO();
        attach1.setLink(links.getFirst());
        SysInfoVO sysInfoVO = new SysInfoVO(null, "unit-test.theme.image", "/sys-info/" + links.getFirst(),
                "單元測試主題", null, null, Collections.singletonList(attach1));
        SysInfoVO dbSysInfo = sysInfoService.save(sysInfoVO);
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.delete("/sys-info/delete-file")
                                    .param("link", links.getFirst()))
                            .andExpect(MockMvcResultMatchers.status().isBadRequest())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("檔案仍被使用")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
            String message = objectMapper.readTree(responseBody).get("message").asText();
            JsonNode idsJson = objectMapper.readTree(message.substring(message.indexOf("{"))).get("id");
            assert idsJson.isArray() && idsJson.size() == 1;

        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            sysInfoService.delete(dbSysInfo.getId());
        }

        //200 OK
        deleteFiles();

        //找不到指定資料
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.delete("/sys-info/delete-file")
                                    .param("link", links.getFirst()))
                            .andExpect(MockMvcResultMatchers.status().isNotFound())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("找不到指定資料")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void clearUnusedFileTest() {
        linkUnusedAttaches();
        addFiles();

        //200 OK
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.delete("/sys-info/clear-unused-files"))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("已清除" + links.size() + "個檔案")));

            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //無需清空檔案
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.delete("/sys-info/clear-unused-files"))
                            .andExpect(MockMvcResultMatchers.status().isNotFound())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("無需清空檔案")));

            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    void getAttachListTest() {
        addFiles();

        FileAttachRequest request = new FileAttachRequest();
        request.setName("單元測試主題背景");

        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getAttachList")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request)))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
            ListSysAttachResponse<?> list = objectMapper.readValue(responseBody, ListSysAttachResponse.class);
            assert list != null && list.getData().size() == this.links.size();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //list is null
        request.setName("單元測試主題背景3");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getAttachList")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request)))
                            .andExpect(MockMvcResultMatchers.status().isNotFound())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("取得資料失敗")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void getUnusedAttachListTest() {
        linkUnusedAttaches();

        //取得資料失敗
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getUnusedAttachList"))
                            .andExpect(MockMvcResultMatchers.status().isNotFound())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("取得資料失敗")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        addFiles();

        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getUnusedAttachList"))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
            ListSysAttachResponse<?> list = objectMapper.readValue(responseBody, ListSysAttachResponse.class);
            assert list != null && list.getData().size() == this.links.size();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    void addTest() {
        //200 OK
        addSysInfos();

        //key為必填欄位
        SysInfoRequest request = new SysInfoRequest();
        request.setKey(" ");
        request.setValue("");
        request.setDescription("單元測試主題3");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/add")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request)))
                            .andExpect(MockMvcResultMatchers.status().isBadRequest())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("key為必填欄位")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //parentKey不存在
        SysInfoRequest request3 = new SysInfoRequest();
        request3.setKey("unit-test.theme2.test3");
        request3.setValue("/sys-info/" + this.links.getFirst());
        request3.setDescription("單元測試主題3");
        request3.setParentKey("unit-test.theme2");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/add")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request3)))
                            .andExpect(MockMvcResultMatchers.status().isBadRequest())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("parentKey不存在")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void editTest() {
        addSysInfos();

        //200 OK (null attaches)
        EditSysInfoRequest request = VoUtils.toVO(this.sysInfos.getFirst(), EditSysInfoRequest.class);
        request.setSysAttachLinks(null);
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.put("/sys-info/edit")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request)))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //200 OK (with attaches)
        EditSysInfoRequest request2 = VoUtils.toVO(this.sysInfos.getLast(), EditSysInfoRequest.class);
        request2.setSysAttachLinks(this.links);
        request2.setParentKey(this.sysInfos.get(1).getKey());
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.put("/sys-info/edit")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request2)))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //key為必填欄位
        EditSysInfoRequest request3 = VoUtils.toVO(this.sysInfos.getFirst(), EditSysInfoRequest.class);
        request3.setKey(" ");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.put("/sys-info/edit")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request3)))
                            .andExpect(MockMvcResultMatchers.status().isBadRequest())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("key為必填欄位")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //找不到指定資料
        EditSysInfoRequest request4 = VoUtils.toVO(this.sysInfos.getFirst(), EditSysInfoRequest.class);
        request4.setId(-1L);
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.put("/sys-info/edit")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request4)))
                            .andExpect(MockMvcResultMatchers.status().isNotFound())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("找不到指定資料")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //parentKey不存在
        EditSysInfoRequest request5 = VoUtils.toVO(this.sysInfos.getFirst(), EditSysInfoRequest.class);
        request5.setParentKey("unit-test.theme2");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.put("/sys-info/edit")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request5)))
                            .andExpect(MockMvcResultMatchers.status().isBadRequest())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("parentKey不存在")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void deleteTest() {
        addSysInfos();
        //200 OK
        List<SysInfoVO> canDel = this.sysInfos.stream().filter(ele -> ele.getKey().equals("unit-test.theme.test1")).toList();
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.delete("/sys-info/delete")
                                    .param("id", canDel.getFirst().getId().toString()))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //找不到指定資料
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.delete("/sys-info/delete")
                                    .param("id", "-1"))
                            .andExpect(MockMvcResultMatchers.status().isNotFound())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("找不到指定資料")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    //deleteTest - 刪除資料失敗，仍有childrenKeys
    //單元測試 OneToMany (getChildren()) value null - 改用非Transactional才成功取值
    @Test
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void deleteTest2() throws Exception {
        SysInfoVO sysInfo1 = new SysInfoVO(null, "unit-test.theme", null, "單元測試主題", null, null, null);
        SysInfoVO dbSysInfoVO1 = sysInfoService.save(sysInfo1);
        assert dbSysInfoVO1 != null && dbSysInfoVO1.getId() != null;
        this.sysInfos.add(dbSysInfoVO1);

        SysAttachVO attach2 = new SysAttachVO();
        MockMultipartFile file2 = new MockMultipartFile("單元測試主題背景2", "單元測試主題背景2.png", "image/png", "hello2".getBytes());
        SysAttachVO dbAttachVO2 = sysAttachService.save(attach2, file2);
        assert dbAttachVO2 != null && dbAttachVO2.getLink() != null;
        assert sysAttachService.getFile(dbAttachVO2.getPath()).delete();

        SysInfoVO sysInfo2 = new SysInfoVO(null, "unit-test.theme.bg", "/sys-info/" + dbAttachVO2.getLink(),
                "單元測試主題背景2", dbSysInfoVO1.getKey(), null, Collections.singletonList(dbAttachVO2));
        SysInfoVO dbSysInfoVO2 = sysInfoService.save(sysInfo2);
        assert dbSysInfoVO2 != null && dbSysInfoVO2.getId() != null;
        this.sysInfos.add(dbSysInfoVO2);

        //仍有childrenKeys
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.delete("/sys-info/delete")
                                    .param("id", dbSysInfoVO1.getId().toString()))
                            .andExpect(MockMvcResultMatchers.status().isBadRequest())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("仍有childrenKeys")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        sysInfoService.delete(dbSysInfoVO2.getId());
        sysInfoService.delete(dbSysInfoVO1.getId());
        sysAttachService.deleteByLink(dbAttachVO2.getLink());
    }


    @Test
    void getPageTest() {
        addSysInfos();

        FindSysInfoPageRequest request = new FindSysInfoPageRequest();
        request.setPage(0);
        request.setPageSize(10);
        request.setDescription("單元測試主題");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getPage")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request)))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
            PageSysInfoResponse<?> page = objectMapper.readValue(responseBody, PageSysInfoResponse.class);
            assert page.getData().getTotal() == this.sysInfos.size();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //page.getTotal() == 0
        request.setKey("unit-test.test");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getPage")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request)))
                            .andExpect(MockMvcResultMatchers.status().isNotFound())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("取得資料失敗")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void getListTest() {
        addSysInfos();

        FindSysInfoListRequest request = new FindSysInfoListRequest();
        request.setDescription("單元測試主題");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getList")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request)))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
            ListSysInfoResponse<?> list = objectMapper.readValue(responseBody, ListSysInfoResponse.class);
            assert list != null && list.getData().size() == this.sysInfos.size();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //list is null
        request.setKey("unit-test.test");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getList")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request)))
                            .andExpect(MockMvcResultMatchers.status().isNotFound())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("取得資料失敗")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void getDataTest() {
        addSysInfos();

        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getData")
                                    .param("key", sysInfos.getFirst().getKey()))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
            SysInfoResponseMessage<?> data = objectMapper.readValue(responseBody, SysInfoResponseMessage.class);
            assert data != null && data.getData().getKey().equals(sysInfos.getFirst().getKey());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //data is null
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/getData")
                                    .param("key", "unit-test.test"))
                            .andExpect(MockMvcResultMatchers.status().isNotFound())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("取得資料失敗")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void attachTest() {
        MockMultipartFile file1 = new MockMultipartFile("file", "單元測試主題背景1.jpg", "image/jpeg", ("hello1").getBytes());
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.multipart("/sys-info/add-file")
                                    .file(file1))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));

            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
            FileAttachResponseMessage<SysAttachVO> data = objectMapper.readValue(responseBody,
                    new TypeReference<FileAttachResponseMessage<SysAttachVO>>() {
                    });
            assert data != null && data.getData().getName().equals("單元測試主題背景1.jpg");
            String link = data.getData().getLink();
            this.links.add(link);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.get("/sys-info/" + links.getFirst()))
                            .andExpect(MockMvcResultMatchers.status().isOk());

            MockHttpServletResponse response = resultActions.andReturn().getResponse();
            assert response.getStatus() == HttpStatus.OK.value();
            assert StringUtils.contains(response.getHeader(HttpHeaders.CONTENT_DISPOSITION), "filename=\"" + URLEncoder.encode(file1.getOriginalFilename(), "UTF-8") + "\"");
            assert Objects.equals(response.getContentType(), file1.getContentType());
            assert Arrays.equals(response.getContentAsByteArray(), file1.getBytes());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //delete Test File
        String path = sysAttachService.findByLink(links.getFirst()).getPath();
        assert sysAttachService.getFile(path).delete();

        //file not found
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.get("/sys-info/" + links.getFirst()))
                            .andExpect(MockMvcResultMatchers.status().isNotFound());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void addFiles() {
        for (int i = 1; i < 3; i++) {
            MockMultipartFile file = new MockMultipartFile("file", "單元測試主題背景" + i + ".jpg", "image/ipg", ("hello" + i).getBytes());
            try {
                ResultActions resultActions =
                        mockMvc.perform(MockMvcRequestBuilders.multipart("/sys-info/add-file")
                                        .file(file))
                                .andExpect(MockMvcResultMatchers.status().isOk())
                                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));

                String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
                log.info(responseBody);
                FileAttachResponseMessage<SysAttachVO> data = objectMapper.readValue(responseBody,
                        new TypeReference<FileAttachResponseMessage<SysAttachVO>>() {
                        });
                assert data != null && data.getData().getName().equals("單元測試主題背景" + i + ".jpg");
                String link = data.getData().getLink();
                this.links.add(link);
                //delete Test File
                String path = sysAttachService.findByLink(link).getPath();
                assert sysAttachService.getFile(path).delete();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }
    }

    private void deleteFiles() {
        for (String link : this.links) {
            try {
                ResultActions resultActions =
                        mockMvc.perform(MockMvcRequestBuilders.delete("/sys-info/delete-file")
                                        .param("link", link))
                                .andExpect(MockMvcResultMatchers.status().isOk())
                                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
                String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
                log.info(responseBody);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void linkUnusedAttaches() {
        //無關聯的正式資料-先強制關聯
        List<SysAttachVO> attaches = sysAttachService.findAllUnusedAttach();
        SysInfoVO sysInfoVO = new SysInfoVO(null, "unit-test.temp", "避免刪除正式資料",
                "單元測試", null, null, attaches);
        SysInfoVO dbSysInfo = sysInfoService.save(sysInfoVO);
        assert dbSysInfo != null && dbSysInfo.getId() != null;
    }

    private void addSysInfos() {
        SysInfoRequest request = new SysInfoRequest();
        request.setKey("unit-test.theme");
        request.setValue("");
        request.setDescription("單元測試主題");
        try {
            ResultActions resultActions =
                    mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/add")
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(request)))
                            .andExpect(MockMvcResultMatchers.status().isOk())
                            .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
            String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
            log.info(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        SysInfoVO dbSysInfo = sysInfoService.findByKey("unit-test.theme");
        assert dbSysInfo != null && dbSysInfo.getId() != null;

        addFiles();
        for (int i = 1; i < 3; i++) {
            SysInfoRequest request2 = new SysInfoRequest();
            request2.setKey("unit-test.theme.test" + i);
            request2.setValue("/sys-info/" + this.links.get(i - 1));
            request2.setDescription("單元測試主題背景" + i);
            request2.setParentKey(dbSysInfo.getKey());
            request2.setSysAttachLinks(Collections.singletonList(this.links.get(i - 1)));
            try {
                ResultActions resultActions =
                        mockMvc.perform(MockMvcRequestBuilders.post("/sys-info/add")
                                        .contentType(MediaType.APPLICATION_JSON)
                                        .content(objectMapper.writeValueAsString(request2)))
                                .andExpect(MockMvcResultMatchers.status().isOk())
                                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
                String responseBody = resultActions.andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
                log.info(responseBody);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        List<SysInfoVO> list = sysInfoService.findAll(SysInfoFilter.builder().description("單元測試主題").build());
        this.sysInfos.addAll(list);
    }

}