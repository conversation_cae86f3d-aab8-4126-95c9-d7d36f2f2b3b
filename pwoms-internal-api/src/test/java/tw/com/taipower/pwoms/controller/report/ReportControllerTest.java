package tw.com.taipower.pwoms.controller.report;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletContext;
import lombok.extern.log4j.Log4j2;
import org.hamcrest.CoreMatchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import tw.com.taipower.pwoms.controller.vo.request.auth.TotpLoginRequest;
import tw.com.taipower.pwoms.controller.vo.request.report.ReportGenLoAppKwhRequest;
import tw.com.taipower.pwoms.service.InternalUserDetailsService;
import tw.com.taipower.pwoms.service.vo.MyUserPrincipal;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.account.AccountVo;
import tw.com.taipower.pwoms.services.vo.account.RoleVo;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;
import static org.springframework.security.web.context.HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

/**
 * @class: ReportControllerTest
 * @author: jingfungchen
 * @version:
 * @since: 2024-08-28 13:45
 * @see:
 **/
@Log4j2
//@ActiveProfiles("ae-s1")
@ActiveProfiles("mte-t1-test-s1")
//@ActiveProfiles("mte-t2-tpc")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
class ReportControllerTest {

    @Autowired
    private WebApplicationContext context;

    @Mock
    private ServletContext servletContext;

    @MockBean
    private InternalUserDetailsService internalUserDetailsService;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    RoleVo keepRole = RoleVo.builder().id(2).alias("").description("轉直供業務管理員").build();

    Long accountId = 46L;
    String accountName = "大象";
    String accountUsername = "asd1715";
    AccountVo accountVo;
    MyUserPrincipal principal;

    Authentication authentication = mock(Authentication.class);
    SecurityContext securityContext = mock(SecurityContext.class);

    String sessionId;
    MockHttpSession mockSession;

    String startI = "2025-05-01";//"2024-09-01";
    Date fDate = new GregorianCalendar(2024, Calendar.SEPTEMBER, 1).getTime();

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .build();
        sessionId = UUID.randomUUID().toString() + "-TEST"; // <- 可檢視 加入 notify 狀況

        List<RoleVo> roles = new ArrayList<>();
        roles.add(keepRole);
        accountVo = AccountVo.builder().id(accountId).name(accountName).username(accountUsername).roleList(roles)
                .active(true).password("sdjfhw").locked(false).useSso(false).build();
        principal = new MyUserPrincipal(accountVo);

        when(authentication.getPrincipal()).thenReturn(principal);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        mockSession = new MockHttpSession(this.servletContext, sessionId);
        mockSession.setAttribute(SPRING_SECURITY_CONTEXT_KEY, securityContext);
    }

    @AfterEach
    public void tearDown() {
        reset(servletContext);
        reset(internalUserDetailsService);
        reset(authentication);
        reset(securityContext);
    }

    /**
     * 因調整成 測試設計可直接輸入 totp 因此 使用此測試觀看
     * @throws Exception
     */
    @Test
    public void postTotpTest_always_pass() throws Exception {
        // for /auth/totp post api fail
        TotpLoginRequest totpLoginRequest = new TotpLoginRequest();
        totpLoginRequest.setTotp("217172");
        doReturn(true).when(internalUserDetailsService).isTOTPCodeValid(totpLoginRequest.getTotp(), accountId);

        String headerPageAndAction= "{\"moduleId\":,\"action\":}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/auth/totp")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(totpLoginRequest)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)));
        String resBody = response.andReturn().getResponse().getContentAsString();
        /*log.info("res: " + resBody);
        // aop inner -> controller.auth.authController.totpauthenticate() { 內部取 } + 沒有api權限判斷 == 2 times
        verify(authentication, times(3)).getPrincipal();
        verify(securityContext, times(3)).getAuthentication();*/
    }

    @Test
    public void downloadFile() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/{useFrom}/{useTo}/original-source #1 原始資料 帳單年月區間
        String startI = "2024-06-01";
        String endI = "2025-05-01";
        String start = outNameTailDateStr_twYMm(startI);
        String end = outNameTailDateStr_twYMm(endI);

        String headerPageAndAction = "{\"moduleId\": 32, \"action\": \"下載原始資料\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/"+startI+"/"+endI+"/original-source")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("原始資料_"+ start+"-"+end+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadFile_null_noContent() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/{useFrom}/{useTo}/original-source #1 原始資料 無資料
        String startI = "2024-05-01";
        String endI = "2024-07-01";

        String headerPageAndAction = "{\"moduleId\": 32, \"action\": \"原始資料\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/"+startI+"/"+endI+"/original-source")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isNotFound());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        //ori 2 + exception 1 + totp 3
        verify(authentication, times(6)).getPrincipal();
        verify(securityContext, times(6)).getAuthentication();
    }

    @Test
    public void downloadMonthlyEnergyRechargeTest_xlsx() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/xlsx/generator-match #3 外部業務處再購組 每月發電媒合度數 xlsx

        String tailName = ".xlsx";

        String headerPageAndAction = "{\"moduleId\": 66, \"action\": \"月轉供媒合度數excel\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/xlsx/generator-match")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // assertion
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        //String start = ExcelHelper.passYearMonthToString(fDate, "TW");
        String dtwYMm = DateUtils.passYearMonthToString(fDate, "twYMm");
        assertEquals("attachment; filename=" + URLEncoder.encode("每月轉直供度數_" + dtwYMm + tailName, "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));
        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadMonthlyEnergyRechargeTest_csv() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/csv/generator-match #4 外部業務處再購組 每月發電媒合度數 csv

        String tailName = ".csv";

        String headerPageAndAction = "{\"moduleId\": 66, \"action\": \"月轉供媒合度數csv\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/csv/generator-match")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // assertion
        assertEquals("application/csv", response.andReturn().getResponse().getContentType());
        String dtwYMm = DateUtils.passYearMonthToString(fDate, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        assertEquals("attachment; filename=" + URLEncoder.encode("TransRelat"+ dtwYMm +"_"+today+ tailName, "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadMonthly15MinEnergyRechargeTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/15-min-generator-match #7 外部 業務處再購組 15分鐘每月發電端度數.csv
        String tailName = ".csv";

        String headerPageAndAction = "{\"moduleId\": 66, \"action\": \"15分鐘月度數\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/15-min-generator-match")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // assertion
        assertEquals("application/csv", response.andReturn().getResponse().getContentType());
        String dtwYMm = DateUtils.passYearMonthToString(fDate, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        assertEquals("attachment; filename=" + URLEncoder.encode("TransRelatRaw"+ dtwYMm +"_"+today+ tailName, "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadMonthly2ThRematchEnergyRecharge() throws Exception {
        postTotpTest_always_pass();  // run totp
        // /report/download/external/month/{useFrom}/generator-rematch #8 外部 業務處再購組 每月發電端4時段轉供度數.csv

        String tailName = ".csv";

        String headerPageAndAction = "{\"moduleId\": 66, \"action\": \"月2Th再媒合度數\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/generator-rematch")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // assertion
        assertEquals("application/csv", response.andReturn().getResponse().getContentType());
        String dtwYMm = DateUtils.passYearMonthToString(fDate, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        assertEquals("attachment; filename=" + URLEncoder.encode("TransRelat2TH"+ dtwYMm +"_"+today+ tailName, "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadApplicationLoadMatchedRmByMonthRangeFileTest() throws Exception {
        postTotpTest_always_pass();  // run totp
        // /report/download/external/month/{useFrom}/{useTo}/load-match #11 轉直供用戶(用電端)各月資料 5~12月
        String startI = "2024-05-01";
        String endI = "2024-12-01";
        String start = outNameTailDateStr_twYMm(startI);
        String end = outNameTailDateStr_twYMm(endI);

        String headerPageAndAction = "{\"moduleId\": 67, \"action\": \"轉直供用戶之各月轉直供資料\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/"+endI+"/load-match")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("轉直供用戶之各月轉直供資料_"+ start+"-"+end+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));
        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadApplicationLoadMatchedRmByMonthRangeFileTest_month6_9() throws Exception {
        postTotpTest_always_pass();  // run totp
        // /report/download/external/month/{useFrom}/{useTo}/load-match #11 轉直供用戶(用電端)各月資料 8~12月
        String endI = "2024-12-01";
        String start = outNameTailDateStr_twYMm(startI);
        String end = outNameTailDateStr_twYMm(endI);

        String headerPageAndAction = "{\"moduleId\": 67, \"action\": \"轉直供用戶之各月轉直供資料\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/"+endI+"/load-match")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("轉直供用戶之各月轉直供資料_"+ start+"-"+end+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));
        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadMonthlyAmiSettlement15MinValueTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/15-min-value #5 15分鐘Ami讀值

        String tailName = ".csv";
        //startI = "2025-06-01";
        //fDate = new GregorianCalendar(2025, Calendar.JUNE, 1).getTime();

        String headerPageAndAction = "{\"moduleId\": 66, \"action\": \"15分鐘Ami月讀值\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/15-min-value")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // assertion
        assertEquals("application/csv", response.andReturn().getResponse().getContentType());
        String dtwYMm = DateUtils.passYearMonthToString(fDate, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        assertEquals("attachment; filename=" + URLEncoder.encode("Surp"+ dtwYMm +"_"+today+ tailName, "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadMonthlyAmiSettlement15MinValueTest_SEP() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/15-min-value #5 15分鐘Ami讀值

        String tailName = ".csv";
        //startI = "2025-06-01";
        //fDate = new GregorianCalendar(2025, Calendar.JUNE, 1).getTime();
        String headerPageAndAction = "{\"moduleId\": 66, \"action\": \"15分鐘Ami月讀值\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/15-min-value")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // assertion
        assertEquals("application/csv", response.andReturn().getResponse().getContentType());
        String dtwYMm = DateUtils.passYearMonthToString(fDate, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        assertEquals("attachment; filename=" + URLEncoder.encode("Surp"+ dtwYMm +"_"+today+ tailName, "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadMonthlyAmiSettlement15MinLimitCapacityTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/15-min-value-limit #6 15分鐘Ami讀值_已併網裝置容量

        String tailName = ".csv";

        String headerPageAndAction = "{\"moduleId\": 66, \"action\": \"15分鐘Ami月讀值_上限已併網裝置容量\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/15-min-value-limit")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // assertion
        assertEquals("application/csv", response.andReturn().getResponse().getContentType());
        String dtwYMm = DateUtils.passYearMonthToString(fDate, "twYMm");
        String today = DateUtils.passYearMonthToString(new Date(), "twYMmDd");
        assertEquals("attachment; filename=" + URLEncoder.encode("SurpLim"+ dtwYMm +"_"+today+ tailName, "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }


    @Test
    public void downloadGreenMatchedTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/{useFrom}/{useTo}/green-match 小額綠電契約4時段轉供度數 #9 外部 業務處再購組
        String startI = "2024-06-01";
        String endI = "2024-12-01";
        String start = outNameTailDateStr_twYMm(startI);
        String end = outNameTailDateStr_twYMm(endI);

        String headerPageAndAction = "{\"moduleId\": 66, \"action\": \"小額綠電契約4時段轉供度數\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/"+startI+"/"+endI+"/green-match")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("小額綠電契約4時段轉供度數_"+ start+"-"+end+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadGreenContractsTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/{useFrom}/{useTo}/green-contract 小額綠電契約報表 #10 外部 業務處再購組
        String startI = "2024-03-01";
        String endI = "2024-12-01";
        String start = outNameTailDateStr_twYMm(startI);
        String end = outNameTailDateStr_twYMm(endI);

        String headerPageAndAction = "{\"moduleId\": 66, \"action\": \"小額綠電契約報表\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/"+startI+"/"+endI+"/green-contract")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("小額綠電契約報表_"+ start+"-"+end+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadYearVoltLevelTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/{useFrom}/volt-level 各電壓層級度數報表 #12 外部 業務處費率組
        //startI = "2025-06-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 68, \"action\": \"各電壓層級度數報表\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/"+startI+"/volt-level")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("各電壓層級度數報表_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadYearVoltLevelTest_null_noContent() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/{useFrom}/volt-level 各電壓層級度數報表 #12 外部 業務處費率組 無資料
        String startI = "2024-04-01";

        String headerPageAndAction = "{\"moduleId\": 68, \"action\": \"各電壓層級度數報表\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/"+startI+"/volt-level")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isNotFound());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);

        //ori 2 + exception 1 + totp 3
        verify(authentication, times(6)).getPrincipal();
        verify(securityContext, times(6)).getAuthentication();
    }

    @Test
    public void downloadMonthExpRateTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/exp-rate 每月轉直供服務各類度數 #15 外部 調度處
        //startI = "2025-06-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 69, \"action\": \"轉直供服務各類度數\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/exp-rate")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("轉直供服務各類度數_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadGeneratorCompanyTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/generator-com 當月發電月報 #16 外部 調度處
        //startI = "2025-06-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 69, \"action\": \"當月發電月報\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/generator-com")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("當月發電月報_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadGeneratorLoadTpcLocationTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/gen-load-tpc 線損報告 #17 外部 調度處 帳單年月
        //startI = "2025-06-01";//"2024-11-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 69, \"action\": \"線損報告\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/gen-load-tpc")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("線損計算_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadGeneratorCompanyCapacityTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/gen-device-capacity 轉直供資料(高雄) 容量相關 #18 外部 調度處
        //startI = "2025-06-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 69, \"action\": \"轉直供資料(高雄)\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/gen-device-capacity")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("轉直供報表(高雄)_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadAmi15EndGenLoadPowerTest() throws Exception {
        postTotpTest_always_pass();
        // /report/download/external/month/{useFrom}/15-min-powers #19 外部 調度處
        //startI = "2025-06-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 69, \"action\": \"15分鐘發用電量報表\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/15-min-powers")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("15分鐘發用電量報表_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadAmi15EndGenLoadPowerTest_July() throws Exception {
        postTotpTest_always_pass();
        // /report/download/external/month/{useFrom}/15-min-powers #19 外部 調度處
        startI = "2024-07-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 69, \"action\": \"15分鐘發用電量報表\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/15-min-powers")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("15分鐘發用電量報表_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadAmi15PowerContractTest() throws Exception {
        postTotpTest_always_pass();
        // /report/download/external/month/{useFrom}/15-min-powers-contract #14 外部 調度處 發轉餘電表 無彈性分配
        //startI = "2025-06-01"; // tpcMark="2025-06-01":
        String start = outNameTailDateStr_twYMm(startI);
        String headerPageAndAction = "{\"moduleId\": 69, \"action\": \"發轉餘電表\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/15-min-power-contract")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("發轉餘電表_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadYearPwDsPowerTest() throws Exception {
        postTotpTest_always_pass();
        // /report/download/external/{useFrom}/year-pw-ds-power #13 外部 調度處 發轉餘總計 無彈性分配

        //startI = "2025-06-01"; // tpcMark="2025-06-01":

        String headerPageAndAction = "{\"moduleId\": 69, \"action\": \"發轉餘總計\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/"+startI+"/year-pw-ds-power")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("每月發轉餘總計.xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadMonthYearGenLoadPowerTest() throws Exception {
        postTotpTest_always_pass();
        // /report/download/external/{useFrom}/month-year-gen-load-power #22 外部 企劃處 電能轉直供資訊
        //startI = "2025-06-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 71, \"action\": \"電能轉直供資訊\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/"+startI+"/month-year-gen-load-power")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("電能轉直供資訊報表_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadAppStatusGenLoadElecNoCountTest() throws Exception {
        postTotpTest_always_pass();
        // /report/download/month/{useFrom}/app-gen-load-count #24 RFP2.7 月案件狀態統計
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 32, \"action\": \"每月案件狀態統計報表\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/month/"+startI+"/app-gen-load-count")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("每月案件狀態統計報表_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadGenLoadElecNoAppKwhInfoTest() throws Exception {
        postTotpTest_always_pass();
        // /report/download/month/{useFrom}/15-min-kwh-gen-load-app #25 RFP3.4 系統管理報表 每月發用電端統計報表
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 32, \"action\": \"每月發用電端統計報表\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ReportGenLoAppKwhRequest request = new ReportGenLoAppKwhRequest(null, null);
        ResultActions response = mockMvc.perform(get("/report/download/month/"+startI+"/15-min-kwh-gen-load-app")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("每月發用電端統計報表_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadMonthlyFuelTypeVoltKwhTest() throws Exception {
        postTotpTest_always_pass();
        // /report/download/external/month/{useFrom}/fuel-volt-kwh #23 外部 環保處 能源別發電報表.csv
        //startI = "2025-06-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 72, \"action\": \"能源別發電報表\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ReportGenLoAppKwhRequest request = new ReportGenLoAppKwhRequest(null, null);
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/fuel-volt-kwh")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/csv", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("能源別發電報表_"+ start+".csv", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void downloadADSTKwhIncomeTest() throws Exception {
        postTotpTest_always_pass(); // run totp
        // /report/download/external/month/{useFrom}/adst-kwh-income #2 外部 會計處 勞務收入轉直供度數
        //startI = "2025-06-01";//"2024-11-01";
        String start = outNameTailDateStr_twYMm(startI);

        String headerPageAndAction = "{\"moduleId\": 65, \"action\": \"勞務收入轉直供度數\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/report/download/external/month/"+startI+"/adst-kwh-income")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk());
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        assertEquals("application/vnd.ms-excel", response.andReturn().getResponse().getContentType());
        assertEquals("attachment; filename=" + URLEncoder.encode("勞務收入轉直供度數_"+ start+".xlsx", "UTF-8")
                , response.andReturn().getResponse().getHeaders("Content-Disposition").get(0));

        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void urlEncodeDecodeTest() {
        int year = 2024;
        int month = 7;
        String contractNo = "1-11110-20-007-00";
        String fileName = "契約" + contractNo + " 15分鐘原始發電量"  + (year-1911) + String.format("%02d", month) +".xlsx";
        String urlName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

        String res = URLDecoder.decode(urlName, StandardCharsets.UTF_8);
        log.info(res+", urlName:"+urlName);
        assertEquals(fileName, res);
    }

    private String outNameTailDateStr_twYMm(String startI) throws Exception {
        return DateUtils.passYearMonthToString(DateUtils.passStringToDate(startI), "twYMm");
    }

}