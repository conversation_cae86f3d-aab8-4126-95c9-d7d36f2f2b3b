package tw.com.taipower.pwoms.controller.Log;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletContext;
import lombok.extern.log4j.Log4j2;
import org.hamcrest.CoreMatchers;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import tw.com.taipower.pwoms.controller.vo.request.account.ChangePasswordRequset;
import tw.com.taipower.pwoms.controller.vo.request.auth.TotpLoginRequest;
import tw.com.taipower.pwoms.controller.vo.request.log.LogRequest;
import tw.com.taipower.pwoms.controller.vo.request.log.LogRequestHeader;
import tw.com.taipower.pwoms.notification.RedisMessageSubscriber;
import tw.com.taipower.pwoms.service.vo.MyUserPrincipal;
import tw.com.taipower.pwoms.services.filter.ApplicantEntityFilter;
import tw.com.taipower.pwoms.services.vo.account.AccountVo;
import tw.com.taipower.pwoms.services.vo.account.RoleVo;
import tw.com.taipower.pwoms.services.vo.log.LogVo;

import org.springframework.data.redis.connection.Message;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.web.context.HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

/**
 * @class: LogControllerTest
 * @author: jingfungchen
 * @version:
 * @since: 2024-06-03 15:07
 * @see:
 **/
@Log4j2
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@ActiveProfiles("ae-dev")
public class LogControllerTest {

    @Autowired
    private WebApplicationContext context;

    @Mock
    private ServletContext servletContext;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    RedisMessageSubscriber redisMessageSubscriber = mock(RedisMessageSubscriber.class);

    RoleVo keepRole = RoleVo.builder().id(2).alias("").description("轉直供業務管理員").build();

    Long accountId = 46L;
    String accountName = "大象";
    String accountUsername = "asd1715";
    AccountVo accountVo;
    MyUserPrincipal principal;

    Authentication authentication = mock(Authentication.class);
    SecurityContext securityContext = mock(SecurityContext.class);

    String sessionId;
    MockHttpSession mockSession;

    @BeforeEach
    public void setUp() {
        doNothing().when(redisMessageSubscriber).onMessage(any(Message.class), any(byte[].class));
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .build();
        sessionId = UUID.randomUUID().toString() + "-TEST"; // <- 可檢視 加入 notify 狀況

        List<RoleVo> roles = new ArrayList<>();
        roles.add(keepRole);
        accountVo = AccountVo.builder().id(accountId).name(accountName).username(accountUsername).roleList(roles)
                .active(true).password("sdjfhw").locked(false).useSso(false).build();
        principal = new MyUserPrincipal(accountVo);

        when(authentication.getPrincipal()).thenReturn(principal);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        mockSession = new MockHttpSession(this.servletContext, sessionId);
        mockSession.setAttribute(SPRING_SECURITY_CONTEXT_KEY, securityContext);
    }

    @AfterEach
    public void tearDown() {
        reset(securityContext);
        reset(authentication);
        reset(redisMessageSubscriber);
    }

    /**
     * 因調整成 測試設計可直接輸入 totp 因此 使用此測試觀看
     * @throws Exception
     */
    @Test
    public void postTotpTest_always_pass() throws Exception {
        // for /auth/totp post api fail
        TotpLoginRequest totpLoginRequest = new TotpLoginRequest();
        totpLoginRequest.setTotp("217172");

        String headerPageAndAction= "{\"moduleId\":,\"action\":}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/auth/totp")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(totpLoginRequest)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // aop inner -> controller.auth.authController.totpauthenticate() { 內部取 } + 沒有api權限判斷 == 2 times
        verify(authentication, times(3)).getPrincipal();
        verify(securityContext, times(3)).getAuthentication();
    }

    /*@Test
    public void postTotpTest_timeout_Fail() throws Exception {
        // for /auth/totp post api fail
        mockSession = new MockHttpSession(this.servletContext, UUID.randomUUID().toString()); // 需要排除 sessionId 測試狀況
        mockSession.setAttribute(SPRING_SECURITY_CONTEXT_KEY, securityContext);
        TotpLoginRequest totpLoginRequest = new TotpLoginRequest();
        totpLoginRequest.setTotp("217172");

        ResultActions response = mockMvc.perform(post("/auth/totp")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .content(objectMapper.writeValueAsString(totpLoginRequest)))
                .andExpect(MockMvcResultMatchers.status().isUnauthorized())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("EMPTY_2FA")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // aop inner -> controller.auth.authController.totpauthenticate() { 內部取 } -> aop direct (global exception) == 3 times
        verify(authentication, times(4)).getPrincipal();
        verify(securityContext, times(4)).getAuthentication();
    }*/

    @Test
    public void getDateRangePageListTest() throws Exception {
        postTotpTest_always_pass(); // run  totp
        String headerPageAndAction = "首頁>畫面跳轉>行動(更新)";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        Long sStart = new GregorianCalendar(2014, 5, 30).getTime().getTime();
        Long eEnd = System.currentTimeMillis();;
        LogRequest logRx = new LogRequest();
        logRx.setUsername(accountUsername);
        logRx.setName(accountName);
        logRx.setStartStamp(sStart);
        logRx.setEndStamp(eEnd);
        logRx.setPage(0);
        logRx.setPageSize(10);

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                .content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        log.info("eEnd:"+eEnd);
        JSONObject obj = new JSONObject(resBody);
        String data = obj.getString("data");
        obj = new JSONObject(data);
        String content = obj.getString("contents");
        assertTrue(content.length() > 0);
        List<LogVo> pageList = objectMapper.readValue(content, new TypeReference<List<LogVo>>(){});
        assertThat(pageList.get(0).getModifiedAt()).isGreaterThanOrEqualTo(pageList.get(1).getModifiedAt());
        // 自己 2次 +totp 3次
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void getDateRangePageListTest_useHeaderUrlEncode() throws Exception {
        postTotpTest_always_pass(); // run  totp
        //{
        //    "moduleId": 27,
        //    "action": "測試"
        //}
        String encodePAA = "%7B%0A%20%20%20%20%22moduleId%22%3A%2027%2C%0A%20%20%20%20%22action%22%3A%20%22%E6%B8%AC%E8%A9%A6%22%0A%7D";

        Long sStart = new GregorianCalendar(2014, 5, 30).getTime().getTime();
        Long eEnd = System.currentTimeMillis();;
        LogRequest logRx = new LogRequest();
        logRx.setUsername(accountUsername);
        logRx.setName(accountName);
        logRx.setStartStamp(sStart);
        logRx.setEndStamp(eEnd);
        logRx.setPage(0);
        logRx.setPageSize(10);

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        log.info("eEnd:"+eEnd);
        JSONObject obj = new JSONObject(resBody);
        String data = obj.getString("data");
        obj = new JSONObject(data);
        String content = obj.getString("contents");
        assertTrue(content.length() > 0);
        List<LogVo> pageList = objectMapper.readValue(content, new TypeReference<List<LogVo>>(){});
        assertThat(pageList.get(0).getModifiedAt()).isGreaterThanOrEqualTo(pageList.get(1).getModifiedAt());
        // 自己 2 次 + totp 3次
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void getDateRangePageListTest_useHeaderJsonString_toUrlEncode() throws Exception {
        postTotpTest_always_pass(); // run  totp
        LogRequestHeader logRequestHeader = new LogRequestHeader();
        logRequestHeader.setModuleId(27);
        logRequestHeader.setAction("載入");
        String headerPageAndAction = objectMapper.writeValueAsString(logRequestHeader);
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        Long sStart = new GregorianCalendar(2014, 5, 30).getTime().getTime();
        Long eEnd = System.currentTimeMillis();;
        LogRequest logRx = new LogRequest();
        logRx.setUsername(accountUsername);
        logRx.setName(accountName);
        logRx.setStartStamp(sStart);
        logRx.setEndStamp(eEnd);
        logRx.setPage(0);
        logRx.setPageSize(10);

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        log.info("eEnd:"+eEnd);
        JSONObject obj = new JSONObject(resBody);
        String data = obj.getString("data");
        obj = new JSONObject(data);
        String content = obj.getString("contents");
        assertTrue(content.length() > 0);
        List<LogVo> pageList = objectMapper.readValue(content, new TypeReference<List<LogVo>>(){});
        assertThat(pageList.get(0).getModifiedAt()).isGreaterThanOrEqualTo(pageList.get(1).getModifiedAt());
        // 自己 2次 +totp 3次
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void getDateRangePageListTest_noPageAndActionHeader() throws Exception {
        postTotpTest_always_pass(); // run  totp
        Long sStart = new GregorianCalendar(2014, 5, 30).getTime().getTime();
        Long eEnd = System.currentTimeMillis();;
        LogRequest logRx = new LogRequest();
        logRx.setUsername("JFGS");
        logRx.setName("法蘭基");
        logRx.setStartStamp(sStart);
        logRx.setEndStamp(eEnd);
        logRx.setPage(0);
        logRx.setPageSize(10);

        String headerPageAndAction="{\"moduleId\":,\"action\":驗證}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        log.info("eEnd:"+eEnd);
        JSONObject obj = new JSONObject(resBody);
        String data = obj.getString("data");
        obj = new JSONObject(data);
        String content = obj.getString("contents");
        assertTrue(content.length() > 0);
        List<LogVo> pageList = objectMapper.readValue(content, new TypeReference<List<LogVo>>(){});
        assertThat(pageList.get(0).getModifiedAt()).isGreaterThanOrEqualTo(pageList.get(1).getModifiedAt());
        // 自己 2 次 + totp 3次
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void getDateRangePageListTest_startStampZero_endStampZero_sizeZero() throws Exception {
        postTotpTest_always_pass(); // run  totp
        LogRequest logRx = new LogRequest();
        logRx.setStartStamp(0L);
        logRx.setEndStamp(0L);
        logRx.setPage(0);
        logRx.setPageSize(0);

        String headerPageAndAction= "{\"moduleId\":\"首頁\",\"action\":\"\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        JSONObject obj = new JSONObject(resBody);
        String data = obj.getString("data");
        obj = new JSONObject(data);
        String content = obj.getString("contents");
        assertTrue(content.length() > 0);
        List<LogVo> pageList = objectMapper.readValue(content, new TypeReference<List<LogVo>>(){});
        assertThat(pageList.get(0).getModifiedAt()).isGreaterThanOrEqualTo(pageList.get(1).getModifiedAt());
        // 自己 2 次 + totp 3次
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    /**
     * startStamp < endStamp 否則無法搜尋, 且 page 跟 size 不能為負
     * @throws Exception
     */
    @Test
    public void getDateRangePageListTest_startStampLessZero_endStampZero_sizeZero() throws Exception {
        postTotpTest_always_pass(); // run  totp
        LogRequest logRx = new LogRequest();
        logRx.setStartStamp(-5L);
        logRx.setEndStamp(0L);
        logRx.setPage(0);
        logRx.setPageSize(0);

        String headerPageAndAction= "{\"moduleId\":\"首頁\",\"action\":\"查詢\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        JSONObject obj = new JSONObject(resBody);
        String data = obj.getString("data");
        obj = new JSONObject(data);
        String content = obj.getString("contents");
        assertTrue(content.length() > 0);
        List<LogVo> pageList = objectMapper.readValue(content, new TypeReference<List<LogVo>>(){});
        assertThat(pageList.get(0).getModifiedAt()).isGreaterThanOrEqualTo(pageList.get(1).getModifiedAt());
        // ori = 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void getDateRangePageListTest_Username_nullName_startStampLessZero_endStampZero_sizeZero() throws Exception {
        postTotpTest_always_pass(); // run  totp
        LogRequest logRx = new LogRequest();
        logRx.setUsername("asd1715");
        logRx.setStartStamp(0L);
        logRx.setEndStamp(0L);
        logRx.setPage(0);
        logRx.setPageSize(0);

        String headerPageAndAction= "{\"moduleId\":,\"action\":\"測試\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        JSONObject obj = new JSONObject(resBody);
        String data = obj.getString("data");
        obj = new JSONObject(data);
        String content = obj.getString("contents");
        assertTrue(content.length() > 0);
        List<LogVo> pageList = objectMapper.readValue(content, new TypeReference<List<LogVo>>(){});
        if (!pageList.isEmpty()) {
            log.info("[modifiedAt]1:"+pageList.get(1).getModifiedAt()+",last:"+pageList.get(pageList.size()-1).getModifiedAt());
            assertNotEquals(pageList.get(1).getModifiedAt(), pageList.get(pageList.size() - 1).getModifiedAt()); //).isGreaterThanOrEqualTo(
        }
        // ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void getDateRangePageListTest_NullUsername_Name_startStampLessZero_endStampZero_sizeZero() throws Exception {
        postTotpTest_always_pass(); // run  totp
        LogRequest logRx = new LogRequest();
        logRx.setName("大象");
        logRx.setStartStamp(0L);
        logRx.setEndStamp(0L);
        logRx.setPage(0);
        logRx.setPageSize(0);

        String headerPageAndAction= "{moduleId>使用日誌查詢";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        JSONObject obj = new JSONObject(resBody);
        String data = obj.getString("data");
        obj = new JSONObject(data);
        String content = obj.getString("contents");
        assertTrue(content.length() > 0);
        List<LogVo> pageList = objectMapper.readValue(content, new TypeReference<List<LogVo>>(){});
        assertThat(pageList.get(0).getModifiedAt()).isGreaterThanOrEqualTo(pageList.get(1).getModifiedAt());
        // ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void getDateRangePageListTest_Fail_allNull() throws Exception {
        postTotpTest_always_pass(); // run  totp
        LogRequest logRx = new LogRequest();
        logRx.setStartStamp(null);
        logRx.setEndStamp(null);
        logRx.setPage(null);
        logRx.setPageSize(null);

        String headerPageAndAction= "{\"moduleId\":首頁,\"action\":\"查詢\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isBadRequest())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("startStamp not null")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("endStamp not null")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("page not null")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("pageSize not null")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void getDateRangePageListTest_Fail_UsernameNotMatchName() throws Exception {
        postTotpTest_always_pass(); // run  totp
        LogRequest logRx = new LogRequest();
        logRx.setUsername("ccc");
        logRx.setName("王東東");
        logRx.setStartStamp(0L);
        logRx.setEndStamp(0L);
        logRx.setPage(0);
        logRx.setPageSize(0);

        String headerPageAndAction = "{\"moduleId\":\"27\",\"action\":\"(登出)\"}";
        LogRequestHeader logHeadder = objectMapper.readValue(headerPageAndAction, LogRequestHeader.class);
        assertEquals(27, logHeadder.getModuleId());
        assertEquals("(登出)", logHeadder.getAction());
        int len = 4+"moduleId".length();
        String pageAndAction = headerPageAndAction.replace(" ", "");
        assertTrue((pageAndAction.split("\"").length  == 9 && pageAndAction.charAt(len) == '"'
                && Character.isDigit(pageAndAction.charAt(len+1))));

        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("Page-And-Action", encodePAA)
                        .session(mockSession).content(objectMapper.writeValueAsString(logRx)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // 查無資料 aop inner -> aop direct (global exception) == 2 times 1. aop 讀取 2. api權限判斷 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void getDateRangePageListTest_Fail_UsernameNotMatchName_haveDateRange() throws Exception {
        postTotpTest_always_pass(); // run  totp
        String headerPageAndAction= "{\"moduleId\":\"23\",\"action\":\"\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        String value = "{\"username\":\"test\",\"name\":\"\",\"startStamp\":\"1717586700000\",\"endStamp\":\"1718179859999\",\"page\":0,\"pageSize\":0}";

        ResultActions response = mockMvc.perform(post("/log/getDateRangePageList")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(value)) //objectMapper.writeValueAsString(logRx)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.containsString("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // 查無資料  aop inner -> aop direct (global exception) == 2 times 1. aop 讀取 2. api權限判斷 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void signout_withPageAndActionHeader() throws Exception {
        // /auth/signout get api
        String headerPageAndAction = "{\"moduleId\": 37, \"action\": (登出)}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");
        ResultActions response = mockMvc.perform(get("/auth/signout")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);

        verify(authentication, times(1)).getPrincipal();
        verify(securityContext, times(1)).getAuthentication();

    }

    @Test
    public void getEntityApplicantListTest_noModuleName_noPageAndActionHeader() throws Exception {
        // /entity/applicant/list GET api
        postTotpTest_always_pass(); // run  totp
        ApplicantEntityFilter applicantEntityFilter = new ApplicantEntityFilter();
        applicantEntityFilter.setPage(0);
        applicantEntityFilter.setPageSize(10);
        applicantEntityFilter.setTaxIdLike("00");

        String headerPageAndAction= "{\"moduleId\":\"\",\"action\":\"\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/entity/applicant/list")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(applicantEntityFilter)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void GeneratorEntityController_getByIdTest_withSessionInfo() throws Exception {
        // /entity/applicant/single/% GET api
        postTotpTest_always_pass(); // run  totp
        String headerPageAndAction= "{moduleId>驗證";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(get("/entity/generator/single/0000000000001")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("Page-And-Action", encodePAA).session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        //ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void SystemRoleController_getAllModuleListTest_withSessionInfo() throws Exception {
        // /auth/getAllModuleList GET api
        postTotpTest_always_pass(); // run  totp
        String headerPageAndAction = "{\"moduleId\":\"\",\"action\":載入}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(get("/auth/getAllModuleList")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("Page-And-Action", encodePAA).session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // ori 1 + totp 3
        verify(authentication, times(4)).getPrincipal();
        verify(securityContext, times(4)).getAuthentication();
    }

    @Test
    public void SystemAccountController_changePassword_withSessionInfo() throws Exception {
        // /system/account/changePassword POST
        postTotpTest_always_pass(); // run  totp
        ChangePasswordRequset changePassword = new ChangePasswordRequset();
        changePassword.setAccountId(accountId);
        changePassword.setPassword("test");


        String headerPageAndAction= "{\"moduleId\":\"\",\"action\":\"修改\"}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(post("/system/companyAccount/changePassword")
                        .contentType(MediaType.APPLICATION_JSON).session(mockSession)
                        .header("Page-And-Action", encodePAA)
                        .content(objectMapper.writeValueAsString(changePassword)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void LoadEntityController_findEntityMeterChildByCustomerNo() throws Exception {
        // /entity/load/findcustomer/3/distinct/meter
        postTotpTest_always_pass(); // run  totp
        String headerPageAndAction = "{\"moduleId\": 23, \"action\": 電號(查詢)}";
        String encodePAA = URLEncoder.encode(headerPageAndAction, "UTF-8");

        ResultActions response = mockMvc.perform(get("/entity/load/findcustomer/3/distinct/meter")
                        .contentType(MediaType.APPLICATION_JSON).header("Page-And-Action", encodePAA)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // ori 2 + totp 3
        verify(authentication, times(5)).getPrincipal();
        verify(securityContext, times(5)).getAuthentication();
    }

    @Test
    public void GeneratorEntityController_getFile_withSessionInfo() throws Exception {
        // /entity/generator/single/{id}/meter/{meterId}/vm post /entity/generator/single/9/meter/4/vm
        postTotpTest_always_pass(); // run  totp
        Resource fileResource = new ClassPathResource("log4j2-dev.yml");
        String originStartDate = "2024-02-03T10:15:30+01:00";
        String originEndDate = "2024-03-03T10:15:30+01:00";
        String startDate = "2024-04-03T10:15:30+01:00";
        String endDate = "2024-05-03T10:15:30+01:00";
        MockMultipartFile firstFile = new MockMultipartFile(
                "file",fileResource.getFilename(),
                MediaType.MULTIPART_FORM_DATA_VALUE,
                fileResource.getInputStream());
        ResultActions response = mockMvc.perform(multipart("/entity/generator/single/9/meter/4/vm")
                        .file(firstFile).session(mockSession).param("originStartDate", originStartDate)
                        .param("originEndDate", originEndDate).param("startDate", startDate)
                        .param("endDate", endDate))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // ori 3 + totp 3
        verify(authentication, times(6)).getPrincipal();
        verify(securityContext, times(6)).getAuthentication();
    }

    @Test
    public void ApplicantEntityController_addNewMeterVm_withSessionInfo() throws Exception {
        // /entity/applicant/single/{id}/document/{documentId}/upload post
        // /entity/applicant/single/1/document/5/upload
        postTotpTest_always_pass(); // run  totp
        Resource fileResource = new ClassPathResource("log4j2-dev.yml");
        String licenseNumber = "123456789";
        String validDate = "2024-07-03T10:15:30+01:00";
        MockMultipartFile firstFile = new MockMultipartFile(
                "file",fileResource.getFilename(),
                MediaType.MULTIPART_FORM_DATA_VALUE,
                fileResource.getInputStream());
        ResultActions response = mockMvc.perform(multipart("/entity/applicant/single/1/document/4/upload")
                        .file(firstFile).session(mockSession).param("licenseNumber", licenseNumber)
                        .param("validDate", validDate))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // ori 3 + totp 3
        verify(authentication, times(6)).getPrincipal();
        verify(securityContext, times(6)).getAuthentication();
    }

    @Test
    public void urlEncode_urlDecode_Test_irregularCase() throws UnsupportedEncodingException {

        String headerPageAndAction= "{\"moduleId\": 23, \"action\":{moduleId>驗證";

        LogRequestHeader lh = filterPageAndActionHeader(headerPageAndAction);
        assertNotNull(lh);

        headerPageAndAction= "{moduleId>驗證";
        lh = filterPageAndActionHeader(headerPageAndAction);
        assertNotNull(lh);

    }

    private LogRequestHeader filterPageAndActionHeader(String pageAndAction) {
        int len = 4+"moduleId".length();
        pageAndAction = pageAndAction.replace("\n", "").replace(" ", "");
        if (pageAndAction.equals("{moduleId>")) return new LogRequestHeader(null, null);
        if (pageAndAction.contains(">")) {
            String[] tt = pageAndAction.split(">");
            Integer mm = null;
            if (pageAndAction.contains(",")) {
                String tmp = pageAndAction.substring(len).split(",")[0];
                if (tmp.matches("\\d+")) mm = Integer.valueOf(tmp);
            }
            if (tt.length == 2) {
                if (tt[1].matches("\\d*"))
                    if (null == mm)
                        return new LogRequestHeader(Integer.valueOf(tt[1]), null);
                    else
                        return new LogRequestHeader(mm, null);
                else
                    return new LogRequestHeader(mm, tt[1]);
            }
        }
        return null;
    }

    @Test
    public void LoadEntityController_getFile_Fail() throws Exception {
        // get /entity/load/single/{id}/meter/{meterId}/vm/{useFrom}/{useTo}/
        // error message: Cannot invoke \"tw.com.taipower.pwoms.services.vo.generated.LoadEntityMeterVmVO.getContent()\" because \"result\" is null
        postTotpTest_always_pass(); // run  totp
        int id = 1;
        int meterId = 1;
        int useFrom = 20240627;
        int useTo = 20240629;
        ResultActions response = mockMvc.perform(get("/entity/load/single/"+id+"/meter/"+meterId+"/vm/"+useFrom+"/"+useTo+"/")
                        .contentType(MediaType.APPLICATION_JSON)
                        .session(mockSession))
                .andExpect(MockMvcResultMatchers.status().isInternalServerError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.pass", CoreMatchers.is(false)));
        //.andExpect(MockMvcResultMatchers.jsonPath("$.message", CoreMatchers.is("OK")));
        String resBody = response.andReturn().getResponse().getContentAsString();
        log.info("res: " + resBody);
        // ori 3 + totp 3
        verify(authentication, times(6)).getPrincipal();
        verify(securityContext, times(6)).getAuthentication();
    }
}
