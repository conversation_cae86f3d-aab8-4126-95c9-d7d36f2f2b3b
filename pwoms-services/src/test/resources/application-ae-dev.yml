chtami:
  baseUrl: ${CHTAMI_BASE_URL:http://13.219.102.74:8883/pwoms} 
  accessToken: ${CHTAMI_ACCESS_TOKEN:1234567890}
  fileDownloadUrl: ${CHTAMI_BASE_FILE_URL:http://13.219.102.74/}
tpc:
  sso:
    apUid: ap_uid_test
    apPassword: ap_uidd
    checkEndpointURL: http://***********:9090/SSO_WEBSERVICE/services/SSOPWDCHECK_X2?wsdl
    unlockEndpointURL: http://***********:9090/SSO_WEBSERVICE/services/SSOUNLOCK_X2?wsdl
  datasource:
    pwoms:
      url: **************************************************************************************
      username: pwomsadm
      password:


    ami:
      url: ************************************************************************************
      username: pwomsadm
      password: pdF0dN9U3y4PJatfucvV
  erd:
    sFTPKnownHost: ${SFTP_KNOWN_HOST:./known_hosts}
    sFTPRemoteDir: ${ERD_SFTP_REMOTE_DIR:/pwomsuser/upload/}
    sFTPHost: ${ERD_SFTP_HOST:***************}
    sFTPUsername: ${ERD_SFTP_USERNAME:pwomsuser}
    sFTPPassword: ${ERD_SFTP_PASSWORD:3478voijm;4g;..j}
    notifyRoleId: ${ERD_NOTIFY_ROLE:2}
  rems:
    nbs1URL: http://1localhost:5555/remsbill/Contract/PowerWheel/getDataNB
    nbs2URL: http://localhost:5555/remsbill/Contract/PowerWheel/getDataNB
    nbs3URL: http://localhost:5555/remsbill/Contract/PowerWheel/getDataNB
    rnis1URL: http://localhost:5555/remsbill/Contract/PowerWheel/getDataRN
    rnis2URL: http://localhost:5555/remsbill/Contract/PowerWheel/getDataRN
    rnis3URL: http://localhost:5555/remsbill/Contract/PowerWheel/getDataRN
    SFTPKnownHost: ${SFTP_KNOWN_HOST}
    rnisSFTPRemoteDir: ${RNIS_SFTP_REMOTE_DIR}
    rnisSFTPHost: ${RNIS_SFTP_HOST}
    rnisSFTPUsername: ${RNIS_SFTP_USERNAME}
    rnisSFTPPassword: ${RNIS_SFTP_PASSWORD}
    nbsSFTPRemoteDir: ${NBS_SFTP_REMOTE_DIR}
    nbsSFTPHost: ${NBS_SFTP_HOST}
    nbsSFTPUsername: ${NBS_SFTP_USERNAME}
    nbsSFTPPassword: ${NBS_SFTP_PASSWORD}
    rnisFTPNotifyRole: ${RNIS_FTP_NOTIFY_ROLE:2}

  jpa:
    pwoms:
      properties:
        hibernate:
          format_sql: false
          show_sql: false
          jdbc:
            batch_size: 200
            order_inserts: true
    ami:
      properties:
        hibernate:
          format_sql: false
          show_sql: false
          jdbc:
            batch_size: 200
            order_inserts: true

settlement:
  test-flag: true
  file-attach:
    download: ~/pwoms/download/
    faq: ~/pwoms/faq/
    news: ~/pwoms/news/
    video: ~/pwoms/video/
    sys-info: ~/pwoms/sys-info/
    filename-strategy: monthly

spring:
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB

erp:
  #url: https://stpcs4qap.taipower.com.tw:54300/sap/bc/srt/wsdl/flv_10002A111AD1/bndg_url/sap/bc/srt/rfc/sap/Z_FI_EI_CUSTOMER_IRT/600/Z_FI_EI_CUSTOMER_IRT/Z_FI_EI_CUSTOMER_IRT?sap-client=600
  url: https://localhost:8443/ws/employees.wsdl
  username: RFCUSER
  password: Stpcs4qap@
