package tw.com.taipower.pwoms.services.report;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.reactive.context.GenericReactiveWebApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.pwoms.services.AbstractServiceTest;
import tw.com.taipower.pwoms.services.config.TpcRemsConfig;
import tw.com.taipower.pwoms.services.tpc.RemsService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.report.BSMIInputInfoVo;
import tw.com.taipower.pwoms.services.vo.report.ReportContractSettleOVo;
import tw.com.taipower.pwoms.services.vo.report.ReportContractSettlesMapDateListVo;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.nio.file.*;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static tw.com.taipower.pwoms.services.constant.Constants.TMP_FOLDER;

/**
 * @class: RoutineReportServiceTest
 * @author: jingfungchen
 * @version:
 * @since: 2025-03-07 11:21
 * @see:
 **/
@Log4j2
//@ActiveProfiles("ae-s1")
@ActiveProfiles("mte-t1-test-s1")
//@ActiveProfiles("mte-test-s1")
//@ActiveProfiles("ae-dev")
public class RoutineReportServiceTest extends AbstractServiceTest {

    @Autowired
    RoutineReportService service;

    @Autowired
    ReportService reportService;

    @Autowired
    CsvService csvService;

    @Autowired
    TpcRemsConfig config;

    @Autowired
    RemsService remsService;

    @Autowired
    private ResourceLoader resourceLoader = null;

    boolean devMark = false;
    boolean mteS1Mark = false;
    boolean s1Mark = false;
    boolean mteT1S1Mark = false;
    boolean modeMark = false;

    @BeforeEach
    public void setUp() {
        devMark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("ae-dev");
        mteS1Mark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-test-s1");
        s1Mark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("ae-s1");
        mteT1S1Mark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t1-test-s1");
        //modeMark = mteT1S1Mark;//mteS1Mark || s1Mark;
    }

    @Test
    public void syncBSMIInputFromSftpTest_noFolder() {
        String fakeFile = "test.csv";
        String wrongRemoteFolder = "11312";
        Exception exception = assertThrows(RuntimeException.class, () -> {
            remsService.syncBSMIInputFromSftp(TMP_FOLDER, wrongRemoteFolder, fakeFile);
        });
        assertEquals("java.lang.RuntimeException: no remote inner folder:"+wrongRemoteFolder, exception.getMessage());
    }

    @Test
    public void syncBSMIInputFromSftpTest_noFile() {
        String fakeFile = "test.csv";
        String remoteInnerDir = DateUtils.passYearMonthToString(new Date(), "twYMm")+"_BSMI";
        Exception exception = assertThrows(RuntimeException.class, () -> {
            remsService.syncBSMIInputFromSftp(TMP_FOLDER, remoteInnerDir, fakeFile);
        });
        assertEquals("java.lang.RuntimeException: no download file:"+fakeFile, exception.getMessage());
    }

    @Test
    @Order(1)
    public void cleanTmpFolder() throws Exception {
        // 第一個 filename 輸入不會出現的字串 就會直接刪掉 tailName關鍵字串檔案
        ByteArrayInputStream bb = csvService.tmpFolderFile("-s", "TEST", true);
        assertNull(bb);
    }
    /* BSMI 內容檢視
        // List<String> emptyBSMI = new ArrayList<>();
        // List<String> insideBSMI = new ArrayList<>();
        // String emptyCsvOut = new String(csvService.getBSMIInputOrOutput("標檢局下載檔頭.csv"));
        // List<String> emptyBSMIFile = new ArrayList<>();
        // for (int i = 0; i < out.getContractDateNames().size(); i++) {
        //     String name = out.getContractDateNames().get(i);
        //     List<String> oo = reportService.getOneBSMICSVString(name, out.getFilesContent());
        //     String filename = name.split("~")[0];
        //     if (null != oo && !oo.isEmpty()) {
        //         ByteArrayInputStream res = csvService.buildCsvFile(oo, folder+filename, "ContractSettleKwhCost");
        //         byte[] resArray = res.readAllBytes();
        //         assertNotNull(resArray);
        //         insideBSMI.add(filename);
        //     }
        //     else {
        //         emptyBSMI.add(filename);
        //         emptyBSMIFile.add(emptyCsvOut);
        //     }
        // }
        // log.info("[BSMI out empty filename list"+emptyBSMI+", size:"+emptyBSMI.size());
        // assertEquals(emptyBSMIFile.size(), emptyBSMI.size());
        // assertTrue(emptyBSMI.size()>insideBSMI.size());
        // String tail = "月份";
        // List<String> files = listFilesUsingDirectoryStream(folder, tail);
        // log.info("[BSMI out name]"+files+",size:"+files.size());
        // assertEquals(files.size(), insideBSMI.size());
        // List<String> tmpBSMIFolderFiles = csvService.listOrDeleteTmpFolder("_BSMI", tail, true);
        // assertEquals(tmpBSMIFolderFiles.size(), insideBSMI.size());
        // Collections.sort(insideBSMI);
        // Collections.sort(tmpBSMIFolderFiles);
        // assertEquals(tmpBSMIFolderFiles, insideBSMI);
     */

    @Test
    @Order(2)
    public void getContractVersionSettlesTest_s1_ori_11405BSMI() throws Exception {
        String localName = prepareBSMIInput("11405-BSMI-s.csv"); //"11402-BSMI.csv"
        String fullName = putBSMIFilesToSftp_from_local_BSMIInput_then_Download(localName);
        String[] pp = fullName.split("/");
        String folder = pp[0]+"/"+pp[1]+"/"+pp[2]+"/";

        BSMIInputInfoVo in = csvService.readBSMICsvInFileToMap(fullName);//log.info("[in]"+in);
        ReportContractSettlesMapDateListVo out = reportService.getContractVersionSettlesToBSMIOut(in);
        //log.info("BSMI output:"+out);
        Set<String> genMeter = new HashSet<>();
        List<String> genMeterList = new ArrayList<>();
        Set<String> custMeter = new HashSet<>();
        List<String> custMeterList = new ArrayList<>();
        List<ReportContractSettleOVo> outData = out.getFilesContent();
        for (int i =0 ; i < outData.size(); i++) {
            ReportContractSettleOVo m = outData.get(i);
            if (m.getCustElecNo().equals("04946961150") && outData.get(i+1).getCustElecNo().equals("04946961150")) {
                if (custMeter.add(m.getCustMeterNo())) {
                    custMeterList.add("04946961150~"+m.getCustMeterNo());
                    custMeterList.add("04946961150~"+outData.get(i+1).getCustMeterNo());
                }
            } else if (m.getCustElecNo().equals("10513336959") && outData.get(i+1).getCustElecNo().equals("10513336959")) {
                if (custMeter.add(m.getCustMeterNo())) {
                    custMeterList.add("10513336959~"+m.getCustMeterNo());
                    custMeterList.add("10513336959~"+outData.get(i+1).getCustMeterNo());
                }
            } else if (m.getCustElecNo().equals("16455276118") && outData.get(i+1).getCustElecNo().equals("16455276118")) {
                if (custMeter.add(m.getCustMeterNo())) {
                    custMeterList.add("16455276118~"+m.getCustMeterNo());
                    custMeterList.add("16455276118~"+outData.get(i+1).getCustMeterNo());
                }
            } else if (m.getGenElecNo().equals("18857150021") && outData.get(i+1).getGenElecNo().equals("18857150021")) {
                if (genMeter.add(m.getGenMeterNo())) {
                    genMeterList.add("18857150021~"+m.getGenMeterNo());
                    genMeterList.add("18857150021~"+outData.get(i+1).getGenMeterNo());
                }
            }
        }
        //log.info("chage meter custMeter:"+custMeterList+", genMeter:"+genMeterList);
        assertEquals(2, genMeterList.size());
        assertEquals("18857150021~TU19619813", genMeterList.get(0));
        assertEquals("18857150021~KU23702412", genMeterList.get(genMeterList.size()-1));
        assertEquals(6, custMeterList.size());
        assertEquals("04946961150~GB11932004", custMeterList.get(0));
        assertEquals("04946961150~TB20853525", custMeterList.get(1));
        assertEquals("10513336959~WA16014081", custMeterList.get(2)); // 2 跟 3 電號相同 但先找到 W表號 表示 表號不一定根據 字母順序排列
        assertEquals("10513336959~TA20854367", custMeterList.get(3));
        assertEquals("16455276118~GB11932820", custMeterList.get(4));
        assertEquals("16455276118~GB16930876", custMeterList.get(custMeterList.size()-1));

        List<String> outName = out.getContractDateNames();
        for (int i=0; i< outName.size(); i++) {
            String m = outName.get(i);
            if (m.contains("1-11211-10-00400 114_05月份.csv")) {
                assertEquals("93~94~95~96~97~98~99~100~101~102", m.split(".csv~")[1]); // 185~186~187~188~189~190~191~192~193~194
                //log.info("distinct-seq:"+m.split(".csv~")[1]);
            } else if (m.contains("1-11106-12-00300 114_04月份.csv")) {
                assertEquals("142~143~144~145~146~147~148~149~150~151~152~153~154~155~156~157~158~159~160~161~162~163~164~165~166~167~168~169~170~171~172~173~174~175", m.split(".csv~")[1]);
                // 265~266~267~268~269~270~271~272~273~274~275~276~277~278~279~280~281~282~283~284~285~286~287~288~289~290~291~292~293~294~295~296~297~298
            }
        }

        if (s1Mark || mteS1Mark) assertNull(out);
        else {
            String insideCount = service.BSMIUOutUpload(out, folder, pp[2]);
            if (devMark) assertEquals("1", insideCount);
            else {
                assertEquals("8", insideCount);
                String filename = "TEST-1-11211-10-00400 114_05月份.csv"; //"TEST-1-11106-12-00300 114_04月份.csv";//
                //String localDir = TMP_FOLDER;
                boolean res = remsService.syncBSMIInputFromSftp(TMP_FOLDER, pp[2], filename);
                assertTrue(res);
                //log.info("[sftp download a file]"+filename);
                int size = 0;
                for (Map.Entry<Integer, Set<String>> entry: in.getDateOriContractMap().entrySet()) {
                    size += entry.getValue().size();
                }
                assertEquals(size, out.getContractDateNames().size()); // 296
                String tmpCsvCheckFileName = folder+filename.replace("TEST-", "");
                byte[] rawO = Files.readAllBytes(Paths.get(TMP_FOLDER+filename));
                byte[] rawI = Files.readAllBytes(Paths.get(tmpCsvCheckFileName));
                //byte[] csvBSMIEmptyOutputHeaderO = csvService.getBSMIInputOrOutput(TMP_FOLDER+filename);
                //byte[] csvBSMIEmptyOutputHeaderI = csvService.getBSMIInputOrOutput(folder+filename.replace("TEST-", ""));
                assertEquals(rawO.length, rawI.length);
                assertEquals(new String(rawO), new String(rawI));
            }
            log.info("[sftp upload all files]");
        }
    }

    @Test
    @Order(3)
    public void getContractVersionSettlesTest_s1_11404BSMIs_process() throws Exception {
        String localName = prepareBSMIInput("11404-BSMI-s.csv"); //"11402-BSMI.csv"
        String fullName = putBSMIFilesToSftp_from_local_BSMIInput_then_Download(localName);
        String[] pp = fullName.split("/");
        String folder = pp[0]+"/"+pp[1]+"/"+pp[2]+"/";

        String insideCount = service.BSMIDownloadUploadProcess();
        if (s1Mark || mteS1Mark) assertEquals("沒有對應結帳資料", insideCount);
        else {
            if (devMark) assertEquals("1", insideCount);
            else {
                assertEquals("18", insideCount);
                log.info("[sftp upload all files]");
                String filename = "TEST-1-11206-20-00400 114_04月份.csv";
                //String localDir = TMP_FOLDER;
                boolean res = remsService.syncBSMIInputFromSftp(TMP_FOLDER, pp[2], filename);
                assertTrue(res);
                //log.info("[sftp download a file]"+filename);
                String tmpCsvCheckFileName = folder+filename.replace("TEST-", "");
                byte[] csvBSMIEmptyOutputHeaderO = csvService.getBSMIInputOrOutput(TMP_FOLDER+filename);
                byte[] csvBSMIEmptyOutputHeaderI = csvService.getBSMIInputOrOutput(tmpCsvCheckFileName);
                assertEquals(csvBSMIEmptyOutputHeaderO.length, csvBSMIEmptyOutputHeaderI.length);
                assertEquals(new String(csvBSMIEmptyOutputHeaderO), new String(csvBSMIEmptyOutputHeaderI));
            }
        }
    }

    @Test
    public void getContractVersionSettlesTest_s1_ori_11404BSMI() throws Exception {
        String localName = prepareBSMIInput("11404-BSMI-s.csv"); //"11402-BSMI.csv"
        String fullName = putBSMIFilesToSftp_from_local_BSMIInput_then_Download(localName);
        String[] pp = fullName.split("/");
        String folder = pp[0]+"/"+pp[1]+"/"+pp[2]+"/";

        //BSMIInputInfoVo in = csvService.readBSMICsvInFileToMap(fullName);//log.info("[in]"+in);
        //ReportContractSettlesMapDateListVo out = reportService.getContractVersionSettlesToBSMIOut(in); //log.info("BSMI output"+out);
        ReportContractSettlesMapDateListVo out = service.prepareBSMIOutInfo(fullName);
        if (s1Mark || mteS1Mark) assertNull(out);
        else {
            List<String> emptyBSMI = new ArrayList<>();
            List<String> insideBSMI = new ArrayList<>();
            String emptyCsvOut = new String(csvService.getBSMIInputOrOutput("標檢局下載檔頭.csv"));
            List<String> emptyBSMIFile = new ArrayList<>();
            for (int i = 0; i < out.getContractDateNames().size(); i++) {
                String name = out.getContractDateNames().get(i);
                List<String> oo = reportService.getOneBSMICSVString(name, out.getFilesContent());
                String filename = name.split("~")[0];
                if (null != oo && !oo.isEmpty()) {
                    ByteArrayInputStream res = csvService.buildCsvFile(oo, folder+filename, "ContractSettleKwhCost");
                    byte[] resArray = res.readAllBytes();
                    assertNotNull(resArray);
                    insideBSMI.add(filename);
                }
                else {
                    emptyBSMI.add(filename);
                    emptyBSMIFile.add(emptyCsvOut);
                }
            }
            //log.info("[BSMI out empty filename list"+emptyBSMI+", size:"+emptyBSMI.size());
            assertEquals(emptyBSMIFile.size(), emptyBSMI.size());
            assertTrue(emptyBSMI.size()>insideBSMI.size());
            String tail = "月份";
            List<String> files = listFilesUsingDirectoryStream(folder, tail);
            //log.info("[BSMI out name]"+files+",size:"+files.size());
            assertEquals(files.size(), insideBSMI.size());
            List<String> tmpBSMIFolderFiles = csvService.listOrDeleteTmpFolder("_BSMI", tail, true);
            assertEquals(tmpBSMIFolderFiles.size(), insideBSMI.size());
            Collections.sort(insideBSMI);
            Collections.sort(tmpBSMIFolderFiles);
            assertEquals(tmpBSMIFolderFiles, insideBSMI);
            if (devMark) assertEquals(1, insideBSMI.size());
            else assertEquals(18, insideBSMI.size());
        }
    }

    /*@Test
    public void getContractVersionSettlesTest_s1_ori_11307BSMI() throws Exception {
        String localName = prepareBSMIInput("11311-BSMI.csv"); //"11402-BSMI.csv"
        String fullName = putBSMIFilesToSftp_from_local_BSMIInput_then_Download(localName);
        String[] pp = fullName.split("/");
        String folder = pp[0]+"/"+pp[1]+"/"+pp[2]+"/";

        BSMIInputInfoVo in = csvService.readBSMICsvInFileToMap(fullName);//log.info("[in]"+in);
        ReportContractSettlesMapDateListVo out = reportService.getContractVersionSettlesToBSMIOut(in);
        log.info("BSMI output:"+out);
        List<String> emptyBSMI = new ArrayList<>();
        List<String> insideBSMI = new ArrayList<>();
        String emptyCsvOut = new String(csvService.getBSMIInputOrOutput("標檢局下載檔頭.csv"));
        List<String> emptyBSMIFile = new ArrayList<>();
        for (int i = 0; i < out.getContractDateNames().size(); i++) {
            String name = out.getContractDateNames().get(i);
            List<String> oo = reportService.getOneBSMICSVString(name, out.getFilesContent());
            String filename = name.split("~")[0];
            if (null != oo && !oo.isEmpty()) {
                ByteArrayInputStream res = csvService.buildCsvFile(oo, folder+filename, "ContractSettleKwhCost");
                byte[] resArray = res.readAllBytes();
                assertNotNull(resArray);
                insideBSMI.add(filename);
            }
            else {
                emptyBSMI.add(filename);
                emptyBSMIFile.add(emptyCsvOut);
            }
        }
        log.info("[BSMI out empty filename list"+emptyBSMI+", size:"+emptyBSMI.size());
        assertEquals(emptyBSMIFile.size(), emptyBSMI.size());
        assertTrue(emptyBSMI.size()>insideBSMI.size());
        String tail = "月份";
        List<String> files = listFilesUsingDirectoryStream(folder, tail);
        log.info("[BSMI out name]"+files+",size:"+files.size());
        assertEquals(files.size(), insideBSMI.size());
        List<String> tmpBSMIFolderFiles = csvService.listOrDeleteTmpFolder("_BSMI", tail, true);
        assertEquals(tmpBSMIFolderFiles.size(), insideBSMI.size());
        Collections.sort(insideBSMI);
        Collections.sort(tmpBSMIFolderFiles);
        assertEquals(tmpBSMIFolderFiles, insideBSMI);
        assertEquals(10, insideBSMI.size());
        assertEquals("2-11112-18-00100 113_11月份.csv", insideBSMI.get(insideBSMI.size()-2));
        assertEquals("4-11208-04-00200 113_11月份.csv", insideBSMI.get(insideBSMI.size()-1));
    }*/

    @Test
    public void prepareBSMIFolderTest() throws Exception {
        String outName = csvService.prepareBSMIFolder();
        List<String> files = csvService.listOrDeleteTmpFolder("_BSMI", null, true); // false 刪除 檔案

        // assertion
        assertTrue(files.isEmpty());
        String tHead = DateUtils.passYearMonthToString(new Date(), "twYMm");
        assertEquals(TMP_FOLDER + tHead + "_BSMI/"+tHead+"-BSMI.csv", outName);
    }

    @Test
    public void compareDirectMoveTmpFolder_sFtpDownFileName() throws Exception {
        String localName = prepareBSMIInput("11402-BSMI-s.csv");
        String fullName = putBSMIFilesToSftp_from_local_BSMIInput_then_Download(localName);
        String localDirName = service.downloadBSMIin();
        assertEquals(fullName.replace("-s", ""), localDirName);

        localName = prepareBSMIInput("11401-BSMI.csv");
        fullName = putBSMIFilesToSftp_from_local_BSMIInput_then_Download(localName);
        localDirName = service.downloadBSMIin();
        assertEquals(fullName, localDirName);
    }

    @Test
    public void prepareBSMIInputFromLocalToSftpTest() throws Exception{
        String path = "/Users/<USER>/Downloads/報表查詢/標檢局/提供標檢局轉供量資料/BSMI_in/";
        String localCsv = "11402-BSMI.csv";
        String remoteBSMIInFilename = service.prepareBSMIInputFromLocalToSftp(localCsv, path);
        String bSMIinFilename = service.downloadBSMIin();
        assertTrue(bSMIinFilename.contains(remoteBSMIInFilename));
        File bS = new File(bSMIinFilename);
        File lS = new File(path+localCsv);
        assertEquals(bS.length(), lS.length());
    }

    private String putBSMIFilesToSftp_from_local_BSMIInput_then_Download(String fullName) throws Exception {
        String[] cut = fullName.split("/");
        String remoteInnerFolder = cut[cut.length-2];
        String remoteBSMIInFilename = cut[cut.length-1];
        byte[] csvBSMIInput = csvService.getBSMIInputOrOutput(fullName);
        String content = new String(csvBSMIInput);
        boolean res = remsService.putBSMIFilesToSftp(List.of(content), List.of(remoteBSMIInFilename), remoteInnerFolder, false);
        if (!res) {
            res = remsService.putBSMIFilesToSftp(List.of(content), List.of(remoteBSMIInFilename), remoteInnerFolder, true);
            assertTrue(res);
            log.info("[after check make remote folder then upload again]");
        } else {
            assertTrue(res);
            log.info("[just upload again]");
        }
        String localDirName = service.downloadBSMIin();
        String[] part = localDirName.split("/");
        assertEquals(remoteBSMIInFilename, part[part.length-1]);
        List<String> tmpBSMIFolderFiles = csvService.listOrDeleteTmpFolder("_BSMI", "BSMI", true);
        assertEquals(1, tmpBSMIFolderFiles.size());
        assertEquals(remoteBSMIInFilename, tmpBSMIFolderFiles.get(0));
        assertEquals(fullName, localDirName);
        return localDirName;
    }

    private String prepareBSMIInput(String name) throws Exception { // name = 準備搬移測試的檔名
        // 刪除 BSMI 資料夾 //String dir = "_BSMI";//String tail = "月份";
        List<String> files = csvService.listOrDeleteTmpFolder("_BSMI", null, false); // false 刪除 檔案
        assertTrue(files.isEmpty());
        // mvBSMIInoutFileToTmpFolder(name);
        String path = "/Users/<USER>/Downloads/報表查詢/標檢局/提供標檢局轉供量資料/BSMI_in/";
        Path ori = Paths.get(path+name);
        String targetDirName = csvService.prepareBSMIFolder();
        //String[] part = targetDirName.split("/");
        //String tHead = part[part.length-1].replace("-BSMI.csv","");
        //name = tHead+"-BSMI.csv";
        Path tmp = Paths.get(targetDirName);
        Files.copy(ori, tmp, StandardCopyOption.REPLACE_EXISTING);

        assertThat(ori).exists();
        assertEquals(Files.readAllLines(ori), Files.readAllLines(tmp));

        return targetDirName;
    }

    private List<String> listFilesUsingDirectoryStream(String dir, String tail) throws Exception {
        List<String> fileList = new ArrayList<>();
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(Paths.get(dir))) {
            for (Path path : stream) {
                if (!Files.isDirectory(path)) {
                    if (path.getFileName().toString().contains(tail))
                        fileList.add(path.getFileName().toString());
                    else log.info("[not tail] file name:"+path.getFileName());
                }
            }
        }
        return fileList;
    }
}