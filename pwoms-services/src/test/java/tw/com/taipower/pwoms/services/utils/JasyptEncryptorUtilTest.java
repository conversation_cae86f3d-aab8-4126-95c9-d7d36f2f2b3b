package tw.com.taipower.pwoms.services.utils;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.pwoms.services.AbstractServiceTest;

import static org.junit.jupiter.api.Assertions.*;
/**
 * @class: JasyptEncryptorUtilTest
 * @author: jingfungchen
 * @version:
 * @since: 2025-06-18 15:48
 * @see:
 **/
@Log4j2
@ActiveProfiles("ae-dev")
class JasyptEncryptorUtilTest extends AbstractServiceTest {

    @Test
    void encrypt_decryptTest() {
        String enhanceStr = "standardCenter"; // #21 標檢局(BSMI Standard Center)
        String info = "w9sxG7yNcy";
        String en = JasyptEncryptorUtil.encrypt(info+"-"+enhanceStr, enhanceStr);
        //log.info("[en]"+en); l4rG/cjBFbJp4dQO5a4Zt1UeaheRIsmx1sfxTozItvmhf6mzIOn45BkvVBo3Y8vJDJaHbPyx/Av4O6NvVgMbdg==
        String de = JasyptEncryptorUtil.decrypt(en, enhanceStr);
        assertEquals(info, de.split("-")[0]);

        enhanceStr = "powerDispatchCenter"; // #13 #14 #15 #16 #17 #18 #19 #2 #12 調度處(PDC Power Dispatch Center powerDispatchCenter) sftp
        info = "tpcsftp";
        en = JasyptEncryptorUtil.encrypt(info+"-"+enhanceStr, enhanceStr);
        //log.info("[en]"+en); Y1l92h6EgeuvhkowJ86AtvYnJeoYcguNJ5CYfNwJj2OaTjnBy76dUezxdRjs4oB4+DZ45L/OC1J9GXPL5cI5pA==
        de = JasyptEncryptorUtil.decrypt(en, enhanceStr);
        assertEquals(info, de.split("-")[0]);

        info = "kcdccpv"; // #18 調度處 高雄 zip
        enhanceStr = "getGenDeviceCapacities";
        en = JasyptEncryptorUtil.encrypt(info+"-"+enhanceStr, enhanceStr);
        de = JasyptEncryptorUtil.decrypt(en, enhanceStr);
        assertEquals(info, de.split("-")[0]);
    }
}