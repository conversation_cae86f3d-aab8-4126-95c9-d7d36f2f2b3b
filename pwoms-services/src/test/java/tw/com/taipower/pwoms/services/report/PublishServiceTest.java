package tw.com.taipower.pwoms.services.report;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.reactive.context.GenericReactiveWebApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.pwoms.services.AbstractServiceTest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static tw.com.taipower.pwoms.services.constant.Constants.TMP_SFTP_FOLDER;

@Log4j2
//@ActiveProfiles("ae-dev")
@ActiveProfiles("mte-t1-test-s1")
//@ActiveProfiles("mte-t2-tpc")
class PublishServiceTest extends AbstractServiceTest {

    @Autowired
    private ResourceLoader resourceLoader = null;

    @Autowired
    PublishService service;

    @Autowired
    CsvService csvService;

    @Autowired
    ReportService reportService;

    Date billDate = new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
    String sFtpFolder = null;

    boolean devMark = false;
    boolean mteS1Mark = false;
    boolean mteT1Mark = false;
    boolean tpcMark = false;
    @BeforeEach
    void setUp() throws Exception {
        devMark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("ae-dev");
        mteS1Mark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-test-s1");
        mteT1Mark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t1-test-s1");
        tpcMark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t2-tpc");
        sFtpFolder = csvService.tmpFolder()+TMP_SFTP_FOLDER;
        csvService.beforeTriggerSftp(null);
        billDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void triggerSyncSFTPTest() throws Exception {
        List<String> res = service.triggerSyncSFTP(billDate);
        log.info("files:"+res);
        // assertion
        int twY = reportService.getYear(billDate) - 1911;
        assertEquals(7, res.size());
        String fName = String.format("%d", twY)+"年-每月發轉餘總計V2.csv";
        assertEquals(TMP_SFTP_FOLDER+fName, res.get(0));
        Path p = Paths.get(sFtpFolder+fName);
        assertTrue(Files.exists(p));

        int month = reportService.getMonth(billDate);
        fName = String.format("附件一、調度處發轉餘電表-%d年%02d月.xlsx", twY, month);
        assertEquals(TMP_SFTP_FOLDER+fName, res.get(1));
        p = Paths.get(sFtpFolder+fName);
        assertTrue(Files.exists(p));

        fName = String.format("附件二、%d年%02d月-轉直供服務各類度數.xlsx", twY, month);
        assertEquals(TMP_SFTP_FOLDER+fName, res.get(2));
        p = Paths.get(sFtpFolder+fName);
        assertTrue(Files.exists(p));

        fName = String.format("附件三、提供調度處發電月報(%d年%02d月).xlsx", twY, month);
        assertEquals(TMP_SFTP_FOLDER+fName, res.get(3));
        p = Paths.get(sFtpFolder+fName);
        assertTrue(Files.exists(p));

        fName = String.format("提供調度處線損計算(%d年%02d月).xlsx", twY, month);
        assertEquals(TMP_SFTP_FOLDER+fName, res.get(4));
        p = Paths.get(sFtpFolder+fName);
        assertTrue(Files.exists(p));

        fName = String.format("轉直供資料_民國%d年%02d月.zip", twY, month);
        assertEquals(TMP_SFTP_FOLDER+fName, res.get(5));
        p = Paths.get(sFtpFolder+fName);
        assertTrue(Files.exists(p));

        fName = String.format("調度處15分鐘發用電量報表(%d年%02d月).xlsx", twY, month);
        assertEquals(TMP_SFTP_FOLDER+fName, res.get(6));
        p = Paths.get(sFtpFolder+fName);
        assertTrue(Files.exists(p));
    }

    @Test
    public void putYearPwDsPowerInfoTest() throws Exception {
        int twY = reportService.getYear(billDate) - 1911;
        csvService.beforeTriggerSftp(TMP_SFTP_FOLDER);
        String ss = service.putYearPwDsPowerInfo(billDate, twY);
        log.info(ss);
        // assertion
        assertEquals(String.format("%d年-每月發轉餘總計V2.csv", twY), ss);
    }
}