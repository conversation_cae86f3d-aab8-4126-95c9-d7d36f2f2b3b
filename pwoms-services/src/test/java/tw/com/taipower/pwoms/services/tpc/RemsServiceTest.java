package tw.com.taipower.pwoms.services.tpc;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import tw.com.taipower.data.repository.pwoms.VoltageLevelRepository;
import tw.com.taipower.pwoms.services.AbstractServiceTest;
import tw.com.taipower.pwoms.services.config.TpcRemsConfig;
import tw.com.taipower.pwoms.services.entitymanage.GeneratorService;
import tw.com.taipower.pwoms.services.tpc.model.*;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @class: RemsServiceTest
 * @author: daniel
 * @version:
 * @since: 2024-06-06 12:25
 * @see:
 **/

@Log4j2
@SpringBootTest(classes = {RemsService.class})
public class RemsServiceTest extends AbstractServiceTest {

    @Autowired
    private RemsService service;

    @Autowired
    private GeneratorService generatorService;
    @Autowired
    private RemsService remsService;
    @Autowired
    private TpcRemsConfig tpcRemsConfig;

    @Autowired
    private RemsTxService remsTxService;

    @Autowired
    private VoltageLevelRepository voltageLevelRepository;

    @Test
    public void TestGetDataNB() {
        Assertions.assertNotNull(service.getDataNB("11033138007").getNbMstTpcmeter());
    }

    @Test
    public void TestGetDataRN() {
        Assertions.assertNotNull(service.getDataRN("xxxxxxxx").getRnMstTpcmeter());
    }

    @Test
    public void TestGetRNFromCSV() throws FileNotFoundException {
        List<String> errors = new ArrayList<>();
        String csvName = "RNIS_noSale_1140318.csv";
        try {
            var voltageLevels = voltageLevelRepository.findAll();
            service.getRNDataFromCSVFile(new FileInputStream(csvName)).forEach((key, entity) -> {
                try {
                    generatorService.saveFromRNIS(entity, voltageLevels, 600L);
                } catch (Throwable ex) {
                    log.error(entity, ex);
                    errors.add(String.format("%s 更新失敗 %s", entity.getRelationId(), ex.getMessage()));
                }
            });
        } catch (Throwable ee) {
            log.error(ee);
            errors.add(String.format("%s 更新失敗 %s", csvName, ee.getMessage()));
        }
        log.info("RNIS csv error:" + errors);

        //if (!errors.isEmpty()) {
        //    throw new RuntimeException(String.join("\n", errors));
        //}
    }

    @Test
    public void meterTest() {
        for (int index = 0; index <= 23; index++) {
            String tpcMeter = String.format("%02d8989898989", index);
            log.info("{} {}", tpcMeter, remsService.decideNBSUrl(tpcMeter));
        }
    }

    @Test
    public void nbssync() {
        remsService.syncNBSWithCustomNumbers(List.of("11033138007"), 0L);
    }

    @Test
    public void rnissync() {
        remsService.syncRNISWithCustomNumbers(List.of("07759590033"), 0L);
    }

    @Test
    public void getFileFromSftp() throws JSchException, SftpException {
        service.syncRNFromSftp("RNIS_Sale_1130802.csv", 600L);
    }

    @Test
    public void getFileFromCSV() throws FileNotFoundException {
        log.info(service.getRNDataFromCSVFile(new FileInputStream("RNIS_Sale_1140318.csv")));
    }

    @Test
    public void putFileToSftp() {
        service.saveNBListToSftp("test.csv");
        service.saveRNListToSftp("test.csv");
    }

    @Test
    public void customerNumberCheck() {
        log.info(service.verifyCustomerNumber("18442600115"));
        log.info(service.verifyCustomerNumber("18442600114"));
    }

    @Test
    public void addressProcessTest() {
        var result1 = remsTxService.findCityArea("    1110055\t 台北市信義區菸廠路88號12樓");
        log.info(result1);
        var result2 = remsTxService.findCityArea(null);
        log.info(result2);
        var result3 = remsTxService.findCityArea("");
        log.info(result3);
    }

    @Test
    public void saveRnData() {
        var rnData = new RNData();
        rnData.setRnMstTpcmeter("0000001222000");
        rnData.setRnMstAprvAddr("    1110055\t 台北市信義區菸廠路88號12樓");
        remsTxService.sucessSave(rnData);

    }

    @Test
    public void saveNbData() {
        var nbData = new PWOMSNBData();
        nbData.setNbMstTpcmeter("00047302082");
        nbData.setNbMstUsagAddr("110055 台北市信義區菸廠路８８號１３樓");
        remsService.formatAddress(nbData);
        remsTxService.sucessSave(nbData, 0L);
    }

    @Test
    public void testVoltageLevel() {
        log.info(RemsVoltageOption.getVoltageLevel("L"));
    }

    @Test
    public void meterdistincttest() {
        List<RNV2DeviceDataMeter> newMeters = new ArrayList();
        newMeters.add(RNV2DeviceDataMeter.builder().groupType("RA01").meterNo("0").build());
        newMeters.add(RNV2DeviceDataMeter.builder().groupType("RA01").meterNo("1").build());
        newMeters.add(RNV2DeviceDataMeter.builder().groupType("RC01").meterNo("2").build());
        log.info(newMeters);
        List<RNV2DeviceDataMeter> distinctList = newMeters.stream()
                .collect(Collectors.toMap(
                        RNV2DeviceDataMeter::getGroupType, // 依據 groupType 過濾
                        meter -> meter, // 直接存物件
                        (existing, replacement) -> existing // 保留第一個出現的
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        log.info(distinctList);
    }

    @Test
    public void nbsv2updateTest() throws JsonProcessingException {
        String value = "{\n" +
                "  \"STATUS\": 200,\n" +
                "  \"NB_MST_ACCTID\": \"7433333333\",\n" +
                "  \"NB_MST_TPCMETER\": \"09036155002\",\n" +
                "  \"NB_MST_CUST_NAME\": \"南都汽車股份有限公司\",\n" +
                "  \"NB_MST_COMPNBR\": \"86120567\",\n" +
                "  \"NB_MST_USAG_ADDR\": \"民雄鄉雙福村中山路２－２號\",\n" +
                "  \"NB_MST_MAIL_ADDR\": \"台南市永康區中正南路２１４號\",\n" +
                "  \"NB_MST_CUST_TELE\": \"(06)2536366#341\",\n" +
                "  \"NB_MST_PCQ\": \"470\",\n" +
                "  \"NB_MST_CUST_SEGT\": \"3\",\n" +
                "  \"NB_MST_IS_SPECLIDT\": \"N\",\n" +
                "  \"NB_MST_CONT_USGE\": \"95\",\n" +
                "  \"NB_MST_FEED_CODE\": \"JP33\",\n" +
                "  \"NB_MST_SICC_CODE\": \"951\",\n" +
                "  \"NB_MST_MTR_DETAIL\": [\n" +
                "    {\n" +
                "      \"MST_MTR_GRPID\": \"01\",\n" +
                "      \"MST_MTR_PHASE\": \"W\",\n" +
                "      \"MST_MTR_TYPE\": \"TI\",\n" +
                "      \"MST_MTR_MNO\": \"19703315\",\n" +
                "      \"MST_MTR_IS_MDMSCOMM\": \"N\",\n" +
                "      \"MST_MTR_MULT\": \"600\",\n" +
                "      \"MST_MTR_INSDT\": \"1120107\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"NB_MST_ERRR_MSGE\": null\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        var nbData = mapper.readValue(value, PWOMSNBData.class);
        log.info(nbData);
    }
    @Test
    public void rnisV2updateTest() throws JsonProcessingException {
        String value = "{\n" +
                "    \"contractSn\": \"25fd9883-e27d-4459-8eb9-8dad7c27aee5\",\n" +
                "    \"custNo\": \"09486261208\",\n" +
                "    \"branchId\": \"09\",\n" +
                "    \"formName\": \"永陽能源國際有限公司\",\n" +
                "    \"taxId\": \"83725347\",\n" +
                "    \"respName\": \"賴春瑛\",\n" +
                "    \"powerType\": \"PV\",\n" +
                "    \"aprvAddr\": \"\",\n" +
                "    \"aplyAddr\": \"台南市北區立賢路一段５７５號\",\n" +
                "    \"perTel\": null,\n" +
                "    \"ecType\": \"第三型\",\n" +
                "    \"aprvFeeType\": \"農林植物\",\n" +
                "    \"shuntType\": \"台電外線\",\n" +
                "    \"contractSold\": \"全額躉售\",\n" +
                "    \"timElePriCate\": \"二段式\",\n" +
                "    \"shuntVotCp\": null,\n" +
                "    \"shuntvotRpb\": null,\n" +
                "    \"feedCd\": \"JE21\",\n" +
                "    \"deviceData\": [\n" +
                "        {\n" +
                "            \"deviceSeq\": \"1\",\n" +
                "            \"deviceCode\": \"0000001\",\n" +
                "            \"soldKw\": 216.75,\n" +
                "            \"mtrRa\": \"RA01\",\n" +
                "            \"mtrRaSn\": \"GE23900560\",\n" +
                "            \"mtrRaMult\": \"120\",\n" +
                "            \"mtrRc\": null,\n" +
                "            \"mtrRcSn\": null,\n" +
                "            \"mtrRcMult\": null,\n" +
                "            \"deviceStatus\": \"正式購電\",\n" +
                "            \"soldStatusFstCdate\": \"2024/04/12\",\n" +
                "            \"soldStatusOdate\": \"2024/10/30\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"deviceSeq\": \"2\",\n" +
                "            \"deviceCode\": \"0000002\",\n" +
                "            \"soldKw\": 99,\n" +
                "            \"mtrRa\": \"RA01\",\n" +
                "            \"mtrRaSn\": \"GE23900560\",\n" +
                "            \"mtrRaMult\": \"120\",\n" +
                "            \"mtrRc\": \"RC01\",\n" +
                "            \"mtrRcSn\": \"GE23900547\",\n" +
                "            \"mtrRcMult\": \"40\",\n" +
                "            \"deviceStatus\": \"正式購電\",\n" +
                "            \"soldStatusFstCdate\": \"2024/04/12\",\n" +
                "            \"soldStatusOdate\": \"2024/11/01\"\n" +
                "        }\n" +
                "    ]}\n";
        ObjectMapper mapper = new ObjectMapper();
        var rnData = mapper.readValue(value, RNDataV2.class);
        log.info(rnData);
        remsTxService.updateFuelTypeList();
        remsTxService.sucessSave(rnData, 0L);
    }
}