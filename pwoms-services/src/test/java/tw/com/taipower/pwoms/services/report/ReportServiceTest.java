package tw.com.taipower.pwoms.services.report;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.reactive.context.GenericReactiveWebApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ActiveProfiles;
import tw.com.taipower.data.repository.pwoms.ApplicationMonthlyCapacityRecordRepository;
import tw.com.taipower.data.repository.pwoms.ApplicationMonthlySettlementRepository;
import tw.com.taipower.data.repository.pwoms.ApplicationRepository;
import tw.com.taipower.data.repository.pwoms.VoltageLevelRepository;
import tw.com.taipower.pwoms.services.AbstractServiceTest;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.report.*;

import java.math.BigDecimal;
import java.util.*;

import static java.lang.Math.abs;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.doThrow;

/**
 * @class: ReportServiceTest
 * @author: jingfungchen
 * @version:
 * @since: 2024-08-27 15:10
 * @see:
 **/
@Log4j2
//@ActiveProfiles("ae-dev")
@ActiveProfiles("mte-t1-test-s1")
//@ActiveProfiles("mte-t2-tpc")
class ReportServiceTest extends AbstractServiceTest {

    @Autowired
    private ResourceLoader resourceLoader = null;

    @Autowired
    private ReportService service;

    @Spy
    private RoutineReportService routineReportService;

    @Autowired
    ApplicationMonthlySettlementRepository repository;

    @Autowired
    ApplicationMonthlyCapacityRecordRepository applicationMonthlyCapacityRecordRepository;

    @Autowired
    VoltageLevelRepository voltageLevelRepository;

    @Autowired
    ApplicationRepository applicationRepository;

    boolean devMark = false;
    boolean mteS1Mark = false;
    boolean mteT1Mark = false;
    boolean tpcMark = false;

    @BeforeEach
    public void setUp() throws Exception {
        devMark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("ae-dev");
        mteS1Mark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-test-s1");
        mteT1Mark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t1-test-s1");
        tpcMark = ((GenericReactiveWebApplicationContext) resourceLoader).getEnvironment().getActiveProfiles()[0].equals("mte-t2-tpc");
        /*if (devMark) {
            Long meterGId1 = service.setApplicationGeneratorRepository(56L, 87L); // 614L
            Long meterGId2 = service.setApplicationGeneratorRepository(90L, 87L); // 497L
            Long meterLId3 = service.setApplicationLoadRepository(44L, 4911L); // s1 改成 APPLICATION_LOAD_ID = 44 meter_id=3597
            //Long meterGId4 = service.setApplicationLoadRepository(45L, 3300L); // s1 改成 APPLICATION_LOAD_ID = 44 meter_id=3658
            assertEquals(87L, meterGId1); // gen meter
            assertEquals(87L, meterGId2); // gen meter
            assertEquals(4911L, meterLId3); // load meter
            //assertEquals(3300L, meterGId4); // gen meter
        }*/
    }

    @AfterEach
    public void tearDown() throws Exception {
        /*if (devMark)
            backChangeMeter();*/
    }

    @Test
    public void getOriginalBillingReportTest_total() throws Exception {
        Date startDate = DateUtils.passStringToDate("2024-01-01");
        int month = service.getMonth(new Date());
        Date endDate = DateUtils.passStringToDate(String.format("2025-%02d-01", month));
        List<ReportVo> res = service.getOriginalBillingReport(startDate, endDate);
        log.info(res + ", size: " + res.size());

        // assertion
        if (devMark) {
            assertTrue(res.get(0).getApplicationGeneratorId() <= res.get(1).getApplicationGeneratorId());
            List<Map<String, Object>> settle = repository.findAllSettlementInfoByBillingDateRange(startDate, endDate);
            int count = settle.size();
            Set<String> sDateApGenLoad = new HashSet<>();
            for (Map<String, Object>m:settle) {
                String[] sDate = m.get("serviceDate").toString().split("-");
                String key = sDate[0] + "-" + Integer.toString(Integer.parseInt(sDate[1]));
                String[] bDate = m.get("billDate").toString().split("-");
                key += "_" + bDate[0] + "-" + Integer.toString(Integer.parseInt(bDate[1]));
                key += "_" + m.get("applicationGeneratorId").toString();
                key += "_" + m.get("applicationLoadId").toString();
                sDateApGenLoad.add(key);
            }
            assertEquals(count, sDateApGenLoad.size());
            Map<String, Integer> sdAGL = new HashMap<>();
            for (String s:sDateApGenLoad) {
                sdAGL.put(s, 0);
            }
            int outRange = 0;
            int change = 0;
            for (int i =0; i< res.size(); i++) {
                ReportVo v = res.get(i);
                String key = Integer.toString(v.getServiceYear()) + "-" + Integer.toString(v.getServiceMonth()) + "_"
                        + Integer.toString(v.getBillYear()) + "-" + Integer.toString(v.getBillMonth()) + "_"
                        + Long.toString(v.getApplicationGeneratorId()) + "_" + Long.toString(v.getApplicationLoadId());
                if (0 == sdAGL.get(key)) sdAGL.put(key, 1);
                else if (null == sdAGL.get(key)) {
                    log.info("[outRange]"+i+"~"+key);
                    outRange ++;
                } else {
                    log.info("[changeMeter]"+i+"~"+key);
                    change ++;
                }
            }
            log.info("[outRange]num:"+outRange+", [changeMeter]num:"+change);
            int sum = sdAGL.values().stream().mapToInt(Integer::intValue).sum();
            log.info("[sdAGL]"+sdAGL);
            assertEquals(count, sum);
        } else assertEquals(0, res.size());
    }

    @Test
    public void getOriginalBillingReportTest() throws Exception {
        Date startDate = DateUtils.passStringToDate("2024-06-01");
        //Date startDate = new GregorianCalendar(2024, Calendar.JULY, 1).getTime();
        Date endDate = DateUtils.passStringToDate("2024-12-01");
        List<ReportVo> res = service.getOriginalBillingReport(startDate, endDate);
        log.info(res+", size: "+res.size());

        // assertion
        if (devMark) {
            assertTrue(res.get(0).getApplicationGeneratorId() <= res.get(1).getApplicationGeneratorId());
            assertEquals(56, res.get(10).getApplicationGeneratorId());
            assertEquals(25, res.get(10).getApplicationLoadId());
            assertEquals("", res.get(10).getGenMeterChangeDate());
            assertEquals("WS79149566", res.get(10).getGenMeterNo());
            assertEquals(56, res.get(21).getApplicationGeneratorId());
            assertEquals(25, res.get(21).getApplicationLoadId());
            assertEquals("", res.get(21).getGenMeterChangeDate());
            assertEquals("WS79149566", res.get(21).getGenMeterNo());
            assertEquals(56, res.get(32).getApplicationGeneratorId());
            assertEquals(25, res.get(32).getApplicationLoadId());
            assertEquals("2024-06-18", res.get(32).getGenMeterChangeDate()); // 2024-06-27
            assertEquals("WS79149566", res.get(32).getGenMeterNo()); // AA00000002
            assertEquals(56, res.get(33).getApplicationGeneratorId());
            assertEquals(25, res.get(33).getApplicationLoadId());
            assertEquals("", res.get(33).getGenMeterChangeDate());
            assertEquals("XY12345678", res.get(33).getGenMeterNo()); // AA00000004
            assertEquals(90, res.get(40).getApplicationGeneratorId());
            assertEquals(44, res.get(40).getApplicationLoadId());
            assertEquals("2024-06-05", res.get(40).getCustMeterChangeDate());
            assertEquals("WU20116101", res.get(40).getCustMeterNo());
            assertEquals(90, res.get(41).getApplicationGeneratorId());
            assertEquals(44, res.get(41).getApplicationLoadId());
            assertEquals("2024-06-18", res.get(41).getGenMeterChangeDate());
            assertEquals("WS79149566", res.get(41).getGenMeterNo());
            assertEquals(90, res.get(43).getApplicationGeneratorId());
            assertEquals(45, res.get(43).getApplicationLoadId());
            assertEquals("2024-06-18", res.get(43).getGenMeterChangeDate());
            assertEquals("WS79149566", res.get(43).getGenMeterNo());
            assertNull(res.get(76).getGenMeterNo()); // XY12345678
            List<Map<String, Object>> settlementList = repository.findAllSettlementInfoByBillingDateRange(startDate, endDate);
            assertTrue(res.size() >= settlementList.size());
            assertEquals(73, settlementList.size()); // 56 -> 58 -> 62
            assertEquals(85, res.size()); // 56+12 -> 74 -> 73+ 12
        } else assertEquals(0, res.size());
    }

    @Test
    public void getOriginalBillingReportTest_20241001_20241001() throws Exception {
        Date startDate = new GregorianCalendar(2024, Calendar.OCTOBER, 1).getTime();
        Date endDate = new Date();
        List<ReportVo> res = service.getOriginalBillingReport(startDate, endDate);
        log.info(res + ", size: " + res.size());

        // assertion
        if (devMark) assertEquals(36, res.size()); //30 -> + 2 換表 32 -> 36
        else assertEquals(0, res.size());
    }

    @Test
    public void getMonthlyGeneratorPowersTest() throws Exception {
        Date billDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportGenPowerVo> res = service.getMonthlyGeneratorMatchedRM(billDate);
        //log.info(res+",size:"+res.size());

        // assertion
        if (tpcMark) {
            assertEquals("2025-03-01", res.get(0).getStartDate());
            assertEquals("2025-05-01", res.get(res.size()-1).getStartDate());
            assertEquals(131, res.size());
            for (int i =0;i<res.size();i++)
                assertNull(res.get(i).getMeterReplacedDate());
        } else if (mteT1Mark) {
            assertEquals("2024-07-01", res.get(0).getStartDate());
            assertEquals("2024-09-01", res.get(res.size()-1).getStartDate());
            assertEquals(143, res.size());
            for (int i =0;i<res.size();i++) {
                ReportGenPowerVo vo = res.get(i);
                if (vo.getStartDate().equals("2024-09-01") && vo.getGenElecNo().equals("18857150021")
                        &&vo.getMeterNo().equals("KU23702412")) {
                    assertEquals("1-11307-18-002-00", vo.getServiceId());
                    assertEquals("2024-09-02 12:00", vo.getMeterReplacedDate());
                    assertEquals(138, i);
                    assertEquals(52L, vo.getApplicationGeneratorId());
                } else if (vo.getStartDate().equals("2024-09-01") && vo.getGenElecNo().equals("18857150021")) {
                    assertEquals("TU19619813", vo.getMeterNo());
                    assertNull(vo.getMeterReplacedDate());
                } else
                    assertNull(vo.getMeterReplacedDate());
            }
            // 2025-04
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getMonthlyGeneratorMatchedRM(billDate);
            //log.info(res+",2025-04 size:"+res.size());
            assertEquals(137, res.size());
            for (int i =0;i<res.size();i++) {
                ReportGenPowerVo vo = res.get(i);
                if (vo.getStartDate().equals("2024-09-01") && vo.getGenElecNo().equals("18857150021")
                        &&vo.getMeterNo().equals("KU23702412")) {
                    assertEquals("1-11307-18-002-00", vo.getServiceId());
                    assertEquals("2024-09-02 12:00", vo.getMeterReplacedDate());
                    assertEquals(52L, vo.getApplicationGeneratorId());
                } else if (vo.getStartDate().equals("2024-09-01") && vo.getGenElecNo().equals("18857150021")) {
                    assertEquals("TU19619813", vo.getMeterNo());
                    assertNull(vo.getMeterReplacedDate());
                } else
                    assertNull(vo.getMeterReplacedDate());
            }
        }
        else assertEquals(0, res.size());
    }

    @Test
    public void get2ThMonthlyGeneratorReMatchedRMTest() throws Exception {
        Date runDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportGenPowerVo> res = service.get2ThMonthlyGeneratorReMatchedRM(runDate);
        //log.info(res+",size:"+res.size());

        // assertion
        if (tpcMark) {
            assertEquals(54, res.size());
            assertEquals("2025-03-01", res.get(0).getStartDate());
            assertEquals("2025-05-01", res.get(res.size()-1).getStartDate());
            for (int i =0;i<res.size();i++) {
                assertNull(res.get(i).getMeterReplacedDate());
            }
        } else if (mteT1Mark) {
            assertEquals(58, res.size());
            assertEquals("2024-07-01", res.get(0).getStartDate());
            assertEquals("2024-09-01", res.get(res.size()-1).getStartDate());
            for (int i =0;i<res.size();i++) {
                if (res.get(i).getApplicationGeneratorId().equals(52L))
                    assertEquals("2024-09-02 12:00", res.get(i).getMeterReplacedDate());
                else assertNull(res.get(i).getMeterReplacedDate());
            }
            runDate = new GregorianCalendar(2025, Calendar.APRIL,1).getTime();
            res = service.get2ThMonthlyGeneratorReMatchedRM(runDate);
            log.info(res+",2025-04 size:"+res.size());
            assertEquals(37, res.size());
            assertEquals("2024-08-01", res.get(0).getStartDate());
            assertEquals("2024-09-01", res.get(res.size()-1).getStartDate());
            for (int i =0;i<res.size();i++) {
                if (res.get(i).getApplicationGeneratorId().equals(52L))
                    assertEquals("2024-09-02 12:00", res.get(i).getMeterReplacedDate());
                else assertNull(res.get(i).getMeterReplacedDate());
            }
        }//else if (tpcMark) assertEquals(4, res.size());
        else assertEquals(0, res.size());
    }

    @Test // #7 每月發電端每15分鐘轉直供度數
    public void get15MinuteMonthlyGeneratorMatchedRMTest() throws Exception {
        Date billDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportGen15powerVo> res = service.get15MinuteMonthlyGeneratorMatchedRM(billDate);
        //log.info(res + ",size:" + res.size());

        // assertion
        if (tpcMark) {
            assertEquals(30, res.size() % 92); // 2025-03/04/05 31+30+31 = 92 天
            assertEquals("2025-03-01", res.get(0).getDate());
            assertEquals("2025-05-31", res.get(res.size()-1).getDate());
            assertEquals(3986, res.size());
        } else if (mteT1Mark) {
            assertEquals(29, res.size() % 92); // 2024-07/08/09 31+31+30 = 92 天
            assertEquals(4353, res.size());
            assertEquals("2024-07-01", res.get(0).getDate());
            assertEquals("2024-09-30", res.get(res.size()-1).getDate());
            assertEquals("1-11307-10-002-00", res.get(res.size()-1).getServiceId());
            assertEquals(56L, res.get(res.size()-1).getApplicationGeneratorId());
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.get15MinuteMonthlyGeneratorMatchedRM(billDate);
            assertEquals(60, res.size() % 92); // 2024-07/08/09 31+31+30 = 92 天
            assertEquals(4108, res.size());
            assertEquals("2024-07-01", res.get(0).getDate());
            assertEquals("2024-09-30", res.get(res.size()-1).getDate());
            assertEquals("1-11307-10-002-00", res.get(res.size()-1).getServiceId());
            assertEquals(56L, res.get(res.size()-1).getApplicationGeneratorId());
        } else assertEquals(0, res.size());
    }

    /*@Test // #7 每月發電端每15分鐘轉直供度數
    public void get15MinuteMonthlyGeneratorMatchedRMTest_JUNE() throws Exception {
        Date runDate = new GregorianCalendar(2024, Calendar.JUNE, 1).getTime();
        List<ReportGen15powerVo> res = service.get15MinuteMonthlyGeneratorMatchedRM(runDate);
        log.info(res+",size:"+res.size());

        // assertion
        if (devMark) {
            int monthday = getDateOfMonth(runDate);
            assertEquals(30, monthday); // 6月 = 30天
            assertEquals(0, res.size()%monthday);
            assertEquals(10, res.size()/monthday); // 10 個發電 電表

            int count=1;
            String keepGenElecNo = "";
            String keepGenMetterNo = "";
            String keepDate = "";
            String loads = "";
            BigDecimal Fee= BigDecimal.ZERO;
            Map<String, BigDecimal> elFee = new HashMap<>();
            for(ReportGen15powerVo vo : res) {
                String date = vo.getDate();
                if (vo.getApplicationGeneratorId().equals(56) && date.substring(0, date.length()-3).equals("2024-06")) {
                    String genMeterNo = vo.getGenMeterNo();
                    if (!genMeterNo.equals("WS79149566"))
                        assertEquals("WS12312312", genMeterNo);
                }
                int aa = Integer.parseInt(vo.getDate().split("-")[2]);
                if (aa == 1 && !keepGenElecNo.isEmpty()) {
                    //log.info("[keep] genElecNo:"+keepGenElecNo+", date:"+keepDate+", Fee:"+Fee);
                    if (null == keepGenMetterNo || keepGenMetterNo.isEmpty()) elFee.put(keepGenElecNo, null);
                    else elFee.put(keepGenElecNo, Fee);
                    count = 1;
                    Fee = BigDecimal.ZERO;
                }
                else count ++;
                keepGenElecNo = vo.getGenElecNo();
                keepGenMetterNo = vo.getGenMeterNo();
                keepDate = vo.getDate();
                loads = vo.getLoads();
                Fee = Fee.add(sumLoads(loads));
                assertFalse(vo.getLoads().isEmpty());
            }
            elFee.put(keepGenElecNo, Fee);
            log.info("[keep] genElecNo:"+keepGenElecNo+", date:"+keepDate+", elFee:"+elFee+", size:"+elFee.size());
            assertEquals("2024-06-01", res.get(0).getDate());
            assertEquals("2024-06-30", res.get(res.size()-1).getDate());
            assertEquals(elFee.size()*30, res.size());

            // RematchedRm 1,3,9,11
            List<ReportGenPowerVo> reside = service.get2ThMonthlyGeneratorReMatchedRM(runDate);
            for (ReportGenPowerVo vo : reside) {
                Fee = elFee.get(vo.getGenElecNo());
                BigDecimal value1 = null == vo.getTable1() ? null: new BigDecimal(vo.getTable1());
                BigDecimal value3 = null == vo.getTable3()? null: new BigDecimal(vo.getTable3());
                BigDecimal value9 = null == vo.getTable9()? null: new BigDecimal(vo.getTable9());
                BigDecimal value11 = null == vo.getTable11()? null: new BigDecimal(vo.getTable11());
                if (null != value1 && null != value3 && null != value9 && null != value11)
                    elFee.put(vo.getGenElecNo(), Fee.add(value1).add(value3).add(value9).add(value11));
            }

            // monthly matchedRm
            List<ReportGenPowerVo> monthRes = service.getMonthlyGeneratorMatchedRM(runDate);
            //log.info(monthRes+",size:"+monthRes.size());
            int idx = 0;
            BigDecimal Fee2 = BigDecimal.ZERO;
            List<Long> gIds = new ArrayList<>();
            for (ReportGenPowerVo vo : monthRes) {
                gIds.add(vo.getApplicationGeneratorId());
                Fee = elFee.get(vo.getGenElecNo());
                if (null != Fee) {
                    BigDecimal value1 = new BigDecimal(vo.getTable1());
                    BigDecimal value3 = new BigDecimal(vo.getTable3());
                    BigDecimal value9 = new BigDecimal(vo.getTable9());
                    BigDecimal value11 = new BigDecimal(vo.getTable11());
                    if (null != value1 && null != value3 && null != value9 && null != value11) {
                        BigDecimal tt = value1.add(value3).add(value9).add(value11);
                        if(gIds.size() > 1 && monthRes.get(idx-1).getApplicationGeneratorId().longValue() == vo.getApplicationGeneratorId()) {
                            //Fee2 = elFee.get(monthRes.get(idx-1).getGenElecNo());
                            ReportGenPowerVo oo = monthRes.get(idx-1);
                            tt = tt.add(new BigDecimal(oo.getTable1())).add(new BigDecimal(oo.getTable3()))
                                    .add(new BigDecimal(oo.getTable9())).add(new BigDecimal(oo.getTable11()));
                            log.info("GenElecNo:"+vo.getGenElecNo()+", Fee:"+Fee+", monthly fee:"+tt
                                    +", genId:"+vo.getApplicationGeneratorId()+", diff:"+abs(Fee.doubleValue() - tt.doubleValue()));
                            //assertTrue(abs(Fee.doubleValue() - tt.doubleValue()) < 0.04);
                        } else {
                            log.info("GenElecNo:" + vo.getGenElecNo() + ", Fee:" + Fee + ", monthly fee:" + tt
                                    + ", genId:" + vo.getApplicationGeneratorId() + ", diff:" + abs(Fee.doubleValue() - tt.doubleValue()));
                            //assertTrue(abs(Fee.doubleValue() - tt.doubleValue()) < 0.04);
                            //assertEquals(tt, Fee);
                        }
                    }
                }
                idx++;
            }
        } else assertEquals(0, res.size());
    }*/

    @Test
    public void getMonthsApplicationLoadMatchedCnTest() throws Exception { // #11
        Date billStart = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date billEnd = new Date();
        List<ReportLoadVo> res = service.getMonthsApplicationLoadMatchedCn(billStart, billEnd);
        //log.info(res+",size:"+res.size());

        // assertion
        // 5月- 5個: 19,20,23,24,25 [[19(=19+21), 20(=20+22)]
        // 6月-13個: 19,20,23,24,25,41,42,43,44,45,60,61,63 [19(=19+21), 20(=20+22), 61(=61+62), 63(=63+64)]
        // -> 17: 19,20,23,24,25,41,42,43,44,45,35262,35263,35265,35267,35268,35269,35270
        if (tpcMark) {
            assertEquals(181, res.size()); // 165
            assertEquals("2025-03-01", res.get(0).getStartDate());
            assertEquals("2025-05-01", res.get(res.size()-1).getStartDate());
        } else if (mteT1Mark) {
            assertEquals(285, res.size()); // 256
            assertEquals("2024-07-01", res.get(0).getStartDate());
            assertEquals("2024-09-01", res.get(res.size()-1).getStartDate());
        } else assertEquals(0, res.size());
    }

    @Test
    public void getMonthsAmiSettlement15RecordTest() throws Exception { // #5
        Date billDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportGen15valueVo> vo = service.getMonthsAmiSettlement15Record(billDate);
        //log.info(vo+",size:"+vo.size());

        //assertion
        if (tpcMark) {
            assertEquals(4293, vo.size()); // 31*64個有效電表
            assertEquals("2025-03-01", vo.get(0).getDate());
            assertEquals("2025-05-31", vo.get(vo.size()-1).getDate());
        } else if (mteT1Mark) {
            assertEquals(3802, vo.size());
            assertEquals("2024-07-01", vo.get(0).getDate());
            ReportGen15valueVo m = vo.get(vo.size()-1);
            assertEquals("2024-09-30", m.getDate());
            assertEquals("21954994920", m.getGenElecNo());
            assertEquals("**********", m.getMeterNo());
            assertEquals("1000", m.getRatio());
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            vo = service.getMonthsAmiSettlement15Record(billDate);
            //log.info(vo+",2025-04 size:"+vo.size());
            assertEquals(3802, vo.size());
            assertEquals("2024-07-01", vo.get(0).getDate());
            m = vo.get(vo.size()-1);
            assertEquals("2024-09-30", m.getDate());
            assertEquals("21954994920", m.getGenElecNo());
            assertEquals("**********", m.getMeterNo());
            assertEquals("1000", m.getRatio());
        } else assertEquals(0, vo.size());
    }

    @Test
    public void getMonthsAmiSettlement15RecordLimitCapacityTest() throws Exception { // #6
        Date billDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportGen15valueVo> vo = service.getMonthsAmiSettlement15RecordLimitCapacity(billDate);
        //log.info(vo+",size:"+vo.size());

        //assertion
        if (tpcMark) {
            assertEquals(4293, vo.size()); // 31*64個有效電表
            assertEquals("2025-03-01", vo.get(0).getDate());
            assertEquals("2025-05-31", vo.get(vo.size()-1).getDate());
        } else if (mteT1Mark) {
            assertEquals(3802, vo.size());
            assertEquals("2024-07-01", vo.get(0).getDate());
            ReportGen15valueVo m = vo.get(vo.size()-1);
            assertEquals("2024-09-30", m.getDate());
            assertEquals("21954994920", m.getGenElecNo());
            assertEquals("**********", m.getMeterNo());
            assertEquals("1", m.getRatio());
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            vo = service.getMonthsAmiSettlement15RecordLimitCapacity(billDate);
            log.info(vo+",2025-04 size:"+vo.size());
            assertEquals(3802, vo.size());
            assertEquals("2024-07-01", vo.get(0).getDate());
            m = vo.get(vo.size()-1);
            assertEquals("2024-09-30", m.getDate());
            assertEquals("21954994920", m.getGenElecNo());
            assertEquals("**********", m.getMeterNo());
            assertEquals("1", m.getRatio());
        } else assertEquals(0, vo.size());
    }

    /*@Test
    public void getSmallGreenSettleServiceDateMatchedTest_type1() throws Exception {
        Date serviceStart = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.DECEMBER, 1).getTime(); // 隔月月初
        String type = "1"; //PWOMS 因為沒有 Q 小額綠電, 因此改為 1;
        List<ReportGreenVo> res = service.getSmallGreenSettleServiceDateMatched(serviceStart, serviceEnd, type);
        log.info(res+",size:"+res.size());

        // assertion
        List<Map<String, Object>> settlementList = repository.findApplicationGeneratorLoadSettleInfoByFixType(serviceStart, serviceEnd, type);
        Set<String> kk = new HashSet<>();
        for (Map<String, Object> st : settlementList) {
            kk.add(st.get("serviceDate") + "~"+st.get("applicationGeneratorId") + "~"+st.get("applicationLoadId"));
        }
        if (devMark) {
            assertEquals(20, kk.size()); // 20個
            assertEquals(4*kk.size(), res.size()); // 20*4
        } else {
            assertEquals(0, kk.size());
            if (mteT1Mark) assertEquals(108, res.size());
            else assertEquals(0, res.size());
        }
    }*/

    @Test
    public void getSmallGreenSettleServiceDateMatchedTest() throws Exception {
        Date serviceStart = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.DECEMBER, 1).getTime(); // 隔月月初
        String type = "Q";
        List<ReportGreenVo> res = service.getSmallGreenSettleServiceDateMatched(serviceStart, serviceEnd, type);
        //log.info(res+",size:"+res.size());

        // assertion
        List<Map<String, Object>> settlementList = applicationMonthlyCapacityRecordRepository.findApplicationGeneratorLoadSettleInfoByFixType(serviceStart, serviceEnd);
        Set<String> kk = new HashSet<>();
        for (Map<String, Object> st : settlementList) {
            kk.add(st.get("serviceDate") + "~"+st.get("applicationGeneratorId") + "~"+st.get("applicationLoadId"));
        }
        if (tpcMark) {
            assertEquals(3, kk.size());
            assertEquals(12, res.size());
            assertEquals("2025/3/01 ~ 2025/3/31", res.get(0).getExpPeriod());
            assertEquals("2025/5/01 ~ 2025/5/31", res.get(res.size()-1).getExpPeriod());
        } else if (mteT1Mark) {
            assertEquals(27, kk.size()); // s1: 27個
            assertEquals(108, res.size());
            assertEquals("2024/7/01 ~ 2024/7/31", res.get(0).getExpPeriod());
            assertEquals("2024/9/01 ~ 2024/9/30", res.get(res.size()-1).getExpPeriod());
        } else {
            assertEquals(0, kk.size());
            assertEquals(0, res.size());
        }
    }

    @Test
    public void getSmallGreenContractsInfoTest_typeQ() throws Exception {
        Date billStart = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();
        Date billEnd = new GregorianCalendar(2025, Calendar.DECEMBER, 1).getTime(); // 隔月月初
        String type = "Q";
        List<ReportGreenContractVo> res = service.getSmallGreenContractsInfo(billStart, billEnd, type);
        //log.info(res+",size:"+res.size());

        // assertion
        if (tpcMark) {
            assertEquals(1, res.size());
            ReportGreenContractVo m = res.get(0);
            assertEquals("1-11212-08-010-02", m.getServiceId());
            assertEquals("2023-12-21", m.getContractEffeDate());
        } else if (mteT1Mark) {
            assertEquals(9, res.size()); //kk.size()
        } else {
            assertEquals(0, res.size()); //kk.size()
        }
    }

    /*@Test
    public void getSmallGreenContractsInfoTest_6month_type1() throws Exception {
        Date serviceStart = new GregorianCalendar(2024, Calendar.JUNE, 1).getTime();
        Date serviceEnd = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime(); // 隔月月初
        String type = "1"; // 因為沒有 Q 小額綠電, 因此改為 1
        List<ReportGreenContractVo> res = service.getSmallGreenContractsInfo(serviceStart, serviceEnd, type);
        log.info(res+",size:"+res.size());

        // assertion
        List<Map<String, Object>> settlementList = repository.findApplicationGeneratorLoadSettleInfoByFixType(serviceStart, serviceEnd, type);
        List<Map<String, Object>> contractsList = repository.findApplicationGeneratorLoadContractsByFixType(serviceStart, serviceEnd, type);
        if (devMark) {
            assertEquals(10, settlementList.size());
            assertEquals(10, contractsList.size());
        }
        else {
            assertEquals(0, settlementList.size());
            assertEquals(0, contractsList.size());
        }
        Set<String> kk = new HashSet<>();
        for (Map<String, Object> st : settlementList) {
            kk.add(st.get("applicationGeneratorId") + "~"+st.get("applicationLoadId"));
        }
        for (ReportGreenContractVo v : res) {
            if (null != v.getFatherContract()) {
                assertNotEquals(v.getFatherContract(), v.getSonContract());
                if (null != v.getFatherContractTerminateDate()) assertEquals("2023-11-30", v.getFatherContractTerminateDate());
                else assertNull(v.getFatherContractTerminateDate());
            }
        }
        if (devMark) {
            assertEquals(10, kk.size()); // 10個
            assertEquals(10, res.size()); //kk.size()
        } else {
            assertEquals(0, kk.size()); // 10個
            if (mteT1Mark) assertEquals(9, res.size()); //kk.size()
            else assertEquals(0, res.size());
        }
    }*/

    @Test
    public void getTotalYearVoltKwhTest() throws Exception {
        Date billDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportVoltVo> res = service.getTotalYearVoltKwh(billDate);
        //log.info(res+",size:"+res.size());

        // assertion
        List<String> level = voltageLevelRepository.findAllVoltInfo();
        //log.info("level:"+level);
        if (!res.isEmpty()) {
            assertEquals("345kV", res.get(0).getVoltageLevel());
            assertEquals("161kV", res.get(1).getVoltageLevel());
            assertEquals("69kV", res.get(2).getVoltageLevel());
            assertEquals("22.8kV", res.get(3).getVoltageLevel());
            assertEquals("11.4kV", res.get(4).getVoltageLevel());
            assertEquals("不滿11.4kV", res.get(5).getVoltageLevel());
            assertEquals("總計(度)", res.get(res.size()-1).getVoltageLevel());
        }
        if (tpcMark) {
            assertEquals(level.size(), res.size()); // devMark 多個一個未確定
            assertEquals(",,,,,,,,,,,", res.get(0).getMonthKwh());
            assertEquals("0", res.get(0).getTotal()); // 345Kv:
            assertEquals(",,3773285,2994013,3442307,,,,,,,", res.get(1).getMonthKwh());
            assertEquals("10209605", res.get(1).getTotal()); // 161kV: 717013 -> 415160
            assertEquals(",,357596,139525,444955,,,,,,,", res.get(2).getMonthKwh());
            assertEquals("942076", res.get(2).getTotal()); // 69kV: 17092460 -> 8871510
            assertEquals(",,2636354,1119708,743582,,,,,,,", res.get(3).getMonthKwh());
            assertEquals("4499644", res.get(3).getTotal()); // 22.8kV 1840493 -> 1634534
            assertEquals(",,222466,233670,269644,,,,,,,", res.get(4).getMonthKwh());
            assertEquals("725780", res.get(4).getTotal()); // 11.4kV: 3891915 -> 2086277
            assertEquals(",,220891,234133,233564,,,,,,,", res.get(5).getMonthKwh());
            assertEquals("688588", res.get(5).getTotal()); // 不滿11.4kV: 891813 -> 722263
            assertEquals(",,7210592,4721049,5134052,,,,,,,", res.get(res.size()-1).getMonthKwh());
            assertEquals("17065693", res.get(res.size()-1).getTotal());
        } else if (mteT1Mark) {
            assertEquals(level.size()+1, res.size());
            assertEquals(",,,,,,,,,,,", res.get(0).getMonthKwh());
            assertEquals("0", res.get(0).getTotal()); // 345Kv:
            assertEquals(",,,,,,16427,15496,14491,,,", res.get(1).getMonthKwh());
            assertEquals("46414", res.get(1).getTotal()); // 161kV: 717013 -> 415160
            assertEquals(",,,,,,108363,148797,212556,,,", res.get(2).getMonthKwh());
            assertEquals("469716", res.get(2).getTotal()); // 69kV: 17092460 -> 8871510
            assertEquals(",,,,,,10984,7173,9634,,,", res.get(3).getMonthKwh());
            assertEquals("27791", res.get(3).getTotal()); // 22.8kV 1840493 -> 1634534
            assertEquals(",,,,,,38615,35995,31702,,,", res.get(4).getMonthKwh());
            assertEquals("106312", res.get(4).getTotal()); // 11.4kV: 3891915 -> 2086277
            assertEquals(",,,,,,8377,8331,7972,,,", res.get(5).getMonthKwh());
            assertEquals("24680", res.get(5).getTotal()); // 不滿11.4kV: 891813 -> 722263
            assertEquals(",,,,,,182766,215792,276355,,,", res.get(res.size()-1).getMonthKwh());
            assertEquals("674913", res.get(res.size()-1).getTotal()); // 24433694 -> 13729744
            // 2025-04
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getTotalYearVoltKwh(billDate);
            //log.info(res+",2025-04 size:"+res.size());
            assertEquals(level.size()+1, res.size());
            assertEquals(",,,,,,,,,,,", res.get(0).getMonthKwh());
            assertEquals("0", res.get(0).getTotal()); // 345Kv:
            assertEquals(",,,,,,12462,18348,17919,,,", res.get(1).getMonthKwh());
            assertEquals("48729", res.get(1).getTotal()); // 161kV: 717013 -> 415160
            assertEquals(",,,,,,108363,148797,212556,,,", res.get(2).getMonthKwh());
            assertEquals("469716", res.get(2).getTotal()); // 69kV: 17092460 -> 8871510
            assertEquals(",,,,,,9750,6582,8505,,,", res.get(3).getMonthKwh());
            assertEquals("24837", res.get(3).getTotal()); // 22.8kV 1840493 -> 1634534
            assertEquals(",,,,,,38615,35995,31702,,,", res.get(4).getMonthKwh());
            assertEquals("106312", res.get(4).getTotal()); // 11.4kV: 3891915 -> 2086277
            assertEquals(",,,,,,8377,7478,7255,,,", res.get(5).getMonthKwh());
            assertEquals("23110", res.get(5).getTotal()); // 不滿11.4kV: 891813 -> 722263
            assertEquals(",,,,,,177567,217200,277937,,,", res.get(res.size()-1).getMonthKwh());
            assertEquals("672704", res.get(res.size()-1).getTotal()); // 24433694 -> 13729744
        } else assertEquals(0, res.size());
    }

    @Test
    public void distinctIntervalSequenceTest() {
        String invStr = "185~186~186~187~188~189~190~191~192~193~194";
        String out = service.distinctIntervalSequence(invStr, "~");
        assertEquals("185~186~187~188~189~190~191~192~193~194", out);
    }

    @Test
    public void getMonthExpRateTest() throws Exception { // #15
        Date billDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportInfoRateVo> res = service.getMonthExpRate(billDate);
        //log.info(res+",size:"+res.size());

        // assertion
        if (tpcMark) {
            assertEquals(5, res.size());
            assertEquals("輔助服務費", res.get(0).getInfo());
            assertEquals("配電進出費", res.get(1).getInfo());
            assertEquals("電力調度費", res.get(2).getInfo());
            assertEquals("輸電進出費", res.get(3).getInfo());
            assertEquals("自建綠電轉供費", res.get(res.size()-1).getInfo());
            assertEquals("16915693", res.get(0).getExpRate()); // AS 10619101
            assertEquals("9547358", res.get(1).getExpRate()); // D 配電 2 + 12 5313394
            assertEquals("16915693", res.get(2).getExpRate()); // 10619101
            assertEquals("11353434", res.get(3).getExpRate()); // T 輸電 1 + 12 // 6761874
            assertEquals("150000", res.get(res.size()-1).getExpRate()); // Q 100000
        } else if (mteT1Mark) {
            assertEquals(5, res.size());
            assertEquals("輔助服務費", res.get(0).getInfo());
            assertEquals("配電進出費", res.get(1).getInfo());
            assertEquals("電力調度費", res.get(2).getInfo());
            assertEquals("輸電進出費", res.get(3).getInfo());
            assertEquals("自建綠電轉供費", res.get(res.size()-1).getInfo());
            assertEquals("41967754", res.get(0).getExpRate()); // 13788393
            assertEquals("18838417", res.get(1).getExpRate()); // D 配電 2 + 12
            assertEquals("41967754", res.get(2).getExpRate());
            assertEquals("35656940", res.get(3).getExpRate()); // T 輸電 1 + 12
            assertEquals("747875", res.get(res.size()-1).getExpRate()); // Q
            // 2025-04
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getMonthExpRate(billDate);
            assertEquals("輔助服務費", res.get(0).getInfo());
            assertEquals("配電進出費", res.get(1).getInfo());
            assertEquals("電力調度費", res.get(2).getInfo());
            assertEquals("輸電進出費", res.get(3).getInfo());
            assertEquals("自建綠電轉供費", res.get(res.size()-1).getInfo());
            assertEquals("42575898", res.get(0).getExpRate()); // 13788393
            assertEquals("18652161", res.get(1).getExpRate()); // D 配電 2 + 12
            assertEquals("42575898", res.get(2).getExpRate());
            assertEquals("36265084", res.get(3).getExpRate()); // T 輸電 1 + 12
            assertNull(res.get(res.size()-1).getExpRate()); // Q
        }else assertEquals(0, res.size());
    }

    @Test
    public void getGeneratorCompaniesTest() throws Exception { // #16 發電月報
        Date billDate = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportGenComVo> res = service.getGeneratorCompanies(billDate);
        //log.info("size:"+res.size());

        // assertion
        if (tpcMark) assertEquals(46, res.size()); // 25 -> 35
        else if (mteT1Mark) {
            assertEquals(51, res.size());
            billDate = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getGeneratorCompanies(billDate);
            assertEquals(51, res.size());
        }
        else assertEquals(0, res.size());
    }

    @Test
    public void getTpcLocationFuelVoltKwhTest_Generator_Load_MAY() throws Exception { // #17 調度處發用電端 2025-05
        int month = tpcMark? Calendar.JUNE:Calendar.MAY;
        Date billDate = new GregorianCalendar(2025, month, 1).getTime();
        List<ReportLocFuelVoltVo> res = service.getTpcLocationFuelVoltKwh(billDate, true);
        //log.info("res:"+res);

        // assertion
        if (tpcMark) {
            assertEquals(18, res.size());
            for (int i =0; i< res.size()-1;i++) {
                ReportLocFuelVoltVo m = res.get(i);
                assertEquals(month+1, m.getBillMonth());
                assertEquals("2025", m.getBillYear());
            }
            ReportLocFuelVoltVo m = res.get(0);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("11.4kV", m.getVoltLevel());
            assertEquals("791", m.getKwh()); // 592
            assertEquals("01", m.getTpcCode());
            m = res.get(7);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("22.8kV", m.getVoltLevel());
            assertEquals("4499644", m.getKwh()); // 1863290
            assertEquals("08", m.getTpcCode());
            m = res.get(res.size()-3);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("69kV", m.getVoltLevel());
            assertEquals("423766", m.getKwh()); //285073
            assertEquals("20", m.getTpcCode());
            m = res.get(res.size()-2);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("11.4kV", m.getVoltLevel());
            assertEquals("342700", m.getKwh()); // 238430
            assertEquals("21", m.getTpcCode());
            m = res.get(res.size()-1);
            assertEquals("總計", m.getBillYear());
            assertEquals("17065693", m.getKwh()); // 10719101
            // 2025-06 Load
            List<ReportLocFuelVoltVo> resLoad = service.getTpcLocationFuelVoltKwh(billDate, false);
            //log.info("load res:"+resLoad+",size:"+resLoad.size());
            assertEquals(22, resLoad.size());
            for (int i =0; i< resLoad.size()-1;i++) {
                m = resLoad.get(i);
                assertEquals(month+1, m.getBillMonth());
                assertEquals("2025", m.getBillYear());
            }
            m = resLoad.get(0);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("22.8kV", m.getVoltLevel());
            assertEquals("474955", m.getKwh()); // matchedKw 330782
            assertEquals("00", m.getTpcCode());
            m = resLoad.get(6);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("161kV", m.getVoltLevel());
            assertEquals("864000", m.getKwh());
            assertEquals("06", m.getTpcCode());
            m = resLoad.get(10);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("22.8kV", m.getVoltLevel());
            assertEquals("545449", m.getKwh()); // 225204
            assertEquals("10", m.getTpcCode());
            m = resLoad.get(resLoad.size()-3);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("22.8kV", m.getVoltLevel());
            assertEquals("516752", m.getKwh()); // 352890
            assertEquals("19", m.getTpcCode());
            m = resLoad.get(resLoad.size()-2);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("161kV", m.getVoltLevel());
            assertTrue(m.getKwh().isEmpty());
            assertEquals("21", m.getTpcCode());
            m = resLoad.get(resLoad.size()-1);
            assertEquals("總計", m.getBillYear());
            assertEquals("17065693", m.getKwh()); // 10719101
            assertEquals(res.get(res.size()-1), resLoad.get(resLoad.size()-1)); // 發用電加總相同
        } else if (mteT1Mark) {
            //log.info(res+",size:"+res.size());
            assertEquals(20, res.size());
            for (int i =0; i< res.size()-1;i++) {
                ReportLocFuelVoltVo m = res.get(i);
                assertEquals(month+1, m.getBillMonth());
                assertEquals("2025", m.getBillYear());
            }
            ReportLocFuelVoltVo m = res.get(0);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("不滿11.4kV", m.getVoltLevel());
            assertEquals("155362", m.getKwh());
            assertEquals("04", m.getTpcCode());
            m = res.get(10);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("22.8kV", m.getVoltLevel());
            assertEquals("657423", m.getKwh()); // matchedKw 232165 -> 232164
            assertEquals("10", m.getTpcCode());
            m = res.get(res.size()-3);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("69kV", m.getVoltLevel());
            assertEquals("7679978", m.getKwh()); // matchedKw 7818586 -> 7818575
            assertEquals("20", m.getTpcCode());
            m = res.get(res.size()-2);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("11.4kV", m.getVoltLevel());
            assertEquals("382857", m.getKwh());
            assertEquals("21", m.getTpcCode());
            m = res.get(res.size()-1);
            assertEquals("總計", m.getBillYear());
            assertEquals("42715629", m.getKwh()); // matchedKw 42715717 -> 42218713
            // 2025-05 Load
            List<ReportLocFuelVoltVo> resLoad = service.getTpcLocationFuelVoltKwh(billDate, false);
            //log.info("load res:"+resLoad);
            assertEquals(37+1, resLoad.size()); // 37 多1行總計
            for (int i =0; i< resLoad.size()-1;i++) {
                m = resLoad.get(i);
                assertEquals(month+1, m.getBillMonth());
                assertEquals("2025", m.getBillYear());
            }
            m = resLoad.get(0);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("不滿11.4kV", m.getVoltLevel());
            assertEquals("21763", m.getKwh()); // matchedKw 7406 -> 7404
            assertEquals("00", m.getTpcCode());
            m = resLoad.get(7);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("69kV", m.getVoltLevel());
            assertEquals("559933", m.getKwh());
            assertEquals("04", m.getTpcCode());
            m = resLoad.get(resLoad.size()-13);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("22.8kV", m.getVoltLevel()); // matchedKw 69kV
            assertEquals("46250", m.getKwh()); // matchedKw 7818586 -> 8344551
            assertEquals("11", m.getTpcCode()); // matchedKw 20
            m = resLoad.get(resLoad.size()-2);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("11.4kV", m.getVoltLevel()); // matchedKw 69kV
            assertEquals("56347", m.getKwh()); // matchedKw 7818586 -> 8344551
            assertEquals("21", m.getTpcCode()); // matchedKw 20
            m = resLoad.get(resLoad.size()-1);
            assertEquals("總計", m.getBillYear());
            assertEquals("42715629", m.getKwh()); // matchedKw 42715717 -> 42218713
            assertEquals(res.get(res.size()-1), resLoad.get(resLoad.size()-1)); // 發用電加總相同
        } else assertNull(res);
    }

    @Test
    public void getTpcLocationFuelVoltKwhTest_Generator_Load_APRIL() throws Exception { // #17 調度處發用電端 2025-04
        int month = Calendar.APRIL;
        Date billDate = new GregorianCalendar(2025, month, 1).getTime();
        List<ReportLocFuelVoltVo> res = service.getTpcLocationFuelVoltKwh(billDate, true);
        //log.info("res:"+res);

        // assertion
        if (mteT1Mark) {
            //log.info(res+",size:"+res.size());
            assertEquals(20, res.size());
            for (int i =0; i< res.size()-1;i++) {
                ReportLocFuelVoltVo m = res.get(i);
                assertEquals(month+1, m.getBillMonth());
                assertEquals("2025", m.getBillYear());
            }
            ReportLocFuelVoltVo m = res.get(0);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("不滿11.4kV", m.getVoltLevel());
            assertEquals("56078", m.getKwh());
            assertEquals("04", m.getTpcCode());
            m = res.get(11);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("22.8kV", m.getVoltLevel());
            assertEquals("657423", m.getKwh()); // matchedKw 232165 -> 232164
            assertEquals("10", m.getTpcCode());
            m = res.get(res.size()-3);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("69kV", m.getVoltLevel());
            assertEquals("7679978", m.getKwh()); // matchedKw 7818586 -> 7818575
            assertEquals("20", m.getTpcCode());
            m = res.get(res.size()-2);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("11.4kV", m.getVoltLevel());
            assertEquals("382857", m.getKwh());
            assertEquals("21", m.getTpcCode());
            m = res.get(res.size()-1);
            assertEquals("總計", m.getBillYear());
            assertEquals("42575898", m.getKwh()); // matchedKw 42715717 -> 42218713
            // 2025-04
            List<ReportLocFuelVoltVo> resLoad = service.getTpcLocationFuelVoltKwh(billDate, false);
            log.info("res:" + resLoad);
            assertEquals(40+1, resLoad.size()); // 12 -> 9 多1行總計
            for (int i =0; i< resLoad.size()-1;i++) {
                m = resLoad.get(i);
                assertEquals(month+1, m.getBillMonth());
                assertEquals("2025", m.getBillYear());
            }
            m = resLoad.get(0);
            assertEquals("太陽能(PV)", m.getFuelLabel());
            assertEquals("不滿11.4kV", m.getVoltLevel());
            assertEquals("21763", m.getKwh()); // matchedKw 7406 -> 7404
            assertEquals("00", m.getTpcCode());
            m = resLoad.get(7);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("69kV", m.getVoltLevel());
            assertEquals("9999", m.getKwh()); // 559933
            assertEquals("04", m.getTpcCode());
            m = resLoad.get(resLoad.size()-14);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("22.8kV", m.getVoltLevel()); // matchedKw 69kV
            assertEquals("46250", m.getKwh()); // matchedKw 7818586 -> 8344551
            assertEquals("11", m.getTpcCode()); // matchedKw 20
            m = resLoad.get(resLoad.size()-3);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("11.4kV", m.getVoltLevel()); // matchedKw 69kV
            assertEquals("56347", m.getKwh()); // matchedKw 7818586 -> 8344551
            assertEquals("21", m.getTpcCode()); // matchedKw 20
            m = resLoad.get(resLoad.size()-2);
            assertEquals("風力能(WP)", m.getFuelLabel());
            assertEquals("161kV", m.getVoltLevel()); // matchedKw 69kV
            assertTrue(m.getKwh().isEmpty());
            assertEquals("21", m.getTpcCode());
            m = resLoad.get(resLoad.size()-1);
            assertEquals("總計", m.getBillYear());
            assertEquals("42575898", m.getKwh()); // matchedKw 42715717 -> 42218713
            assertEquals(res.get(res.size()-1), resLoad.get(resLoad.size()-1)); // 發用電加總相同
        } else assertNull(res);
    }

    @Test
    public void getGenDeviceCapacitiesTest() throws Exception { // #18
        Date date = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportGenCapacityVo> res = service.getGenDeviceCapacities(date);
        //log.info(res+",size:"+res.size());

        // assertion
        if (tpcMark) assertEquals(41, res.size());
        else if (mteT1Mark) {
            assertEquals(46, res.size());
            date = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getGenDeviceCapacities(date);
            assertEquals(46, res.size());
        } else assertEquals(0, res.size());
    }

    @Test
    public void getAmi15GeneratorLoadEndPowersTest_GenMode() throws Exception { //#19
        Date date = tpcMark ? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime(); // Calendar.JUNE
        List<ReportGenLoad15powerVo> res = service.getAmi15GeneratorLoadEndPowers(date, true);
        //log.info(res + ",size:" + res.size());

        // assertion
        if (tpcMark) {
            assertEquals(92, res.size()); // 2025/3/4/5 31+30+31
            assertEquals("發電量加總", res.get(0).getChannel());
            assertEquals("2025-03-01", res.get(0).getDate());
            assertEquals("-22.4,-8.692,8.808,-14.4,-19.892,-18.4,32.808,18.508,38.4,-1.492,2.408,-7.2,-19.892,-33.592,-32.7,-39.192,-38.392,-35.9,-40.792,-46.392,-88.7,-58.392,-59.192,-53.5,-70.392,-74.924,426.828,2531.464,4846.4,6885.356,9360.512,13960.712,17096.512,19090.932,27178.9,33984.12,35832.496,41645.192,41996.816,34386.88,50026.728,48265.908,45537.308,54785.404,55290.252,56203.984,54542.408,58622.084,58831.564,58813.304,58639.048,57160.436,54714.672,49181.748,55830.396,52232.504,50793.692,48151.38,45796.424,43117.276,40651.56,35135.232,27712.396,26502.02,24790.944,20510.448,16205.184,12141.904,8192.756,4407.088,970.336,101.66,-120.552,-126.4,-90.292,-25.6,103.308,36,8.008,-22.3,-43.992,-32.792,-39.9,-51.992,-39.1,-38.392,-26.392,-21.5,-37.592,-43.1,-46.392,-38.392,-43.1,-35.192,-39.892,-28.8"
                    , res.get(0).getLoads());
            assertEquals("2025-04-02", res.get(32).getDate());
            assertEquals("6764.8,6812.108,7067.208,7045.7,6891.208,6816.808,6342.5,6363.208,6451.308,6024.8,5392.008,5732.108,5810.4,4997.708,5333.608,5916,6177.708,5332,4339.208,4080.108,3494.4,3237.708,2855.208,2916.016,3477.092,3687.768,3608.484,3869.736,5177.26,7217.928,9547.184,11723.792,14856.588,16355.2,18053.036,19999.244,21941.768,22515.54,24956.192,31133.584,39613.736,43014.976,46455.564,48560.92,50142.916,49506.38,50780.268,53591.352,56189.864,55518.436,56181.384,55679.736,53958.94,51650.244,50469.632,49760.464,42716.34,36520.472,36903.88,31928.744,35981.748,34127.844,29185.248,24984.352,23395.46,18996.576,18551.428,15151.812,12238.956,9963.604,7731.436,5653.688,4859.816,4550.712,4497.6,4678.508,4963.2,5209.608,4808.9,4522.408,4718.408,4328.1,4212.008,3734.5,3524.008,3352.1,3457.608,3101.608,3179.3,2739.208,2335.208,2453.6,2164.808,2365.6,2047.308,1527.208"
                    , res.get(32).getLoads());
            assertEquals("2025-05-31", res.get(res.size()-1).getDate());
            assertEquals("-57.5,-67.192,-57.6,-67.092,-67.192,-57.5,-67.192,-57.6,-67.092,-57.6,-53.492,-28.792,-67.2,-67.092,-57.6,-67.092,-57.592,-67.1,-58.392,-76.8,-182.252,49.564,875.62,2998.42,5740.788,8788.252,14053.032,17015.712,20541.856,25675.46,29522.98,27487.88,28039.664,28888.364,35981.496,42377.396,39552.216,39608.372,50754.224,49433.948,59258.776,61863.168,60665.676,64491.14,66364.368,66235.728,66544.184,65748.304,64080.54,61542.376,58613.356,59909.512,59952.64,56722.02,54475.448,50111.44,45640.3,44997,38757.476,36970.432,35460.9,32608.396,33036.66,29875.288,23794.204,19939.744,15328.536,12003.056,10202.816,8096.036,5447.492,2154.028,872.76,198.924,-43.64,-47.1,85.7,-26.392,30.5,180.808,84.1,92.008,91.2,16.908,-14.4,-23.992,-39.1,-42.392,-46.3,-42.392,-41.6,-43.092,-56.792,-36.7,-47.192,-13.6"
                    , res.get(res.size()-1).getLoads());
        } else if (mteT1Mark) {
            assertEquals(92, res.size()); // 2024-7/8/9月 =31+31+30
            assertEquals("發電量加總", res.get(0).getChannel());
            assertEquals("2024-07-01", res.get(0).getDate());
            assertEquals("567.3,539.3,488,323.3,248,219.3,331.3,408,436.1,482.4,524.9,416,352.1,364.1,335.2,244.1,164.8,253.7,410.4,407.3,235.2,605.184,2476.5664,5612.2598,9903.28,13656.5708,18695.3783,22139.5177,27716.7486,32437.7568,40707.442,44124.8967,45634.1022,54056.6394,62004.9957,65996.0042,64779.0427,68856.5568,80698.8235,84249.0582,84687.6661,89320.634,91712.4889,93302.7353,93941.1142,92961.5003,93880.4801,94382.3633,94476.2293,93148.8648,94280.6073,95080.8005,90522.3258,89097.8563,84373.055,81401.1729,78833.2749,78493.7779,76435.467,74177.3286,70277.683,66956.6188,60750.467,53011.4089,49079.7327,44988.1164,38492.3979,32626.485,23682.3605,12639.1896,8476.0856,6239.7446,4625.0132,2829.0922,1925.912,646.392,796,698.5,535.2,437.7,493.6,220.1,143.2,102.5,36.8,92.9,64,38.5,113.6,144.9,217.6,250.5,220,167.3,250.4,262.5"
                    , res.get(0).getLoads());
            assertEquals("2024-08-02", res.get(32).getDate());
            assertEquals("68.9,57.7,51.2,64.9,64,97.7,167.3,171.2,130.5,102.5,136,119.3,110.4,77.7,118.5,84,65.7,102.4,67.3,67.2,9.7,30.532,313.212,1669.6007,4084.7169,7923.7001,11932.3197,18231.1005,23308.2915,29461.9445,32288.7408,41900.1629,49528.5569,53648.2271,60966.2117,66221.9234,71479.8659,75528.5174,78875.7062,81130.6993,84661.2504,86500.4056,88800.9586,91073.4266,91285.9572,92724.8524,92537.9237,94744.0668,93868.7295,93149.548,92919.0448,93899.7549,93612.409,91730.5916,89903.9064,87277.0453,84981.1626,82278.0391,80115.7662,75824.6331,71142.0539,66495.8441,62640.0214,57485.537,51698.5795,46905.8045,37375.8708,29347.9029,21827.1473,17193.3962,13624.6889,9499.6021,5527.2432,2428.0936,1243.1817,-3.576,-5.5,2.4,10.5,40.8,64,123.3,111.2,160.1,89.6,88.9,49.7,74.4,62.5,60.9,52,101.7,73.6,60.9,76.1,56"
                    , res.get(32).getLoads());
            assertEquals("2024-09-30", res.get(res.size()-1).getDate());
            assertEquals("16.016,160.916,110.516,148.016,392.116,484.116,627.216,612.116,413.616,289.716,296.916,124.016,32.916,-19.984,2.516,-7.884,-27.184,-32.684,-31.884,-5.584,-60.684,-97.584,-92.684,-114.684,828.3003,2782.455,4532.757,5156.8668,6881.4052,10249.0483,15552.6767,17902.4638,16404.2522,12504.7345,14569.0558,22846.7137,26333.0439,30106.1298,34008.1558,38316.9243,42230.0522,44081.6494,47714.3622,59057.1261,61169.168,56633.7226,53207.6321,53679.7875,53195.8923,54247.0754,71330.0577,60956.0475,59050.5678,56487.7007,62497.8133,56513.0643,52450.8995,43834.5105,39602.9182,31004.4923,28045.4564,24656.3589,24369.1241,24538.983,29308.4985,18913.3602,10529.8202,7949.5053,4486.3413,1279.237,215.52,-171.484,-183.092,-163.084,-120.784,-96.684,-32.784,21.716,48.016,49.716,17.608,-38.284,5.616,-43.084,-47.984,-49.484,-49.584,-49.484,-51.184,-59.084,-51.084,-39.184,-52.684,-49.584,-49.484,-39.984"
                    , res.get(res.size()-1).getLoads());
            date = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getAmi15GeneratorLoadEndPowers(date, true);
            assertEquals(92, res.size());
            assertEquals("發電量加總", res.get(0).getChannel());
            assertEquals("2024-07-01", res.get(0).getDate());
            assertEquals("567.3,539.3,488,323.3,248,219.3,331.3,408,436.1,482.4,524.9,416,352.1,364.1,335.2,244.1,164.8,253.7,410.4,407.3,235.2,605.184,2476.5664,5612.2598,9903.28,13656.5708,18695.3783,22139.5177,27716.7486,32437.7568,40707.442,44124.8967,45634.1022,54056.6394,62004.9957,65996.0042,64779.0427,68856.5568,80698.8235,84249.0582,84687.6661,89320.634,91712.4889,93302.7353,93941.1142,92961.5003,93880.4801,94382.3633,94476.2293,93148.8648,94280.6073,95080.8005,90522.3258,89097.8563,84373.055,81401.1729,78833.2749,78493.7779,76435.467,74177.3286,70277.683,66956.6188,60750.467,53011.4089,49079.7327,44988.1164,38492.3979,32626.485,23682.3605,12639.1896,8476.0856,6239.7446,4625.0132,2829.0922,1925.912,646.392,796,698.5,535.2,437.7,493.6,220.1,143.2,102.5,36.8,92.9,64,38.5,113.6,144.9,217.6,250.5,220,167.3,250.4,262.5"
                    , res.get(0).getLoads());
            assertEquals("2024-08-02", res.get(32).getDate());
            assertEquals("68.9,57.7,51.2,64.9,64,97.7,167.3,171.2,130.5,102.5,136,119.3,110.4,77.7,118.5,84,65.7,102.4,67.3,67.2,9.7,30.532,313.212,1669.6007,4084.7169,7923.7001,11932.3197,18231.1005,23308.2915,29461.9445,32288.7408,41900.1629,49528.5569,53648.2271,60966.2117,66221.9234,71479.8659,75528.5174,78875.7062,81130.6993,84661.2504,86500.4056,88800.9586,91073.4266,91285.9572,92724.8524,92537.9237,94744.0668,93868.7295,93149.548,92919.0448,93899.7549,93612.409,91730.5916,89903.9064,87277.0453,84981.1626,82278.0391,80115.7662,75824.6331,71142.0539,66495.8441,62640.0214,57485.537,51698.5795,46905.8045,37375.8708,29347.9029,21827.1473,17193.3962,13624.6889,9499.6021,5527.2432,2428.0936,1243.1817,-3.576,-5.5,2.4,10.5,40.8,64,123.3,111.2,160.1,89.6,88.9,49.7,74.4,62.5,60.9,52,101.7,73.6,60.9,76.1,56"
                    , res.get(32).getLoads());
            assertEquals("2024-09-30", res.get(res.size()-1).getDate());
            assertEquals("16.016,160.916,110.516,148.016,392.116,484.116,627.216,612.116,413.616,289.716,296.916,124.016,32.916,-19.984,2.516,-7.884,-27.184,-32.684,-31.884,-5.584,-60.684,-97.584,-92.684,-114.684,828.3003,2782.455,4532.757,5156.8668,6881.4052,10249.0483,15552.6767,17902.4638,16404.2522,12504.7345,14569.0558,22846.7137,26333.0439,30106.1298,34008.1558,38316.9243,42230.0522,44081.6494,47714.3622,59057.1261,61169.168,56633.7226,53207.6321,53679.7875,53195.8923,54247.0754,71330.0577,60956.0475,59050.5678,56487.7007,62497.8133,56513.0643,52450.8995,43834.5105,39602.9182,31004.4923,28045.4564,24656.3589,24369.1241,24538.983,29308.4985,18913.3602,10529.8202,7949.5053,4486.3413,1279.237,215.52,-171.484,-183.092,-163.084,-120.784,-96.684,-32.784,21.716,48.016,49.716,17.608,-38.284,5.616,-43.084,-47.984,-49.484,-49.584,-49.484,-51.184,-59.084,-51.084,-39.184,-52.684,-49.584,-49.484,-39.984"
                    , res.get(res.size()-1).getLoads());
        } else assertEquals(0, res.size());
    }

    @Test
    public void getAmi15GeneratorLoadEndPowersTest_LoadMode() throws Exception {
        Date date = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime(); // Calendar.JUNE
        List<ReportGenLoad15powerVo> res = service.getAmi15GeneratorLoadEndPowers(date, false);
        //log.info(res+",size:"+res.size());

        // assertion
        if (tpcMark) {
            assertEquals(92, res.size()); // 2025/3/4/5 31+30+31
            assertEquals("用電量加總", res.get(0).getChannel());
            assertEquals("2025-03-01", res.get(0).getDate());
            assertEquals("1117720.72,1118049.784,1114644.886,1114072.692,1125963.646,1112001.956,1106651.064,1107940.68,1118996.94,1119120.1,1102713.272,1105044.994,1103189.91,1102199.97,1115653.92,1114102.922,1121616.504,1122792.34,1114636.688,1111287.388,1113937.478,1111435.922,1106268.14,1127078.466,1133557.3,1118452.386,1107137.772,1120353.996,1133561.634,1114778.678,1107713.488,1127752.202,1142906.114,1127125.32,1119934.014,1126884.524,1140159.312,1128614.502,1113492.876,1135621.206,1128430.638,1105911.818,1102333.566,1103653.168,1100835.232,1102805.458,1103369.656,1114254.196,1121653.152,1119946,1129085.798,1126208.962,1117723.246,1146839.856,1123939.258,1133711.372,1150658.29,1142849.662,1123754.178,1137228.608,1157208.868,1143979.418,1134506.19,1147024.66,1156862.274,1147961.94,1169913.364,1176245.658,1142879.856,1151519.41,1163965.438,1162533.47,1145937.104,1175899.114,1152229.228,1125296.342,1130897.692,1128662.818,1131437.122,1135460.314,1134779.822,1132064.896,1143527.222,1148559.712,1139429.38,1137909.896,1135231.556,1129397.472,1130730.516,1138674.06,1145892.238,1141134.412,1141316.47,1140049.738,1153296.324,1149814.668"
                    , res.get(0).getLoads());
            assertEquals("2025-04-04", res.get(34).getDate());
            assertEquals("1100446.808,1128957.926,1125640.38,1106166.696,1130391.752,1146576.06,1115409.584,1097881.16,1108173.748,1129066.546,1137176.544,1120480.044,1100353.768,1109826.77,1121200.394,1110128.934,1125369.2,1142504.132,1121732.342,1114267.318,1129917.18,1136008.54,1120234.982,1146708.634,1123075.568,1101635.012,1127062.238,1132376.49,1098807.588,1092716.428,1142290.97,1136662.738,1121388.054,1103448.738,1123933.35,1142659.534,1106097.876,1106345.026,1114492.094,1103362.516,1092950.376,1095994.928,1104514.948,1106189.132,1109052.818,1110304.01,1104817.268,1111113.74,1101528,1092420.244,1102587.656,1106023.326,1093791.874,1097422.526,1099575.086,1100521.576,1111469.958,1118782.834,1114833.19,1110896.242,1107740.908,1104380.756,1102846.7,1116801.912,1111543.562,1110792.43,1118202.31,1118038.096,1115875.61,1116294.814,1125779.814,1122472.798,1125540.188,1121767.138,1126456.518,1145910.326,1139300.564,1123420.466,1107362.76,1141044.648,1146024.386,1122210.426,1113457.514,1128789.178,1119527.796,1147254.218,1155246.822,1153298.638,1115628.684,1135993.612,1143713.5,1135849.624,1131021.976,1130373.422,1155698.142,1159919.734"
                    , res.get(34).getLoads());
            assertEquals("2025-05-31", res.get(res.size()-1).getDate());
            assertEquals("1178172.774,1164265.588,1200522.196,1199444.056,1157850.342,1162574.946,1146225.476,1153753.548,1186250.274,1195381.602,1173654.414,1168757.4,1186064.412,1188367.07,1180054.086,1159717.256,1153106.676,1141654.7,1174284.206,1184201.298,1180743.434,1149247.01,1159943.862,1160049.878,1192390.64,1192004.84,1155559.096,1155283.912,1173262.102,1160692.974,1155651.49,1149773.172,1154197.878,1164946.186,1186782.66,1211795.664,1209072.942,1188869.598,1178822.474,1192625.022,1209069.894,1208087.984,1179460.832,1187141.42,1185660.404,1193958.186,1205890.774,1190927.512,1199706.364,1197963.216,1219720.248,1201481.59,1176734.118,1162957.51,1170524.026,1199096.832,1198404.066,1189474.996,1170290.988,1181873.372,1203329.012,1220057.498,1191905.568,1192005.404,1197494.758,1192169.32,1181358.492,1222186.106,1199569.658,1171589.972,1172789.762,1175048.11,1163122.632,1166850.082,1198060.298,1188778.054,1180542.262,1187734.51,1206965.082,1231491.394,1207806.806,1181616.258,1192589.2,1189048.482,1194336.684,1227367.144,1226420.48,1188761.216,1186209.71,1185071.11,1175594.054,1199438.724,1210550.866,1188172.92,1178249.256,1181823.348"
                    , res.get(res.size()-1).getLoads());
        } else if (mteT1Mark) {
            assertEquals(92, res.size()); // 2024-7/8/9月 =31+31+30
            assertEquals("用電量加總", res.get(0).getChannel());
            assertEquals("2024-07-01", res.get(0).getDate());
            assertEquals("273405.8964,255203.7699,258671.2619,255848.6244,247607.081,257817.7527,261154.0855,261426.5002,248766.0238,253882.8168,266348.7892,261729.3369,254805.9462,248751.2976,249086.6685,259800.2904,262388.1123,251140.5176,247589.2156,255799.8678,251877.7065,250261.3102,265070.0012,254877.3517,242974.0784,254105.3768,261524.6387,254957.7483,253082.7584,264738.6384,256368.7004,253972.0408,258212.0135,258919.6258,252261.5254,252746.4888,260884.8677,253323.7933,245510.8681,247318.779,243245.7975,242970.3213,255849.4602,255375.9973,251863.8035,257466.4229,258004.5841,254092.6238,258786.829,257613.654,258731.8419,249790.9623,256099.4369,255676.2135,253100.1948,253077.605,254160.4714,254277.3341,246811.7473,251837.479,258678.9647,247375.8504,250582.7603,250128.204,236832.6876,237288.6994,239198.8505,238771.4707,234624.836,234572.5668,233318.2703,232058.6174,232214.1404,235941.9979,239866.1944,237778.632,237706.9104,234882.1253,235690.1271,234097.329,239698.3864,239489.9596,236859.4683,243966.4548,240799.6096,245879.5972,244247.3163,252098.0266,262225.4961,261438.9835,252480.3091,261231.6935,259485.8663,261602.4324,258998.465,253378.5667"
                    , res.get(0).getLoads());
            assertEquals("2024-08-02", res.get(32).getDate());
            assertEquals("262623.6452,260667.0347,260511.8655,255948.4091,256643.4723,258539.5569,259738.433,250590.8159,254820.9888,254259.3976,257258.716,263610.2273,265952.3977,254052.4601,255594.8561,254235.8417,253190.1199,251703.5248,250142.4701,253010.0547,254708.0814,251920.0971,258215.8092,256543.2782,255953.9374,255393.6674,254389.4865,247615.2934,239436.35,251382.1822,256354.1257,257517.5267,258826.3577,258731.6924,263072.9825,262078.808,261177.2982,261892.1842,258853.3159,253324.1423,253193.0484,258977.8189,259902.3684,255341.1576,252296.0218,250682.6618,246403.9127,249780.9915,249198.2395,249547.5979,241351.4839,241395.2497,242172.1248,249359.8987,243949.6963,244637.397,248387.0418,240820.3979,242542.6534,244939.562,248368.081,245596.5543,243919.1511,240296.1717,237687.4225,237845.0965,240106.276,240245.0397,240241.0876,239876.3647,234468.0652,233324.7201,232624.3802,232067.5539,232516.1026,236074.4343,234731.3148,234124.6079,233018.7396,231486.1192,230860.1186,232140.2145,233508.8498,236898.493,240199.2924,236869.7152,237233.3216,236711.4193,239676.9673,251090.5616,249419.0593,257725.5208,259317.5581,256835.1376,259153.6499,265716.2904"
                    , res.get(32).getLoads());
            assertEquals("2024-09-30", res.get(res.size()-1).getDate());
            assertEquals("350439.1203,353139.0625,350966.8355,346532.634,347988.4373,353539.4326,337877.33,340009.1863,353739.5376,345450.2657,351222.8796,364133.5484,345254.6716,344098.6965,349701.2819,355020.7716,339025.1296,346994.6624,353905.9809,342782.8341,334201.2048,333559.7882,351442.6442,352838.6391,340750.998,333368.6972,335278.5938,336472.3725,343996.5353,344255.172,344603.1043,344088.3685,333721.2329,339162.8393,342079.1437,338539.8215,337163.3118,339226.0218,351202.0447,347041.9377,349130.6357,347815.0749,347562.4767,341139.2056,338810.4795,347928.5186,351729.0974,345842.2468,368162.729,353452.2015,352121.7265,351088.5816,366187.3243,363543.2306,353158.9388,349699.9016,349002.3288,353174.0628,347520.1695,349993.1372,341267.9687,333258.2939,326732.8909,328427.0031,335299.8592,331935.2199,335321.5496,333135.975,334470.8209,339734.1759,331474.1788,332850.6125,331882.3577,327693.3105,321118.1725,325012.8796,330543.9509,325547.8521,325316.2592,329596.7261,324238.5205,318808.8297,322468.5461,326726.0418,327169.5998,326105.1973,340587.1432,334539.8128,354233.2617,358770.0894,354680.0996,363516.4193,361227.7745,352972.4928,349357.5367,345400.7111"
                    , res.get(res.size()-1).getLoads());
            date = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getAmi15GeneratorLoadEndPowers(date, false);
            assertEquals(92, res.size());
            assertEquals("用電量加總", res.get(0).getChannel());
            assertEquals("2024-07-01", res.get(0).getDate());
            assertEquals("273405.8964,255203.7699,258671.2619,255848.6244,247607.081,257817.7527,261154.0855,261426.5002,248766.0238,253882.8168,266348.7892,261729.3369,254805.9462,248751.2976,249086.6685,259800.2904,262388.1123,251140.5176,247589.2156,255799.8678,251877.7065,250261.3102,265070.0012,254877.3517,242974.0784,254105.3768,261524.6387,254957.7483,253082.7584,264738.6384,256368.7004,253972.0408,258212.0135,258919.6258,252261.5254,252746.4888,260884.8677,253323.7933,245510.8681,247318.779,243245.7975,242970.3213,255849.4602,255375.9973,251863.8035,257466.4229,258004.5841,254092.6238,258786.829,257613.654,258731.8419,249790.9623,256099.4369,255676.2135,253100.1948,253077.605,254160.4714,254277.3341,246811.7473,251837.479,258678.9647,247375.8504,250582.7603,250128.204,236832.6876,237288.6994,239198.8505,238771.4707,234624.836,234572.5668,233318.2703,232058.6174,232214.1404,235941.9979,239866.1944,237778.632,237706.9104,234882.1253,235690.1271,234097.329,239698.3864,239489.9596,236859.4683,243966.4548,240799.6096,245879.5972,244247.3163,252098.0266,262225.4961,261438.9835,252480.3091,261231.6935,259485.8663,261602.4324,258998.465,253378.5667"
                    , res.get(0).getLoads());
            assertEquals("2024-08-02", res.get(32).getDate());
            assertEquals("262623.6452,260667.0347,260511.8655,255948.4091,256643.4723,258539.5569,259738.433,250590.8159,254820.9888,254259.3976,257258.716,263610.2273,265952.3977,254052.4601,255594.8561,254235.8417,253190.1199,251703.5248,250142.4701,253010.0547,254708.0814,251920.0971,258215.8092,256543.2782,255953.9374,255393.6674,254389.4865,247615.2934,239436.35,251382.1822,256354.1257,257517.5267,258826.3577,258731.6924,263072.9825,262078.808,261177.2982,261892.1842,258853.3159,253324.1423,253193.0484,258977.8189,259902.3684,255341.1576,252296.0218,250682.6618,246403.9127,249780.9915,249198.2395,249547.5979,241351.4839,241395.2497,242172.1248,249359.8987,243949.6963,244637.397,248387.0418,240820.3979,242542.6534,244939.562,248368.081,245596.5543,243919.1511,240296.1717,237687.4225,237845.0965,240106.276,240245.0397,240241.0876,239876.3647,234468.0652,233324.7201,232624.3802,232067.5539,232516.1026,236074.4343,234731.3148,234124.6079,233018.7396,231486.1192,230860.1186,232140.2145,233508.8498,236898.493,240199.2924,236869.7152,237233.3216,236711.4193,239676.9673,251090.5616,249419.0593,257725.5208,259317.5581,256835.1376,259153.6499,265716.2904"
                    , res.get(32).getLoads());
            assertEquals("2024-09-30", res.get(res.size()-1).getDate());
            assertEquals("350439.1203,353139.0625,350966.8355,346532.634,347988.4373,353539.4326,337877.33,340009.1863,353739.5376,345450.2657,351222.8796,364133.5484,345254.6716,344098.6965,349701.2819,355020.7716,339025.1296,346994.6624,353905.9809,342782.8341,334201.2048,333559.7882,351442.6442,352838.6391,340750.998,333368.6972,335278.5938,336472.3725,343996.5353,344255.172,344603.1043,344088.3685,333721.2329,339162.8393,342079.1437,338539.8215,337163.3118,339226.0218,351202.0447,347041.9377,349130.6357,347815.0749,347562.4767,341139.2056,338810.4795,347928.5186,351729.0974,345842.2468,368162.729,353452.2015,352121.7265,351088.5816,366187.3243,363543.2306,353158.9388,349699.9016,349002.3288,353174.0628,347520.1695,349993.1372,341267.9687,333258.2939,326732.8909,328427.0031,335299.8592,331935.2199,335321.5496,333135.975,334470.8209,339734.1759,331474.1788,332850.6125,331882.3577,327693.3105,321118.1725,325012.8796,330543.9509,325547.8521,325316.2592,329596.7261,324238.5205,318808.8297,322468.5461,326726.0418,327169.5998,326105.1973,340587.1432,334539.8128,354233.2617,358770.0894,354680.0996,363516.4193,361227.7745,352972.4928,349357.5367,345400.7111"
                    , res.get(res.size()-1).getLoads());
        } else assertEquals(0, res.size());
    }

    @Test // #14 發轉餘 ami發電量
    public void getAmi15PowerContractNoTest() throws Exception {
        Date date = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime(); // Calendar.AUGUST
        List<ReportGen15TranReVo> res = service.getAmi15PowerContractNo(date);
        //log.info(res+",size:"+res.size());

        // assertion
        //int monthDays = getDateOfMonth(date);// 7
        if (tpcMark) assertEquals(92, res.size());
        else if (mteT1Mark) {
            assertEquals(3432, res.size()); // 7月 ami36(契約46) + 8月 ami36(契約47) + 9月 ami40(契約49)
            // 3432 = 36*31+36*31+40*30
        } else assertEquals(0, res.size()); // 輸入 6月 沒ami資料, 輸入8月 沒有結帳資料
    }

    @Test // #14 調度處 發轉餘報表 - 統計頁籤- 餘電總和
    public void getAmi15PowerContractNoSummaryTest() throws Exception {
        Date date = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportGen15TranReSummaryVo> res = service.getAmi15PowerContractNoSummary(date);
        //log.info(res+",size:"+res.size());

        // assertion
        //assertEquals(16, res.size()); //s1 16個 settle 取出19個 4個電號 同一個契約 整合成1筆
        if (tpcMark) assertEquals(38, res.size());
        else if (mteT1Mark) assertEquals(44, res.size());
        else assertEquals(0, res.size()); // 輸入 6月 沒ami資料, 輸入8月 沒有結帳資料
    }

    @Test // #13 調度處 每月發轉餘總計
    public void getYearPwDsPowerInfoTest() throws Exception {
        Date date = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportGenTransReYearVo> res = service.getYearPwDsPowerInfo(date);
        //log.info(res+",size:"+res.size());
        // assertion
        if (tpcMark) {
            assertEquals(3, res.size());
            ReportGenTransReYearVo m = res.get(0);
            assertEquals(2025, m.getServiceYear());
            assertEquals(3, m.getServiceMonth());
            assertEquals("4641560", m.getPwPower());
            assertEquals("0", m.getPwAdjust());
            assertEquals("1705032", m.getDsPower());
            assertEquals("0", m.getDsAdjust());
            assertEquals("6346592", m.getTotalPwDs());
            m = res.get(1);
            assertEquals(2025, m.getServiceYear());
            assertEquals(4, m.getServiceMonth());
            assertEquals("2931544", m.getPwPower());
            assertEquals("0", m.getPwAdjust());
            assertEquals("1789505", m.getDsPower());
            assertEquals("0", m.getDsAdjust());
            assertEquals("4721049", m.getTotalPwDs());
            m = res.get(res.size()-1);
            assertEquals(2025, m.getServiceYear());
            assertEquals(5, m.getServiceMonth());
            assertEquals("3066330", m.getPwPower());
            assertEquals("0", m.getPwAdjust());
            assertEquals("2067722", m.getDsPower());
            assertEquals("0", m.getDsAdjust());
            assertEquals("5134052", m.getTotalPwDs());
        } else if (mteT1Mark) {
            assertEquals(3, res.size());
            ReportGenTransReYearVo m = res.get(0);
            assertEquals(2024, m.getServiceYear());
            assertEquals(7, m.getServiceMonth());
            assertEquals("9271027", m.getPwPower());
            assertEquals("329069", m.getPwAdjust());
            assertEquals("2295865", m.getDsPower());
            assertEquals("0", m.getDsAdjust());
            assertEquals("11566892", m.getTotalPwDs());
            m = res.get(1);
            assertEquals(2024, m.getServiceYear());
            assertEquals(8, m.getServiceMonth());
            assertEquals("11525386", m.getPwPower());
            assertEquals("291662", m.getPwAdjust());
            assertEquals("2132429", m.getDsPower());
            assertEquals("0", m.getDsAdjust());
            assertEquals("13657815", m.getTotalPwDs());
            m = res.get(res.size()-1);
            assertEquals(2024, m.getServiceYear());
            assertEquals(9, m.getServiceMonth());
            assertEquals("15626553", m.getPwPower());
            assertEquals("313400", m.getPwAdjust());
            assertEquals("1864369", m.getDsPower());
            assertEquals("0", m.getDsAdjust());
            assertEquals("17490922", m.getTotalPwDs());
            // 2025-04
            date = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getYearPwDsPowerInfo(date);
            log.info("[2025-04]"+res+",size:"+res.size());
            m = res.get(0);
            assertEquals(3, res.size());
            assertEquals(2024, m.getServiceYear());
            assertEquals(7, m.getServiceMonth());
            assertEquals("8941958", m.getPwPower());
            assertEquals("0", m.getPwAdjust());
            assertEquals("2295865", m.getDsPower());
            assertEquals("0", m.getDsAdjust());
            assertEquals("11237823", m.getTotalPwDs());
            m = res.get(1);
            assertEquals(2024, m.getServiceYear());
            assertEquals(8, m.getServiceMonth());
            assertEquals("11233724", m.getPwPower());
            assertEquals("0", m.getPwAdjust());
            assertEquals("2132429", m.getDsPower());
            assertEquals("0", m.getDsAdjust());
            assertEquals("13366153", m.getTotalPwDs());
            m = res.get(res.size()-1);
            assertEquals(2024, m.getServiceYear());
            assertEquals(9, m.getServiceMonth());
            assertEquals("15313153", m.getPwPower());
            assertEquals("0", m.getPwAdjust());
            assertEquals("1864369", m.getDsPower());
            assertEquals("0", m.getDsAdjust());
            assertEquals("17177522", m.getTotalPwDs());
        } else assertEquals(0, res.size());
    }

    @Test // #22
    public void getMonthYearGenLoadPowerInfoTest() throws Exception {
        Date date = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportPowerGenLoadVo> res = service.getMonthYearGenLoadPowerInfo(date);
        //log.info(res+",size:"+res.size());
        // assertion
        int count = 0;
        for (ReportPowerGenLoadVo v:res) {
            if (null != v.getServiceId() && !v.getServiceId().isEmpty()) {
                if (v.getServiceId().equals("1-11105-08-007-02")) { // 113_03 標扁局輸出範例
                    assertEquals("1-10907-08-000-01", v.getFatherContract());
                    count++;
                } else if (v.getServiceId().equals("1-11005-14-001-01")) { // 113_06 標檢局輸出範例
                    assertEquals("1-10904-14-000-01", v.getFatherContract());
                    count++;
                }
            }
        }
        if (tpcMark) {
            assertEquals(0, count);
            assertEquals(32, res.size()); //1106 ->998 -> 1026 -> 1143 -> 1151 -> 1153 -> 1154
        }  else if (mteT1Mark) {
            assertEquals(1, count);
            assertEquals(36, res.size());
            // 2025-04
            new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getMonthYearGenLoadPowerInfo(date);
            log.info(res+", 2025-04 size:"+res.size());
            assertEquals(1, count);
            assertEquals(36, res.size());
        } else {
            assertEquals(0, count);
            assertEquals(0, res.size());
        }

        if (!res.isEmpty()) {
            int rSize = res.size() - 4; // 減掉 4 行 統計報表
            ReportPowerGenLoadVo vo = res.get(res.size() -1);
            ReportPowerGenLoadVo vo2 = res.get(res.size() -2);
            int cc = 0;
            cc += Integer.parseInt(vo.getSubmittedAt());
            cc += Integer.parseInt(vo.getDocsFinDate());
            cc += Integer.parseInt(vo.getReviewDate());
            cc += Integer.parseInt(vo.getAllConfirmDate());
            cc += Integer.parseInt(vo.getContractRequestAt());
            cc += Integer.parseInt(vo.getContractedStart());
            cc += Integer.parseInt(vo.getOnlineAt());
            log.info("DS piece:"+cc);
            cc += Integer.parseInt(vo2.getSubmittedAt());
            cc += Integer.parseInt(vo2.getDocsFinDate());
            cc += Integer.parseInt(vo2.getReviewDate());
            cc += Integer.parseInt(vo2.getAllConfirmDate());
            cc += Integer.parseInt(vo2.getContractRequestAt());
            cc += Integer.parseInt(vo2.getContractedStart());
            cc += Integer.parseInt(vo2.getOnlineAt());
            log.info("total piece:"+cc);
            assertEquals(cc, rSize);
        }
    }

    @Test
    public void getAppStatusGenLoadElecNoCountsTest() throws Exception {
        Date date = new GregorianCalendar(2024, Calendar.JUNE, 1).getTime();
        List<ReportAppStatusGenLoadVo> res = service.getAppStatusGenLoadElecNoCounts(date);
        //log.info(res+",size:"+res.size());

        // assertion
        assertEquals(6, res.size());
        int[] total = {0,0,0,0,0}; // 案件數量 發電端虛擬集團數量 發電端電號數量 用電端虛擬集團數量 用電端電號數量
        for (ReportAppStatusGenLoadVo v:res) {
            if (v.getAppStatus().equals("總共")) {}
            else {
                total[0] += v.getAppCount();
                total[1] += v.getGenVirtualComCount();
                total[2] += v.getGenElecNoCount();
                total[3] += v.getLoadVirtualComCount();
                total[4] += v.getLoadElecNoCount();
            }
        }
        ReportAppStatusGenLoadVo fin = res.get(res.size() -1);
        assertEquals(total[0], fin.getAppCount());
        assertEquals(total[1], fin.getGenVirtualComCount());
        assertEquals(total[2], fin.getGenElecNoCount());
        assertEquals(total[3], fin.getLoadVirtualComCount());
        assertEquals(total[4], fin.getLoadElecNoCount());
    }

    @Test
    public void getGenLoadElecNoAppKwhInfoTest() throws Exception { // #25 RFP 3.4
        Date date = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        ReportAppKwGenLoadVo res = service.getGenLoadElecNoAppKwhInfo(date);
        //log.info(res+", GenSize:"+res.getGenSide().size()+", LoadSize:"+res.getLoadSide().size());

        // assertion
        if (tpcMark) {
            for (int i = 0; i< res.getGenSide().size(); i++)
                assertEquals(2025, res.getGenSide().get(i).getServiceYear());
            for (int i = 0; i< res.getLoadSide().size(); i++)
                assertEquals(2025, res.getLoadSide().get(i).getServiceYear());
            assertEquals(3, res.getGenSide().get(0).getServiceMonth());
            assertEquals(3, res.getLoadSide().get(0).getServiceMonth());
            assertEquals(5, res.getGenSide().get(res.getGenSide().size()-1).getServiceMonth());
            assertEquals(5, res.getLoadSide().get(res.getLoadSide().size()-1).getServiceMonth());
            assertEquals(122, res.getGenSide().size());
            assertEquals(216, res.getLoadSide().size());
        } else if (mteT1Mark) {
            for (int i = 0; i< res.getGenSide().size(); i++)
                assertEquals(2024, res.getGenSide().get(i).getServiceYear());
            for (int i = 0; i< res.getLoadSide().size(); i++)
                assertEquals(2024, res.getLoadSide().get(i).getServiceYear());
            assertEquals(7, res.getGenSide().get(0).getServiceMonth());
            assertEquals(7, res.getLoadSide().get(0).getServiceMonth());
            assertEquals(9, res.getGenSide().get(res.getGenSide().size()-1).getServiceMonth());
            assertEquals(9, res.getLoadSide().get(res.getLoadSide().size()-1).getServiceMonth());
            assertEquals(124, res.getGenSide().size());
            assertEquals(198, res.getLoadSide().size());
            // 2025-04
            date = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getGenLoadElecNoAppKwhInfo(date);
            //log.info(res+", 2025-04 GenSize:"+res.getGenSide().size()+", LoadSize:"+res.getLoadSide().size());
            for (int i = 0; i< res.getGenSide().size(); i++)
                assertEquals(2024, res.getGenSide().get(i).getServiceYear());
            for (int i = 0; i< res.getLoadSide().size(); i++)
                assertEquals(2024, res.getLoadSide().get(i).getServiceYear());
            assertEquals(7, res.getGenSide().get(0).getServiceMonth());
            assertEquals(7, res.getLoadSide().get(0).getServiceMonth());
            assertEquals(9, res.getGenSide().get(res.getGenSide().size()-1).getServiceMonth());
            assertEquals(9, res.getLoadSide().get(res.getLoadSide().size()-1).getServiceMonth());
            assertEquals(124, res.getGenSide().size());
            assertEquals(198, res.getLoadSide().size());
        } else {
            assertNull(res);
        }
    }

    @Test // #23 環保處
    public void getFuelLabelPwDsVoltClassKwhTest() throws Exception {
        Date date = tpcMark?new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                :new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportFuelLabelAppTypeVoltClassVo> res = service.getFuelLabelPwDsVoltClassKwh(date);
        //log.info(res+",size:"+res.size());

        // assertion
        if (tpcMark) {
            assertEquals(5, res.size());
            ReportFuelLabelAppTypeVoltClassVo m = res.get(0);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("直", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("5562259", m.getMatchedKw()); //3857227
            m = res.get(1);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("4359126", m.getMatchedKw()); // 2982407
            m = res.get(2);
            assertEquals("風力能(陸域)", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("5363644", m.getMatchedKw()); // 2727290
            m = res.get(3);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("低", m.getVoltClass());
            assertEquals("602332", m.getMatchedKw()); // 406951
            m = res.get(res.size()-1);
            assertEquals("台電太陽能", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("150000", m.getMatchedKw()); // 100000
        } else if (mteT1Mark) {
            assertEquals(6, res.size());
            ReportFuelLabelAppTypeVoltClassVo m = res.get(0);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("直", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("5480129", m.getMatchedKw());
            m = res.get(1);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("直", m.getPwDs());
            assertEquals("低", m.getVoltClass());
            assertEquals("812534", m.getMatchedKw());
            m = res.get(2);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("24008263", m.getMatchedKw());
            m = res.get(3);
            assertEquals("風力能(陸域)", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("8781190", m.getMatchedKw());
            m = res.get(4);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("低", m.getVoltClass());
            assertEquals("174593", m.getMatchedKw());
            m = res.get(res.size()-1);
            assertEquals("台電太陽能", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("747875", m.getMatchedKw());
            // 2025-04
            date = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getFuelLabelPwDsVoltClassKwh(date);
            //log.info(res+",4mon-size:"+res.size());
            assertEquals(5, res.size());
            m = res.get(0);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("直", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("5480129", m.getMatchedKw());
            m = res.get(1);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("直", m.getPwDs());
            assertEquals("低", m.getVoltClass());
            assertEquals("812534", m.getMatchedKw());
            m = res.get(2);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("24108262", m.getMatchedKw());
            m = res.get(3);
            assertEquals("風力能(陸域)", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("高", m.getVoltClass());
            assertEquals("8594218", m.getMatchedKw());
            m = res.get(res.size()-1);
            assertEquals("太陽能", m.getFuelLabel());
            assertEquals("轉", m.getPwDs());
            assertEquals("低", m.getVoltClass());
            assertEquals("75309", m.getMatchedKw());
        } else assertEquals(0, res.size());
    }

    @Test // #2 會計室
    public void getADSTKwhTest() throws Exception {
        Date date = tpcMark? new GregorianCalendar(2025, Calendar.JUNE, 1).getTime()
                : new GregorianCalendar(2025, Calendar.MAY, 1).getTime();
        List<ReportADSTKwhVo> res = service.getADSTKwh(date);
        //log.info("res:"+res);

        // assertion
        String commaStr = ",,,,,";
        if (tpcMark) {
            assertEquals("11503434"+commaStr, res.get(1).getKwh()); // 轉供電能輸電服務收入 6861874
            assertEquals("9697358"+commaStr, res.get(2).getKwh()); // 轉供電能配電服務收入 5413394
            assertEquals("17065693"+commaStr, res.get(3).getKwh()); // 調度服務收入 10719101
            assertEquals("17065693"+commaStr, res.get(4).getKwh()); // 轉直供電能傳輸損失收入 10719101
            assertEquals("17065693"+commaStr, res.get(5).getKwh()); // 輔助服務收入 10719101
            assertEquals("72397871"+commaStr, res.get(6).getKwh()); // 小計 44432571
        } else if (mteT1Mark) {
            assertEquals("139731"+commaStr, res.get(1).getKwh()); // 30724 轉供電能輸電服務收入
            assertEquals("934131"+commaStr, res.get(2).getKwh()); // 30724 轉供電能配電服務收入
            assertEquals("139731"+commaStr, res.get(3).getKwh()); // 30731 調度服務收入
            assertEquals("139731"+commaStr, res.get(4).getKwh()); // 30731 轉直供電能傳輸損失收入
            assertEquals("139731"+commaStr, res.get(5).getKwh()); // 30731 輔助服務收入
            assertEquals("1493055"+commaStr, res.get(6).getKwh()); // 153641 小計
            // 2025-04
            date = new GregorianCalendar(2025, Calendar.APRIL, 1).getTime();
            res = service.getADSTKwh(date);
            //log.info("res-4:"+res);
            assertEquals("36265084"+commaStr, res.get(1).getKwh()); // 轉供電能輸電服務收入
            assertEquals("18652161"+commaStr, res.get(2).getKwh()); // 轉供電能配電服務收入
            assertEquals("42575898"+commaStr, res.get(3).getKwh()); // 調度服務收入
            assertEquals("42575898"+commaStr, res.get(4).getKwh()); // 轉直供電能傳輸損失收入
            assertEquals("42575898"+commaStr, res.get(5).getKwh()); // 輔助服務收入
            assertEquals("182644939"+commaStr, res.get(6).getKwh()); // 小計
        } else assertNull(res);
    }

    @Test
    public void download_BSMIInputTest_Fail() throws Exception {
        doThrow(new RuntimeException("java.lang.RuntimeException: sync host or port:null")).when(routineReportService).downloadBSMIin();
        Exception exception = assertThrows(RuntimeException.class, () -> { routineReportService.downloadBSMIin(); });
        //log.info(exception);

        assertEquals("java.lang.RuntimeException: sync host or port:null", exception.getMessage());
    }

    @Test
    public void BSMIDownloadUploadProcessTest_Fail() throws Exception {
        doThrow(new RuntimeException("java.lang.RuntimeException: sync host or port:null")).when(routineReportService).BSMIDownloadUploadProcess();
        Exception exception = assertThrows(RuntimeException.class, () -> {
            routineReportService.BSMIDownloadUploadProcess(); });
        //assertEquals("BSMI取檔清單下載失敗", name);
        log.info(exception);
        assertEquals("java.lang.RuntimeException: sync host or port:null", exception.getMessage());
    }

    @Test
    public void checkCurrentYearMonthTest() {
        Date tt = service.getMonthFirstDate(new Date());
        boolean res = service.checkCurrentYearMonth(tt);
        assertTrue(res);
        res = service.checkCurrentYearMonth(new Date());
        assertFalse(res);
    }

    private int getDateOfMonth(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        return after.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    private BigDecimal sumLoads(String loads) {
        if (loads.equals(",".repeat(95)) || loads.equals("0,".repeat(95)+"0")) {
            return BigDecimal.ZERO;
        } else {
            BigDecimal sum = BigDecimal.ZERO;
            String [] cc = loads.split(",");
            for (String s : cc) {
                sum = sum.add(new BigDecimal(s));
            }
            return sum;
        }
    }

    private void backChangeMeter() {
        Long meterGId1 = service.setApplicationGeneratorRepository(56L, 4L);
        Long meterGId2 = service.setApplicationGeneratorRepository(90L, 608L);
        Long meterLId3 = service.setApplicationLoadRepository(44L, 467L);
        Long meterGId4 = service.setApplicationLoadRepository(45L, 468L);
        assertEquals(4L, meterGId1);
        assertEquals(608L, meterGId2);
        assertEquals(467L, meterLId3);
        assertEquals(468L, meterGId4);
    }
}