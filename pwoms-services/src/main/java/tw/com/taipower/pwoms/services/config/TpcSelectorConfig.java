package tw.com.taipower.pwoms.services.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @class: TpcRemsConfig
 * @author: daniel
 * @version:
 * @since: 2024-06-06 12:10
 * @see:
 **/

@Data
@Configuration
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "tpc.selector")
public class TpcSelectorConfig {
    Boolean skip;
    /**
     * Selector API 連線設定
     */
    private String url;
    private String username;
    private String password;
    private String clientId;

    public Boolean getSkip() {
        return skip != null ? skip : true;
    }

    public String getUrl() {
        return url != null ? url : "";
    }

    public String getUsername() {
        return username != null ? username : "";
    }

    public String getPassword() {
        return password != null ? password : "";
    }

    public String getClientId() {
        return clientId != null ? clientId : "";
    }
}
