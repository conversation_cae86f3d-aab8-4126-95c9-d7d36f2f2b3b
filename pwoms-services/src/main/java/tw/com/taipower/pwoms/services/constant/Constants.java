package tw.com.taipower.pwoms.services.constant;

/**
 * Constants
 *
 * @class: Constant
 * @author: ting
 * @version: 0.1.0
 * @since: 2023-11-08 19:58
 **/
public class Constants {

    public static final String DATETIME_FORMAT_YYYYMMDD_HHMM_WITH_SLASH = "yyyy/MM/dd HH:mm";
    public static final String DATETIME_FORMAT_YYYYMMDD_HHMMSS_WITH_SLASH = "yyyy/MM/dd HH:mm:ss";
    public static final String DATETIME_FORMAT_YYYYMMDD_HHMM_WITH_DASH = "yyyy-MM-dd HH:mm";
    public static final String DATETIME_FORMAT_YYYYMMDD_HHMMSS_WITH_DASH = "yyyy-MM-dd HH:mm:ss";
    public static final String DATETIME_FORMAT_YYYMM_WITH_DASH = "yyy-MM";
    public static final String DATETIME_FORMAT_YYYYMMDD_WITH_DASH = "yyyy-MM-dd ";
    public static final String DATETIME_FORMAT_YYYMM_HHMM_WITH_DASH = "yyy-MM-dd HH:mm";

    public static final String DATETIME_FORMAT_YYYYMM = "yyyyMM";
    public static final String DATETIME_FORMAT_YYYMMDD = "yyyMMdd";
    public static final String DATETIME_FORMAT_YYYMM = "yyyMM";

    public static final Integer BIGDECIMAL_PRECISION = 9;
    public static final Integer BIGDECIMAL_KW_PRECISION = 4;
    public static final Integer BIGDECIMAL_RATE_PRECISION = 6;

    public static final String NOTIFY_MARK_KEY = "notifyMark";
    public static final String NOTIFY_MARK_POP = "notifyPOP";
    public static final String NOTIFY_SETTING_ERROR_MESSAGE = "[設定通知錯誤訊息]";


    public static final String SUCCESS = "SUCCESS";
    public static final String FAILURE = "FAILURE";
    public static final String IN_PROCESS = "IN PROCESS";

    public static final String NOTIFICATION_URL_MARK = "作業待確認";

    public static final String TMP_FOLDER = "/tmp/";

    // 提供 mte windows vm環境 使用
    public static final String WIN_TMP_FOLDER = "C:\\Users\\<USER>\\Downloads\\";

    public static final String TMP_NOTIFY_FOLDER = "tmp.notify/";
    public static final String TMP_SFTP_FOLDER = "tmp.sftp/";

    public static final Integer ZIP_KAOHSIUNG_GEN_DEVICE_MODE = 1; // 提供 ZIP 加密模式設定
    public static final Integer SFTP_PDC_MODE = 2; // 提供 調度處 sftp 處裡模式設定
    public static final Integer SFTP_PDC_DATE_REMOTE_FOLDER_DATA = 10; // 提供 調度處 sftp 資料類 遠端資料夾
    public static final Integer SFTP_PDC_DATE_REMOTE_FOLDER_LOST_TABLE = 20; // 提供 調度處 sftp 線損表格 遠端資料夾
    public static final Integer SFTP_PDC_DATE_REMOTE_FOLDER_KAO_DATA = 30; // 提供 調度處 sftp 高雄資料 遠端資料夾
    public static final Integer SFTP_PDC_DATE_REMOTE_FOLDER_AD_DATA = 40; // 提供 調度處 sftp 會計處資料 遠端資料夾
    public static final Integer SFTP_PDC_DATE_REMOTE_FOLDER_VL_VALUE = 50; // 提供 調度處 sftp 業務處費率組資料 遠端資料夾
}