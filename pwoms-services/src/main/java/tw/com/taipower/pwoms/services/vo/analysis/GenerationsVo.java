package tw.com.taipower.pwoms.services.vo.analysis;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerationsVo {

    private Long id;
    private Long applicationId;
    private Long generatorId;
    private Boolean isSaleInTrialRun;
    private BigDecimal pmi;
    private String responsibilityVoltage;
    private List<GeneratorEntityMeterVo> generatorEntityMeterList;

}