package tw.com.taipower.pwoms.services.entitymanage;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lowagie.text.Document;
import com.lowagie.text.Font;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import io.micrometer.common.util.StringUtils;
import jakarta.transaction.Transactional;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.logger.CustomLog;
import tw.com.taipower.pwoms.services.BaseService;
import tw.com.taipower.pwoms.services.entitymanage.vo.MeterChildVO;
import tw.com.taipower.pwoms.services.enumclass.ErrorCode;
import tw.com.taipower.pwoms.services.filter.GeneratorEntityFilter;
import tw.com.taipower.pwoms.services.flowcontrol.vo.ConsentLetterVO;
import tw.com.taipower.pwoms.services.system.VoltageLevelService;
import tw.com.taipower.pwoms.services.tpc.RemsService;
import tw.com.taipower.pwoms.services.utils.FileTypeValidator;
import tw.com.taipower.pwoms.services.utils.VoUtils;
import tw.com.taipower.pwoms.services.vo.entity.SimpleMeterSpVO;
import tw.com.taipower.pwoms.services.vo.exception.MessageArgsException;
import tw.com.taipower.pwoms.services.vo.generated.*;
import tw.com.taipower.pwoms.services.vo.utils.PageVo;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 發電端管理服務
 *
 * @class:
 * @author: linyu-sheng
 * @version: 0.0.0
 * @since: 2024/5/3
 * @see:
 */
@Service
@CustomLog
public class GeneratorService extends BaseService {
    @Autowired
    GeneratorEntityRepository generatorEntityRepository;

    @Autowired
    private GeneratorEntityMeterRepository generatorEntityMeterRepository;

    @Autowired
    private GeneratorEntityMeterDisplayRepository generatorEntityMeterDisplayRepository;

    @Autowired
    private GeneratorEntityMeterChildRepository generatorEntityMeterChildRepository;

    @Autowired
    private GeneratorEntityCombinedCapacityRepository generatorEntityCombinedCapacityRepository;

    @Autowired
    private GeneratorEntityMeterVmRepository generatorEntityMeterVmRepository;

    @Autowired
    private EntityDocumentRequiredRepository entityDocumentRequiredRepository;

    @Autowired
    private GeneratorEntityDocumentRepository generatorEntityDocumentRepository;

    @Autowired
    WorkflowDocumentRepository workflowDocumentRepository;

    @Autowired
    private ViewAllEntityMeterRepository viewAllEntityMeterRepository;

    @Autowired
    private TaipowerCompanyUnitRepository taipowerCompanyUnitRepository;

    @Autowired
    private EntityMeterSpRepository entityMeterSpRepository;

    @Autowired
    private EntityService entityService;

    @Autowired
    private MeterCrossService meterCrossService;

    @Autowired
    GeneratorTypeRepository generatorTypeRepository;

    @Autowired
    CityAreaRepository cityAreaRepository;

    @Autowired
    GenerationUnitTypeRepository generationUnitTypeRepository;

    @Autowired
    FuelTypeRepository fuelTypeRepository;

    @Autowired
    FuelFormRepository fuelFormRepository;

    @Autowired
    CombineMethodRepository combineMethodRepository;

    @Autowired
    GeneratorEntityChangeRecordJsonRepository generatorEntityChangeRecordJsonRepository;

    @Autowired
    AccountRepository accountRepository;

    @Autowired
    VoltageLevelService voltageLevelService;

    private final RemsService remsService;

    @Autowired(required = false)
    private MeterHistoryNotifier meterHistoryNotifier;

    // 因為相互依賴，採用lazy注入
    public GeneratorService(@Lazy RemsService remsService) {
        this.remsService = remsService;
    }

    /**
     * 透過進階搜尋過濾查詢結果
     *
     * @param filter
     * @return
     */
    public PageVo<GeneratorEntityVO> findAllByPage(GeneratorEntityFilter filter) {
        var page = generatorEntityRepository.findAll(filter.createCriteria(), filter.toPageable());
        var pageVO = PageVo.toVO(page, GeneratorEntityVO.class);
        return pageVO;

    }

    /**
     * 利用電號查詢相關的發電端( 僅查詢50筆)
     *
     * @param nbsCustomerNumberLike 電號前幾碼
     * @return List<GeneratorEntity>
     */
    public List<GeneratorEntityVO> findByNbsCustomerNumberLike(String nbsCustomerNumberLike) {
        var filter = GeneratorEntityFilter.builder().nbsCustomerNumberLike(nbsCustomerNumberLike).build();
        filter.setPage(0);
        filter.setPageSize(50);
        var pageVO = findAllByPage(filter);
        var simpilified = pageVO.getContents().stream().map(a -> {
            GeneratorEntityVO vo = new GeneratorEntityVO();
            vo.setId(a.getId());
            vo.setNbsCustomerNumber(a.getNbsCustomerNumber());
            vo.setName(a.getName());
            vo.setAlias(a.getAlias());
            return vo;
        }).collect(Collectors.toList());
        return simpilified;
    }

    /**
     * 取得發電端資料VO
     *
     * @param id 發電端ID
     * @return Optional&lt;GeneratorEntity>
     */
    public GeneratorEntityVO findVoById(Long id) {
        var entity = findOneById(id);
        if (entity.isPresent()) {
            return VoUtils.toVO(entity.get(), GeneratorEntityVO.class);
        }
        return null;
    }

    /**
     * 依據電號重新設定台電對應區處
     *
     * @param vo
     */
    public void renewTpcDeptId(GeneratorEntityVO vo) {
        if (StringUtils.isNotBlank(vo.getNbsCustomerNumber())) {
            if (vo.getNbsCustomerNumber().length() >= 2) {
                String code = vo.getNbsCustomerNumber().substring(0, 2);
                try {
                    var unit = taipowerCompanyUnitRepository.findByLayerAndCode(2, code);
                    if (unit == null) {
                        throwException(ErrorCode.ELECNO_ERROR);
                    } else {
                        vo.setTpcDeptId(unit.getId());
                    }
                } catch (MessageArgsException ex) {
                    throw ex;
                } catch (Throwable ex) {
                    log.error("{} {}", code, ex);
                    throwException(ErrorCode.TPC_DEPT_ERROR);
                }
            }
        }
    }

    public void renewTpcDeptId(GeneratorEntity entity) {
        if (StringUtils.isNotBlank(entity.getNbsCustomerNumber())) {
            if (entity.getNbsCustomerNumber().length() >= 2) {
                String code = entity.getNbsCustomerNumber().substring(0, 2);
                var unit = taipowerCompanyUnitRepository.findByLayerAndCode(2, code);
                if (unit == null) {
                    entity.setTpcDeptId(null);
                } else {
                    entity.setTpcDeptId(unit.getId());
                }
            }
        }
    }

    /**
     * 依據getGeneratorEntityCombinedCapacities更新容量
     *
     * @param vo
     */
    public void renewCapacity(GeneratorEntityVO vo) {
        var totalCppacity = vo.getGeneratorEntityCombinedCapacities().stream().filter(
                        a -> a.getCombinedDate() != null && a.getLicenseDate() != null).map(
                        GeneratorEntityCombinedCapacityVO::getCapacity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        var totalCombineCppacity = vo.getGeneratorEntityCombinedCapacities().stream().filter(
                        a -> a.getCombinedDate() != null).map(
                        GeneratorEntityCombinedCapacityVO::getCapacity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        vo.setCapacityApplied(totalCppacity);
        vo.setCapacityUndergo(totalCombineCppacity);

        if (vo.getCapacityLive() == null) {
            vo.setCapacityLive(BigDecimal.ZERO);
        }

    }

    /**
     * 儲存發電端資料，包含併網資料
     *
     * @param vo
     */
    public void save(GeneratorEntityVO vo, Long userId) {
        renewTpcDeptId(vo);

        var opt = generatorEntityRepository.findById(vo.getId());
        if (opt.isPresent()) {
            VoUtils.mergeObjectWithNullIgnore(vo, opt.get());
            // opt.get().setTpcDeptId(vo.getTpcDeptId()); // 有可能為null
            //
            // if (StringUtils.isNotBlank(vo.getNbsCustomerNumber())) {
            // if (vo.getNbsCustomerNumber().length() >= 2) {
            // String code = vo.getNbsCustomerNumber().substring(0, 2);
            // var unit = taipowerCompanyUnitRepository.findByLayerAndCode(2, code);
            // if (unit == null) {
            // opt.get().setTpcDeptId(null);
            // } else {
            // opt.get().setTpcDeptId(unit.getId());
            // }
            // }
            // }
            opt.get().setModifiedBy(userId);
            try {
                if (opt.get().getIsPvStorage() != null && opt.get().getIsPvStorage()) {
                    /**
                     * 如果有光儲的話，將其產生一個中間表，
                     */
                    addXS9SpeicalMeter(userId, opt.get());
                }
            } catch (Throwable ex) {
                log.error(ex, ex);
            }

            if (vo.getGeneratorEntityCombinedCapacities() != null) {
                renewCapacity(vo);
                var capacityList = getCombinedCapacityList(vo.getId());
                var updateCombinedList = vo.getGeneratorEntityCombinedCapacities().stream().map(updateCapacity -> {
                    updateCapacity.setModifiedBy(userId);
                    updateCapacity.setGeneratorEntityId(vo.getId());
                    var found = capacityList.stream().filter(
                            capacity -> capacity.getId().equals(updateCapacity.getId())).findFirst();
                    if (found.isPresent()) {
                        VoUtils.mergeObjectWithNullIgnore(updateCapacity, found.get());
                        // 日期有可能為null，還是要強制修改
                        found.get().setLicenseDate(updateCapacity.getLicenseDate());
                        found.get().setCombinedDate(updateCapacity.getCombinedDate());
                        return VoUtils.toEntity(found.get(), GeneratorEntityCombinedCapacity.class);
                    } else {
                        return VoUtils.toEntity(updateCapacity, GeneratorEntityCombinedCapacity.class);
                    }
                }).collect(Collectors.toList());
                generatorEntityCombinedCapacityRepository.saveAll(updateCombinedList);
                /**
                 * 已取得電業執照裝置容量（kW）（自動算出不人工輸入）：
                 * 有併網首日、有取得執照日期的容量加總
                 * 因為列表會用到，所以更新時一並計算更新
                 */
                opt.get().setCapacityApplied(vo.getCapacityApplied());
                opt.get().setCapacityUndergo(vo.getCapacityUndergo());
            }
        }
        generatorEntityRepository.save(opt.get());
        // else {
        // // throw exception if not found ?
        // }
    }

    /**
     * 查詢電壓層級對應標籤
     *
     * @param value
     * @param voltageLevels
     * @return
     */
    String voltageLabel(Integer value, List<VoltageLevel> voltageLevels) {
        if (value == null) {

            return null;
        }
        var optVoltage = voltageLevels.stream().filter(a -> a.getId().equals(value)).findFirst();
        return optVoltage.map(VoltageLevel::getLabel).orElse(null);
    }

    public String compareResult(GeneratorEntity dbGen, GeneratorEntityVO newVO, List<VoltageLevel> voltageLevels) {
        boolean notSame = false;
        /**
         * 更新vo整理的容量資訊
         */
        if (newVO.getGeneratorEntityCombinedCapacities() != null) {
            renewCapacity(newVO);
        }
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("電號：%s %s 修改項目如下：", dbGen.getNbsCustomerNumber(), dbGen.getName()));
        List<String> changes = new ArrayList<>();
        if (!dbGen.getNbsCustomerNumber().equals(newVO.getNbsCustomerNumber())) {
            changes.add(String.format("%s ( %s -> %s )", "電號", dbGen.getNbsCustomerNumber(),
                    newVO.getNbsCustomerNumber()));
        }

        if ((dbGen.getVoltage() != null && newVO.getVoltage() == null) ||
                (dbGen.getVoltage() == null && newVO.getVoltage() != null) ||
                (dbGen.getVoltage() != null && newVO.getVoltage() != null && !dbGen.getVoltage().equals(
                        newVO.getVoltage()))) {
            changes.add(
                    String.format("%s ( %s -> %s )", "併接點電壓層級", voltageLabel(dbGen.getVoltage(), voltageLevels),
                            voltageLabel(newVO.getVoltage(), voltageLevels)));

        }
        if ((dbGen.getResponsibilityVoltage() != null && newVO.getResponsibilityVoltage() == null) ||
                (dbGen.getResponsibilityVoltage() == null && newVO.getResponsibilityVoltage() != null) ||
                (dbGen.getResponsibilityVoltage() != null && newVO.getResponsibilityVoltage() != null && !dbGen.getResponsibilityVoltage().equals(
                        newVO.getResponsibilityVoltage()))) {
            changes.add(String.format("%s ( %s -> %s )", "責任分界點電壓層級",
                    voltageLabel(dbGen.getResponsibilityVoltage(), voltageLevels),
                    voltageLabel(newVO.getResponsibilityVoltage(), voltageLevels)));

        }
        try {

            if ((dbGen.getCapacityApplied() != null && newVO.getCapacityApplied() == null) ||
                    (dbGen.getCapacityApplied() == null && newVO.getCapacityApplied() != null) ||
                    (dbGen.getCapacityApplied() != null && newVO.getCapacityApplied() != null && !dbGen.getCapacityApplied().equals(
                            newVO.getCapacityApplied()))) {
                changes.add(String.format("%s ( %s -> %s )", "電業執照裝置容量",
                        dbGen.getCapacityApplied(),
                        newVO.getCapacityApplied()));
            }
        } catch (Throwable ex) {
            log.error("compare error, {}, {}", dbGen, newVO);
            changes.add(String.format("%s ( %s -> %s )", "電業執照裝置容量",
                    dbGen.getCapacityApplied(),
                    newVO.getCapacityApplied()));
        }
        try {

            if ((dbGen.getCapacityUndergo() != null && newVO.getCapacityUndergo() == null) ||
                    (dbGen.getCapacityUndergo() == null && newVO.getCapacityUndergo() != null) ||
                    (dbGen.getCapacityUndergo() != null && newVO.getCapacityUndergo() != null && !dbGen.getCapacityUndergo().equals(
                            newVO.getCapacityUndergo()))) {
                changes.add(String.format("%s ( %s -> %s )", "併網容量",
                        dbGen.getCapacityUndergo(),
                        newVO.getCapacityUndergo()));
            }
        } catch (Throwable ex) {
            log.error("compare error, {}, {}", dbGen, newVO);
            changes.add(String.format("%s ( %s -> %s )", "併網容量",
                    dbGen.getCapacityUndergo(),
                    newVO.getCapacityUndergo()));
        }
        if (changes.isEmpty()) {
            return null;
        }
        return sb.toString() + String.join(",", changes);
    }

    /**
     * 從RNIS儲存發電端資料，包含併網資料
     *
     * @param vo
     */
    public String saveFromRNIS(GeneratorEntityVO vo, List<VoltageLevel> voltageLevels, Long userId) {
        renewTpcDeptId(vo);
        Optional<GeneratorEntity> opt = Optional.empty();
        if (vo.getRelationId() != null)
            opt = Optional.ofNullable(generatorEntityRepository.findByRelationId(vo.getRelationId()));
        if (opt.isEmpty()) {
            /**
             * 換以電號尋找
             */
            opt = Optional.ofNullable(generatorEntityRepository.findOneByNbsCustomerNumber(vo.getNbsCustomerNumber()));
        }
        if (opt.isEmpty()) {
            vo.setCreatedAt(new Date());
            vo.setCreatedBy(userId);
            opt = Optional.of(generatorEntityRepository.save(VoUtils.toEntity(vo, GeneratorEntity.class)));
        }
        var dbGen = opt.get();
        String message = compareResult(dbGen, vo, voltageLevels);
        vo.setId(opt.get().getId());
        VoUtils.mergeObjectWithNullIgnore(vo, opt.get());
        opt.get().setTpcDeptId(vo.getTpcDeptId()); // 有可能為null
        opt.get().setModifiedBy(userId);
        if (opt.get().getIsPvStorage()) {
            /**
             * 如果有光儲的話，將其產生一個中間表，
             */
            addXS9SpeicalMeter(userId, opt.get());
        }

        opt = Optional.of(generatorEntityRepository.save(opt.get()));

        if (vo.getGeneratorEntityCombinedCapacities() != null) {
            renewCapacity(vo);
            var capacityList = getCombinedCapacityList(vo.getId());
            Optional<GeneratorEntity> finalOpt = opt;
            var updateCombinedList = vo.getGeneratorEntityCombinedCapacities().stream().map(updateCapacity -> {
                updateCapacity.setModifiedBy(userId);
                updateCapacity.setGeneratorEntityId(finalOpt.get().getId());
                var found = capacityList.stream().filter(
                                capacity -> capacity.getGenCode().equals(
                                        updateCapacity.getGenCode())
                                        && capacity.getGenNo().equals(
                                        updateCapacity.getGenNo()))
                        .findFirst();
                if (found.isPresent()) {
                    VoUtils.mergeObjectWithNullIgnore(updateCapacity, found.get());
                    // 2025/06/25 日期如果null，則不強制修改
//                    found.get().setLicenseDate(updateCapacity.getLicenseDate());
//                    found.get().setCombinedDate(updateCapacity.getCombinedDate());
//                    if (found.get().getTerminateDate() == null)
//                        found.get().setTerminateDate(updateCapacity.getTerminateDate());
                    return VoUtils.toEntity(found.get(), GeneratorEntityCombinedCapacity.class);
                } else {
                    return VoUtils.toEntity(updateCapacity, GeneratorEntityCombinedCapacity.class);
                }
            }).collect(Collectors.toList());
            generatorEntityCombinedCapacityRepository.saveAll(updateCombinedList);
            /**
             * 已取得電業執照裝置容量（kW）（自動算出不人工輸入）：
             * 有併網首日、有取得執照日期的容量加總
             * 因為列表會用到，所以更新時一並計算更新
             */
            opt.get().setCapacityApplied(vo.getCapacityApplied());
            opt.get().setCapacityUndergo(vo.getCapacityUndergo());
            generatorEntityRepository.save(opt.get());
        }
        return message;
    }

    /**
     * 檢查發電端電表是否有光儲表，如果沒有就新增
     * 如果有新增回傳 true
     *
     * @param userId
     * @param opt
     */
    public boolean addXS9SpeicalMeter(Long userId, GeneratorEntity opt) {
        var existMeters = generatorEntityMeterRepository.findByGeneratorEntityId(opt.getId());
        if (existMeters.stream().filter(a -> a.getMeterNo().startsWith("XS9")).count() == 0) {
            var customerNo = opt.getNbsCustomerNumber();
            var xs9meter = GeneratorEntityMeterVO.builder().groupType("ESS").generatorEntityId(opt.getId()).meterNo(
                            "XS9" + customerNo.substring(customerNo.length() - 7)).hasAmi(true).connected(true).forceUse(
                            false)
                    .build();
            addMeterOnly(xs9meter, opt.getId(), userId);
            return true;
        }
        return false;
    }

    /**
     * 取得發電端資料
     *
     * @param id 發電端ID
     * @return Optional&lt;GeneratorEntity>
     */
    public Optional<GeneratorEntityVO> findOneById(Long id) {
        return generatorEntityRepository.findById(id).isPresent() ? Optional.of(
                VoUtils.toVO(generatorEntityRepository.findById(id).get(), GeneratorEntityVO.class)) : Optional.empty();
    }

    /**
     * 透過發電端中間表ID尋找發電端子表以及其實際對應
     * 含過濾特殊電表
     *
     * @param generatorEntityId
     * @return
     */
    public List<GeneratorEntityMeterVO> findMeterByGeneratorEntityId(Long generatorEntityId) {
        var meters = generatorEntityMeterRepository.findByGeneratorEntityId(generatorEntityId);
        // meters = meters.stream().filter(a ->
        // a.getIsSpecial().equals(isSpecial)).collect(Collectors.toList());
        return fillDisplayAndChildsInfo(meters);
    }

    public List<GeneratorEntityMeterVO> findSpecialMeterByGeneratorEntityId(Long generatorEntityId) {
        var meters = generatorEntityMeterRepository.findByGeneratorEntityId(generatorEntityId);
        meters = meters.stream().filter(a -> a.getIsSpecial() != null && a.getIsSpecial()).collect(Collectors.toList());
        return fillDisplayAndChildsInfo(meters);
    }

    public List<GeneratorEntityMeterVO> findNoneSpecialMeterByGeneratorEntityId(
            Long generatorEntityId) throws JsonProcessingException {
        var meters = generatorEntityMeterRepository.findByGeneratorEntityId(generatorEntityId);
        meters = meters.stream().filter(a -> a.getIsSpecial() == null || !a.getIsSpecial()).toList();
        return fillDisplayAndChildsInfo(meters);
    }

    public List<GeneratorEntityMeterVO> findMeterByGeneratorEntityIdWithWorkflow3a(
            Long generatorEntityId) throws JsonProcessingException {
        var meters = generatorEntityMeterRepository.findByGeneratorEntityId(generatorEntityId);
        var tempGeneratorEntity = this.getTempGeneratorEntity(generatorEntityId);
        if (tempGeneratorEntity.getIsPvStorage()) {
            var entity = generatorEntityRepository.findById(generatorEntityId).orElseThrow();
            if (this.addXS9SpeicalMeter(0L, entity)) {
                meters = generatorEntityMeterRepository.findByGeneratorEntityId(generatorEntityId);
            }

        }
        return fillDisplayAndChildsInfo(meters);
    }

    /**
     * 依據GeneratorEntityMeter補上display以及child關聯
     *
     * @param meters
     * @return
     */
    public List<GeneratorEntityMeterVO> fillDisplayAndChildsInfo(List<GeneratorEntityMeter> meters) {
        var generatorEntityMeterIds = meters.stream().map(a -> a.getId()).collect(Collectors.toList());
        var meterChilds = generatorEntityMeterChildRepository.findByGeneratorEntityMeterIdIn(generatorEntityMeterIds);
        var spMeterChilds = entityService.findEntityMeterByEntityTypeAndIds("G", generatorEntityMeterIds);
        var meterDisplays = generatorEntityMeterDisplayRepository
                .findByGeneratorEntityMeterIdIn(generatorEntityMeterIds);
        var meterVos = meters.stream().map(a -> VoUtils.toVO(a, GeneratorEntityMeterVO.class))
                .collect(Collectors.toList());
        meterVos.parallelStream().forEach(vo -> {
            var list = meterChilds.stream().filter(a -> a.getGeneratorEntityMeterId().equals(vo.getId())).map(
                    a -> VoUtils.toVO(a, GeneratorEntityMeterChildVO.class)).collect(Collectors.toList());
            vo.setGeneratorEntityMeterChildren(list);

            var splist = spMeterChilds.stream().filter(a -> a.getEntityMeterId().equals(vo.getId())).collect(
                    Collectors.toList());

            vo.setEntityMeters(splist);

            var displays = meterDisplays.stream().filter(a -> a.getGeneratorEntityMeterId().equals(vo.getId())).map(
                    a -> VoUtils.toVO(a, GeneratorEntityMeterDisplayVO.class)).collect(Collectors.toList());
            vo.setGeneratorEntityMeterDisplays(displays);
        });
        return meterVos;
    }

    /**
     * 利用客戶電號，尋找所有電表（包含過去的電表）
     *
     * @param customerNo
     * @return
     */
    public List<ViewAllEntityMeterVO> findEntityMeterChildByCustomerNo(String customerNo) {
        var meters = viewAllEntityMeterRepository.findDistinctByCustomerNo(customerNo);
        return meters.stream().map(a -> VoUtils.toVO(a, ViewAllEntityMeterVO.class)).collect(Collectors.toList());
    }

    public List<SimpleMeterSpVO> findEntityMeterByCustomerNo(String customerNo) {
        return entityService.findEntityMeterChildByCustomerNo(customerNo);
    }

    /**
     * 更新電表基座，看需求要不要補上Transactional
     *
     * @param vo
     */
    public Long updateFullMeter(GeneratorEntityMeterVO vo, Long userId) {

        /**
         * 儲存電表基座
         */

        var meter = VoUtils.toEntity(vo, GeneratorEntityMeter.class);
        meter.setModifiedBy(userId);
        meter.renewAmi();
        var dbdata = generatorEntityMeterRepository.save(meter);

        /**
         * 處理子表關聯
         */

        if (vo.getGeneratorEntityMeterChildren() != null) {
            var existIds = vo.getGeneratorEntityMeterChildren().stream().filter(a -> a.getId() != null).map(
                    a -> a.getId()).collect(Collectors.toList());
            var existDatas = generatorEntityMeterChildRepository.findAllById(existIds).stream()
                    .collect(Collectors.toList());

            var newDatas = vo.getGeneratorEntityMeterChildren().stream().map(child -> {
                if (child.getId() != null) {
                    var opt = existDatas.stream().filter(a -> a.getId().equals(child.getId())).findFirst();
                    if (opt.isPresent()) {
                        VoUtils.mergeObjectWithNullIgnore(child, opt.get());
                    }
                    return null;
                } else {
                    var newData = VoUtils.toEntity(child, GeneratorEntityMeterChild.class);
                    newData.setGeneratorEntityMeterId(dbdata.getId());
                    return newData;
                }
            }).filter(a -> a != null).collect(Collectors.toList());
            existDatas.addAll(newDatas);
            generatorEntityMeterChildRepository.saveAll(existDatas);
        }

        /**
         * 處理電表顯示資訊
         */

        if (vo.getGeneratorEntityMeterDisplays() != null) {
            var existIds = vo.getGeneratorEntityMeterDisplays().stream().filter(a -> a.getId() != null).map(
                    a -> a.getId()).collect(Collectors.toList());
            var existDatas = generatorEntityMeterDisplayRepository.findAllById(existIds);

            List<GeneratorEntityMeterDisplay> newDatas = vo.getGeneratorEntityMeterDisplays().stream().map(child -> {
                if (child.getId() != null) {
                    var opt = existDatas.stream().filter(a -> a.getId().equals(child.getId())).findFirst();
                    if (opt.isPresent()) {
                        VoUtils.mergeObjectWithNullIgnore(child, opt.get());
                    }
                    return null;
                } else {
                    var newData = VoUtils.toEntity(child, GeneratorEntityMeterDisplay.class);
                    newData.setGeneratorEntityMeterId(dbdata.getId());
                    return (newData);
                }
            }).filter(a -> a != null).collect(Collectors.toList());
            existDatas.addAll(newDatas);
            generatorEntityMeterDisplayRepository.saveAll(existDatas);

        }
        return dbdata.getId();
    }

    public Long updateMeterOnly(GeneratorEntityMeterVO vo, Long userId) {

        /**
         * 儲存電表基座
         */
        var opt = generatorEntityMeterRepository.findById(vo.getId());
        if (opt.isPresent()) {
            VoUtils.mergeObjectWithNullIgnore(vo, opt.get());
            opt.get().setModifiedBy(userId);
            opt.get().renewAmi();
            var result = generatorEntityMeterRepository.save(opt.get());
            return result.getId();
        }
        throw new RuntimeException("data not found.");
    }

    public void deleteMeterOnly(Long meterId, Long userId) {

        /**
         * 需要一個DB trigger紀錄刪除的電表項目
         */
        // var opt = generatorEntityMeterRepository.findById(meterId);
        // if (opt.isPresent()) {
        // opt.get().setModifiedBy(userId);
        // opt.get().setActive(false);
        // generatorEntityMeterRepository.save(opt.get());
        // }
        generatorEntityMeterRepository.deleteById(meterId);
    }

    public void deleteMeterChildByMeterId(Long meterId, Long userId) {

        /**
         * 需要一個DB trigger紀錄刪除的電表項目
         */
        var children = generatorEntityMeterChildRepository.findByGeneratorEntityMeterIdIn(List.of(meterId));
        if (!children.isEmpty()) {
            generatorEntityMeterChildRepository.deleteAll(children);
        }
    }

    public void deleteMeterDisplayByMeterId(Long meterId, Long userId) {

        /**
         * 需要一個DB trigger紀錄刪除的電表項目
         */
        var children = generatorEntityMeterDisplayRepository.findByGeneratorEntityMeterIdIn(List.of(meterId));
        if (!children.isEmpty()) {
            generatorEntityMeterDisplayRepository.deleteAll(children);
        }
    }

    /**
     * @param vo
     * @param entityId
     * @param userId
     */
    @Transactional
    public void addMeterOnly(GeneratorEntityMeterVO vo, Long entityId, Long userId) {
        vo.setModifiedAt(null);
        var entity = generatorEntityRepository.findById(entityId).get();
        var meter = VoUtils.toEntity(vo, GeneratorEntityMeter.class);

        /**
         * 新增電表的時候也得做表號重複檢查
         */
        if (meter.getIsSpecial() == null || !meter.getIsSpecial()) {

            var meterNos = List.of(vo.getMeterNo());
            var duplicateRecords = generatorEntityMeterChildRepository.countDuplicateMeterNoExceptSelf(-1L, meterNos);
            if (!duplicateRecords.isEmpty()) {
                var duplicateMeters = duplicateRecords.stream().map(a -> a[0]).collect(Collectors.toList());
                throw new RuntimeException("meterNo errors with " + duplicateMeters.toString());
            }
        }
        meter.setModifiedBy(userId);
        meter.setGeneratorEntityId(entityId);
        meter.renewAmi();
        meter = generatorEntityMeterRepository.save(meter);
        Date useFrom = DateUtils.truncate(new Date(), Calendar.DATE);

        /**
         * 初次新建，需要建立display對應
         */
        var display = GeneratorEntityMeterDisplay.builder().generatorEntityMeterId(meter.getId()).meterNo(
                meter.getMeterNo()).modifiedBy(userId).useFrom(useFrom).build();
        generatorEntityMeterDisplayRepository.save(display);

        /**
         * 只有非特殊電表初次新建，才建立child關聯
         * 特殊電表實際關聯跟當前表號可能無關
         */
        if (meter.getIsSpecial() == null || !meter.getIsSpecial()) {

            var child = GeneratorEntityMeterChild.builder().generatorEntityMeterId(meter.getId()).meterNo(
                            meter.getMeterNo()).customerNo(entity.getNbsCustomerNumber()).channel(3L).operator(1L).useFrom(
                            useFrom)
                    .build();
            generatorEntityMeterChildRepository.save(child);
        }
    }

    /**
     * 確認對象更新電表清單，如果是特殊表計，檢查圖檔並且變更圖檔使用區間
     *
     * @param sourceIds
     * @param vos
     * @param userId
     */
    @Transactional
    public void updateMeterChildsWithIds(Long generatorEntityMeterId, List<Long> sourceIds,
                                         List<GeneratorEntityMeterChildVO> vos, Long vmId, Long userId) {

        var meter = generatorEntityMeterRepository.findById(generatorEntityMeterId);
        if (!meter.isPresent()) {
            throw new RuntimeException("meter not found.");
        }
        var generatorEntity = generatorEntityRepository.findById(meter.get().getGeneratorEntityId()).orElseThrow();
        var customerNo = generatorEntity.getNbsCustomerNumber();
        /**
         * 如果非特殊表計更新電表歷程，需檢查表號是否有重複
         */
        if (meter.get().getIsSpecial() == null || !meter.get().getIsSpecial()) {
            var meterNos = vos.stream().map(a -> a.getMeterNo()).collect(Collectors.toList());
            var duplicateRecords = generatorEntityMeterChildRepository.countDuplicateMeterNoExceptSelf(
                    generatorEntityMeterId,
                    meterNos);
            if (!duplicateRecords.isEmpty()) {
                var duplicateMeters = duplicateRecords.stream().map(a -> a[0]).collect(Collectors.toList());
                throw new RuntimeException("meterNo errors with " + duplicateMeters.toString());
            }
        }

        List<MeterChildVO> updateRecords = new ArrayList<>();

        var oldRelations = generatorEntityMeterChildRepository.findAllById(sourceIds);
        GeneratorEntityMeterVm oldVm = null;
        if (!oldRelations.isEmpty()) {
            var first = oldRelations.stream().findFirst().get();
            oldVm = GeneratorEntityMeterVm.builder().generatorEntityMeterId(first.getGeneratorEntityMeterId()).useFrom(
                    first.getUseFrom()).useTo(first.getUseTo()).build();
        }

        vos.stream().forEach(vo -> {
            vo.setModifiedAt(null);
            var origin = oldRelations.stream().filter(a -> a.getId().equals(vo.getId())).findFirst();
            if (origin.isPresent()) {
                var record = MeterChildVO.builder()
                        .oldCustomerNo(customerNo)
                        .oldMeterNo(origin.get().getMeterNo())
                        .oldStartDate(origin.get().getUseFrom())
                        .oldEndDate(origin.get().getUseTo())
                        .newCustomerNo(customerNo)
                        .newMeterNo(vo.getMeterNo())
                        .newStartDate(vo.getUseFrom())
                        .newEndDate(vo.getUseTo())
                        .action("UPDATE")
                        .entityType("G")
                        .build();
                updateRecords.add(record);
                VoUtils.mergeObjectWithNullIgnore(vo, origin.get());
                // 例外，如果有傳送，就得按照原本資料定義更新
                origin.get().setUseTo(vo.getUseTo());
            }
        });
        var keepIds = vos.stream().map(a -> a.getId()).filter(a -> a != null).collect(Collectors.toList());
        var keepRelations = oldRelations.stream().filter(a -> keepIds.contains(a.getId())).collect(Collectors.toList());
        var newRelations = vos.stream().filter(a -> a.getId() == null).map(
                a -> VoUtils.toEntity(a, GeneratorEntityMeterChild.class)).collect(Collectors.toList());

        keepRelations.addAll(newRelations);
        var removeRelations = oldRelations.stream().filter(a -> !keepIds.contains(a.getId()))
                .collect(Collectors.toList());
        generatorEntityMeterChildRepository.saveAll(keepRelations);

        newRelations.stream().forEach(a -> {
            var record = MeterChildVO.builder()
                    .newCustomerNo(customerNo)
                    .newMeterNo(a.getMeterNo())
                    .newStartDate(a.getUseFrom())
                    .newEndDate(a.getUseTo())
                    .action("ADD")
                    .entityType("G")
                    .build();
            updateRecords.add(record);
        });
        removeRelations.stream().forEach(a -> {
            var record = MeterChildVO.builder()
                    .oldCustomerNo(customerNo)
                    .oldMeterNo(a.getMeterNo())
                    .oldStartDate(a.getUseFrom())
                    .oldEndDate(a.getUseTo())
                    .action("DELETE")
                    .entityType("G")
                    .build();
            updateRecords.add(record);
        });
        generatorEntityMeterChildRepository.deleteAll(removeRelations);
        var firstRelation = keepRelations.stream().findFirst();

        /**
         * 非特殊電表的話，同步更新display資料與Child同
         */
        if (meter.get().getIsSpecial() == null || !meter.get().getIsSpecial()) {
            var displays = generatorEntityMeterDisplayRepository.findByGeneratorEntityMeterId(generatorEntityMeterId);
            var meterNos = keepRelations.stream().map(a -> a.getMeterNo()).collect(Collectors.toList());
            var removeDisplays = displays.stream().filter(a -> !meterNos.contains(a.getMeterNo()))
                    .collect(Collectors.toList());
            generatorEntityMeterDisplayRepository.deleteAll(removeDisplays);
            var keepDisplays = displays.stream().filter(a -> meterNos.contains(a.getMeterNo()))
                    .collect(Collectors.toList());
            var indbDisplays = keepRelations.stream().map(relation -> {
                var first = keepDisplays.stream().filter(a -> a.getMeterNo().equals(relation.getMeterNo())).findFirst();
                if (first.isPresent()) {
                    first.get().setUseFrom(relation.getUseFrom());
                    first.get().setUseTo(relation.getUseTo());
                    return first.get();
                }
                GeneratorEntityMeterDisplay display = GeneratorEntityMeterDisplay.builder()
                        .generatorEntityMeterId(generatorEntityMeterId).meterNo(
                                relation.getMeterNo())
                        .useFrom(relation.getUseFrom()).useTo(
                                relation.getUseTo())
                        .build();
                return display;
            }).collect(Collectors.toList());
            generatorEntityMeterDisplayRepository.saveAll(indbDisplays);
            // 還要修改當前電表的顯示名稱
            var first = indbDisplays.stream().sorted(
                    Comparator.comparing(GeneratorEntityMeterDisplay::getUseFrom).reversed()).findFirst();
            if (first.isPresent()) {
                meter.get().setMeterNo(first.get().getMeterNo());
                meter.get().renewAmi();
                generatorEntityMeterRepository.save(meter.get());
            }

        } else {
            /**
             * 特殊電表的話，可能會有圖檔，所以需要尋找舊圖檔並且更新日期使用區間
             */
            if (oldVm != null) {
                var oldDbVm = generatorEntityMeterVmRepository.findOne(Example.of(oldVm));
                if (oldDbVm.isPresent()) {
                    oldDbVm.get().setUseFrom(firstRelation.get().getUseFrom());
                    oldDbVm.get().setUseTo(firstRelation.get().getUseTo());
                    generatorEntityMeterVmRepository.save(oldDbVm.get());
                }
            }
            if (vmId != null) {
                var dbVm = generatorEntityMeterVmRepository.findById(vmId);
                if (dbVm.isPresent()) {
                    dbVm.get().setUseFrom(firstRelation.get().getUseFrom());
                    dbVm.get().setUseTo(firstRelation.get().getUseTo());
                    generatorEntityMeterVmRepository.save(dbVm.get());
                }
            }
        }

        // 如果有注入 MeterHistoryNotifier，則發送通知
        if (meterHistoryNotifier != null) {
            meterHistoryNotifier.notifyMeterHistoryChange(updateRecords, userId);
        }
    }

    @Transactional
    public void updateSPMeterChildsWithIds(Long generatorEntityMeterId, List<Long> sourceIds, List<EntityMeterSpVO> vos,
                                           String image, Long userId) {

        var optMeter = generatorEntityMeterRepository.findById(generatorEntityMeterId);
        if (!optMeter.isPresent()) {
            throw new RuntimeException("meter not found.");
        }
        var meter = optMeter.get();
        if (!meter.getIsSpecial()) {
            throw new RuntimeException("meter is not special.");
        }

        var result = entityService.addOrUpdateSPRelations(sourceIds, vos, userId);
        var oldRelations = result.oldRelations();
        var firstRelation = result.firstRelation();

        GeneratorEntityMeterVm oldVm = null;
        if (!oldRelations.isEmpty()) {
            var first = oldRelations.stream().findFirst().get();
            oldVm = GeneratorEntityMeterVm.builder().generatorEntityMeterId(first.getEntityMeterId()).useFrom(
                    first.getUseFrom()).useTo(first.getUseTo()).build();
        }
        /**
         * 特殊電表的話，可能會有圖檔，所以需要尋找舊圖檔並且更新日期使用區間
         */

        if (oldVm != null) {
            var oldDbVm = generatorEntityMeterVmRepository.findAll(Example.of(oldVm)).stream().findFirst();
            if (oldDbVm.isPresent()) {
                oldDbVm.get().setUseFrom(firstRelation.get().getUseFrom());
                oldDbVm.get().setUseTo(firstRelation.get().getUseTo());
                if (image != null) {
                    image = image.replace("data:image/png;base64,", "");
                    byte[] decodedBytes = Base64.getDecoder().decode(image);
                    oldDbVm.get().setContent(decodedBytes);
                }
                generatorEntityMeterVmRepository.save(oldDbVm.get());
            }
        } else {
            var first = firstRelation.get();
            if (image != null) {
                var vm = GeneratorEntityMeterVm.builder().generatorEntityMeterId(first.getEntityMeterId()).useFrom(
                        first.getUseFrom()).useTo(first.getUseTo()).build();

                image = image.replace("data:image/png;base64,", "");
                byte[] decodedBytes = Base64.getDecoder().decode(image);
                vm.setContent(decodedBytes);
                generatorEntityMeterVmRepository.save(vm);
            }

        }

    }

    // 移除，有entityService統一處理
    // @Transactional
    // public void removeSPMeterChildsWithIds(List<Long> sourceIds, Long userId) {
    // entityMeterSpRepository.deleteAllById(sourceIds);
    // }

    /**
     * 根據來源ＩＤ刪除電表關聯
     *
     * @param sourceIds
     * @param userId
     */
    @Transactional
    public void removeMeterChildsWithIds(List<Long> sourceIds, Long userId) {
        generatorEntityMeterChildRepository.deleteAllById(sourceIds);

    }

    /**
     * 新增中間表別名
     *
     * @param meterId
     * @param vo
     * @param userId
     */
    public void addMeterDisplay(Long meterId, GeneratorEntityMeterDisplayVO vo, Long userId) {
        updateMeterDisplay(meterId, vo, userId);
    }

    /**
     * 更新中間表別名
     *
     * @param meterId
     * @param vo
     * @param userId
     */
    public void updateMeterDisplay(Long meterId, GeneratorEntityMeterDisplayVO vo, Long userId) {
        GeneratorEntityMeterDisplay entity = VoUtils.toEntity(vo, GeneratorEntityMeterDisplay.class);
        entity.setGeneratorEntityMeterId(meterId);
        entity.setModifiedBy(userId);
        generatorEntityMeterDisplayRepository.save(entity);
    }

    /**
     * 更新目標電表顯示歷程
     *
     * @param sourceIds
     * @param vos
     * @param userId
     */
    @Transactional
    public void updateMeterDisplaysWithIds(List<Long> sourceIds, List<GeneratorEntityMeterDisplayVO> vos, Long userId) {
        var oldRelations = generatorEntityMeterDisplayRepository.findAllById(sourceIds);
        vos.stream().forEach(vo -> {
            var origin = oldRelations.stream().filter(a -> a.getId().equals(vo.getId())).findFirst();
            if (origin.isPresent()) {
                VoUtils.mergeObjectWithNullIgnore(vo, origin.get());
            }
        });
        var keepIds = vos.stream().map(a -> a.getId()).filter(a -> a != null).collect(Collectors.toList());
        var keepRelations = oldRelations.stream().filter(a -> keepIds.contains(a.getId())).collect(Collectors.toList());
        var newRelations = vos.stream().filter(a -> a.getId() == null).map(
                a -> VoUtils.toEntity(a, GeneratorEntityMeterDisplay.class)).collect(Collectors.toList());
        keepRelations.addAll(newRelations);
        var removeRelations = oldRelations.stream().filter(a -> !keepIds.contains(a.getId()))
                .collect(Collectors.toList());
        generatorEntityMeterDisplayRepository.saveAll(keepRelations);
        generatorEntityMeterDisplayRepository.deleteAll(removeRelations);
    }

    /**
     * 利用發電端ID查詢實際可用電表
     *
     * @param generatorEntityId 發電端ID
     * @return
     */
    public List<GeneratorEntityMeterChildVO> getAvailableMeter(Long generatorEntityId) {
        var optEntity = generatorEntityRepository.findById(generatorEntityId);
        if (!optEntity.isPresent()) {
            return List.of();
        }
        var entity = optEntity.get();
        // 取得對應電號
        var electricNo = entity.getNbsCustomerNumber();
        var childs = generatorEntityMeterChildRepository.findByCustomerNo(electricNo);
        var today = new Date().getTime();
        return childs.stream().filter(a -> a.getUseFrom().getTime() <= today && today <= a.getUseTo().getTime()).map(
                a -> VoUtils.toVO(a, GeneratorEntityMeterChildVO.class)).collect(Collectors.toList());
    }

    /**
     * 利用發電端ID查詢實際可用電表
     *
     * @param generatorEntityId 發電端ID
     * @return
     */
    public List<GeneratorEntityCombinedCapacityVO> getCombinedCapacityList(Long generatorEntityId) {
        return generatorEntityCombinedCapacityRepository.findByGeneratorEntityId(generatorEntityId).stream().map(
                a -> VoUtils.toVO(a, GeneratorEntityCombinedCapacityVO.class)).collect(Collectors.toList());
    }

    /**
     * 取得單一電表所有圖檔清單
     *
     * @param generatorEntityMeterId
     * @return
     */
    public List<GeneratorEntityMeterVmVO> getMeterVmList(Long generatorEntityMeterId) {
        var example = GeneratorEntityMeterVm.builder().generatorEntityMeterId(generatorEntityMeterId).build();
        var list = generatorEntityMeterVmRepository.findAll(Example.of(example));
        return list.stream().map(vm -> VoUtils.toVO(vm, GeneratorEntityMeterVmVO.class)).collect(Collectors.toList());
    }

    /**
     * 上傳特殊表計的架構關聯圖
     *
     * @param generatorEntityMeterId
     * @param userId
     * @param file
     * @throws IOException
     */
    public Long uploadMeterVm(Long generatorEntityMeterId, Date startDate, Date endDate, MultipartFile file,
                              Long userId) throws Exception {
        FileTypeValidator.checkValid(file);
        /**
         * 前端使用上，都沒有傳送originStartDate跟originEndDate，所以取消這個判斷，直接根據startDate跟endDate尋找並且更新
         */
        if (startDate == null && endDate == null) {
            // ，再新增新的檔案
            var newvmFile = GeneratorEntityMeterVm.builder().generatorEntityMeterId(generatorEntityMeterId).modifiedBy(
                    userId).useFrom(startDate).useTo(endDate).content(file.getBytes()).build();
            var result = generatorEntityMeterVmRepository.save(newvmFile);
            return result.getId();
        }

        var example = GeneratorEntityMeterVm.builder().generatorEntityMeterId(generatorEntityMeterId).useFrom(
                startDate).useTo(endDate).build();
        var updateTarget = generatorEntityMeterVmRepository.findOne(Example.of(example));
        if (updateTarget.isPresent()) {
            updateTarget.get().setContent(file.getBytes());
            var result = generatorEntityMeterVmRepository.save(updateTarget.get());
            return result.getId();
        } else {
            // ，再新增新的檔案
            var newvmFile = GeneratorEntityMeterVm.builder().generatorEntityMeterId(generatorEntityMeterId).modifiedBy(
                    userId).useFrom(startDate).useTo(endDate).content(file.getBytes()).build();
            var result = generatorEntityMeterVmRepository.save(newvmFile);
            return result.getId();
        }

    }

    /**
     * 回傳特殊表計關聯圖檔
     *
     * @param generatorEntityMeterId
     * @return
     */
    public GeneratorEntityMeterVmVO getMeterVm(Long generatorEntityMeterId, Date startDate, Date endDate) {
        var example = GeneratorEntityMeterVm.builder().generatorEntityMeterId(generatorEntityMeterId).useFrom(
                startDate).useTo(endDate).build();
        var optTarget = generatorEntityMeterVmRepository.findOne(Example.of(example));
        if (!optTarget.isPresent()) {
            return null;
        }

        var vo = VoUtils.toVO(optTarget.get(), GeneratorEntityMeterVmVO.class);
        vo.setContent(optTarget.get().getContent());
        return vo;

    }

    /**
     * 回傳特殊表計關聯圖檔
     *
     * @param generatorEntityMeterId
     * @return
     */
    public GeneratorEntityMeterVmVO getMeterVmById(Long generatorEntityMeterId, Long id) {
        var example = GeneratorEntityMeterVm.builder().generatorEntityMeterId(generatorEntityMeterId).id(id).build();
        var optTarget = generatorEntityMeterVmRepository.findOne(Example.of(example));
        if (!optTarget.isPresent()) {
            return null;
        }

        var vo = VoUtils.toVO(optTarget.get(), GeneratorEntityMeterVmVO.class);
        vo.setContent(optTarget.get().getContent());
        return vo;

    }

    /**
     * 編輯特殊表計關聯圖檔使用日期區間
     *
     * @param generatorEntityMeterId
     * @param originStartDate
     * @param originEndDate
     * @param startDate
     * @param endDtate
     * @param userId
     */
    public void updateMeterVmUseRangeOnly(Long generatorEntityMeterId, Date originStartDate, Date originEndDate,
                                          Date startDate, Date endDtate, Long userId) {
        var example = GeneratorEntityMeterVm.builder().generatorEntityMeterId(generatorEntityMeterId).useFrom(
                originStartDate).useTo(originEndDate).build();
        var optTarget = generatorEntityMeterVmRepository.findOne(Example.of(example));
        if (!optTarget.isPresent()) {
            throw new RuntimeException("meter picture not found");
        }
        var target = optTarget.get();
        target.setUseFrom(startDate);
        target.setUseTo(endDtate);
        target.setModifiedBy(userId);
        generatorEntityMeterVmRepository.save(target);
    }

    /**
     * 因應申請書搜尋功能、利用filter查詢，並以暫存區資料優先
     *
     * @param filter
     * @return
     */
    public PageVo<GeneratorEntityVO> getTempGeneratorEntitysByFilter(GeneratorEntityFilter filter, Long userId) {
        ObjectMapper objectMapper = new ObjectMapper();
        var pageVo = findAllByPage(filter);
        // var generatorEntityIds = pageVo.getContents().stream().map(a ->
        // a.getId()).collect(Collectors.toList());
        try {
            // 檢驗電號規則是不是完整的，且有記錄
            if (remsService.verifyCustomerNumber(filter.getNbsCustomerNumberOrNameLike())) {
                if (pageVo.getTotal() == 0) {
                    // 如果電號正確且查無資料，代表需要進行同步
                    remsService.syncRNISWithCustomNumbers(List.of(filter.getNbsCustomerNumberOrNameLike()), userId);
                    // 同步以後再次查詢一次
                    pageVo = findAllByPage(filter);
                }
            } else {
                // 如果不是正確的電號格式，不做任何處理
            }
        } catch (Throwable ex) {
            log.error(ex, ex);
        }
        // 不再需要暫存區資料
        // var temps =
        // tempGeneratorEntityRepository.findByGeneratorEntityIdIn(generatorEntityIds).stream().map(temp
        // -> {
        // try {
        // return objectMapper.readValue(temp.getContent(), GeneratorEntityVO.class);
        // } catch (JsonProcessingException e) {
        // return null;
        // }
        // }).filter(a -> a != null).collect(Collectors.toList());
        var newContents = pageVo.getContents().stream().map(entity -> {
            // var optFirst = temps.stream().filter(a ->
            // a.getId().equals(entity.getId())).findFirst();
            // if (optFirst.isPresent()) {
            // return optFirst.get();
            // }
            // 需要撈取併網容量以及電表資訊
            entity.setGeneratorEntityMeterList(findMeterByGeneratorEntityId(entity.getId()));
            entity.setGeneratorEntityCombinedCapacities(getCombinedCapacityList(entity.getId()));
            return entity;
        }).collect(Collectors.toList());
        pageVo.setContents(newContents);
        return pageVo;
    }

    /**
     * 以電號查找批次發電端ＩＤ查詢暫存區以及正式區
     *
     * @param customerNos
     * @return
     */
    public List<GeneratorEntityVO> getTempGeneratorEntitysByCustomerNos(List<String> customerNos) {
        var formalEntitys = generatorEntityRepository.findAllByNbsCustomerNumberIn(customerNos);
        var result = formalEntitys.stream().map(formal -> {
            var vo = VoUtils.toVO(formal, GeneratorEntityVO.class);
            vo.setGeneratorEntityMeterList(findMeterByGeneratorEntityId(vo.getId()));
            vo.setGeneratorEntityCombinedCapacities(getCombinedCapacityList(vo.getId()));
            return vo;
        }).collect(Collectors.toList());
        return result;
    }

    /**
     * 批次發電端ＩＤ查詢暫存區以及正式區
     *
     * @param generatorEntityIds
     * @return
     */
    public List<GeneratorEntityVO> getTempGeneratorEntitys(List<Long> generatorEntityIds) {
        // 需要在正式區尋找資料
        var formalEntities = generatorEntityRepository.findAllById(generatorEntityIds).stream().map(
                a -> VoUtils.toVO(a, GeneratorEntityVO.class)).collect(Collectors.toList());
        formalEntities.stream().forEach(entity -> {
            entity.setGeneratorEntityMeterList(findMeterByGeneratorEntityId(entity.getId()));
            entity.setGeneratorEntityCombinedCapacities(getCombinedCapacityList(entity.getId()));
        });
        return formalEntities;
    }

    /**
     * 回傳草稿
     */

    public GeneratorEntityVO getTempGeneratorEntity(Long generatorEntityId) throws JsonProcessingException {
        log.info("load from entity");
        var data = findOneById(generatorEntityId);
        if (data.isPresent()) {
            data.get().setGeneratorEntityMeterList(findMeterByGeneratorEntityId(generatorEntityId));
            data.get().setGeneratorEntityCombinedCapacities(getCombinedCapacityList(generatorEntityId));
            return data.get();
        } else
            return null;
    }

    public void setTempGeneratorEntity(GeneratorEntityVO vo, Long userId) throws JsonProcessingException {
        vo.setModifiedAt(null);
        this.save(vo, userId);
    }

    public List<EntityDocumentRequiredVO> findAllRequiredDocumentsWithoutGeneratorInfo() {
        var vos = entityDocumentRequiredRepository.findAllByType("G").stream().map(
                a -> VoUtils.toVO(a, EntityDocumentRequiredVO.class)).collect(Collectors.toList());
        var documents = workflowDocumentRepository.findAllById(
                vos.stream().map(EntityDocumentRequiredVO::getDocumentId).toList());
        documents.forEach(doc -> {
            vos.stream().filter(vo -> vo.getDocumentId().equals(doc.getId())).forEach(vo -> {
                resetDocRequired(vo, doc);
            });
        });
        return vos;
    }

    // /**
    // * 根據發電端ID以及契約種類，取得發電端應附文件
    // *
    // * @param id
    // * @param applicationType
    // * @return
    // */
    // public List<EntityDocumentRequiredVO> findAllRequiredDocuments(Long id,
    // String applicationType) throws
    // JsonProcessingException {
    // GeneratorEntityVO generator = getTempGeneratorEntity(id);
    // var ag = ApplicationGenerator.builder().txTargetIsBranch(false).build();
    // return findAllRequiredDocuments(generator, applicationType, ag);
    // }

    /**
     * 根據發電端資訊以及契約種類，取得發電端應附文件
     *
     * @param generator
     * @param applicationType
     * @return
     * @throws JsonProcessingException
     */
    public List<EntityDocumentRequiredVO> findGeneratorAllRequiredDocuments(GeneratorEntityVO generator,
                                                                            String applicationType,
                                                                            ApplicationGenerator ag) throws JsonProcessingException {
        var vos = entityDocumentRequiredRepository.findAllByType("G").stream().map(
                a -> VoUtils.toVO(a, EntityDocumentRequiredVO.class)).collect(Collectors.toList());
        var documentIds = vos.stream().map(EntityDocumentRequiredVO::getDocumentId).collect(Collectors.toList());
        var documents = workflowDocumentRepository.findAllById(documentIds);
        vos = vos.stream().filter(vo -> {
            try {
                if (vo.getDocumentId().equals(3)) {
                    // 發電業籌設或擴建許可函
                    // 本發電端有新增設發電設備=是 AND 設備型別=第一型
                    if (generator.getIsNewDevice() && generator.getGenerationUnitType().equals(1L)) {
                        return true;
                    }
                    return false;
                }
                if (vo.getDocumentId().equals(4)) {
                    // 工作許可函或、工作許可證或、同意備案函
                    // 本發電端有新增設發電設備=是 AND 設備型別=第二、三型
                    if (generator.getIsNewDevice()) {
                        if ((generator.getGenerationUnitType().equals(2L) || generator.getGenerationUnitType().equals(
                                3L))) {
                            return true;
                        }
                    }
                    return false;
                }
                if (vo.getDocumentId().equals(7)) {
                    // 竣工現勘審查會議紀錄函
                    // 本發電端有既設發電設備=是 AND 設備型別=三轉一
                    if (generator.getIsOldDevice() && generator.getGenerationUnitType().equals(4L)) {
                        return true;
                    }
                    return false;
                }
                if (vo.getDocumentId().equals(8)) {
                    // 電業執照繳費證明掃描檔案
                    // 本發電端有既設發電設備=是 AND 設備型別=三轉一
                    if (generator.getIsOldDevice() && generator.getGenerationUnitType().equals(4L)) {
                        return true;
                    }
                    return false;
                }
                if (vo.getDocumentId().equals(9)) {
                    // 再生能源電業執照 (發電) + 經營方式4選項
                    // 本發電端有既設發電設備=是 AND 設備型別=第一型、三轉一
                    if (generator.getIsOldDevice() && (generator.getGenerationUnitType().equals(
                            1L) || generator.getGenerationUnitType().equals(4L))) {
                        return true;
                    }
                    return false;
                }
                if (vo.getDocumentId().equals(10)) {
                    // 自用發電設備登記證
                    // 本發電端有既設發電設備=是 AND 設備型別=第二型
                    if (generator.getIsOldDevice() && generator.getGenerationUnitType().equals(2L)) {
                        return true;
                    }
                    return false;
                }
                if (vo.getDocumentId().equals(11)) {
                    // 設備登記證明文件
                    // 本發電端有既設發電設備=是 AND 設備型別=第三型
                    if (generator.getIsOldDevice() && generator.getGenerationUnitType().equals(3L)) {
                        return true;
                    }
                    return false;
                }
                if (vo.getDocumentId().equals(12)) {
                    // 經濟部能源局核發之儲能容量核配同意函
                    // 是否有光儲=是
                    if (generator.getIsPvStorage()) {
                        return true;
                    }
                    return false;
                }
                if (vo.getDocumentId().equals(13)) {
                    // 經濟部「商工登記公示資料查詢服務」查詢結果網頁截圖
                    // 轉供對象是否為發電端分支機構=是，要看發用電端申請者統編比對，比較方法還是得再送審出去時去控管
                    if (applicationType.equals("5")) {
                        return ag.getTxTargetIsBranch();
                    }
                    return false;
                }
                if (vo.getDocumentId().equals(14)) {
                    // 財政部「稅籍登記資料公示查詢」查詢結果網頁截圖
                    // 轉供對象是否為發電端分支機構=是，要看發用電端申請者統編比對，比較方法還是得再送審出去時去控管
                    if (applicationType.equals("5")) {
                        return ag.getTxTargetIsBranch();
                    }
                    return false;
                }

                if (vo.getDocumentId().equals(15)) {
                    // 台電函覆轉/直供同意函

                    return true;
                }
                if (vo.getDocumentId().equals(16)) {
                    // 電業管制機關核發之直供核准函
                    // 契約類型=直供、直供+轉供
                    if (applicationType.equals("2") || applicationType.equals("3")) {
                        return true;
                    }
                    return false;
                }

                if (vo.getDocumentId().equals(17)) {
                    // 主管機關核發之同意併聯試運轉期間轉供相關證明文件
                    // 併聯試運轉期間是否轉供=是

                    if (generator.getIsSaleInTrialRun()) {
                        return true;
                    }

                    return false;
                }

                if (vo.getDocumentId().equals(18)) {
                    // 加強電力網之繳費憑證（共2期）
                    // 能源別=風力（離岸）
                    if (generator.getFuelType().equals(2) && generator.getFuelForm().equals(5)) {
                        return true;
                    }
                    return false;
                }

                if (vo.getDocumentId().equals(19)) {
                    // 台電同意終止（電能購售）契約函
                    // 需要發電端為躉售
                    if (generator.getIsWholesale()) {
                        return true;
                    }
                    return false;
                }

                return true;
            } catch (Throwable ex) {
                return false;
            }
        }).collect(Collectors.toList());
        vos.forEach(vo -> {
            var optFirst = documents.stream().filter(a -> a.getId().equals(vo.getDocumentId())).findFirst();
            optFirst.ifPresent(workflowDocument -> vo.setName(workflowDocument.getName()));
        });
        return vos;
    }

    /**
     * 取得當前發電端已上傳之文件
     *
     * @param entityId
     * @return
     */
    public List<GeneratorEntityDocumentVO> findUploadedDocumentsByEntityId(Long entityId) {
        var list = generatorEntityDocumentRepository.findAllByGeneratorEntityId(entityId).stream().map(
                a -> VoUtils.toVO(a, GeneratorEntityDocumentVO.class)).collect(Collectors.toList());
        list.forEach(a -> a.setContent(null));
        return list;
    }

    /**
     * 取得對應文件
     *
     * @param id
     * @return
     */
    public GeneratorEntityDocumentVO getUploadedDocumentByEntityIdAndId(Long id) {
        var model = generatorEntityDocumentRepository.findById(id).get();
        var result = VoUtils.toVO(model, GeneratorEntityDocumentVO.class);
        result.setContent(model.getContent());
        return result;
    }

    /**
     * 儲存檔案並且回報檔案ＩＤ
     *
     * @param entityId
     * @param documentId
     * @param file
     * @param userId
     * @return
     * @throws IOException
     */
    public Long uploadDocumentByEntityIdAndDocumentId(Long entityId, Integer documentId, MultipartFile file,
                                                      String unit, String serialNumber, String operationMode,
                                                      Date validDate, String comment, Date issueDate,
                                                      Long userId) throws Exception {

        FileTypeValidator.checkValid(file);
        checkFileUploadColumnValid(documentId, unit, serialNumber, operationMode, validDate, comment, issueDate);
        var model = GeneratorEntityDocument.builder().generatorEntityId(entityId).documentId(documentId).build();
        var dbDocument = generatorEntityDocumentRepository.findOne(Example.of(model));
        if (dbDocument.isPresent()) {
            model = dbDocument.get();
            if (file != null) {
                model.setContent(file.getBytes());
                model.setExt(getFileExtension(file));
            }
            model.setUnit(unit);
            model.setSerialNumber(serialNumber);
            model.setOperationMode(operationMode);
            model.setValidDate(validDate);
            model.setModifiedBy(userId);
            model.setComment(comment);
            var result = generatorEntityDocumentRepository.save(dbDocument.get());
            return result.getId();
        } else {
            model.setModifiedBy(userId);
            if (file != null) {
                model.setContent(file.getBytes());
                model.setExt(getFileExtension(file));
            }
            model.setUnit(unit);
            model.setSerialNumber(serialNumber);
            model.setOperationMode(operationMode);
            model.setValidDate(validDate);
            model.setComment(comment);
            model.setModifiedBy(userId);
            var result = generatorEntityDocumentRepository.save(model);
            return result.getId();
        }
    }

    /**
     * 依據表號，找出最新特殊表計公式組成
     *
     * @param meterId
     * @return
     */
    public List<EntityMeterSpVO> findSpecialMeterChilds(Long meterId) {
        var list = entityMeterSpRepository.findByEntityTypeAndEntityMeterId("G", meterId);
        var first = list.stream().sorted(Comparator.comparing(EntityMeterSp::getUseFrom).reversed()).map(
                a -> a.getUseFrom()).findFirst();
        if (first.isPresent()) {
            var useFrom = first.get();
            var results = list.stream().filter(a -> a.getUseFrom().equals(useFrom)).map(a -> VoUtils.toVO(a,
                    EntityMeterSpVO.class)).toList();
            return results;
        }
        return new ArrayList();
    }

    /**
     * 處理特殊表計並且回傳表計ID
     *
     * @param meter
     * @param userId
     * @return
     */
    public Long processSpecialMeter(GeneratorEntityMeterVO meter, Long userId) {

        var generatorId = meter.getGeneratorEntityId();
        var spMeter = generatorEntityMeterRepository.findByGeneratorEntityId(generatorId).stream().filter(
                a -> a.getIsSpecial() != null && a.getIsSpecial()).findFirst();
        if (spMeter.isPresent()) {
            var meterId = spMeter.get().getId();
            var currentChilds = findSpecialMeterChilds(meterId);
            var spChilds = meter.getEntityMeters();
            for (EntityMeterSpVO spChild : spChilds) {
                spChild.setEntityMeterId(meterId);
                spChild.setEntityType("G");
                entityService.fillChildEntityTypeAndMeterIdForVO(spChild);
            }
            entityService.processMeterChilds(currentChilds, spChilds, meter.getImage());
            return meterId;
        } else {
            var newMeter = VoUtils.toEntity(meter, GeneratorEntityMeter.class);
            newMeter.setGeneratorEntityId(generatorId);
            newMeter.renewAmi();
            var resultMeter = generatorEntityMeterRepository.save(newMeter);
            var spChilds = meter.getEntityMeters().stream().map(a -> {
                a.setEntityType("G");
                a.setEntityMeterId(resultMeter.getId());
                entityService.fillChildEntityTypeAndMeterIdForVO(a);
                return a;
            }).toList();
            entityService.processMeterChilds(List.of(), spChilds, meter.getImage());
            return resultMeter.getId();
        }
    }

    /**
     * 超過2100個參數需要分段查詢
     *
     * @param loadIds
     * @return
     */
    public List<GeneratorEntity> batchQuery(List<Long> loadIds) {
        var totalSize = loadIds.size();
        log.info("batch query size {}", totalSize);
        if (totalSize < 2000) {
            return generatorEntityRepository.findAllById(loadIds);
        }
        List<GeneratorEntity> results = new ArrayList<>();

        int index = 0;
        int batchSize = 2000;
        do {
            int fromIndex = index * batchSize;
            if (fromIndex > totalSize) {
                break;
            }
            var subLoadIds = loadIds.subList(fromIndex, Math.min((index + 1) * batchSize, totalSize));
            if (subLoadIds.isEmpty()) {
                break;
            }
            var loads = generatorEntityRepository.findAllById(subLoadIds);
            results.addAll(loads);
            index++;
        } while (true);

        return results;
    }

    boolean recordStart = true;

    public ConsentLetterVO getGeneratorChangeRecords(Integer generatorId) {
        if (columnDescriptionMap == null)
            initColumnLang();
        ByteArrayOutputStream bos = new ByteArrayOutputStream();

        Document document = new Document(PageSize.A4);
        String no = generatorId.toString();
        try {
            var list = generatorEntityChangeRecordJsonRepository.findAllByOriginalId(generatorId);
            var entity = generatorEntityRepository.findById(list.getFirst().getOriginalId().longValue()).get();
            no = entity.getNbsCustomerNumber();
            // FileOutputStream fos = new FileOutputStream("change.pdf");
            PdfWriter.getInstance(document, bos);
            document.open();
            addTextCenter(document, entity.getName() + "  " + entity.getNbsCustomerNumber(), Font.NORMAL);
            PdfPTable table = new PdfPTable(5);
            table.setWidthPercentage(new float[]{1f, 1f, 1f, 1f, 1f}, document.getPageSize());
            table.setSpacingBefore(10);
            table.setSpacingAfter(10);
            table.setTotalWidth(document.getPageSize().getWidth() - document.leftMargin() - document.rightMargin());
            table.setLockedWidth(true);
            String[] content = {"修改日期", "修改人", "欄位", "舊值", "新值"};
            for (String s : content) {
                addTextCenter(table, s, Font.BOLD);
            }
            list.forEach(record -> {
                try {
                    recordStart = true;
                    addText(table, record.getChangeAt().toString(), Font.NORMAL);
                    var account = accountRepository.findById(record.getModifiedBy().longValue()).get();
                    addText(table, account.getName(), Font.NORMAL);
                    HashMap<String, String> columns = new ObjectMapper().readValue(record.getChanges(), HashMap.class);
                    columns.forEach((column, changes) -> {
                        try {
                            if (!recordStart) {
                                addText(table, "", Font.NORMAL);
                                addText(table, "", Font.NORMAL);
                            }
                            addText(table, columnDescriptionMap.get(column), Font.NORMAL);
                            HashMap<String, String> values = new ObjectMapper().readValue(changes, HashMap.class);
                            addText(table, idToLabel(column, values.get("OldValue")), Font.NORMAL);
                            addText(table, idToLabel(column, values.get("NewValue")), Font.NORMAL);
                            recordStart = false;
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
            document.add(table);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        document.close();
        return ConsentLetterVO.builder().content(bos.toByteArray()).no(no).build();
    }

    void addText(Document document, String text, int fontStyle) throws IOException {
        Font font = new Font(getBaseFont(), 12, fontStyle);
        Paragraph paragraph = new Paragraph(text, font);
        document.add(paragraph);
    }

    void addTextCenter(Document document, String text, int fontStyle) throws IOException {
        Font font = new Font(getBaseFont(), 12, fontStyle);
        Paragraph paragraph = new Paragraph(text, font);
        paragraph.setAlignment(Paragraph.ALIGN_CENTER);
        document.add(paragraph);
    }

    void addText(PdfPTable table, String text, int fontStyle) throws IOException {
        Font font = new Font(getBaseFont(), 12, fontStyle);
        Paragraph paragraph = new Paragraph(text, font);
        PdfPCell cell = new PdfPCell(paragraph);
        cell.setPadding(5);
        table.addCell(cell);
    }

    void addTextCenter(PdfPTable table, String text, int fontStyle) throws IOException {
        Font font = new Font(getBaseFont(), 12, fontStyle);
        Paragraph paragraph = new Paragraph(text, font);
        PdfPCell cell = new PdfPCell(paragraph);
        cell.setPadding(5);
        cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
        table.addCell(cell);
    }

    private BaseFont getBaseFont() throws IOException {
        if (bfChinese == null)
            bfChinese = BaseFont.createFont("NotoSerifTC-VariableFont_wght.ttf", BaseFont.IDENTITY_H,
                    BaseFont.NOT_EMBEDDED, true, IOUtils.toByteArray(new ClassPathResource("fonts/NotoSerifTC" +
                            "-VariableFont_wght.ttf").getInputStream()),
                    null);
        return bfChinese;
    }

    private String idToLabel(String column, String sId) {
        Integer id;
        try {
            id = Integer.parseInt(sId);
        } catch (Exception e) {
            return sId;
        }
        if (column.contentEquals("TYPE")) {
            return generatorTypeRepository.findById(id.longValue()).get().getLabel();
        } else if (column.contentEquals("ADDRESS_AREA") || column.contentEquals("RESPONSIBLE_PERSON_ADDRESS_AREA")) {
            var city = cityAreaRepository.findById(id.longValue()).get();
            return city.getCityName() + city.getAreaName();
        } else if (column.contentEquals("VOLTAGE")) {
            return voltageLevelService.findById(id).getLabel();
        } else if (column.contentEquals("GENERATION_UNIT_TYPE")) {
            return generationUnitTypeRepository.findById(id.longValue()).get().getLabel();
        } else if (column.contentEquals("FUEL_TYPE")) {
            return fuelTypeRepository.findById(id).get().getLabel();
        } else if (column.contentEquals("FUEL_FORM")) {
            return fuelFormRepository.findById(id).get().getLabel();
        } else if (column.contentEquals("HANDLE_REMAINING")) {
            return id == 1 ? "全額轉供自用" : "自發自用後餘電轉供自用";
        } else if (column.contentEquals("IS_PV_STORAGE_REASON")) {
            return id == 1 ? "已取得正式文件：已取得儲能容量核配同意函" : "還未取得文件（必須在提交本計畫書時附上文件，否則無法提交）";
        } else if (column.contentEquals("WHOLESALE_REASON")) {
            return id == 1 ? "已取得台電同意終止（電能購售）契約函" : "還未取得文件（必須在轉供前提交） ";
        } else if (column.contentEquals("WHOLESALE_TYPE")) {
            return id == 1 ? "轉供餘躉" : "直供餘躉";
        } else if (column.contentEquals("NEW_DEVICE_REASON")) {
            return id == 1 ? "已取得正式文件：再生能源發電業執照或設備登記之證明" : "還未取得正式文件：但有籌設擴建/工作許可函/同意備案（正式文件必須在轉供前提交） ";
        } else if (column.contentEquals("SALE_IN_TRIAL_RUN_REASON")) {
            return id == 1 ? "已取得併聯試運轉期間可轉供證明文件" : "還未取得文件（必須在併聯前提交）";
        } else if (column.contentEquals("RESPONSIBILITY_VOLTAGE")) {
            return voltageLevelService.findById(id).getLabel();
        } else if (column.contentEquals("COMBINE_METHOD")) {
            return combineMethodRepository.findById(id).get().getLabel();
        } else if (column.contentEquals("TPC_DEPT_ID")) {
            return taipowerCompanyUnitRepository.findById(id).get().getUnitName();
        }

        return id.toString();
    }

    BaseFont bfChinese = null;
    HashMap<String, String> columnDescriptionMap;

    private void initColumnLang() {
        columnDescriptionMap = new HashMap<>();
        columnDescriptionMap.put("ID", "ID");
        columnDescriptionMap.put("NAME", "發電端名稱");
        columnDescriptionMap.put("ALIAS", "發電端簡稱");
        columnDescriptionMap.put("TYPE", "發電類別");
        columnDescriptionMap.put("NBS_CUSTOMER_NUMBER", "發電端電號");
        columnDescriptionMap.put("TAX_ID", "發電端統編/身分證號");
        columnDescriptionMap.put("RESPONSIBLE_PERSON", "負責人名稱");
        columnDescriptionMap.put("RESPONSIBLE_PERSON_PHONE", "負責人電話");
        columnDescriptionMap.put("RESPONSIBLE_PERSON_ADDRESS_AREA", "負責人地址區域");
        columnDescriptionMap.put("RESPONSIBLE_PERSON_ADDRESS_OTHER", "負責人地址其他");
        columnDescriptionMap.put("NOTES", "發電端備註");
        columnDescriptionMap.put("RNIS_NOTES", "RNIS 備註");
        columnDescriptionMap.put("BASIC_INFO_APPROVAL_AT", "基本訊息批准時間");
        columnDescriptionMap.put("CREATED_AT", "創建時間");
        columnDescriptionMap.put("CREATED_BY", "創建者");
        columnDescriptionMap.put("MODIFIED_BY", "修改者");
        // columnDescriptionMap.put("STATUS", "狀態");
        columnDescriptionMap.put("ADDRESS_AREA", "發電設備地址區域");
        columnDescriptionMap.put("ADDRESS_OTHER", "發電設備地址其他");
        // columnDescriptionMap.put("SITE_NAME", "場地名稱");
        // columnDescriptionMap.put("NBS_METER_NUMBER", "NBS 電表號碼");
        // columnDescriptionMap.put("FIT_TERMINATED", "FIT 終止");
        columnDescriptionMap.put("VOLTAGE", "併接點電壓層級");
        columnDescriptionMap.put("FEEDER", " 饋線別（最多4碼）");
        // columnDescriptionMap.put("METER", "電表");
        // columnDescriptionMap.put("PCC_TYPE", "PCC 類型");
        // columnDescriptionMap.put("PCC_LOCATION", "PCC 位置");
        // columnDescriptionMap.put("VM_ILLUSTRATION", "VM 圖示");
        columnDescriptionMap.put("GENERATION_UNIT_TYPE", "設備型別");
        columnDescriptionMap.put("FUEL_TYPE", "能源別");
        columnDescriptionMap.put("FUEL_FORM", "能源設置方式");
        // columnDescriptionMap.put("IS_MULTI_UNIT", "是否多單元");
        columnDescriptionMap.put("HANDLE_REMAINING", "轉供自用類型");
        columnDescriptionMap.put("IS_MULTI_STAGE", "本發電端採分期併網");
        columnDescriptionMap.put("CAPACITY_APPLIED", "電業執照裝置容量 (kW)");
        columnDescriptionMap.put("CAPACITY_UNDERGO", "併網容量(kW)");
        columnDescriptionMap.put("CAPACITY_LIVE", "籌設許可函裝置容量 (kW)");
        // columnDescriptionMap.put("GENERATION_UNIT_AGREEMENT", "發電單元協議");
        // columnDescriptionMap.put("GRID_CONNECTION_AGREEMENT", "併網協議");
        columnDescriptionMap.put("IS_PV_STORAGE", "是否有光儲");
        // columnDescriptionMap.put("IS_TEMP_NBS_CUSTOMER_NUMBER", "本電號為臨時電號");
        columnDescriptionMap.put("TIME_STG", "時間 STG");
        columnDescriptionMap.put("CONTRACT_STG", "合同 STG");
        columnDescriptionMap.put("IS_PV_STORAGE_REASON", "光儲原因");
        // columnDescriptionMap.put("IS_MULTI_STAGE_REASON", "本發電端採分期併網原因");
        // columnDescriptionMap.put("IS_TEMP_NBS_CUSTOMER_NUMBER_REASON", "臨時 NBS
        // 客戶號原因");
        // columnDescriptionMap.put("TAIPOWER_COMPANY_UNIT_ID", "台電公司單元 ID");
        columnDescriptionMap.put("IS_WHOLESALE", "是否有躉售合約");
        // columnDescriptionMap.put("WHOLESALE_FROM", "批發始於");
        columnDescriptionMap.put("WHOLESALE_REASON", "躉售原因");
        columnDescriptionMap.put("WHOLESALE_TYPE", "躉購方式");
        columnDescriptionMap.put("IS_NEW_DEVICE", "新增設發電設備");
        columnDescriptionMap.put("NEW_DEVICE_REASON", "新增設發電設備原因");
        columnDescriptionMap.put("IS_SALE_IN_TRIAL_RUN", "併聯試運轉期間是否轉供");
        columnDescriptionMap.put("SALE_IN_TRIAL_RUN_REASON", "併聯試運轉期間是否轉供原因");
        columnDescriptionMap.put("RESPONSIBILITY_VOLTAGE", "責任分界點電壓層級");
        columnDescriptionMap.put("COMBINE_METHOD", "併網方式");
        columnDescriptionMap.put("IS_OLD_DEVICE", "既有發電設備");
        columnDescriptionMap.put("TPC_DEPT_ID", "本發電端負責區處");
        columnDescriptionMap.put("RELATION_ID", "Relation ID");
        columnDescriptionMap.put("LAST_REVIEW_PASSED_DATE", "最後一次審查通過日期");
    }

    @Transactional
    public void addMeterOnlyWithSpecificDate(GeneratorEntityMeterVO vo, Long entityId, Long userId, Date useFrom) {
        vo.setModifiedAt(null);
        var entity = generatorEntityRepository.findById(entityId).get();
        var meter = VoUtils.toEntity(vo, GeneratorEntityMeter.class);

        /**
         * 新增電表的時候也得做表號重複檢查
         */
        if (meter.getIsSpecial() == null || !meter.getIsSpecial()) {

            var meterNos = List.of(vo.getMeterNo());
            var duplicateRecords = generatorEntityMeterChildRepository.countDuplicateMeterNoExceptSelf(-1L,
                    meterNos);
            if (!duplicateRecords.isEmpty()) {
                var duplicateMeters = duplicateRecords.stream().map(a -> a[0]).collect(Collectors.toList());
                throw new RuntimeException("meterNo errors with " + duplicateMeters.toString());
            }
        }
        meter.setModifiedBy(userId);
        meter.setGeneratorEntityId(entityId);
        meter.renewAmi();
        meter = generatorEntityMeterRepository.save(meter);

        /**
         * 初次新建，需要建立display對應
         */
        var display = GeneratorEntityMeterDisplay.builder().generatorEntityMeterId(meter.getId()).meterNo(
                meter.getMeterNo()).modifiedBy(userId).useFrom(useFrom).build();
        generatorEntityMeterDisplayRepository.save(display);

        /**
         * 只有非特殊電表初次新建，才建立child關聯
         * 特殊電表實際關聯跟當前表號可能無關
         */
        if (meter.getIsSpecial() == null || !meter.getIsSpecial()) {

            var child = GeneratorEntityMeterChild.builder().generatorEntityMeterId(meter.getId()).meterNo(
                            meter.getMeterNo()).customerNo(entity.getNbsCustomerNumber()).channel(1L).operator(
                            1L)
                    .useFrom(useFrom).build();
            generatorEntityMeterChildRepository.save(child);
        }
    }

    List<GeneratorEntityMeter> findByCustomerNoAndMeterNo(String customerNo, String meterNo) {
        var genEntity = generatorEntityRepository.findOneByNbsCustomerNumber(customerNo);
        if (genEntity == null) {
            return new ArrayList<>();
        }
        return generatorEntityMeterRepository.findByGeneratorEntityIdAndMeterNo(genEntity.getId(), meterNo);
    }

    public void markMeterForceUse(String customerNo, String meterNo) {
        var meters = findByCustomerNoAndMeterNo(customerNo, meterNo);
        meters.stream().forEach(a -> a.setForceUse(true));
        if (!meters.isEmpty()) {
            generatorEntityMeterRepository.saveAll(meters);
        }
    }
}
