package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.ami.SettlementTempAmiPwr15Record;
import tw.com.taipower.data.repository.ami.AmiCmDateStatRepository;
import tw.com.taipower.data.repository.ami.SettlementTempAmiPwr15RecordRepository;
import tw.com.taipower.data.repository.ami.SimulationSettlementTempAmiPwr15RecordRepository;
import tw.com.taipower.pwoms.services.enumclass.MeterChannelEnum;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static tw.com.taipower.pwoms.services.enumclass.MeterChannelEnum.METER_CHANNEL_GENERATOR;
import static tw.com.taipower.pwoms.services.enumclass.MeterChannelEnum.METER_CHANNEL_LOAD;

@Log4j2
@Service
public class SimulationAmiService {


    @Autowired
    private SimulationSettlementTempAmiPwr15RecordRepository settlementTempAmiPwr15RecordRepository;

    @Autowired
    private AmiCmDateStatRepository amiCmDateStatRepository;

    private final static int ACCEPTABLE_DATA_COUNT_PER_A_DAY = 96;


    public void deleteSettlementTempRecordBySettlementId(Long settlementId){
        settlementTempAmiPwr15RecordRepository.deleteBySettlementId(settlementId);
    }

    public List<Map<String, Object>> getComputableMeter(Date pwrDate, MeterChannelEnum channel, List<String> meterIdList) {
        return amiCmDateStatRepository.findComputableMeterByPwDateAndValCntAndChannelAndMeterIdIn(pwrDate, ACCEPTABLE_DATA_COUNT_PER_A_DAY, channel.getChannelNumber(), meterIdList);
//        List<Map<String, Object>> specXCmList = amiCmDateStatRepository.findSpecXComputableMeterByPwDateAndValCntAndChannelAndMeterIdIn(pwrDate, ACCEPTABLE_DATA_COUNT_PER_A_DAY, channel.getChannelNumber(), meterIdList);
//        List<Map<String, Object>> totalCmList = new ArrayList<>();
//        if(CollectionUtils.isNotEmpty(amiCmList)){
//            totalCmList.addAll(amiCmList);
//        }
//        if(CollectionUtils.isNotEmpty(specXCmList)){
//            totalCmList.addAll(specXCmList);
//        }
//        return totalCmList;
    }

    public void saveAllFromAmiPwr15Mdes(Long settlementId
            , Date date
            , List<String> genMeterIdList
            , List<String> loadMeterIdList) throws Exception{
        try{
            if(CollectionUtils.isNotEmpty(genMeterIdList)) {
                String commaList = StringUtils.join(genMeterIdList.stream().map(Object::toString).toList(), ",");
                settlementTempAmiPwr15RecordRepository.saveAllFromAmiPwr15Mdes(date, settlementId, METER_CHANNEL_GENERATOR.getChannelNumber(), commaList);
            }
            if(CollectionUtils.isNotEmpty(loadMeterIdList)) {
                String commaList = StringUtils.join(loadMeterIdList.stream().map(Object::toString).toList(), ",");
                settlementTempAmiPwr15RecordRepository.saveAllFromAmiPwr15Mdes(date, settlementId, METER_CHANNEL_LOAD.getChannelNumber(), commaList);
            }
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new Exception(exception.getMessage());
        }
    }

    public List<SettlementTempAmiPwr15Record> getPwr15MdesFromKwHistory(Date startTime, Date endTime, Long settlementId, List<String> custIdList, List<String> meterIdList, MeterChannelEnum channel) {
        return settlementTempAmiPwr15RecordRepository.findByMeterIdInAndChannelAndPwrTimeBetweenOrderByPwrTime(settlementId, startTime, endTime, custIdList, meterIdList, channel.getChannelNumber());
    }

    public Map<String, Date> getTop1MeterIdAndFirstDateAndKwAndRatioGreaterThanZero(Date startTime, Date endTime, Long settlementId, List<String> meterIdList, MeterChannelEnum channel) {
        //meterId, pwrTime
        Map<String, Date> meterPwrTimeMap = new HashMap<>();
        List<Map<String, Object>> idValueMapList = settlementTempAmiPwr15RecordRepository.findPwrTimeAndMeterIdBySettlementIdAndMeterIdInAndChannelAndTimeInterval(settlementId, startTime, endTime, meterIdList, channel.getChannelNumber());
//        List<Map<String, Object>> idValueMapList = settlementAmiPwr15RecordRepository.findPwrTimeAndMeterIdBySettlementIdAndMeterIdInAndChannelAndTimeInterval(settlementId, startTime, endTime, meterIdList, channel.getChannelNumber());
        if(CollectionUtils.isNotEmpty(idValueMapList)){
            for(Map<String, Object> idValueMap : idValueMapList){
                meterPwrTimeMap.put((String)idValueMap.get("meterId"), (Date)idValueMap.get("pwrTime"));
            }
        }
        return meterPwrTimeMap;
    }

}
