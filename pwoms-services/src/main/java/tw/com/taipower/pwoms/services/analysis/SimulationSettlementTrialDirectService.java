package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum;
import tw.com.taipower.pwoms.services.settlement.SettlementContractVer2Service;
import tw.com.taipower.pwoms.services.settlement.SettlementTrialService;
import tw.com.taipower.pwoms.services.vo.settlement.LoadAppMeterVo;
import tw.com.taipower.pwoms.services.vo.settlement.TempGenGmiVo;
import tw.com.taipower.pwoms.services.vo.settlement.TempLoadUniVo;
import tw.com.taipower.pwoms.services.vo.utils.TimeIntervalVo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static java.util.stream.Collectors.toMap;
import static tw.com.taipower.data.constant.Constants.FIELD_NAME_APPLICATION_GENERATOR_ID;
import static tw.com.taipower.data.constant.Constants.FIELD_NAME_APPLICATION_LOAD_ID;
import static tw.com.taipower.pwoms.services.constant.Constants.BIGDECIMAL_PRECISION;
import static tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum.APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING;
import static tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum.APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE;

@Log4j2
@Service
public class SimulationSettlementTrialDirectService extends SimulationSettlementTrialService {

    @Autowired
    private SimulationTempApplicationMonthlyDirectGeneratorRecordRepository monthlyGenRecordRepository;

    @Autowired
    private SimulationTempApplicationMonthlyDirectGeneratorLoadRecordRepository monthlyGenLoadRecordRepository;

    @Autowired
    private SimulationTempApplicationMonthlyDirectLoadRecordRepository monthlyLoadRecordRepository;

    @Autowired
    private SimulationTempApplicationDailyDirectGeneratorLoadRecordRepository dailyGenLoadRecordRepository;

    @Autowired
    private SimulationTempApplicationTimelyDirectGeneratorLoadRecordRepository timelyGenLoadRecordRepository;

    @Autowired
    private SimulationTempApplicationDailyDirectLoadRecordRepository dailyLoadRecordRepository;

    @Autowired
    private SimulationTempApplicationDailyDirectGeneratorRecordRepository dailyGenRecordRepository;

    @Autowired
    private SimulationTempApplicationTimelyDirectLoadRecordRepository timelyLoadRecordRepository;

    @Autowired
    private SimulationTempApplicationTimelyDirectGeneratorRecordRepository timelyGenRecordRepository;



    @Override
    public void deleteAllRecordBySettlementId(Long settlementId) throws Exception {
        try{
            monthlyGenLoadRecordRepository.deleteAllRecordBySettlementId(settlementId);
            dailyGenLoadRecordRepository.deleteAllRecordBySettlementId(settlementId);
            timelyGenLoadRecordRepository.deleteAllRecordBySettlementId(settlementId);
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new Exception(exception.getMessage());
        }
    }

    @Override
    protected Map<ApplicationTypeEnum, Map<Long, BigDecimal>> getAnnualYni(TimeIntervalVo timeIntervalVo, TimeIntervalVo annualTimeIntervalVo, Map<Long, LoadAppMeterVo> appLoadVoMap, List<Long> resetAppIdList) {

        Map<ApplicationTypeEnum, Map<Long, BigDecimal>> directAnnualYni = new HashMap<>();
        directAnnualYni.put(APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE, new HashMap<>());
        directAnnualYni.put(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING, new HashMap<>());

        if(MapUtils.isNotEmpty(appLoadVoMap)){
            Date startDate = annualTimeIntervalVo.getStartTime().getTime();
            Date endDate = annualTimeIntervalVo.getEndTime().getTime();

            Map<Long, LoadAppMeterVo> directLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(directLoadMap)) {
                directAnnualYni.get(APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE).putAll(contractServiceVer2.getDirectAnnualYni(startDate, endDate, directLoadMap, resetAppIdList));
            }

            Map<Long, LoadAppMeterVo> directPwLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(directPwLoadMap)) {
                directAnnualYni.get(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING).putAll(contractServiceVer2.getDirectAnnualYni(startDate, endDate, directPwLoadMap, resetAppIdList));
            }
        }
        return directAnnualYni;
    }

    @Override
    public void saveDailyRecordByDateIntervalAndSettlementId(Date startDate, Date endDate, Long settlementId) throws Exception{
        try{
            dailyLoadRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId);
            dailyGenRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId);
            dailyGenLoadRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId);
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new Exception(exception.getMessage());
        }
    }

    @Override
    protected Map<ApplicationTypeEnum, Map<Long, BigDecimal>> getMonthlyNi(TimeIntervalVo timeIntervalVo, Long settlementId, Map<Long, LoadAppMeterVo> appLoadVoMap) {
        Map<ApplicationTypeEnum, Map<Long, BigDecimal>> directMonthlyNi = new HashMap<>();
        directMonthlyNi.put(APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE, new HashMap<>());
        directMonthlyNi.put(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING, new HashMap<>());

        if(MapUtils.isNotEmpty(appLoadVoMap)){
//            TimeIntervalVo monthlyTimeIntervalVo = getMonthToEnd(timeIntervalVo.getEndTime());
            Date startTime = timeIntervalVo.getStartTime().getTime();
            Date endTime = timeIntervalVo.getEndTime().getTime();

            Map<Long, LoadAppMeterVo> directLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(directLoadMap)) {
                directMonthlyNi.get(APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE).putAll(tempService.getDirectMonthlyNi(startTime, endTime, settlementId, directLoadMap));
            }

            Map<Long, LoadAppMeterVo> directPwLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(directPwLoadMap)) {
                directMonthlyNi.get(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING).putAll(tempService.getDirectMonthlyNi(startTime, endTime, settlementId, directPwLoadMap));
            }
        }
        return directMonthlyNi;
    }

    @Override
    protected Boolean calculateDaily(TimeIntervalVo timeIntervalVo
            , Long settlementId
            , Long selfAppId
            , List<Long> relationAppIdList
            , Map<Long, BigDecimal> currentNiMap) {

        try {
            Date startTime = timeIntervalVo.getStartTime().getTime();
            Date endTime = timeIntervalVo.getEndTime().getTime();

            Map<String, List<Long>> appGenLoadMap = tempService.getApplicationGenLoadIdList(startTime, settlementId, selfAppId);

            //若是先直後轉移除isDirect = false
            List<Long> selfLoadIdList = getRidOfDirectLoadByIsDirectAndApplicationType(selfAppId, appGenLoadMap.get(FIELD_NAME_APPLICATION_LOAD_ID));
            List<Long> selfGenIdList = appGenLoadMap.get(FIELD_NAME_APPLICATION_GENERATOR_ID);

            //若契約發電端皆為零，更新GMI=Capacity*PMI*0.01
            //這個處理的原因是因為，雖然發電量但還是要算出用電端可媒合量Uni
            //當跨契約時，需要算出關聯契約用電端可媒合量比例
            //所以，需使用Capacity來計算
            //這個在直供不需要考慮
//            tempService.updateZeroGmiByDatetimeAndApplicationGeneratorIdIn(startTime, endTime, selfGenIdList);

            Map<Long, BigDecimal> selfSumGmiMap = tempService.getSumGmiByDatetimeIntervalAndApplicationGeneratorIdIn(startTime, endTime, selfGenIdList);
            Map<Long, TempLoadUniVo> appLoadUniVoMap = assembleTempLoadUniVo(startTime, endTime, currentNiMap, selfLoadIdList);
            Map<Long, TempGenGmiVo> appGenGmiVoMap = assembleTempGeneratorUniVo(startTime, endTime, selfGenIdList);

            List<SimulationTempApplicationTimelyDirectGeneratorLoadRecord> genLoadRecordList = new ArrayList<>();
            List<SimulationTempApplicationTimelyDirectLoadRecord> loadRecordList = new ArrayList<>();

            long quarterInMillis = TimeUnit.MINUTES.toMillis(15);
            for (long datetime = startTime.getTime(); datetime <= endTime.getTime(); datetime += quarterInMillis) {
                //calculate qni 媒合量
//                loadRecordList.addAll(calculateLoadMatchedCn(datetime, settlementId, appLoadUniVoMap, selfSumGmiMap.get(datetime)));
//                BigDecimal sumMatchedCn = loadRecordList.stream().map(TempApplicationTimelyDirectLoadRecord::getMatchedCn).reduce(BigDecimal.ZERO, BigDecimal::add);
                List<SimulationTempApplicationTimelyDirectLoadRecord> dateLoadRecordList = calculateLoadMatchedCn(datetime, settlementId, appLoadUniVoMap, selfSumGmiMap.get(datetime));
                loadRecordList.addAll(dateLoadRecordList);
                BigDecimal sumMatchedCn = dateLoadRecordList.stream().map(SimulationTempApplicationTimelyDirectLoadRecord::getMatchedCn).reduce(BigDecimal.ZERO, BigDecimal::add);
                genLoadRecordList.addAll(calculateGeneratorMatchedRm(datetime, settlementId, appGenGmiVoMap, appLoadUniVoMap, selfSumGmiMap.get(datetime), sumMatchedCn));
            }
            //
            saveTempApplicationTimelyDirectLoadRecord(loadRecordList);
            //
            saveTempApplicationTimelyDirectGeneratorLoadRecord(genLoadRecordList);
            //
            saveTempApplicationTimelyDirectGeneratorRecord(startTime, endTime, settlementId, selfGenIdList);
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    private List<Long> getRidOfDirectLoadByIsDirectAndApplicationType(Long selfAppId, List<Long> selfLoadIdList){
        ApplicationTypeEnum typeEnum = contractServiceVer2.getApplicationTypeById(selfAppId);
        if(typeEnum.equals(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING)){
            return contractServiceVer2.getDirectApplicationLoad(selfLoadIdList, true);
        }
        return selfLoadIdList;
    }

    private Map<Long, TempLoadUniVo> assembleTempLoadUniVo(Date startTime
            , Date endTime
            , Map<Long, BigDecimal> currNiMap
            , List<Long> selfLoadIdList){

        Map<Long, Map<Long, TempTempApplicationLoadKw>> appLoadKwMap = tempService.getApplicationLoadKw(startTime, endTime, selfLoadIdList);


        Map<Long, TempLoadUniVo> appLoadUnVoMap = new HashMap<>();

        for(Long appLoadId : selfLoadIdList){
            appLoadUnVoMap.put(appLoadId, TempLoadUniVo.builder()
                    .cni(BigDecimal.ZERO)
                    .currentNi(currNiMap.get(appLoadId))
                    .timeLoadKwMap(appLoadKwMap.get(appLoadId))
                    .relSumGmiMap(new HashMap<>())
                    .appLoadId(appLoadId)
                    .matchedCn(new HashMap<>())
                    .build());
        }
        return appLoadUnVoMap;
    }

    private Map<Long, TempGenGmiVo> assembleTempGeneratorUniVo(Date startTime
            , Date endTime
            , List<Long> selfGenIdList){

        Map<Long, TempGenGmiVo> appGenIdGmVoMap = new HashMap<>();

        Map<Long, Map<Long, TempTempApplicationGeneratorKw>> appGenKwMap = tempService.getApplicationGeneratorKw(startTime, endTime, selfGenIdList);
        for(Long appGenId : selfGenIdList){
            appGenIdGmVoMap.put(appGenId, TempGenGmiVo.builder()
                    .appGenId(appGenId)
                    .timeGenKwMap(appGenKwMap.get(appGenId))
                    .matchedRm(new HashMap<>())
                    .build());
        }
        return appGenIdGmVoMap;
    }

    private List<SimulationTempApplicationTimelyDirectLoadRecord> calculateLoadMatchedCn(long datetime, Long settlementId, Map<Long, TempLoadUniVo> appLoadUnVoMap, BigDecimal selfSumGmi){

        List<SimulationTempApplicationTimelyDirectLoadRecord> timelyLoadRecordList = new ArrayList<>();

        BigDecimal sumUni = BigDecimal.ZERO;
        //calculate uni
        for(TempLoadUniVo tempLoadUniVo : appLoadUnVoMap.values()){

            //Cni未考慮上限前的數據
            tempLoadUniVo.setCni(tempLoadUniVo.getTimeLoadKwMap().get(datetime).getKwRatio());
            //檢查上限與用電量取最小值
            BigDecimal uni = tempLoadUniVo.getCurrentNi().min(tempLoadUniVo.getCni());

            tempLoadUniVo.setUni(uni);
            sumUni = sumUni.add(uni);
        }
        //可媒合之轉供量
        BigDecimal qi = sumUni.min(selfSumGmi);

        BigDecimal sumMatchedCn = BigDecimal.ZERO;
        Date date = new Date(datetime);

        //(qi*uni)/sumUni
        for(TempLoadUniVo tempLoadUniVo : appLoadUnVoMap.values()){
            BigDecimal matchedCn = BigDecimal.ZERO;
            if(0 < tempLoadUniVo.getUni().compareTo(BigDecimal.ZERO)) {
                matchedCn = qi.multiply(tempLoadUniVo.getUni()).divide(sumUni, BIGDECIMAL_PRECISION, RoundingMode.DOWN);
                sumMatchedCn = sumMatchedCn.add(matchedCn);
            }
            tempLoadUniVo.getMatchedCn().put(datetime, matchedCn);
            tempLoadUniVo.setCurrentNi(tempLoadUniVo.getCurrentNi().subtract(matchedCn));

            TempTempApplicationLoadKw appLoadKw = tempLoadUniVo.getTimeLoadKwMap().get(datetime);
            timelyLoadRecordList.add(SimulationTempApplicationTimelyDirectLoadRecord.builder()
                    .loadId(tempLoadUniVo.getAppLoadId())
                    .datetime(date)
                    .energyChargeSectionId(appLoadKw.getEnergyChargeSectionId())
//                    .unmatchedCn(tempLoadUniVo.getUni().subtract(matchedCn))
                    .unmatchedCn(tempLoadUniVo.getCni().subtract(matchedCn))
                    .matchedCn(matchedCn)
                    .actualLoad(appLoadKw.getKwRatio())
                    .uni(tempLoadUniVo.getUni())
                    .kwUpdateTime(appLoadKw.getKwUpdateTime())
                    .settlementId(settlementId)
                    .build());
        }

        return timelyLoadRecordList;
    }

    private List<SimulationTempApplicationTimelyDirectGeneratorLoadRecord> calculateGeneratorMatchedRm(long datetime
            , Long settlementId
            , Map<Long, TempGenGmiVo> appGenGmiVoMap
            , Map<Long, TempLoadUniVo> appLoadUnVoMap
            , BigDecimal selfSumGmi
            , BigDecimal sumMatchedCn){

        List<SimulationTempApplicationTimelyDirectGeneratorLoadRecord> genLoadRecordList = new ArrayList<>();

        Date date = new Date(datetime);
        if(0 < sumMatchedCn.compareTo(BigDecimal.ZERO)) {
            for (TempGenGmiVo tempGenGmiVo : appGenGmiVoMap.values()) {
                for(TempLoadUniVo tempLoadUniVo : appLoadUnVoMap.values()){
                    BigDecimal gmi = tempGenGmiVo.getTimeGenKwMap().get(datetime).getGmi();
                    //每個發電量媒合用電端量  (matchedCn * gmi)/(selfSumGmi)
                    BigDecimal matchedRm = BigDecimal.ZERO;
                    if(0 < gmi.compareTo(BigDecimal.ZERO)) {
                        matchedRm = tempLoadUniVo.getMatchedCn().get(datetime).multiply(gmi).divide(selfSumGmi, BIGDECIMAL_PRECISION, RoundingMode.DOWN);
                    }
                    genLoadRecordList.add(SimulationTempApplicationTimelyDirectGeneratorLoadRecord.builder()
                            .generatorId(tempGenGmiVo.getAppGenId())
                            .loadId(tempLoadUniVo.getAppLoadId())
                            .datetime(date)
                            .energyChargeSectionId(tempGenGmiVo.getTimeGenKwMap().get(datetime).getEnergyChargeSectionId())
                            .matchedRm(matchedRm)
                            .settlementId(settlementId)
                            .build());
                }
            }
        }else{
            for (TempGenGmiVo tempGenGmiVo : appGenGmiVoMap.values()) {
                for(TempLoadUniVo tempLoadUniVo : appLoadUnVoMap.values()){
                    genLoadRecordList.add(SimulationTempApplicationTimelyDirectGeneratorLoadRecord.builder()
                            .generatorId(tempGenGmiVo.getAppGenId())
                            .loadId(tempLoadUniVo.getAppLoadId())
                            .datetime(date)
                            .energyChargeSectionId(tempGenGmiVo.getTimeGenKwMap().get(datetime).getEnergyChargeSectionId())
                            .matchedRm(BigDecimal.ZERO)
                            .settlementId(settlementId)
                            .build());
                }
            }
        }
        return genLoadRecordList;
    }

    public boolean saveTempApplicationTimelyDirectLoadRecord(List<SimulationTempApplicationTimelyDirectLoadRecord> loadRecordList ) {
        try {
            List<SimulationTempApplicationTimelyDirectLoadRecord> storedRecordList = timelyLoadRecordRepository.saveAll(loadRecordList);
            return storedRecordList.size() == loadRecordList.size();
        } catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
    }

    public boolean saveTempApplicationTimelyDirectGeneratorLoadRecord(List<SimulationTempApplicationTimelyDirectGeneratorLoadRecord> genLoadRecordList ) {
        try {
            List<SimulationTempApplicationTimelyDirectGeneratorLoadRecord> storedRecordList = timelyGenLoadRecordRepository.saveAll(genLoadRecordList);
            return storedRecordList.size() == genLoadRecordList.size();
        } catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
    }

    public boolean saveTempApplicationTimelyDirectGeneratorRecord(Date startTime, Date endTime, Long settlementId, List<Long> appGenIdList) {
        try {
            timelyGenRecordRepository.saveAllByDateIntervalApplicationGeneratorIdIn(startTime, endTime, settlementId, appGenIdList);
        } catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public boolean hasTimelyUnmatchedRmByDateIntervalAndSettlementId(Date startTime, Date endTime, Long settlementId, Long appId){
        BigDecimal unmatchedRm = timelyGenRecordRepository.findByUnmatchedRmByDateIntervalAndApplicationId(startTime, endTime, settlementId, appId);
        return (0 < unmatchedRm.compareTo(BigDecimal.ZERO));
    }

    @Override
    protected Map<Long, Boolean> calculateMonthly(TimeIntervalVo timeIntervalVo
            , Long settlementId
            , List<EnergyChargeSection> chargeSectionList
            , Map.Entry<ApplicationTypeEnum, List<Long>> typeEnumIdEntry
            , Map<Long, BigDecimal> currentNiMap) {

        boolean result = saveMonthlyRecordByDateIntervalAndSettlementId(timeIntervalVo.getStartTime().getTime()
                , timeIntervalVo.getEndTime().getTime()
                , settlementId
                , typeEnumIdEntry.getKey());

        Map<Long, Boolean> appIdResultMap = new HashMap<>();
        for(Long appId : typeEnumIdEntry.getValue()){
            appIdResultMap.put(appId, result);
        }
        return appIdResultMap;
    }

    private boolean saveMonthlyRecordByDateIntervalAndSettlementId(Date startDate, Date endDate, Long settlementId, ApplicationTypeEnum appTypeEnum){
        try{
            List<String> appTypeList = Collections.singletonList(appTypeEnum.getId());
            monthlyLoadRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId, appTypeList);
            monthlyGenRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId, appTypeList);
            monthlyGenLoadRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId, appTypeList);
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

}
