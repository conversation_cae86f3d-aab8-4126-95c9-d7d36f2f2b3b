package tw.com.taipower.pwoms.services.tpc;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jcraft.jsch.*;
import org.apache.axis.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.logger.CustomLog;
import tw.com.taipower.pwoms.services.BaseService;
import tw.com.taipower.pwoms.services.cityarea.CityAreaService;
import tw.com.taipower.pwoms.services.config.TpcRemsConfig;
import tw.com.taipower.pwoms.services.entitymanage.GeneratorService;
import tw.com.taipower.pwoms.services.enumclass.ErrorCode;
import tw.com.taipower.pwoms.services.tpc.model.*;
import tw.com.taipower.pwoms.services.utils.JasyptEncryptorUtil;
import tw.com.taipower.pwoms.services.utils.TaiwanDateUtil;
import tw.com.taipower.pwoms.services.vo.cityarea.CityAreaVo;
import tw.com.taipower.pwoms.services.vo.generated.GeneratorEntityCombinedCapacityVO;
import tw.com.taipower.pwoms.services.vo.generated.GeneratorEntityVO;
import tw.com.taipower.pwoms.services.vo.system.HostPortVo;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static tw.com.taipower.pwoms.services.constant.Constants.*;

/**
 * @class: RemsService
 * @author: daniel
 * @version:
 * @since: 2024-05-30 10:54
 * @see:
 **/

@CustomLog
@Service
public class RemsService extends BaseService {

    private final RestTemplate restTemplate;

    @Autowired
    private GeneratorEntityRepository generatorEntityRepository;

    @Autowired
    private LoadEntityRepository loadEntityRepository;

    @Autowired
    TpcRemsConfig config;

    @Autowired
    GeneratorService generatorService;

    @Autowired
    CityAreaService cityAreaService;

    @Autowired
    VoltageLevelRepository voltageLevelRepository;

    @Autowired
    private RemsTxService remsTxService;

    @Autowired
    FuelTypeRepository fuelTypeRepository;

    @Autowired
    FuelFormRepository fuelFormRepository;

    @Autowired
    AccountRepository accountRepository;

    List<String> north = List.of("00", "01", "02", "03", "04", "05", "06", "13", "14", "16", "22", "23");
    List<String> middle = List.of("07", "08", "17", "19", "21");
    List<String> south = List.of("09", "10", "11", "12", "15", "18", "20");

    /**
     * RNIS發電端整理暫存區，因為有多機組要處理，所以需要map 來cache
     */
    private HashMap<String, GeneratorEntityVO> generatorSyncMap = new HashMap<>();

    public RemsService() {
        this.restTemplate = new RestTemplate();
    }


    public String decideNBSUrl(String tpcMeter) {
        tpcMeter = tpcMeter.trim().replaceAll("-", "");
        String code = tpcMeter.substring(0, 2);
        if (north.contains(code)) {
            return config.getNbs1URL();
        } else if (middle.contains(code)) {
            return config.getNbs2URL();
        } else if (south.contains(code)) {
            return config.getNbs3URL();
        }
        throw new RuntimeException("tpcMeter error");
    }

    public String decideRNISUrl(String tpcMeter) {
        tpcMeter = tpcMeter.trim().replaceAll("-", "");
        String code = tpcMeter.substring(0, 2);
        if (north.contains(code)) {
            return config.getRnis1URL();
        } else if (middle.contains(code)) {
            return config.getRnis2URL();
        } else if (south.contains(code)) {
            return config.getRnis3URL();
        }
        throw new RuntimeException("tpcMeter error");
    }

    /**
     * 電號資訊標準化
     *
     * @param tpcMeter
     * @return
     */
    public String formatCustomerNo(String tpcMeter) {
        return tpcMeter.trim().replaceAll("-", "");
    }

    /**
     * 地址統一化處理
     *
     * @param address
     * @return
     */
    public String formatAddress(String address) {
        try {
            if (address == null) {
                return null;
            }
            return address.replaceAll("^[\\d\\s]+", "");
        } catch (Throwable ex) {
            return address;
        }
    }

    public void formatAddress(PWOMSNBData nbData) {
        try {
            var address = formatAddress(nbData.getNbMstUsagAddr());
            nbData.setNbMstUsagAddr(address);
        } catch (Throwable ex) {

        }
    }

    public void formatAddress(RNData rnData) {
        try {
            var address = formatAddress(rnData.getRnMstAprvAddr());
            rnData.setRnMstAprvAddr(address);
        } catch (Throwable ex) {

        }
        try {
            var address = formatAddress(rnData.getRnMstMailAddr());
            rnData.setRnMstMailAddr(address);
        } catch (Throwable ex) {

        }
    }

    public void formatAddress(RNDataV2 rnData) {
        try {
            var address = formatAddress(rnData.getAprvAddr());
            rnData.setAprvAddr(address);
        } catch (Throwable ex) {

        }
        try {
            var address = formatAddress(rnData.getAplyAddr());
            rnData.setAplyAddr(address);
        } catch (Throwable ex) {

        }
    }

    public PWOMSNBData getDataNB(String tpcMeter) {
        tpcMeter = tpcMeter.trim().replaceAll("-", "");
        PWOMSNBData nbData = post(decideNBSUrl(tpcMeter), RequestMeter.builder().tpcMeter(tpcMeter).build(),
                PWOMSNBData.class);
        formatAddress(nbData);
        log.info("{}", nbData);
        return nbData;
    }

    public PWOMSNBData getDataNBByAccountId(String tpcMeter, String accountId) {
        tpcMeter = tpcMeter.trim().replaceAll("-", "");
        PWOMSNBData nbData = post(decideNBSUrl(tpcMeter), RequestMeter.builder().tpcMeter(tpcMeter).build(),
                PWOMSNBData.class);
        formatAddress(nbData);
        log.info("{}", nbData);
        return nbData;
    }

    public RNData getDataRN(String tpcMeter) {
        tpcMeter = tpcMeter.trim().replaceAll("-", "");
        RNData rnData = post(decideRNISUrl(tpcMeter), RequestMeter.builder().tpcMeter(tpcMeter).build(), RNData.class);
        formatAddress(rnData);
        log.info("{}", rnData);
        return rnData;
    }

    public RNData getDataRNByRelationId(String tpcMeter, String relationId) {
        RNData rnData = post(decideRNISUrl(tpcMeter), RequestMeter.builder().tpcMeter(tpcMeter).build(), RNData.class);
        formatAddress(rnData);
        log.info("{}", rnData);
        return rnData;
    }

    public <T> T post(final String url, RequestMeter meter, final Class<T> data) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<RequestMeter> request = new HttpEntity<>(meter, headers);
            log.info("{} {} {} ", url, request, data);

            ResponseEntity<String> response = this.restTemplate.postForEntity(new URI(url), request, String.class);
            String responseBody = response.getBody();
            log.info("Response: {}", responseBody);

            if (responseBody != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readValue(responseBody, data);
            }
            return null;
        } catch (URISyntaxException e) {
            log.error(e);
            return null;
        } catch (JsonProcessingException e) {
            log.error("JSON parsing error: {}", e.getMessage());
            return null;
        }
    }


    public String customerMeterFormat(String tpcMeter) {
        try {
            String digitsOnly = tpcMeter.replaceAll("[^0-9]", "");

            // 使用正則表達式組織成所需的格式
            String formatted = digitsOnly.replaceAll("(\\d{2})(\\d{2})(\\d{4})(\\d{2})(\\d{1})", "$1-$2-$3-$4-$5");

            return formatted;
        } catch (Exception e) {
            return tpcMeter;
        }
    }

    /**
     * 電號規則檢驗
     *
     * @param electricityNumber
     * @return
     */
    public boolean verifyCustomerNumber(String electricityNumber) {
        // 清除格式化字符，如 "-"

        String digitsOnly = electricityNumber.replaceAll("[^0-9]", "");
        if (digitsOnly.length() != 11) {
            return false; //throw new RuntimeException("電號長度錯誤");
        }
        //只取前面10碼計算
        String useDigit = digitsOnly.substring(0, 10);
        String checkCode = digitsOnly.substring(10, 11);

        // 取 sum 的個位數字即為檢算號
        return checkCode.equals(createCheckCodeByCustomerNumber(useDigit));
    }

    /**
     * 依據10碼電號產生檢核碼
     *
     * @param useDigit
     * @return
     */
    public String createCheckCodeByCustomerNumber(String useDigit) {
        int sum = 0;
        for (int i = 0; i < useDigit.length(); i++) {
            int digit = Character.getNumericValue(useDigit.charAt(i));

            if ((i + 1) % 2 != 0) {  // 奇數位
                digit *= 2;
                if (digit > 9) {  // 若大於10，取十位數字和個位數字之和
                    digit = digit / 10 + digit % 10;
                }
            }
            // 偶數位的情況直接相加
            sum += digit;
        }
        return String.valueOf(sum % 10);
    }

    /**
     * 依據電號清單同步用電端
     * 資訊提供如下：
     * <p>
     * 主機                   ip                            區處代碼
     * =============================================================
     * 北資正式機 **********  00  01 02 03 04 05 06 13 14 16 22 23
     * 中資正式機 **********  07  08 17 19 21
     * 中資正式機 **********  09  10 11 12 15 18 20
     * <p>
     * 謝謝!
     *
     * @param tpcMeters
     */
    public void syncNBSWithCustomNumbers(List<String> tpcMeters, Long userId) {

        List<String> success = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        tpcMeters.forEach(meter -> {
            try {
                meter = formatCustomerNo(meter);
                if (!verifyCustomerNumber(meter)) {
                    throw new RuntimeException("電號規則錯誤");
                }
                var nbData = getDataNB(meter);
                if (!StringUtils.isEmpty(nbData.getNbMstErrrMsge())) {
                    // 如果同步不到，新增一個對應電號的用電端;
                    var loadEntity =
                            LoadEntity.builder().nbsCustomerNumber(meter).build();
                    var saveResult = remsTxService.sucessSave(loadEntity);
                    throw new RuntimeException(
                            nbData.getNbMstErrrMsge() + (!saveResult ? "但轉直供平台已存在此電號" : ""));
                } else {
                    remsTxService.sucessSave(nbData, userId);
                }
                success.add(String.format("%s 同步成功", customerMeterFormat(meter)));
            } catch (Throwable ex) {
                log.error(meter, ex);
                errors.add(String.format("%s 同步失敗( %s )", customerMeterFormat(meter), ex.getMessage()));
            }
        });
        syncMessageReport(tpcMeters, success, errors);

    }

    /**
     * 依據電號清單同步發電端
     *
     * @param tpcMeters
     * @param userId
     */
    public void syncRNISWithCustomNumbers(List<String> tpcMeters, Long userId) {

        List<String> success = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        remsTxService.updateFuelTypeList();
        tpcMeters.forEach(meter -> {
            try {
                meter = formatCustomerNo(meter);
                if (!verifyCustomerNumber(meter)) {
                    throw new RuntimeException("電號規則錯誤");
                }
                try {
                    var rnData = getDataRNV2(meter);
                    remsTxService.sucessSave(rnData, userId);
                } catch (Throwable ex) {
                    log.error(meter, ex);
                    var generatorEntity =
                            GeneratorEntity.builder().nbsCustomerNumber(meter).build();
                    var saveResult = remsTxService.sucessSave(generatorEntity);
                    throw new RuntimeException((!saveResult ? "但轉直供平台已存在此電號" : "未查詢到對應電號資料"));
                }

                success.add(String.format("%s 同步成功", customerMeterFormat(meter)));
            } catch (Throwable ex) {
                log.error(meter, ex);
                errors.add(String.format("%s 同步失敗( %s )", customerMeterFormat(meter), ex.getMessage()));
            }
        });
        syncMessageReport(tpcMeters, success, errors);
    }

    private static void syncMessageReport(List<String> tpcMeters, List<String> success,
                                          List<String> errors) {
        String summary;
        summary = String.format("輸入%d個電號，共%d個同步成功，%d個同步失敗\n\n", tpcMeters.size(), success.size(),
                errors.size());
        summary += String.join("\n", success) + String.join("\n", errors);
        throw new RuntimeException(summary);
    }

    public HashMap<String, GeneratorEntityVO> getRNDataFromCSVFile(InputStream stream) {
        try (Scanner scanner = new Scanner(stream)) {
            scanner.nextLine();
            while (scanner.hasNextLine()) {
                getRecordFromLine(scanner.nextLine());
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return generatorSyncMap;
    }

    private void getRecordFromLine(String line) throws ParseException {
        List<String> values = new ArrayList<String>();
        try (Scanner rowScanner = new Scanner(line)) {
            rowScanner.useDelimiter("\",\"");
            while (rowScanner.hasNext()) {
                String value = rowScanner.next();
                if (value.split("\"").length == 1) {

                } else if (value.split("\"").length == 2) {
                    if (value.split("\"")[0].isEmpty())
                        value = value.split("\"")[1];
                    else
                        value = value.split("\"")[0];
                } else value = "";
                values.add(value);
            }
        }
        fromCSVLine(values);
    }


    GeneratorEntityVO fromCSVLine(List<String> values) throws ParseException {
        GeneratorEntityVO generator;
        if (generatorSyncMap.containsKey(values.getFirst())) {
            generator = generatorSyncMap.get(values.getFirst());
        } else {

            generator =
                    GeneratorEntityVO.builder()
                            .relationId(values.get(0))
                            .nbsCustomerNumber(values.get(1))
                            .tpcDeptId(Integer.valueOf(values.get(2)))
                            .name(values.get(3)).taxId(values.get(4))
                            .responsiblePerson(values.get(5))
                            .fuelType(getFuelType(values.get(6)))
                            .responsiblePersonPhone(values.get(9))
                            .generationUnitType(getGeneratorUnitType(values.get(10)))
                            .fuelForm(getFuelForm(values.get(11)))
                            .combineMethod(values.get(12).contains("內線") ? 1 : 2)
                            .isWholesale(!values.get(13).contentEquals("僅併聯不躉售"))
                            .timeStg(values.get(23).isEmpty() ? 1 : Integer.parseInt(values.get(23)))
                            .voltage(!values.get(27).isEmpty() ? RemsVoltageOption.getVoltageLevel(values.get(27)) :
                                    null)
                            .responsibilityVoltage(!values.get(28).isEmpty() ?
                                    RemsVoltageOption.getVoltageLevel(values.get(28)) : null)
                            .feeder(values.get(29)).rnisNotes(values.size() == 31 ? values.get(30) : null)
                            .isPvStorage(false)
                            .build();


            try {
                var responsibleAddress = formatAddress(values.get(8));
                CityAreaVo responsible = findCityAreaInCache(responsibleAddress);
                if (responsible != null) {
                    generator.setResponsiblePersonAddressArea(responsible.getId().intValue());
                    generator.setResponsiblePersonAddressOther(responsibleAddress.substring(
                            responsible.getCityName().length() + responsible.getAreaName().length()));
                } else {
                    generator.setResponsiblePersonAddressOther(responsibleAddress);
                }
            } catch (Throwable ex) {
                log.error(values.get(8), ex);
            }
            try {
                var siteAddress = formatAddress(values.get(7));
                CityAreaVo site = findCityAreaInCache(siteAddress);

                if (site != null) {
                    generator.setAddressArea(site.getId().intValue());
                    generator.setAddressOther(
                            siteAddress.substring(site.getCityName().length() + site.getAreaName().length()));
                } else {
                    generator.setAddressOther(values.get(7));
                }
            } catch (Throwable ex) {
                log.error(values.get(7), ex);
            }
            generator.setGeneratorEntityCombinedCapacities(new ArrayList<>());
        }

        List<GeneratorEntityCombinedCapacityVO> list = generator.getGeneratorEntityCombinedCapacities();

        SimpleDateFormat dateformat = new SimpleDateFormat("yyyy/MM/dd");
        String strCombineDate = values.get(25);
        if (!strCombineDate.isEmpty()) strCombineDate =
                (1911 + Integer.parseInt(strCombineDate.split("/")[0])) + strCombineDate.substring(
                        strCombineDate.indexOf("/"));

        String strLicenseDate = values.get(26);
        if (!strLicenseDate.isEmpty()) strLicenseDate =
                (1911 + Integer.parseInt(strLicenseDate.split("/")[0])) + strLicenseDate.substring(
                        strLicenseDate.indexOf("/"));

        list.add(GeneratorEntityCombinedCapacityVO.builder().capacity(new BigDecimal(values.get(16).isEmpty() ? "0" :
                values.get(16))).genNo(
                values.get(14)).genCode(values.get(15)).combinedDate(
                !values.get(25).isEmpty() ? dateformat.parse(strCombineDate) : null).licenseDate(
                !values.get(26).isEmpty() ? dateformat.parse(strLicenseDate) : null).terminateDate(
                values.get(24).contentEquals("終止契約") ? new Date() : null).build());


        if (list.size() > 1) generator.setIsMultiUnit(true);

//        GeneratorEntityMeter.builder()
//                .capacity(new BigDecimal(values.get(16)))
//                .active(values.get(24).contentEquals("併聯完成"))
//                .build();

        generatorSyncMap.put(values.getFirst(), generator);
        return generator;
    }


    long getGeneratorUnitType(String type) {
        if (type.contentEquals("第一型")) {
            return 1;
        } else if (type.contentEquals("第二型")) {
            return 2;
        } else if (type.contentEquals("第三型")) {
            return 3;
        }
        return -1;
    }

    int getFuelType(String type) {
        if (fuelTypeList.isEmpty())
            init();
        var fuelType = this.fuelTypeList.stream().filter(a -> a.getLabel().contains(type)).findFirst();

        if (fuelType.isPresent()) return fuelType.get().getId();
        else return 6;
    }

    int getFuelForm(String form) {
        if (fuelFormList.isEmpty())
            init();
        var fuelForm = fuelFormList.stream().filter(a -> a.getLabel().equals(form)).findFirst();
        if (fuelForm.isPresent()) return fuelForm.get().getId();
        else return 8;
    }

    //    long getWholeSaleType(String type) {
//        if (type.contentEquals("僅併聯不躉售"))
//            return -1;
//    }
    private ChannelSftp setupJsch(String host, String username, String password) throws JSchException {
        JSch jsch = new JSch();
        jsch.setKnownHosts(config.getSFTPKnownHost());
        Session jschSession = jsch.getSession(username, host);
        jschSession.setPassword(password);
        jschSession.connect();
        return (ChannelSftp) jschSession.openChannel("sftp");
    }

    private Session setupJschWithPort(String host, String username, String password,
                                      Integer port) throws JSchException {
        JSch jsch = new JSch();
        jsch.setKnownHosts(config.getSFTPKnownHost());
        Session jschSession =
                (null == port || 0 >= port) ? jsch.getSession(username, host) : jsch.getSession(username, host, port);
        jschSession.setPassword(password);
        jschSession.connect();
        return jschSession;
    }


    /**
     * 依據對應檔案類型進行同步
     *
     * @param fileName
     * @param userId
     * @throws JSchException
     * @throws SftpException
     */
    public void syncRNFromSftp(String fileName, long userId) throws JSchException, SftpException {
        ChannelSftp channelSftp = setupJsch(config.getRnisSFTPHost(), config.getRnisSFTPUsername(),
                config.getRnisSFTPPassword());
        channelSftp.connect();
        String remoteFile = config.getRnisSFTPRemoteDir() + "/" + fileName;
        log.info("syncRNFromSftp {}", remoteFile);
        List<String> errors = new ArrayList<>();
        generatorSyncMap.clear(); // 每次同步時，清空map
        List<String> message = new ArrayList<>();
        var voltageLevels = voltageLevelRepository.findAll();
        getRNDataFromCSVFile(channelSftp.get(remoteFile)).forEach((key, entity) -> {
            try {
                var compareResult = generatorService.saveFromRNIS(entity, voltageLevels, userId);
                if (compareResult != null) {
                    message.add(compareResult);
                }
            } catch (Throwable ex) {
                log.error("{}", entity, ex);
                errors.add(String.format("%s 更新失敗 %s", entity.getRelationId(), ex.getMessage()));
            }
        });
        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join("\n", errors));
        }
        channelSftp.exit();
        try {
            if (!message.isEmpty()) {
                var roleIds = config.getRnisFTPNotifyRole();
                var accounts = accountRepository.findAllByRoleId(roleIds);
                var accountIds = accounts.stream().map(Account::getId).toList();
                this.sendNotificationWithAccountIdOnly(userId, accountIds, "RNIS 每日異動通知",
                        String.join("\n", message));
            }
        } catch (Throwable ex) {
            log.error("send notification failed", ex);
        }
    }


    /**
     * 從FTP更新NBS用戶端資料
     *
     * @param fileName
     * @param userId
     * @throws JSchException
     * @throws SftpException
     */
    public void syncNBFromSftp(String fileName, long userId) throws JSchException, SftpException {
        ChannelSftp channelSftp = setupJsch(config.getNbsSFTPHost(), config.getNbsSFTPUsername(),
                config.getNbsSFTPPassword());
        channelSftp.connect();
        String remoteFile = config.getNbsSFTPRemoteDir() + "/" + fileName;
        List<String> errors = new ArrayList<>();
        // TODO need implement nbs data parser
//        getRNDataFromCSVFile(channelSftp.get(remoteFile)).forEach((key, entity) -> {
//            try {
//                generatorService.saveFromRNIS(entity, userId);
//            } catch (Throwable ex) {
//                log.error(entity, ex);
//                errors.add(String.format("%s 更新失敗 %s", entity.getRelationId(), ex.getMessage()));
//            }
//        });
        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join("\n", errors));
        }
        channelSftp.exit();
        throw new RuntimeException("Not implemented");
    }

    private void putFileToSftp(String content, String filename, String hostname, String username, String password,
                               String remoteDir) {
        try {
            ChannelSftp channelSftp = setupJsch(hostname, username, password);
            channelSftp.connect();
            OutputStream outputStream = channelSftp.put(remoteDir + "/" + filename);
            outputStream.write(content.getBytes());
            channelSftp.exit();
        } catch (JSchException e) {
            throw new RuntimeException(e);
        } catch (SftpException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /** 提供 調度處 PDC sFtp 檔案上傳使用
     * @param content
     * @param filename
     * @param remoteDirIdx
     * @param pIdx
     */
    public boolean putPDCFileToSftp(String content, String filename, Integer remoteDirIdx, Integer pIdx) {
        try {
            HostPortVo hp = parsingSftpHostInfo(config.getPdcSFTPHost());
            if (null == hp) throw new RuntimeException("put host or port:" + config.getPdcSFTPHost()); //return false;
            Session jschSession = getSftpSetup(hp.getHost(), hp.getPort(), pIdx);
            if (null == jschSession) return false;
            String prefix = filename.contains("xlsx")|| filename.contains("xls") || filename.contains("csv")
                    || filename.contains("zip") ? null == config.getPdcSFTPUploadPrefix() ? ""
                    : config.getPdcSFTPUploadPrefix() : "";
            log.info("prefix:"+prefix);
            ChannelSftp channelSftp = (ChannelSftp) jschSession.openChannel("sftp");
            channelSftp.connect();
            String remoteDir = config.getPdcSFTPRemoteDirData(); // SFTP_PDC_DATE_REMOTE_FOLDER_DATA
            if (SFTP_PDC_DATE_REMOTE_FOLDER_LOST_TABLE.equals(remoteDirIdx)) remoteDir = config.getPdcSFTPRemoteDirLostTable();
            else if (SFTP_PDC_DATE_REMOTE_FOLDER_KAO_DATA.equals(remoteDirIdx)) remoteDir = config.getPdcSFTPRemoteDirKaoData();
            else if (SFTP_PDC_DATE_REMOTE_FOLDER_AD_DATA.equals(remoteDirIdx)) remoteDir = config.getPdcSFTPRemoteDirADData();
            else if (SFTP_PDC_DATE_REMOTE_FOLDER_VL_VALUE.equals(remoteDirIdx)) remoteDir = config.getPdcSFTPRemoteDirVLValue();
            remoteDir = remoteDir.replace("'","");
            boolean dirCheck = channelSftp.stat(remoteDir).isDir();
            if (!dirCheck) {
                channelSftp.disconnect();
                jschSession.disconnect();
                return false;
            }
            channelSftp.cd(remoteDir);
            OutputStream outputStream = channelSftp.put(remoteDir + "/" + prefix + filename);
            outputStream.write(content.getBytes());
            channelSftp.disconnect();
            jschSession.disconnect();
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 提供 標檢局 BSMI sFtp 檔案上傳使用
     *
     * @param content
     * @param filename
     * @param remoteInnerDir
     */
    public boolean putBSMIFilesToSftp(List<String> content, List<String> filename, String remoteInnerDir,
                                      boolean mark) {
        try {
            HostPortVo hp = parsingSftpHostInfo(config.getBsmiSFTPHost());
            if (null == hp) throw new RuntimeException("put host or port:" + config.getBsmiSFTPHost()); //return false;
            Session jschSession = getSftpSetup(hp.getHost(), hp.getPort(), null);
            String prefix = filename.get(0).contains("月份") ? null == config.getBsmiSFTPUploadPrefix() ? ""
                    : config.getBsmiSFTPUploadPrefix() : "";
            for (int i = 0; i < content.size(); i++) {
                ChannelSftp channelSftp = (ChannelSftp) jschSession.openChannel("sftp");
                channelSftp.connect();
                channelSftp.cd(config.getBsmiSFTPRemoteDir());

                if (0 == i) {
                    boolean dirCheck = retrievalFileTree(channelSftp.ls(config.getBsmiSFTPRemoteDir()), remoteInnerDir);
                    if (!dirCheck && mark) channelSftp.mkdir(remoteInnerDir);
                    else if (!dirCheck) {
                        channelSftp.disconnect();
                        jschSession.disconnect();
                        return false;
                    }
                }

                OutputStream outputStream = channelSftp.put(remoteInnerDir + "/" + prefix + filename.get(i));
                outputStream.write(content.get(i).getBytes());
                channelSftp.disconnect();
            }
            jschSession.disconnect();
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 標檢局 sFTP 下載檔案使用
     *
     * @param localDir
     * @param remoteInnerDir
     * @param filename
     * @return
     */
    public boolean syncBSMIInputFromSftp(String localDir, String remoteInnerDir, String filename) {
        try {
            HostPortVo hp = parsingSftpHostInfo(config.getBsmiSFTPHost());
            if (null == hp) throw new RuntimeException("sync host or port:" + config.getBsmiSFTPHost()); //return false;
            Session jschSession = getSftpSetup(hp.getHost(), hp.getPort(), null);
            ChannelSftp channelSftp = (ChannelSftp) jschSession.openChannel("sftp");
            channelSftp.connect();
            channelSftp.cd(config.getBsmiSFTPRemoteDir());
            boolean dirCheck = retrievalFileTree(channelSftp.ls(config.getBsmiSFTPRemoteDir()), remoteInnerDir);
            if (dirCheck) {
                channelSftp.cd(remoteInnerDir);
                String path = config.getBsmiSFTPRemoteDir() + "/" + remoteInnerDir;
                dirCheck = retrievalFileTree(channelSftp.ls(path), filename);
                if (dirCheck) {
                    channelSftp.get(filename, localDir + filename);
                    channelSftp.disconnect();
                    jschSession.disconnect();
                    return true;
                } else {
                    channelSftp.disconnect();
                    jschSession.disconnect();
                    throw new RuntimeException("no download file:" + filename);
                }
            } else {
                channelSftp.disconnect();
                jschSession.disconnect();
                throw new RuntimeException("no remote inner folder:" + remoteInnerDir);
            }/*channelSftp.disconnect();jschSession.disconnect();return false;*/
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private HostPortVo parsingSftpHostInfo(String info) {
        if (null == info) return null;
        String[] hp = info.replace("'", "").split(":");
        return hp.length > 1 ? HostPortVo.builder().host(hp[0]).port(Integer.parseInt(hp[1])).build()
                : HostPortVo.builder().host(hp[0]).build();
    }

    private Session getSftpSetup(String host, Integer port, Integer pIdx) throws Exception {
        String ans = decryptPIdx(pIdx);
        if (null == ans) return null;
        String[] pp = ans.split("~");
        return setupJschWithPort(host, pp[1], pp[0], port);
    }

    private String decryptPIdx(Integer pIdx) {
        if (SFTP_PDC_MODE.equals(pIdx)) {
            String enhanceStr = "powerDispatchCenter";
            if (null == config.getPdcSFTPUsername()) return null;
            else if (null == config.getPdcSFTPPassword())
                return JasyptEncryptorUtil.decrypt(
                        "Y1l92h6EgeuvhkowJ86AtvYnJeoYcguNJ5CYfNwJj2OaTjnBy76dUezxdRjs4oB4+DZ45L/OC1J9GXPL5cI5pA=="
                        , enhanceStr).split("-")[0]+"~"+config.getPdcSFTPUsername();
            else return config.getPdcSFTPPassword()+"~"+config.getPdcSFTPUsername();
        } else {
            String enhanceStr = "standardCenter";
            if (null == config.getBsmiSFTPPassword())
                return JasyptEncryptorUtil.decrypt(
                        "l4rG/cjBFbJp4dQO5a4Zt1UeaheRIsmx1sfxTozItvmhf6mzIOn45BkvVBo3Y8vJDJaHbPyx/Av4O6NvVgMbdg=="
                        , enhanceStr).split("-")[0]+"~"+config.getBsmiSFTPUsername();
            return config.getBsmiSFTPPassword() + "~" + config.getBsmiSFTPUsername();
        }
    }

    private boolean retrievalFileTree(Vector<ChannelSftp.LsEntry> fileTree, String name) {
        boolean mark = false;
        for (ChannelSftp.LsEntry lsEntry : fileTree) {
            if (lsEntry.getFilename().contains(name)) {
                mark = true;
                break;
            }
        }
        return mark;
    }

    public void saveNBListToSftp(String filename) {
        var list = loadEntityRepository.findAll().stream().map(LoadEntity::getNbsCustomerNumber).toList();
        putFileToSftp(String.join(",", list), filename, config.getNbsSFTPHost(), config.getNbsSFTPUsername(),
                config.getNbsSFTPPassword(), config.getNbsSFTPRemoteDir());
    }

    public void saveRNListToSftp(String filename) {
        var list = generatorEntityRepository.findAll().stream().map(GeneratorEntity::getNbsCustomerNumber).toList();
        putFileToSftp(String.join(",", list), filename, config.getRnisSFTPHost(), config.getRnisSFTPUsername(),
                config.getRnisSFTPPassword(), config.getRnisSFTPRemoteDir());
    }

    /**
     * 用於新舊DB轉換使用
     *
     * @param address
     * @return
     */
    public AddressResult transformAddressWithCache(String address) {
        CityAreaVo responsible = findCityAreaInCache(address);
        if (responsible != null) {
            String other = address.substring(
                    responsible.getCityName().length() + responsible.getAreaName().length());
            return AddressResult.builder().cityAreaVo(responsible).other(other).build();
        } else {
            return AddressResult.builder().cityAreaVo(null).other(address).build();
        }


    }

    /**
     * 排程作業執行，TASK_UPDATE_PWOMS_NBS_LIST
     */
    public void updateAllLoadEntities() {
        var userId = 0L;
        var loads = loadEntityRepository.findAll();
        var tpcMeters = loads.stream().map(LoadEntity::getNbsCustomerNumber).toList();
        List<String> success = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        loads.forEach(load -> {
            var meter = load.getNbsCustomerNumber();
            var accountId = load.getNbMstAccountId();
            try {
                meter = formatCustomerNo(meter);
                if (!verifyCustomerNumber(meter)) {
                    throw new RuntimeException("電號規則錯誤");
                }
                PWOMSNBData nbData = null;
                if (StringUtils.isEmpty(accountId)) {
                    nbData = getDataNB(meter);
                } else {
                    // 等待nbs api更新替換
//                    nbData = getDataNBByAccountId(meter,accountId);
                    nbData = getDataNB(meter);
                }

                if (!StringUtils.isEmpty(nbData.getNbMstErrrMsge())) {
                    // 如果同步不到，新增一個對應電號的用電端;
                    var loadEntity =
                            LoadEntity.builder().nbsCustomerNumber(meter).build();
                    var saveResult = remsTxService.sucessSave(loadEntity);
                    throw new RuntimeException(
                            nbData.getNbMstErrrMsge() + (!saveResult ? "但轉直供平台已存在此電號" : ""));
                } else {
                    remsTxService.sucessSave(nbData, userId);
                }
                success.add(String.format("%s 同步成功", customerMeterFormat(meter)));
            } catch (Throwable ex) {
                log.error(meter, ex);
                errors.add(String.format("%s 同步失敗( %s )", customerMeterFormat(meter), ex.getMessage()));
            }
        });
        syncMessageReport(tpcMeters, success, errors);
    }

    /**
     * 排程作業執行，TASK_UPDATE_PWOMS_RNIS_LIST
     * 預留未來同步以relationId同步的方法
     */
    public void updateAllGeneratorEntities() {
        var userId = 0L;
        var gens = generatorEntityRepository.findAll();
        var tpcMeters = gens.stream().map(GeneratorEntity::getNbsCustomerNumber).toList();
        List<String> success = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        remsTxService.updateFuelTypeList();
        gens.forEach(gen -> {
            var meter = gen.getNbsCustomerNumber();
            try {

                var relationId = gen.getRelationId();

                meter = formatCustomerNo(meter);

                try {
                    RNDataV2 rnData = null;
                    if (StringUtils.isEmpty(relationId)) {
                        rnData = getDataRNV2(meter);
                    } else {
                        // 等待rnis api提供relationId開放查詢使用
//                        rnData = getDataRNV2ByRelationid(meter, relationId);
                        rnData = getDataRNV2(meter);
                    }
                    remsTxService.sucessSave(rnData, userId);
                } catch (Throwable ex) {
                    log.error(meter, ex);
                    var generatorEntity =
                            GeneratorEntity.builder().nbsCustomerNumber(meter).build();
                    var saveResult = remsTxService.sucessSave(generatorEntity);
                    throw new RuntimeException((!saveResult ? "但轉直供平台已存在此電號" : "未查詢到對應電號資料"));
                }

                success.add(String.format("%s 同步成功", customerMeterFormat(meter)));
            } catch (Throwable ex) {
                log.error(meter, ex);
                errors.add(String.format("%s 同步失敗( %s )", customerMeterFormat(meter), ex.getMessage()));
            }
        });
        syncMessageReport(tpcMeters, success, errors);
    }

    public RNDataV2 getDataRNV2(String tpcMeter) {
        tpcMeter = tpcMeter.trim().replaceAll("-", "");
        RNDataV2 rnData = post(decideRNISUrl(tpcMeter), RequestMeter.builder().tpcMeter(tpcMeter).build(),
                RNDataV2.class);
        formatAddress(rnData);
        log.info("{}", rnData);
        return rnData;
    }

    public RNDataV2 getDataRNV2ByRelationid(String tpcMeter, String relationId) {
        tpcMeter = tpcMeter.trim().replaceAll("-", "");
        RNDataV2 rnData = post(decideRNISUrl(tpcMeter), RequestMeter.builder().tpcMeter(tpcMeter).build(),
                RNDataV2.class);
        formatAddress(rnData);
        log.info("{}", rnData);
        return rnData;
    }

    /**
     * 判斷不知道格式的日期，以對應方法處理
     *
     * @param date
     * @param userId
     * @throws JSchException
     * @throws SftpException
     */
    public void syncRNFromSftpByUnknownDate(String date, long userId) {

        try {
            date = date.replaceAll("[/-]", "");
            if (date.length() == 7) {
                syncRNFromSftpByTaiwanDate(date, userId);
            } else {

                syncRNFromSftpByDate(date, userId);
            }
        } catch (Throwable ex) {
            log.error(ex, ex);
            throwException(ErrorCode.RNIS_FTP_SYNC_FAILED);
        }

    }

    /**
     * 以西元日期進行RNIS FTP同步
     *
     * @param date
     * @param userId
     * @throws JSchException
     * @throws SftpException
     */
    public void syncRNFromSftpByDate(String date, long userId) throws JSchException, SftpException {
        date = TaiwanDateUtil.toTaiwanDate(date);
        syncRNFromSftpByTaiwanDate("RNIS_noSale_" + date + ".csv", userId);
    }

    /**
     * 以台灣日期進行RNIS FTP同步
     *
     * @param date
     * @param userId
     * @throws JSchException
     * @throws SftpException
     */
    public void syncRNFromSftpByTaiwanDate(String date, long userId) throws JSchException, SftpException {
        // 只同步RNIS非躉售資料
        syncRNFromSftp("RNIS_noSale_" + date + ".csv", userId);
    }


    /**
     * 每日同步RNIS發電端資料
     */
    public void syncRNISSchedule() {
        boolean hasError = false;
        try {
            this.updateAllGeneratorEntities();
        } catch (Throwable ex) {
            log.error("updateAllGeneratorEntities failed", ex);
            hasError = true;
        }
        try {
            syncRNFromSftpByTaiwanDate(TaiwanDateUtil.toTaiwanDate(new Date()), 0L);
        } catch (Throwable ex) {
            log.error("syncRNFromSftpByTaiwanDate", ex);
            hasError = true;
        }
        if (hasError) {
            throw new RuntimeException("execute failed.");
        }
    }

    /**
     * 每日同步RNIS發電端資料
     */
    public void syncNBSSchedule() {
        try {
            this.updateAllLoadEntities();
        } catch (Throwable ex) {
            log.error("updateAllLoadEntities", ex);
            throw ex;
        }
    }

    private List<FuelType> fuelTypeList = new ArrayList<>();
    private List<CityAreaVo> cityAreaCaches = new ArrayList<>();
    private List<FuelForm> fuelFormList = new ArrayList<>();

    public void init() {

        cityAreaCaches = cityAreaService.getAll().getList();
        this.updateFuelTypeList();

    }

    /**
     * 更新燃料清單/燃料設置類型清單
     */
    public void updateFuelTypeList() {
        this.fuelTypeList = fuelTypeRepository.findAll();
        this.fuelFormList = fuelFormRepository.findAll();
    }

    public CityAreaVo findCityAreaInCache(String address) {
        if (address == null) {
            return null;
        }
        try {
            address = address.replaceAll("^[\\d\\s]+", "");
        } catch (Throwable ex) {
            log.error(address, ex);
        }
        int indexArea = -1;
        indexArea = address.indexOf("縣");
        if (indexArea < 1 || indexArea > 5) indexArea = address.indexOf("市");
        if (indexArea < 1 || indexArea > 5) {
//            log.info(address);
            return null;
        } else {
            int indexArea2 = address.substring(indexArea + 1).indexOf("鄉");
            if (indexArea2 < 1 || indexArea2 > 5) indexArea2 = address.substring(indexArea + 1).indexOf("區");
            if (indexArea2 < 1 || indexArea2 > 5) indexArea2 = address.substring(indexArea + 1).indexOf("市");
            if (indexArea2 < 1 || indexArea2 > 5) indexArea2 = address.substring(indexArea + 1).indexOf("鎮");

            if (indexArea2 < 1 || indexArea2 > 5) {
//                log.info(address);
                return null;
            }
            int finalIndexArea = indexArea;
            int finalIndexArea1 = indexArea2;
            String finalAddress = address;
            return this.cityAreaCaches.stream().filter(cityAreaVo -> cityAreaVo.getCityName().contentEquals(
                    tai(finalAddress.substring(0, finalIndexArea + 1))) && cityAreaVo.getAreaName().contentEquals(
                    tai(finalAddress.substring(finalIndexArea + 1).substring(0,
                            finalIndexArea1 + 1)))).findFirst().orElse(null);
        }
    }

    public String tai(String city) {
        if (city.contains("台")) {
            return city.replaceAll("台", "臺");
        } else return city;
    }
}
