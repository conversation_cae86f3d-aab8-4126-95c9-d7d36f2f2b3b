package tw.com.taipower.pwoms.services.tpc;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.pwoms.logger.CustomLog;
import tw.com.taipower.pwoms.services.BaseService;
import tw.com.taipower.pwoms.services.config.TpcSelectorConfig;
import tw.com.taipower.pwoms.services.enumclass.ErrorCode;
import tw.com.taipower.pwoms.services.tpc.model.SimpleApiResult;

import javax.net.ssl.*;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

/**
 * @class: 參考台電的SelectorExample.java，執行資料清洗
 * @author: linyu-sheng
 * @version: 0.0.0
 * @since: 2025/6/23
 * @see:
 */
@Service
@CustomLog
public class SelectorITService extends BaseService {
    @Autowired
    private TpcSelectorConfig selectorConfig;

    /**
     * 台電資料清洗檢查服務，呼叫這個方法進行檢查，如果錯誤就直接中斷操作
     * @param fileContent
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     */
    public void checkWithoutResult(MultipartFile fileContent) throws NoSuchAlgorithmException, KeyManagementException {
        var response = check(fileContent);
        log.info("File name: {}, File size: {} bytes, Response: {}", 
                fileContent.getOriginalFilename(), 
                fileContent.getSize(), 
                response);
        if (response.getCode() != 200) {
            throwException(ErrorCode.TPC_SELECTOR_FAIL);
        }
    }

    private SimpleApiResult<Void> check(
            MultipartFile fileContent) throws NoSuchAlgorithmException, KeyManagementException {
        try {
            if (selectorConfig.getSkip()) {
                return SimpleApiResult.<Void>builder()
                        .code(200)
                        .message("跳過檢查")
                        .build();
            }
            // Create a trust manager that does not validate certificate chains
            TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }

                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }};
            // Install the all-trusting trust manager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = (hostname, session) -> true;
            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }
        // 不驗證 PDF 檔案，不知道檔案格式

        try {
            String url = selectorConfig.getUrl();
            String username = selectorConfig.getUsername();
            String password = selectorConfig.getPassword();
            String clientId = selectorConfig.getClientId();
            String plainCreds = username + ":" + password;
            byte[] plainCredsBytes = plainCreds.getBytes();
            byte[] base64CredsBytes = Base64.encodeBase64(plainCredsBytes);
            String base64Creds = new String(base64CredsBytes);

            HttpHeaders headers = new HttpHeaders();
            headers.add("Authorization", "Basic " + base64Creds);
            headers.add("Content-Name", "file.pdf");
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            HttpEntity<byte[]> request = new HttpEntity<>(fileContent.getBytes(), headers);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
            HttpHeaders responseHeaders = response.getHeaders();

            // 取得 X-Session-ID
            String sessionId = responseHeaders.getFirst("X-Session-ID");
            if (sessionId == null) {
                return SimpleApiResult.<Void>builder()
                        .code(422)
                        .message("無效檔案")
                        .build();
            }
            headers.add("X-Session-ID", sessionId);

            // 準備第二次請求
            headers.setContentType(MediaType.APPLICATION_JSON);
            String jsonBody = String.format("{\"policy\":\"api\",\"client\":\"%s\",\"return\":\"json\"}", clientId);
            HttpEntity<String> data2 = new HttpEntity<>(jsonBody, headers);
            ResponseEntity<String> response2 = restTemplate.exchange(url, HttpMethod.PUT, data2, String.class);
            String responseBody = response2.getBody();
            // 這裡建議用 Jackson 解析 responseBody，假設格式如下：{"Files":[{"Status":"Modified"/"Blocked"/"Intact"}]}
            com.fasterxml.jackson.databind.JsonNode root =
                    new com.fasterxml.jackson.databind.ObjectMapper().readTree(responseBody);
            String compareString = root.path("Files").get(0).path("Status").asText();
            if ("Intact".equalsIgnoreCase(compareString)) {
                return SimpleApiResult.<Void>builder()
                        .code(200)
                        .message("上傳成功")
                        .build();
            }
            // 如果 需要修改乾脆作為無效檔案阻擋上傳
//            else if ("Modified".equals(compareString)) {
//                // get data back and save to DB
//                HttpEntity<String> dataTogetBack = new HttpEntity<>("{}", headers);
//                restTemplate.exchange(url, HttpMethod.GET, dataTogetBack, byte[].class);
//                return SimpleApiResult.<Void>builder()
//                        .code(200)
//                        .message("上傳成功")
//                        .build();
//            } else if ("Blocked".equalsIgnoreCase(compareString)) {
//                return SimpleApiResult.<Void>builder()
//                        .code(422)
//                        .message("無效檔案")
//                        .build();
//            }
            else {
                return SimpleApiResult.<Void>builder()
                        .code(422)
                        .message("無效檔案")
                        .build();
            }
        } catch (Exception e) {
            return SimpleApiResult.<Void>builder()
                    .code(422)
                    .message("無效檔案")
                    .build();
        }
    }
}
