package tw.com.taipower.pwoms.services.tpc;

import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.transaction.Transactional;
import org.apache.axis.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.logger.CustomLog;
import tw.com.taipower.pwoms.services.BaseService;
import tw.com.taipower.pwoms.services.cityarea.CityAreaService;
import tw.com.taipower.pwoms.services.entitymanage.GeneratorService;
import tw.com.taipower.pwoms.services.entitymanage.LoadService;
import tw.com.taipower.pwoms.services.tpc.model.*;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.utils.VoUtils;
import tw.com.taipower.pwoms.services.vo.cityarea.CityAreaVo;
import tw.com.taipower.pwoms.services.vo.generated.GeneratorEntityMeterVO;
import tw.com.taipower.pwoms.services.vo.generated.LoadEntityMeterVO;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @class: RemsService
 * @author: daniel
 * @version:
 * @since: 2024-05-30 10:54
 * @see:
 **/

@CustomLog
@Service
@Transactional
public class RemsTxService extends BaseService {

    @Autowired
    private GeneratorEntityRepository generatorEntityRepository;

    @Autowired
    private LoadEntityRepository loadEntityRepository;

    @Autowired
    private LoadService loadService;

    @Autowired
    private CityAreaService cityAreaService;

    @Autowired
    FuelTypeRepository fuelTypeRepository;

    @Autowired
    FuelFormRepository fuelFormRepository;

    @Autowired
    private GeneratorEntityCombinedCapacityRepository generatorEntityCombinedCapacityRepository;

    @Autowired
    private GeneratorService generatorService;

    private List<FuelType> fuelTypeList = new ArrayList<>();
    private List<CityAreaVo> cityAreaCaches = new ArrayList<>();
    private List<FuelForm> fuelFormList = new ArrayList<>();

    public void init() {

        cityAreaCaches = cityAreaService.getAll().getList();
        this.updateFuelTypeList();

    }

    /**
     * 更新燃料清單/燃料設置類型清單
     */
    public void updateFuelTypeList() {
        this.fuelTypeList = fuelTypeRepository.findAll();
        this.fuelFormList = fuelFormRepository.findAll();
    }

    /**
     * 預期時間段別
     *
     * @param data
     * @return
     */
    public Integer turnTimeStg(String data) {
        if ("三段式".equals(data) || "三段".equals(data) || "3".equals(data)) {
            return 3;
        } else if ("二段式".equals(data) || "二段".equals(data) || "2".equals(data)) {
            return 2;
        } else if ("一段式".equals(data) || "一段".equals(data) || "1".equals(data)) {
            return 1;
        }
        return null;
    }

    public String tai(String city) {
        if (city.contains("台")) {
            return city.replaceAll("台", "臺");
        } else
            return city;
    }

    public CityAreaVo findCityArea(String address) {
        if (address == null) {
            return null;
        }

        int indexArea = -1;
        indexArea = address.indexOf("縣");
        if (indexArea < 1 || indexArea > 5)
            indexArea = address.indexOf("市");
        if (indexArea < 1 || indexArea > 5) {
            // log.info(address);
            return null;
        } else {
            int indexArea2 = address.substring(indexArea + 1).indexOf("鄉");
            if (indexArea2 < 1 || indexArea2 > 5)
                indexArea2 = address.substring(indexArea + 1).indexOf("區");
            if (indexArea2 < 1 || indexArea2 > 5)
                indexArea2 = address.substring(indexArea + 1).indexOf("市");
            if (indexArea2 < 1 || indexArea2 > 5)
                indexArea2 = address.substring(indexArea + 1).indexOf("鎮");

            if (indexArea2 < 1 || indexArea2 > 5) {
                // log.info(address);
                return null;
            }
            int finalIndexArea = indexArea;
            int finalIndexArea1 = indexArea2;
            String finalAddress = address;
            return cityAreaService.getAll().getList().stream()
                    .filter(cityAreaVo -> cityAreaVo.getCityName()
                            .contentEquals(tai(finalAddress.substring(0, finalIndexArea + 1)))
                            && cityAreaVo.getAreaName()
                            .contentEquals(tai(finalAddress.substring(finalIndexArea + 1)
                                    .substring(0, finalIndexArea1 + 1))))
                    .findFirst().orElse(null);
        }
    }

    public CityAreaVo findCityAreaInCache(String address) {
        if (address == null) {
            return null;
        }
        try {
            address = address.replaceAll("^[\\d\\s]+", "");
        } catch (Throwable ex) {
            log.error(address, ex);
        }
        int indexArea = -1;
        indexArea = address.indexOf("縣");
        if (indexArea < 1 || indexArea > 5)
            indexArea = address.indexOf("市");
        if (indexArea < 1 || indexArea > 5) {
            // log.info(address);
            return null;
        } else {
            int indexArea2 = address.substring(indexArea + 1).indexOf("鄉");
            if (indexArea2 < 1 || indexArea2 > 5)
                indexArea2 = address.substring(indexArea + 1).indexOf("區");
            if (indexArea2 < 1 || indexArea2 > 5)
                indexArea2 = address.substring(indexArea + 1).indexOf("市");
            if (indexArea2 < 1 || indexArea2 > 5)
                indexArea2 = address.substring(indexArea + 1).indexOf("鎮");

            if (indexArea2 < 1 || indexArea2 > 5) {
                // log.info(address);
                return null;
            }
            int finalIndexArea = indexArea;
            int finalIndexArea1 = indexArea2;
            String finalAddress = address;
            return this.cityAreaCaches.stream()
                    .filter(cityAreaVo -> cityAreaVo.getCityName()
                            .contentEquals(tai(finalAddress.substring(0, finalIndexArea + 1)))
                            && cityAreaVo.getAreaName()
                            .contentEquals(tai(finalAddress.substring(finalIndexArea + 1)
                                    .substring(0, finalIndexArea1 + 1))))
                    .findFirst().orElse(null);
        }
    }

    /**
     * 回傳true: 新增成功，回傳false 轉直供已經有此筆資料
     *
     * @param generatorEntity
     * @return
     */
    @Transactional
    public boolean sucessSave(GeneratorEntity generatorEntity) {

        var dbEntity = generatorEntityRepository
                .findOneByNbsCustomerNumber(generatorEntity.getNbsCustomerNumber());
        if (dbEntity == null) {
            generatorEntityRepository.save(generatorEntity);
            return true;
        }
        return false;
    }


    /**
     * 回傳true: 新增成功，回傳false 轉直供已經有此筆資料
     *
     * @param loadEntity
     * @return
     */
    @Transactional
    public boolean sucessSave(LoadEntity loadEntity) {
        var dbEntity =
                loadEntityRepository.findOneByNbsCustomerNumber(loadEntity.getNbsCustomerNumber());
        if (dbEntity == null) {
            loadEntityRepository.save(loadEntity);
            return true;
        }
        return false;
    }

    @Transactional
    public void sucessSave(PWOMSNBData nbData, Long userId) {
        var nbsCustomerNo = nbData.getNbMstTpcmeter();
        var loadEntity = loadEntityRepository.findOneByNbsCustomerNumber(nbsCustomerNo);
        if (loadEntity == null) {
            loadEntity = LoadEntity.builder().nbsCustomerNumber(nbData.getNbMstTpcmeter()).build();
        }
        if (!StringUtils.isEmpty(nbData.getNbMstCustName())) {
            loadEntity.setName(nbData.getNbMstCustName());
        }
        if (!StringUtils.isEmpty(nbData.getNbMstContUsge())) {
            loadEntity.setContractStg(nbData.getNbMstContUsge());
        }
        if (!StringUtils.isEmpty(nbData.getNbMstCustSegt())) {
            var timeStg = turnTimeStg(nbData.getNbMstCustSegt());
            if (timeStg != null) {
                loadEntity.setTimeStg(timeStg);
            }
        }
        if (!StringUtils.isEmpty(nbData.getNbMstFeedCode())) {
            loadEntity.setFeeder(nbData.getNbMstFeedCode());
        }
        if (!StringUtils.isEmpty(nbData.getNbMstSiccCode())) {
            loadEntity.setNbMstSiccCode(nbData.getNbMstSiccCode());
        }
        try {
            /**
             * 2025/06/24 新增 NBS關聯ID儲存
             */
            if (!StringUtils.isEmpty(nbData.getNbMstAccountId())) {
                loadEntity.setNbMstAccountId(nbData.getNbMstAccountId());
            }
        } catch (Throwable ex) {
            log.error("NbMstAccountId轉換異常", ex);
        }
        /**
         * 地址解析
         */
        try {
            log.info(nbData.getNbMstUsagAddr());
            CityAreaVo usageAddr = findCityArea(nbData.getNbMstUsagAddr());
            if (usageAddr == null) {
                log.warn("用電端: {} 地址解析失敗 {}", nbsCustomerNo, nbData.getNbMstUsagAddr());
                loadEntity.setAddressArea(null);
                loadEntity.setAddressOther(nbData.getNbMstUsagAddr());

            } else {
                loadEntity.setAddressArea(usageAddr.getId().intValue());
                loadEntity.setAddressOther(nbData.getNbMstUsagAddr().substring(
                        usageAddr.getCityName().length() + usageAddr.getAreaName().length()));
            }
        } catch (Throwable ex) {
            log.error(ex, ex);
        }
        if (nbData.getNbMstPcq() != null) {
            loadEntity.setContractCapacity(new BigDecimal(nbData.getNbMstPcq()));
        }
        if (nbData.getNbMstContUsge() != null) {
            loadEntity.setContractStg(nbData.getNbMstContUsge().substring(0, 1));
            loadEntity.setUsageStg(nbData.getNbMstContUsge().substring(1));
        }
        if (nbData.getNbMstCompnbr() != null) {
            loadEntity.setTaxId(nbData.getNbMstCompnbr());
        }
        loadEntity.setNbMstSiccCode(nbData.getNbMstSiccCode());
        loadEntity.setFeeder(nbData.getNbMstFeedCode());

        loadEntityRepository.save(loadEntity);
        loadEntity = loadEntityRepository.findOneByNbsCustomerNumber(nbsCustomerNo);

        if (nbData.getNbMstMtrDetail() != null) {
            var level = nbData.getNbMstMtrDetail().stream()
                    .filter(a -> a.getMstMtrGrpid().contentEquals("01")).findFirst();
            if (level.isPresent()) {
                if (!Objects
                        .requireNonNull(
                                RemsVoltageOption.getVoltageOption(level.get().getMstMtrPhase()))
                        .voltageLevelIn(loadEntity.getResponsibilityVoltage()))
                    loadEntity.setResponsibilityVoltage(
                            RemsVoltageOption.getVoltageLevel(level.get().getMstMtrPhase()));
            }

            final var meterList =
                    loadService.findNoneSpecialMeterByLoadEntityId(loadEntity.getId());
            LoadEntity finalLoadEntity = loadEntity;
            nbData.getNbMstMtrDetail().forEach(detail -> {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                    var useFrom = (Integer.parseInt(detail.getMstMtrInsdt().substring(0,
                            detail.getMstMtrInsdt().length() - 4)) + 1911)
                            + detail.getMstMtrInsdt()
                            .substring(detail.getMstMtrInsdt().length() - 4);
                    var option =
                            meterList
                                    .stream().filter(loadEntityMeterVO -> loadEntityMeterVO
                                            .getGroupType().contentEquals(detail.getMstMtrGrpid()))
                                    .findFirst();
                    String meterNo = detail.getMstMtrType() + detail.getMstMtrMno();
                    String ratio = detail.getMstMtrMult();
                    if (option.isEmpty()) {
                        var vo = LoadEntityMeterVO.builder().meterNo(meterNo)
                                .groupType(detail.getMstMtrGrpid()).build();
                        try {
                            vo.setRatio(Integer.parseInt(ratio));
                        } catch (Throwable ex) {
                            log.error("ratio 更新失敗{} {}", vo.getMeterNo(), ratio);
                        }
                        loadService.addMeterOnlyWithSpecificDate(vo, finalLoadEntity.getId(),
                                userId, dateFormat.parse(useFrom));
                    } else {
                        /**
                         * 如果已經有相同組別的電表存在，檢查表號是否相同，並且更新表倍率
                         */

                        var dbMeter = option.get();
                        try {
                            // 如果表號相同，則更新電表倍率
                            if (dbMeter.getMeterNo().equals(meterNo)) {
                                dbMeter.setRatio(Integer.parseInt(ratio));
                                loadService.updateMeterOnly(dbMeter, userId);
                            } else {
                                log.error("電表表號不一致，不更新 {}", meterNo);
                            }
                        } catch (Throwable ex) {
                            log.error("ratio 更新失敗{} {}", meterNo, ratio, ex);
                        }
                    }
                } catch (Throwable e) {
                    log.error(e);
                }
            });
        }

        log.info("用電端: {} 更新完成 {}", nbsCustomerNo, loadEntity);
    }

    @Transactional
    public void sucessSave(RNData rnData) {
        var nbsCustomerNo = rnData.getRnMstTpcmeter();
        var generatorEntity = generatorEntityRepository.findOneByNbsCustomerNumber(nbsCustomerNo);
        if (generatorEntity == null) {

            generatorEntity =
                    GeneratorEntity.builder().nbsCustomerNumber(rnData.getRnMstTpcmeter()).build();
        }
        if (!StringUtils.isEmpty(rnData.getRnMstAplyName())) {
            generatorEntity.setName(rnData.getRnMstAplyName());
        }
        if (!StringUtils.isEmpty(rnData.getRnMstRspnName())) {
            generatorEntity.setResponsiblePerson(rnData.getRnMstRspnName());
        }
        try {
            /**
             * 設備用電地址
             */
            CityAreaVo usageAddr = findCityArea(rnData.getRnMstAprvAddr());
            if (usageAddr == null) {
                log.warn("設備用電地址 {} 無法解析", rnData.getRnMstAprvAddr());
                generatorEntity.setAddressArea(null);
                generatorEntity.setAddressOther(rnData.getRnMstAprvAddr());
            } else {
                generatorEntity.setAddressArea(usageAddr.getId().intValue());
                generatorEntity.setAddressOther(rnData.getRnMstAprvAddr().substring(
                        usageAddr.getCityName().length() + usageAddr.getAreaName().length()));
            }

        } catch (Throwable ex) {
            log.error(nbsCustomerNo, ex);
        }

        try {
            /**
             * 負責人聯絡地址
             */
            CityAreaVo usageAddr = findCityArea(rnData.getRnMstMailAddr());
            if (usageAddr == null) {
                log.warn("負責人聯絡地址 {} 無法解析", rnData.getRnMstMailAddr());
                generatorEntity.setResponsiblePersonAddressArea(null);
                generatorEntity.setResponsiblePersonAddressOther(rnData.getRnMstMailAddr());
            } else {
                generatorEntity.setResponsiblePersonAddressArea(usageAddr.getId().intValue());
                generatorEntity.setResponsiblePersonAddressOther(
                        rnData.getRnMstMailAddr().substring(usageAddr.getCityName().length()
                                + usageAddr.getAreaName().length()));
            }

        } catch (Throwable ex) {
            log.error(nbsCustomerNo, ex);
        }
        if (!StringUtils.isEmpty(rnData.getRnMstAplyTele())) {
            generatorEntity.setResponsiblePersonPhone(rnData.getRnMstAplyTele());
        }

        if (!StringUtils.isEmpty(rnData.getRnMstCustSegt())) {
            var timeStg = turnTimeStg(rnData.getRnMstCustSegt());
            if (timeStg != null) {
                generatorEntity.setTimeStg(timeStg);
            }

        }
        if (!StringUtils.isEmpty(rnData.getRnMstFeedCode())) {
            generatorEntity.setFeeder(rnData.getRnMstFeedCode());
        }

        generatorEntityRepository.save(generatorEntity);
        log.info("發電端: {} 更新完成 {}", nbsCustomerNo, generatorEntity);
    }

    @Transactional
    public void sucessSave(RNDataV2 rnData, Long userId) throws JsonProcessingException {
        var nbsCustomerNo = rnData.getCustNo();
        var relationId = rnData.getContractSn();
        // 先用關聯id找尋
        var generatorEntity = generatorEntityRepository.findByRelationId(relationId);
        if (generatorEntity == null) {
            // 再找不到就用電號找尋
            generatorEntity = generatorEntityRepository.findOneByNbsCustomerNumber(nbsCustomerNo);

            if (generatorEntity == null) {
                // 再沒有再新建立
                generatorEntity = GeneratorEntity.builder().relationId(rnData.getContractSn())
                        .nbsCustomerNumber(nbsCustomerNo).build();
            } else {
                generatorEntity.setRelationId(relationId);
            }
        }
        if (!StringUtils.isEmpty(rnData.getFormName())) {
            generatorEntity.setName(rnData.getFormName());
        }
        if (!StringUtils.isEmpty(rnData.getTaxId())) {
            generatorEntity.setTaxId(rnData.getTaxId());
        }
        if (!StringUtils.isEmpty(rnData.getRespName())) {
            generatorEntity.setResponsiblePerson(rnData.getRespName());
        }
        try {
            /**
             * 設備用電地址
             */
            CityAreaVo usageAddr = findCityArea(rnData.getAprvAddr());
            if (usageAddr == null) {
                log.warn("設備用電地址 {} 無法解析", rnData.getAprvAddr());
                generatorEntity.setAddressArea(null);
                generatorEntity.setAddressOther(rnData.getAprvAddr());
            } else {
                generatorEntity.setAddressArea(usageAddr.getId().intValue());
                generatorEntity.setAddressOther(rnData.getAprvAddr().substring(
                        usageAddr.getCityName().length() + usageAddr.getAreaName().length()));
            }

        } catch (Throwable ex) {
            log.error(nbsCustomerNo, ex);
        }

        try {
            /**
             * 負責人聯絡地址
             */
            CityAreaVo usageAddr = findCityArea(rnData.getAplyAddr());
            if (usageAddr == null) {
                log.warn("負責人聯絡地址 {} 無法解析", rnData.getAplyAddr());
                generatorEntity.setResponsiblePersonAddressArea(null);
                generatorEntity.setResponsiblePersonAddressOther(rnData.getAplyAddr());
            } else {
                generatorEntity.setResponsiblePersonAddressArea(usageAddr.getId().intValue());
                generatorEntity.setResponsiblePersonAddressOther(rnData.getAplyAddr().substring(
                        usageAddr.getCityName().length() + usageAddr.getAreaName().length()));
            }

        } catch (Throwable ex) {
            log.error(nbsCustomerNo, ex);
        }
        if (!StringUtils.isEmpty(rnData.getPerTel())) {
            generatorEntity.setResponsiblePersonPhone(rnData.getPerTel());
        }

        if (!StringUtils.isEmpty(rnData.getTimElePriCate())) {
            var timeStg = turnTimeStg(rnData.getTimElePriCate());
            if (timeStg != null) {
                generatorEntity.setTimeStg(timeStg);
            }

        }
        if (!StringUtils.isEmpty(rnData.getFeedCd())) {
            generatorEntity.setFeeder(rnData.getFeedCd());
        }
        /**
         * 原本API沒有，額外新增部分
         */
        if (!StringUtils.isEmpty(rnData.getEcType())) {
            generatorEntity.setGenerationUnitType(getGeneratorUnitType(rnData.getEcType()));
        }
        if (!StringUtils.isEmpty(rnData.getPowerType())) {
            generatorEntity.setFuelType(getFuelType(rnData.getPowerType()));
        }
        if (!StringUtils.isEmpty(rnData.getAprvFeeType())) {
            generatorEntity.setFuelForm(getFuelForm(rnData.getAprvFeeType()));
        }
        if (!StringUtils.isEmpty(rnData.getShuntType())) {
            generatorEntity.setCombineMethod("台電內線".equals(rnData.getShuntType()) ? 1 : 2);
        }
        if (!StringUtils.isEmpty(rnData.getTimElePriCate())) {
            if ("一段式".equals(rnData.getTimElePriCate())) {
                generatorEntity.setTimeStg(1);
            } else if ("二段式".equals(rnData.getTimElePriCate())) {
                generatorEntity.setTimeStg(2);
            } else if ("三段式".equals(rnData.getTimElePriCate())) {
                generatorEntity.setTimeStg(3);
            } else {
                generatorEntity.setTimeStg(0);
            }

        }

        if (!StringUtils.isEmpty(rnData.getShuntVotCp())) {
            generatorEntity.setVoltage(RemsVoltageOption.getVoltageLevel(rnData.getShuntVotCp()));
        }

        if (!StringUtils.isEmpty(rnData.getShuntvotRpb())) {
            generatorEntity.setResponsibilityVoltage(
                    RemsVoltageOption.getVoltageLevel(rnData.getShuntvotRpb()));
        }
        if (!StringUtils.isEmpty(rnData.getContractSold())) {
            generatorEntity.setIsWholesale(!rnData.getContractSold().contentEquals("僅併聯不躉售"));
        }
        if (rnData.getDeviceData().size() > 1) {
            generatorEntity.setIsMultiUnit(true);
        } else {
            generatorEntity.setIsMultiUnit(false);
        }
        generatorEntity.setModifiedBy(userId);
        generatorEntityRepository.save(generatorEntity);
        /**
         * 處理機組更新
         */
        List<RNV2DeviceDataMeter> newMeters = new ArrayList();
        final var meterList =
                generatorService.findNoneSpecialMeterByGeneratorEntityId(generatorEntity.getId());

        GeneratorEntity finalGeneratorEntity = generatorEntity;

        var units = rnData.getDeviceData().stream()
                .map(a -> GeneratorEntityCombinedCapacity.builder()
                        .generatorEntityId(finalGeneratorEntity.getId()).genNo(a.getDeviceSeq())
                        .genCode(a.getDeviceCode()).capacity(BigDecimal.valueOf(a.getSoldKw()))
                        .combinedDate(a.getSoldStatusFstCdate()).licenseDate(a.getSoldStatusOdate())
                        .build())
                .toList();

        var dbUnits = generatorEntityCombinedCapacityRepository
                .findByGeneratorEntityId(generatorEntity.getId());
        dbUnits.forEach(du -> {
            var first = units.stream().filter(a -> a.getGenCode().equals(du.getGenCode())
                    && a.getGenNo().equals(du.getGenNo())).findFirst();
            if (first.isEmpty()) {
                // 如果沒有找到，代表該機組被廢棄，所以要壓上終止日期
                du.setTerminateDate(DateUtils.getTruncatedDate(new java.util.Date()));
            }
        });
        units.forEach(u -> {
            var dbUnit = dbUnits.stream().filter(
                            a -> a.getGenCode().equals(u.getGenCode()) && a.getGenNo().equals(u.getGenNo()))
                    .findFirst();
            if (dbUnit.isEmpty()) {
                dbUnits.add(u);
                // 電表同步作業統一處理，一次更新所有ratio
            } else {
                VoUtils.mergeObjectWithNullIgnore(u, dbUnit.get());
            }
        });
        generatorEntityCombinedCapacityRepository.saveAll(dbUnits);
        var totalCppacity = dbUnits.stream()
                .filter(a -> a.getCombinedDate() != null && a.getLicenseDate() != null)
                .map(GeneratorEntityCombinedCapacity::getCapacity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        var totalCombineCppacity = dbUnits.stream().filter(a -> a.getCombinedDate() != null)
                .map(GeneratorEntityCombinedCapacity::getCapacity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        generatorEntity.setCapacityApplied(totalCppacity);
        generatorEntity.setCapacityUndergo(totalCombineCppacity);
        generatorEntityRepository.save(generatorEntity);

        /**
         * 處理電表更新 統一整理所有裝置電表，如果表號一致，對其電號進行更新。
         */

        rnData.getDeviceData().forEach(deviceData -> {

            // 針對RA組別電表處理
            if (!StringUtils.isEmpty(deviceData.getMtrRa())) {
                newMeters.add(RNV2DeviceDataMeter.builder().groupType(deviceData.getMtrRa())
                        .meterNo(deviceData.getMtrRaSn()).ratio(deviceData.getMtrRaMult())
                        .from(deviceData.getSoldStatusFstCdate()).build());
            }
            // 針對RC組別電表進行處理
            if (!StringUtils.isEmpty(deviceData.getMtrRc())) {
                newMeters.add(RNV2DeviceDataMeter.builder().groupType(deviceData.getMtrRc())
                        .meterNo(deviceData.getMtrRcSn()).ratio(deviceData.getMtrRcMult())
                        .from(deviceData.getSoldStatusFstCdate()).build());
            }
        });

        List<RNV2DeviceDataMeter> distinctList = newMeters.stream()
                .collect(Collectors.toMap(RNV2DeviceDataMeter::getGroupType, // 依據 groupType 過濾
                        meter -> meter, // 直接存物件
                        (existing, replacement) -> existing // 保留第一個出現的
                )).values().stream().toList();
        distinctList.stream().distinct().forEach(meter -> {
            processGeneratorMeter(userId, meterList, finalGeneratorEntity.getId(),
                    meter.getGroupType(), meter.getMeterNo(), meter.getRatio(), meter.getFrom());
        });

        log.info("發電端: {} 更新完成 {}", nbsCustomerNo, generatorEntity);
    }

    private void processGeneratorMeter(Long userId, List<GeneratorEntityMeterVO> meterList,
                                       Long entityId, String group, String meterNo, String ratio, Date from) {

        try {
            var option = meterList.stream().filter(
                            genEntityMeterVO -> genEntityMeterVO.getGroupType().contentEquals(group))
                    .findFirst();
            if (option.isEmpty()) {
                var vo = GeneratorEntityMeterVO.builder().meterNo(meterNo).groupType(group).build();
                try {
                    vo.setRatio(Integer.parseInt(ratio));
                } catch (Throwable ex) {
                    log.error("電表倍率更新失敗 {} {} {}", meterNo, ratio, ex);
                }
                generatorService.addMeterOnlyWithSpecificDate(vo, entityId, userId, from);
            } else {
                /**
                 * 如果已經有相同組別的電表存在，檢查表號是否相同，並且更新表倍率
                 */


                var dbMeter = option.get();
                try {
                    // 如果表號相同，則更新電表倍率
                    if (dbMeter.getMeterNo().equals(meterNo)) {
                        dbMeter.setRatio(Integer.parseInt(ratio));
                        generatorService.updateMeterOnly(dbMeter, userId);
                    }
                } catch (Throwable ex) {
                    log.error("電表倍率更新失敗 {} {} {}", meterNo, ratio, ex);
                }
            }
        } catch (Throwable ex) {
            log.error("{} {}", group, meterNo, ex);
        }
    }

    int getFuelType(String type) {
        var fuelType =
                this.fuelTypeList.stream().filter(a -> a.getLabel().contains(type)).findFirst();

        if (fuelType.isPresent())
            return fuelType.get().getId();
        else
            // 調整能源分類，將其他設定為99
            return 99;
    }

    int getFuelForm(String form) {
        var fuelForm = fuelFormList.stream().filter(a -> a.getLabel().equals(form)).findFirst();
        if (fuelForm.isPresent())
            return fuelForm.get().getId();
        else
            return 8;
    }

    long getGeneratorUnitType(String type) {
        if (type.contentEquals("第一型")) {
            return 1;
        } else if (type.contentEquals("第二型")) {
            return 2;
        } else if (type.contentEquals("第三型")) {
            return 3;
        }
        return -1;
    }
}
