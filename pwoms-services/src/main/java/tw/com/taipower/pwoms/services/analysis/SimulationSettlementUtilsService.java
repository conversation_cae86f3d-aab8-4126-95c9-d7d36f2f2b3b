package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.SimulationSettlementCalculation;
import tw.com.taipower.data.repository.pwoms.SimulationSettlementPropertyRepository;
import tw.com.taipower.pwoms.services.enumclass.SettlementMethodEnum;
import tw.com.taipower.pwoms.services.settlement.SettlementCalculationInterface;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.settlement.CalculationRecordVo;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

import static tw.com.taipower.data.entity.pwoms.enums.SettlementPropertyEnum.SETTLEMENT_CALCULATION_STATUS;
import static tw.com.taipower.pwoms.services.constant.Constants.DATETIME_FORMAT_YYYMM_HHMM_WITH_DASH;
import static tw.com.taipower.pwoms.services.constant.Constants.DATETIME_FORMAT_YYYMM_WITH_DASH;

@Service
@Log4j2
public class SimulationSettlementUtilsService {

    @Autowired
    private SimulationSettlementPropertyRepository propertyRepository;

    public static final String SETTLEMENT_ERROR_HEADER = "*$#%^&@#%^&";


    public boolean setSettlementCalculation(SettlementMethodEnum methodEnum){
        try {
            propertyRepository.updateValueById(SETTLEMENT_CALCULATION_STATUS.name(), methodEnum.name());
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }


    public boolean cleanSettlementCalculation(){
        try {
            propertyRepository.updateValueById(SETTLEMENT_CALCULATION_STATUS.name(), null);
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public boolean sendToFront(Integer userId, Long settlementId, BigDecimal percentage, SettlementCalculationInterface caller){
//        calculationService.updateCalculationPercentageBySettlementId(settlementId, percentage);
        log.info("userId = {} , settlement = {}, percentage = {}", userId,  settlementId, percentage);
        CalculationRecordVo recordVo = CalculationRecordVo.builder()
                .settlementId(settlementId)
                .percentage(percentage.intValue()).build();

        return sendToCaller(userId, recordVo, caller);
    }

    public boolean sendToFront(Integer userId, SimulationSettlementCalculation sc, SettlementCalculationInterface caller){
        //set to front
        log.info("userId = {} , settlement = {}", userId, sc.getSettlementId());
        CalculationRecordVo recordVo = CalculationRecordVo.builder()
                .settlementId(sc.getSettlementId())
                .serviceDate(DateUtils.convertADDateToROCDate(sc.getServiceDate(), DATETIME_FORMAT_YYYMM_WITH_DASH))
                .executionProgress(sc.getExecutionLog())
                .executionStart(DateUtils.convertADDateToROCDate(sc.getExecutionStart(), DATETIME_FORMAT_YYYMM_HHMM_WITH_DASH))
                .percentage(sc.getExecutionPercentage().intValue())
                .build();

        return sendToCaller(userId, recordVo, caller);
    }

    private boolean sendToCaller(Integer userId, CalculationRecordVo recordVo, SettlementCalculationInterface caller){
        if(null != caller){
            caller.onMessage(userId, recordVo);
        }
        return true;
    }

    public boolean sendToFront(Integer userId, Long settlementId, Long datetime, String progressLog, BigDecimal percentage, SettlementCalculationInterface caller){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return sendToFront(userId, settlementId, simpleDateFormat.format(new Date(datetime)) + " " + progressLog, percentage, caller);
    }

    public boolean sendToFront(Integer userId, Long settlementId, String progressLog, BigDecimal percentage, SettlementCalculationInterface caller){
//        calculationService.updateCalculationLog(settlementId, progressLog, percentage);
        log.info("userId = {} , settlement = {}, processLog = {}, percentage = {}", userId, settlementId, progressLog, percentage);
        CalculationRecordVo recordVo = CalculationRecordVo.builder()
                .settlementId(settlementId)
                .executionProgress(progressLog)
                .percentage(percentage.intValue())
                .build();
        return sendToCaller(userId, recordVo, caller);
    }

    public boolean sendToFront(Integer userId, Long settlementId, int totalTask, int currentTask, SettlementCalculationInterface caller){
        int percentage = (int) ((currentTask / (double)totalTask) * 100);
        return sendToFront(userId, settlementId, new BigDecimal(Math.min(percentage, 100)), caller);
    }

    public boolean sendToFront(Integer userId, Long settlementId, boolean success, String progressLog, BigDecimal percentage, Date endDate, SettlementCalculationInterface caller){
//        calculationService.updateCalculationStatusAndLog(settlementId, success, progressLog, percentage);
        log.info("userId = {} , settlement = {}, success = {}, processLog = {}, percentage = {} executionEnd = {}", userId, settlementId, success, progressLog, percentage, endDate);
        CalculationRecordVo recordVo = CalculationRecordVo.builder()
                .settlementId(settlementId)
                .executionProgress(progressLog)
                .bResult(success)
                .executionEnd(DateUtils.convertADDateToROCDate(endDate, DATETIME_FORMAT_YYYMM_HHMM_WITH_DASH))
                .percentage(percentage.intValue())
                .build();
        return sendToCaller(userId, recordVo, caller);
    }

    public String translateExceptionToReadableSentence(SettlementMethodEnum methodEnum, String exceptionMsg){
        String error = exceptionMsg.trim();
        if(error.startsWith("JDBC exception")){
            return "JDBC例外";
        }else if(error.startsWith(SETTLEMENT_ERROR_HEADER)){
            return error.replace(SETTLEMENT_ERROR_HEADER, "");
        }else{
            return switch (methodEnum) {
                case SETTLEMENT_METHOD_CHECK_COMPUTABLE_CONTRACT -> "檢查失敗";
                case SETTLEMENT_METHOD_INITIAL_ALL_CALCULATION, SETTLEMENT_METHOD_INITIAL_TRIAL_CALCULATION ->
                        "初始化失敗";
                case SETTLEMENT_METHOD_ALL_CALCULATION, SETTLEMENT_METHOD_TRIAL_CALCULATION -> "結算失敗";
                case SETTLEMENT_METHOD_ACCEPT_TRIAL_CALCULATION -> "採用失敗";
                case SETTLEMENT_METHOD_FLEXIBLE_CALCULATION, SETTLEMENT_METHOD_FLEXIBLE_CALCULATION_CONVERT_CSV_TO_CHECK_LIST ->
                        "彈性分配失敗";
                default -> "失敗";
            };
        }
    }

}
