package tw.com.taipower.pwoms.services.report;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.ApplicationGenerator;
import tw.com.taipower.data.entity.pwoms.ApplicationLoad;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.data.vo.powms.IFuelCodeRate;
import tw.com.taipower.data.vo.powms.IMatchedRmEcChangeId;
import tw.com.taipower.pwoms.services.ami.AmiService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.report.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

import static tw.com.taipower.data.constant.Constants.*;

/**
 * 報表模組
 *
 * @class: ReportService
 * @author: ting
 * @version: 0.1.0
 * @since: 2024-06-02 13:56
 * @see:
 **/
@Log4j2
@Service
public class ReportService {

    @Autowired
    PwFuelRateRepository pwFuelRateRepository;

    @Autowired
    ApplicationMonthlySettlementRepository repository;

    @Autowired
    ApplicationMonthlyCapacityRecordRepository applicationMonthlyCapacityRecordRepository;

    @Autowired
    ApplicationMonthlyCapacitySettlementRepository applicationMonthlyCapacitySettlementRepository;

    @Autowired
    ApplicationLoadRepository applicationLoadRepository;

    @Autowired
    ApplicationMonthlyGeneratorRecordRepository applicationMonthlyGeneratorRecordRepository;

    @Autowired
    ApplicationTimelyGeneratorRecordRepository applicationTimelyGeneratorRecordRepository;

    @Autowired
    ApplicationTimelyGeneratorLoadRecordRepository applicationTimelyGeneratorLoadRecordRepository;

    @Autowired
    ApplicationMonthlyRematchGeneratorLoadRecordRepository applicationMonthlyRematchGeneratorLoadRecordRepository;

    @Autowired
    ApplicationMonthlyFlexibleGeneratorLoadRecordRepository applicationMonthlyFlexibleGeneratorLoadRecordRepository;

    @Autowired
    ApplicationGeneratorRepository applicationGeneratorRepository;

    @Autowired
    ApplicationRepository applicationRepository;

    @Autowired
    VoltageLevelRepository voltageLevelRepository;

    @Autowired
    GeneratorEntityMeterRepository generatorEntityMeterRepository;

    @Autowired
    AmiService amiService;

    String lowVoltageLevel = "低壓";
    int sqlCheckSum = 1800; // 因 SQL 限定 in 欄位最多 2000 個 因此調整成 1800
    String dateFormat = "yyyy-MM-dd";
    String dateFormatFull = "yyyy-MM-dd HH:mm:ss";
    String timeFormat = "HH:mm";
    String zeroPointZero = "0.0";
    BigDecimal zeroPointBigDecimal = new BigDecimal(zeroPointZero);
    BigDecimal zeroBigDecimal = new BigDecimal("0");
    String comma95Str = ",".repeat(95);
    int daily15Inv = 96;
    String[] day15Str={"00:00","00:15","00:30","00:45","01:00","01:15","01:30","01:45","02:00","02:15","02:30","02:45",
            "03:00","03:15","03:30","03:45","04:00","04:15","04:30","04:45","05:00","05:15","05:30","05:45","06:00","06:15",
            "06:30","06:45","07:00","07:15","07:30","07:45","08:00","08:15","08:30","08:45","09:00","09:15","09:30","09:45",
            "10:00","10:15","10:30","10:45","11:00","11:15","11:30","11:45","12:00","12:15","12:30","12:45","13:00","13:15",
            "13:30","13:45","14:00","14:15","14:30","14:45","15:00","15:15","15:30","15:45","16:00","16:15","16:30","16:45",
            "17:00","17:15","17:30","17:45","18:00","18:15","18:30","18:45","19:00","19:15","19:30","19:45","20:00","20:15",
            "20:30","20:45","21:00","21:15","21:30","21:45","22:00","22:15","22:30","22:45","23:00","23:15","23:30","23:45"};

    // 因應 s1 契約資訊不足, 從 pwoms 取得父契約後, 補上 "1-11205-04-001-00"
    Map<String, String> s1FatherContractMaps = Map.ofEntries(
            new AbstractMap.SimpleEntry<String, String>("1-10902-04-001", "1-10902-04-000-01"),
            new AbstractMap.SimpleEntry<String, String>("1-10902-10-001", "1-10902-10-000-01"),
            new AbstractMap.SimpleEntry<String, String>("1-11105-08-007", "1-10907-08-000-01"),
            new AbstractMap.SimpleEntry<String, String>("1-11106-12-003", "1-11106-12-003-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11110-20-002", "1-11110-20-002-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11110-20-003", "1-11110-20-003-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11110-20-004", "1-11110-20-004-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11110-20-005", "1-11110-20-005-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11112-21-002", "1-11112-21-002-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11204-08-004", "1-11204-08-004-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11206-08-003", "1-11206-08-003-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11206-09-004", "1-11206-09-004-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11206-20-004", "1-11206-20-004-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11208-08-003", "1-11208-08-003-00"),
            new AbstractMap.SimpleEntry<String, String>("1-11208-08-006", "1-11208-08-006-00"),
            new AbstractMap.SimpleEntry<String, String>("2-11112-07-001", "2-11112-07-001-00"),
            new AbstractMap.SimpleEntry<String, String>("2-11112-18-001", "2-11112-18-001-00"),
            new AbstractMap.SimpleEntry<String, String>("4-11208-04-002", "4-11208-04-002-00")
    );

    // #21 標檢局
    public ReportContractSettlesMapDateListVo getContractVersionSettlesToBSMIOut(BSMIInputInfoVo input) throws Exception {
        Date startDate = new GregorianCalendar(2022, Calendar.JANUARY, 1).getTime();
        Date endDate = getNextMonthFirstDate(input.getRunDate());
        Set<String> oriContractSet = input.getOriContractSet();
        Map<Integer, Set<String>> dateOriContractMap = input.getDateOriContractMap();
        List<Map<String, Object>> info = applicationMonthlyCapacityRecordRepository.sumMatchedKwCostsByIdsBillingDate(startDate, endDate);
        if (null == info || info.isEmpty()) {
            Long ss = repository.count();
            if (!ss.equals(0L)) {
                info = repository.sumMatchedKwCostsByIdsBillingDate(endDate);
                if (null == info || info.isEmpty()) return null;
            }
            else return null; // 表示無結帳資料
        }
        return getMonthSettlementInfoForBSMI(info, oriContractSet, dateOriContractMap);
    }

    // #2 會計室
    public List<ReportADSTKwhVo> getADSTKwh(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);

        List<Map<String, Object>> info = applicationMonthlyCapacityRecordRepository.sumKwhADSTByBillingDate(billDate);
        if (checkCurrentYearMonth(billDate)) {
            info = applicationMonthlyCapacitySettlementRepository.sumKwhADSTByBillingDate(billDate);
        }
        if (null == info || info.isEmpty()) return null;

        BigDecimal tKwh = zeroBigDecimal; // APPLICATION_GENERATOR_LOAD_TYPE.TYPE=1
        BigDecimal dKwh = zeroBigDecimal; // APPLICATION_GENERATOR_LOAD_TYPE.TYPE=2
        BigDecimal tdKwh = zeroBigDecimal;
        BigDecimal aKwh = zeroBigDecimal;
        for (Map<String, Object> map: info) {
            Integer powerType = (Integer) map.get("powerType");
            BigDecimal kwh = null == map.get("kwh")? zeroBigDecimal: (BigDecimal) map.get("kwh");
            if (powerType == 12) tdKwh = kwh;
            else if (powerType == 1) tKwh = kwh;
            else if (powerType == 2) dKwh = kwh;
            else aKwh = kwh; // powerType = 3
        }

        return computeBeforeKwhs(getBeforeMonthFirstDate(billDate), tKwh, dKwh, tdKwh, aKwh);
    }

    // #23 環保處
    public List<ReportFuelLabelAppTypeVoltClassVo> getFuelLabelPwDsVoltClassKwh(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);

        List<Map<String, Object>> info = applicationMonthlyCapacityRecordRepository.sumMatchedKwByVoltageClassFuelLabelApplicationType(billDate);
        if (checkCurrentYearMonth(billDate)) {
            info = applicationMonthlyCapacitySettlementRepository.sumMatchedKwByVoltageClassFuelLabelApplicationType(billDate);
        }
        if (null == info || info.isEmpty()) return new ArrayList<>();
        List<ReportFuelLabelAppTypeVoltClassVo> voList = new ArrayList<>();
        Map<String, Integer> voMap = new HashMap<>();
        Map<Integer, BigDecimal> kwhMap = new HashMap<>();
        int count = 0;
        for (Map<String, Object> map: info) {
            String fuel = map.get("FUEL_LABEL").toString().split("\\(")[0];
            String pwDs = map.get("PWDS").toString();
            if (pwDs.equals("Q")) {
                fuel = "台電"+fuel;
                pwDs = "轉";
            } else pwDs = pwDs.equals("DS")? "直": "轉";
            if (null != map.get("FUEL_FORM") && fuel.contains("風力")) {
                fuel = fuel + "(" + map.get("FUEL_FORM").toString() + ")";
            }

            String voltClass = map.get("VOLT_LEVEL_CLASS").toString().equals("lv") ? "低":"高";
            String matchedKw = null;
            BigDecimal kw = zeroBigDecimal;
            if (null != map.get("matchedKw")) {
                kw = (BigDecimal) map.get("matchedKw");
                matchedKw = bigDecimalStrShortZero(map.get("matchedKw").toString(), null);
            }
            String key = fuel+"~"+pwDs+"~"+voltClass;
            if (null == voMap || null == voMap.get(key)) {
                voList.add(ReportFuelLabelAppTypeVoltClassVo.builder().fuelLabel(fuel).pwDs(pwDs)
                        .voltClass(voltClass).matchedKw(matchedKw).build());
                voMap.put(key, count);
                kwhMap.put(count++, kw);
            } else {
                int cc = voMap.get(key);
                String dd = bigDecimalStrShortZero(kwhMap.get(cc).add(kw).toString(), null);
                ReportFuelLabelAppTypeVoltClassVo vo = voList.get(cc);
                vo.setMatchedKw(dd);
            }
        }
        return voList;
    }

    /** #25 RFP3.4 系統管理報表 每月發用電端統計報表
     *
     * @param runDate
     * @return
     * @throws Exception
     */
    public ReportAppKwGenLoadVo getGenLoadElecNoAppKwhInfo(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> dd = applicationMonthlyCapacityRecordRepository.findSettleServiceDates(billDate);
        if (checkCurrentYearMonth(billDate))
            dd = applicationMonthlyCapacitySettlementRepository.findSettleServiceDates(billDate);
        if (null == dd || dd.isEmpty()) return null;
        List<Date> serviceDList = new ArrayList<>();
        for (int i =0; i< dd.size();i++)
            serviceDList.add((Date) dd.get(i).get("serviceDate"));

        List<ReportGenElecNoAppKwVo> genSide = new ArrayList<>();
        List<ReportLoadElecNoAppKwVo> loadSide = new ArrayList<>();

        for (int j =0; j< serviceDList.size(); j++) {
            Date startDate = serviceDList.get(j);
            Date endDate = getNextMonthFirstDate(startDate);

            String[] yrMo = yearMonthStr(startDate).split("-");
            int year = Integer.parseInt(yrMo[0]);
            int month = Integer.parseInt(yrMo[1]);

            int channel = AMI_GEN_CHANNEL; // 發電端 3
            List<String> elecNos = amiService.getAmiSettle15RecordElecNos(channel, startDate, endDate);
            int size = elecNos.size();
            int subListNum = (size + sqlCheckSum - 1) / sqlCheckSum;

            for (int i =0; i < subListNum; i++) {
                List<String> genElecNos = elecNos.subList(i * sqlCheckSum, ((i + 1) * sqlCheckSum > size ? size : sqlCheckSum * (i + 1)));
                List<ReportGenElecNoAppKwVo> genSideS = collectGenElecNoAppAmiKwh(genElecNos, startDate, endDate, year, month);
                genSide.addAll(genSideS);
            }

            channel = AMI_LOAD_CHANNEL; // 用電端 1 -> 0
            elecNos = amiService.getAmiSettle15RecordElecNos(channel, startDate, endDate);
            size = elecNos.size();
            subListNum = (size + sqlCheckSum - 1) / sqlCheckSum;
            for (int i =0; i < subListNum; i++) {
                List<String> loadElecNos = elecNos.subList(i * sqlCheckSum, ((i + 1) * sqlCheckSum > size ? size : sqlCheckSum * (i + 1)));
                List<ReportLoadElecNoAppKwVo> loadSideS = collectLoadElecNoAppAmiKwh(loadElecNos, startDate, endDate, year, month);
                loadSide.addAll(loadSideS);
            }
        }

        return ReportAppKwGenLoadVo.builder().genSide(genSide).loadSide(loadSide).build();
    }

    // RFP2.7 每月案件統計報表 目前狀態 與 APPLICATION資料庫欄位對應 APPLICATION.STATUS[計畫書狀態]
    //      APPLICATION.CONTRACT_STATUS[契約狀態]
    // 審查中 - APPLICATION.STATUS = 2, 3
    // 已函覆 - APPLICATION.STATUS = 4
    // 申請中 - APPLICATION.CONTRACT_STATUS = 1, 2, 3
    // 已簽約 - APPLICATION.CONTRACT_STATUS = 4
    // 運轉中 - APPLICATION.CONTRACT_STATUS  = 5 or 6
    //        [注意 APPLICATION.CONTRACT_STATUS = 6 APPLICATION.CONTRACTED_END is null 或者 APPLICATION.CONTRACTED_END >= 輸入月底
    // 已終止 - APPLICATION.CONTRACTED_END < 輸入月底
    public List<ReportAppStatusGenLoadVo> getAppStatusGenLoadElecNoCounts(Date runDate) throws Exception {
        Date startDate = regularDate(runDate);
        Date endDate = getNextMonthFirstDate(runDate);
        List<Map<String, Object>> infos = applicationRepository.findApplicationGeneratorLoadElecNos(startDate, endDate);
        if (null == infos || infos.isEmpty()) return new ArrayList<>();
        return collectAppStatusGenLoadElecNo(infos, runDate);
    }

    // 企劃室 電能轉直供資訊報表 目前狀態 與 APPLICATION資料庫欄位對應 APPLICATION.STATUS[計畫書狀態]
    //      APPLICATION.CONTRACT_STATUS[契約狀態]
    //  刪除  - APPLICATION.STATUS = -1
    // 審查中 - APPLICATION.STATUS = 1, 2, 3
    // 已函覆 - APPLICATION.STATUS = 4
    // 申請中 - APPLICATION.CONTRACT_STATUS = 1, 2, 3
    // 已簽約 - APPLICATION.CONTRACT_STATUS = 4
    // 運轉中 - APPLICATION.CONTRACT_STATUS  = 5 or 6
    // 已終止 - APPLICATION.CONTRACTED_END < 輸入月底
    /** #22 企劃室 電能轉直供資訊報表
     * 1.轉直供度數(共6欄)應進位至整數再統計加總。
     * 2.用戶資料介接尚不完全。 目前缺 用電端 總契約度數
     * @param runDate
     * @return
     * @throws Exception
     */
    public List<ReportPowerGenLoadVo> getMonthYearGenLoadPowerInfo(Date runDate) throws Exception {
        Date billD = regularDate(runDate);
        List<Map<String, Object>> res = applicationMonthlyCapacityRecordRepository.sumMatchedKwByApplicationId(billD);
        if (checkCurrentYearMonth(billD)) {
            res = applicationMonthlyCapacitySettlementRepository.sumMatchedKwByApplicationId(billD);
        }
        if (null == res || res.isEmpty()) return new ArrayList<>();

        Date sDate = (Date) res.get(0).get("serviceDate");
        Date startDate = getYearFirstDay(sDate);
        Date eDate = (Date) res.get(res.size()-1).get("serviceDate");
        Date endDate = getNextYearFirstDay(eDate);
        Map<String, String> dateMap = new HashMap<>();
        String theDate = regularDateStr(runDate);
        Map<Long, MatchedKwVo> appIdMatchedKwMap = new HashMap<>();
        for (Map<String, Object> m:res) {
            Long appId = (Long)m.get("ID");
            String serviceDate = m.get("serviceDate").toString() +"~"+m.get("ID").toString();
            String billDate = m.get("billDate").toString();
            BigDecimal matchedKw = null == m.get("matchedKw")?zeroBigDecimal:(BigDecimal) m.get("matchedKw");
            if (dateMap.isEmpty() || null == dateMap.get(serviceDate)) {
                dateMap.put(serviceDate, billDate);
            }
            if (null != dateMap.get(serviceDate) && dateMap.get(serviceDate).equals(billDate)) {
                if (appIdMatchedKwMap.isEmpty() || null == appIdMatchedKwMap.get(appId))
                    appIdMatchedKwMap.put(appId, MatchedKwVo.builder().yearKw(matchedKw).build());
                else {
                    MatchedKwVo mh = appIdMatchedKwMap.get(appId);
                    BigDecimal cc = mh.getYearKw().add(matchedKw);
                    mh.setYearKw(cc);
                    appIdMatchedKwMap.put(appId, mh);
                }
                if (serviceDate.split("~")[0].equals(theDate)) {
                    MatchedKwVo mh = appIdMatchedKwMap.get(appId);
                    mh.setMonthKw(matchedKw);
                    appIdMatchedKwMap.put(appId, mh);
                }
            }
        }
        return collectYearMonthApplicationInfo(appIdMatchedKwMap, eDate, startDate, endDate);
    }

    /** #13 外部 調度處 每月發轉餘總計 月份 由 小 -> 大
     * 度數應進位至整數再統計加總。
     * @param runDate
     * @return
     * @throws Exception
     */
    public List<ReportGenTransReYearVo> getYearPwDsPowerInfo(Date runDate) throws Exception {
        Date billD = regularDate(runDate);
        List<Map<String, Object>> infos = applicationMonthlyCapacityRecordRepository.sumMatchedKmByEachApplicationGeneratorId(billD);
        if (checkCurrentYearMonth(billD)) {
            infos = applicationMonthlyCapacitySettlementRepository.sumMatchedKmByEachApplicationGeneratorId(billD);
        }
        if (null == infos || infos.isEmpty()) return new ArrayList<>();

        Set<String> monthSet = new HashSet<>();
        Map<String, String> monthMap = new HashMap<>();
        int count = 0;
        List<ReportGenTransReYearVo> voList = new ArrayList<>();
        for (Map<String, Object> map:infos) {
            String serviceDate = map.get("serviceDate").toString();
            String[] ss = serviceDate.split("-");
            int yy = Integer.parseInt(ss[0]);
            String billDate = map.get("billDate").toString();
            String pwDs = map.get("PWDS").toString();
            BigDecimal rawMatchKw = (BigDecimal)map.get("matchedKw");
            String matchedKw = null == map.get("matchedKw") ? "" : bigDecimalShortZero(rawMatchKw, false).toPlainString();
            if (monthSet.add(serviceDate)) {
                voList.add(ReportGenTransReYearVo.builder().serviceYear(yy).serviceMonth(Integer.parseInt(ss[1]))
                        .pwPower(matchedKw).pwAdjust("0").dsPower("0").dsAdjust("0").totalPwDs("0").build());
                monthMap.put(serviceDate, billDate+"~ ~"+count);
                count ++;
            } else if (monthSet.contains(serviceDate)) {
                String[] ms = monthMap.get(serviceDate).split("~");
                String bD = ms[0];
                String bD2 = ms[1];
                if (bD2.equals(" ")) {
                    int cc = Integer.parseInt(ms[2]);
                    ReportGenTransReYearVo vo = voList.get(cc);
                    if (bD.equals(billDate) && pwDs.equals("DS")) {
                        String pwPower = vo.getPwPower();
                        BigDecimal total = new BigDecimal(pwPower).add(rawMatchKw);
                        vo.setDsPower(matchedKw);
                        vo.setTotalPwDs(bigDecimalShortZero(total, false).toPlainString());
                    } else if (!bD.equals(billDate)) {
                        if (pwDs.equals("PW")) {
                            String pwPower = vo.getPwPower();
                            BigDecimal pwAdjust = new BigDecimal(pwPower).subtract(rawMatchKw);
                            vo.setPwAdjust(bigDecimalShortZero(pwAdjust, false).toPlainString());
                        } else { // DS
                            String dsPower = vo.getDsPower();
                            BigDecimal dsAdjust = new BigDecimal(dsPower).subtract(rawMatchKw);
                            vo.setDsAdjust(bigDecimalShortZero(dsAdjust, false).toPlainString());
                            monthMap.put(serviceDate, billDate+"~"+ serviceDate +"~"+cc);
                        }
                    }
                }
            }
        }
        return voList;
    }

    /** #14 調度處 發轉餘報表 - 統計頁籤- 餘電總和(一階餘電減去二階轉供) 欄位 = 再媒合的餘電 使用
     * APPLICATION_MONTHLY_REMATCH_GENERATOR_RECORD.UNMATCHED_RM 若契約發電端和關聯契約發電端皆為零，更新可供應電量=Capacity*PMI*0.01,
     * 因此可能造成 這個 餘電總和 > 發電總和 現象
     * 1.轉供/直供總和應進位至整數。
     * 2.計費度數應進位至整數。
     * 3.調整項亦為整數。
     * @param runDate
     * @return java.util.List
     * @throws Exception
     */
    public List<ReportGen15TranReSummaryVo> getAmi15PowerContractNoSummary(Date runDate) throws Exception {
        Date billD = regularDate(runDate);
        List<Map<String, Object>> infos = applicationMonthlyCapacityRecordRepository.sumMatchedKmGeneratorEndInfo(billD);
        boolean current = checkCurrentYearMonth(billD);
        if (current) {
            infos = applicationMonthlyCapacitySettlementRepository.sumMatchedKmGeneratorEndInfo(billD);
        } else current = false;
        if (null == infos || infos.isEmpty()) return new ArrayList<>();
        List<ReportGen15TranReSummaryVo> voList = new ArrayList<>();
        Map<Long, Integer> apGenIdCount = new HashMap<>();
        Map<String, Integer> elecNoCount = new HashMap<>();
        Set<String> elecNoSet = new HashSet<>();
        Map<String, Object> m = infos.get(0);
        Date startDate = (Date) m.get("serviceStart");
        Date endDate = (Date) m.get("serviceEndNext");
        String keepBillDate = m.get("billDate").toString();
        infos.remove(0);
        int count = 0;
        int len = infos.size();
        for (Map<String, Object> map: infos) {
            String billDate = map.get("billDate").toString();
            Long apGenId = (long)map.get("applicationGeneratorId");
            String matchedKw = null == map.get("matchedKw")? "" :bigDecimalShortZero((BigDecimal) map.get("matchedKw"), false).toPlainString();
            if (billDate.compareTo(keepBillDate) == 0) {
                String pwDsv = getPwDs(map, "PWDS");
                String pwDs = pwDsv.isEmpty() ? "" : pwDsv.equals("PW") ? "轉供": "直供";
                String info = map.get("PWDS_CONTRACT_TYPE").toString();
                String elecNo = map.get("GEN_ELEC_NO").toString();
                String genName = map.get("GEN_NAME").toString();
                if (elecNoSet.add(elecNo)) {
                    voList.add(ReportGen15TranReSummaryVo.builder().genName(genName).genElecNo(elecNo).matchedKw(matchedKw)
                            .unmatchedRm("0").adjustValue("0").pwDs(pwDs).pwdsContractType(info).applicationGeneratorId(apGenId)
                            .gEntityId(map.get("gEntityId").toString()).build());
                    apGenIdCount.put(apGenId, count);
                    elecNoCount.put(elecNo, count++);
                } else {
                    ReportGen15TranReSummaryVo vo = voList.get(elecNoCount.get(elecNo));
                    BigDecimal addMatch = new BigDecimal(vo.getMatchedKw()).add(new BigDecimal(elecNo));
                    vo.setMatchedKw(bigDecimalShortZero(addMatch, false).toPlainString());
                    apGenIdCount.put(apGenId, count);
                }
            } else if (count < len && !(infos.get(count).get("billDate").toString().equals(billDate))) {
                if (null != matchedKw && !matchedKw.isEmpty()) {
                    ReportGen15TranReSummaryVo vo = voList.get(apGenIdCount.get(apGenId));
                    String dbMaatchedKw = vo.getMatchedKw();
                    if (null != dbMaatchedKw && !dbMaatchedKw.isEmpty()) {
                        vo.setAdjustValue(bigDecimalShortZero(new BigDecimal(dbMaatchedKw).subtract(new BigDecimal(matchedKw)), false).toPlainString());
                    }
                }
            }
        }
        return collectAmi15PowerSummary(elecNoCount, apGenIdCount, voList, startDate, endDate, billD, current);
    }

    // # 14 發轉餘 ami發電量
    public List<ReportGen15TranReVo> getAmi15PowerContractNo(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> infos = applicationMonthlyCapacityRecordRepository.find15MinutesGeneratorSettleInfoByServiceDate(billDate);
        if (checkCurrentYearMonth(billDate))
            infos = applicationMonthlyCapacitySettlementRepository.find15MinutesGeneratorSettleInfoByServiceDate(billDate);
        if (null == infos || infos.isEmpty()) return new ArrayList<>();
        Set<Date> serviceDates = new HashSet<>();
        List<Date> serviceDateList = new ArrayList<>();
        Map<Date, List<String>> serviceElecMap = new HashMap<>();
        Map<String, String> elecNoContractPwDsMap = new HashMap<>();
        for (Map<String, Object> map: infos) {
            Date serviceDate = (Date) map.get("serviceDate");
            if (serviceDates.add(serviceDate)) {
                serviceDateList.add(serviceDate);
                serviceElecMap.put(serviceDate, new ArrayList<>());
            }
            List<String> elec = serviceElecMap.get(serviceDate);
            String elecNo = map.get("GEN_ELEC_NO").toString();
            elec.add(elecNo);
            serviceElecMap.put(serviceDate, elec);
            String serviceId = map.get("SERVICE_ID").toString();
            String pwDs = getPwDs(map, "appType");
            elecNoContractPwDsMap.put(elecNo, serviceId+"~"+pwDs+"~"+map.get("applicationGeneratorId"));
        }
        return collectAmiSettle15RecordDateSum(elecNoContractPwDsMap, serviceDateList, serviceElecMap);
    }

    // #19 調度處 15分鐘發用電量 - ami15 發用電量
    public List<ReportGenLoad15powerVo> getAmi15GeneratorLoadEndPowers(Date runDate, boolean genLoad) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> dd = applicationMonthlyCapacityRecordRepository.findSettleServiceDates(billDate);
        if (checkCurrentYearMonth(billDate))
            dd = applicationMonthlyCapacitySettlementRepository.findSettleServiceDates(billDate);
        if (null == dd || dd.isEmpty()) return new ArrayList<>();
        List<Date> serviceDList = new ArrayList<>();
        for (int i =0; i< dd.size();i++)
            serviceDList.add((Date) dd.get(i).get("serviceDate"));
        Date startDate = serviceDList.get(0);
        Date endDate = getNextMonthFirstDate(serviceDList.get(dd.size()-1));
        int channel = AMI_GEN_CHANNEL; // 發電端 3 - > 1
        List<String> elecNos = new ArrayList<>();
        int inv = 1000;
        if (genLoad) {
            List<String> elecNo = new ArrayList<>();
            for (int i =0 ;i < dd.size(); i++) {
                Date start = (Date) dd.get(i).get("serviceDate");
                Date end = getNextMonthFirstDate(start);
                List<String> en = amiService.getAmiSettle15RecordElecNos(channel, start, end);
                elecNo.addAll(en);
            }
            Set<String> elecNosSet = new HashSet<>(elecNo);
            elecNos = new ArrayList<>(elecNosSet);
        } else {
            channel = AMI_LOAD_CHANNEL; // 用電端 1 -> 0
            List<String> elecNo = new ArrayList<>();
            for (int i =0;i<dd.size();i++) {
                Date start = (Date) dd.get(i).get("serviceDate");
                Date end = getNextMonthFirstDate(start);
                List<String> en = amiService.getAmiSettle15RecordElecNos(channel, start, end);
                elecNo.addAll(en);
            }
            Set<String> elecNosSet = new HashSet<>(elecNo);
            elecNos = new ArrayList<>(elecNosSet);
        }
        if (null == elecNos || elecNos.isEmpty()) { return new ArrayList<>(); }
        List<String> avaElecNos = new ArrayList<>();
        if (AMI_LOAD_CHANNEL == channel) {
            for (int i =0 ;i < elecNos.size(); i+=inv) {
                int end = i+inv > elecNos.size()? elecNos.size(): i+inv;
                List<String> cc = applicationRepository.findLoadEntityNbsCustomerNumbers(elecNos.subList(i, end), startDate, endDate);
                avaElecNos.addAll(cc);
            }
        } else {
            for (int i =0 ;i < elecNos.size(); i+=inv) {
                int end = i+inv > elecNos.size()? elecNos.size(): i+inv;
                List<String> cc = applicationRepository.findGeneratorEntityNbsCustomerNumbers(elecNos.subList(i, end), startDate, endDate);
                avaElecNos.addAll(cc);
            }
        }

        if (null == avaElecNos || avaElecNos.size() == 0) { return new ArrayList<>(); }

        return collectAmi15PowerByMonthDate(avaElecNos, channel, serviceDList);
    }

    /** #18 外部 調度處高雄報表
     * 可轉供容量比例、換算後比例應顯示為%值
     * @param runDate
     * @return
     * @throws Exception
     */
    public List<ReportGenCapacityVo> getGenDeviceCapacities(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> dd = applicationMonthlyCapacityRecordRepository.findSettleServiceDates(billDate);
        if (checkCurrentYearMonth(billDate))
            dd = applicationMonthlyCapacitySettlementRepository.findSettleServiceDates(billDate);
        if (null == dd || dd.isEmpty()) return new ArrayList<>();
        Date startDate = (Date) dd.get(0).get("serviceDate");
        Date endDate = getNextMonthFirstDate((Date)dd.get(dd.size()-1).get("serviceDate"));

        List<Map<String, Object>> info = applicationGeneratorRepository.findGeneratorElecNoContractCapacity(startDate, endDate);
        if (null == info || info.isEmpty()) return new ArrayList<>();

        Map<String, Integer> elecNoPmiMap = new HashMap<>();
        List<ReportGenCapacityVo> voList = new ArrayList<>();
        int count = 0;
        for (Map<String, Object> map : info) {
            String serviceId = (String) map.get("SERVICE_ID");
            String genElecNo = (String) map.get("GEN_ELEC_NO");
            BigDecimal pmi = null == map.get("PMI")? zeroPointBigDecimal : (BigDecimal) map.get("PMI");
            BigDecimal sPmi = voList.isEmpty() || null == elecNoPmiMap.get(genElecNo)? zeroPointBigDecimal: new BigDecimal(voList.get(elecNoPmiMap.get(genElecNo)).getPmi());
            if (null != elecNoPmiMap.get(genElecNo) && !pmi.equals(zeroPointBigDecimal) && !sPmi.equals(zeroPointBigDecimal)
                    && (pmi.toString().equals("100") || sPmi.toString().equals("100"))) {}
            else if (null == elecNoPmiMap.get(genElecNo)) {
                elecNoPmiMap.put(genElecNo, count++);
                String pwDs = getPwDs(map, "PWDS");
                BigDecimal licenseCap = (BigDecimal) map.get("licenseCap");
                BigDecimal totalCap = (BigDecimal) map.get("totalCap");
                BigDecimal pwRate = totalCap.compareTo(zeroBigDecimal) > 0? (licenseCap.divide(totalCap, 4)).multiply(new BigDecimal("100")): zeroBigDecimal;
                BigDecimal pwRate2 = totalCap.compareTo(zeroBigDecimal) > 0? licenseCap.divide(totalCap, 4): zeroBigDecimal;
                BigDecimal computeRate = pmi.multiply(pwRate2);
                String elecSubstationCode = null == map.get("feederCode") ? "" : map.get("feederCode").toString().length()>=2? map.get("feederCode").toString().substring(0, 2): map.get("feederCode").toString();
                voList.add(ReportGenCapacityVo.builder().pwDs(pwDs).genName((String) map.get("GEN_NAME")).genElecNo(genElecNo)
                        .deviceAddress(map.get("DEVICE_ADDRESS").toString().replace(",", "、")).pwFuelType((String) map.get("PW_FUEL"))
                        .energyType((String) map.get("FUEL_LABEL")).elecSubstationCode(elecSubstationCode)
                        .pmi(bigDecimalShortZero(pmi, false).toPlainString())
                        .totalCap(bigDecimalShortZero(totalCap, false).toPlainString())
                        .licenseCap(bigDecimalShortZero(licenseCap, false).toPlainString())
                        .pwRate(bigDecimalShortZero(pwRate, false).toPlainString())
                        .computeRate(bigDecimalShortZero(computeRate, false).toPlainString()).gId((Long)map.get("gId"))
                        .apId((Long)map.get("apId"))
                        .serviceId(serviceId).build());
            } else {
                ReportGenCapacityVo vo = voList.get(elecNoPmiMap.get(genElecNo));
                BigDecimal dbPmi = new BigDecimal(vo.getPmi());
                BigDecimal nPmi = dbPmi.add(pmi);
                BigDecimal computeRate = new BigDecimal(vo.getPwRate()).multiply(nPmi).divide(new BigDecimal("100"), 4);
                vo.setPmi(bigDecimalShortZero(nPmi, false).toPlainString());
                vo.setComputeRate(bigDecimalShortZero(computeRate, true).toPlainString());
            }
        }
        voList.add(ReportGenCapacityVo.builder().build());
        return voList;
    }

    /** #17 外部 調度處 線損計算
     * 度數應進位至整數再統計加總
     * @param runDate
     * @param genLoad
     * @return
     * @throws Exception
     */
    public List<ReportLocFuelVoltVo> getTpcLocationFuelVoltKwh(Date runDate, boolean genLoad) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> info = new ArrayList<>();
        if (genLoad) { // genLoad = true 取得發電端資料
            info = applicationMonthlyCapacityRecordRepository.findGeneratorFuelTypeTpcCompanyVoltLevel(billDate);
            if (checkCurrentYearMonth(billDate))
                info = applicationMonthlyCapacitySettlementRepository.findGeneratorFuelTypeTpcCompanyVoltLevel(billDate);
        } else {
            info = applicationMonthlyCapacityRecordRepository.findLoadFuelTypeTpcCompanyVoltLevel(billDate);
            if (checkCurrentYearMonth(billDate))
                info = applicationMonthlyCapacitySettlementRepository.findLoadFuelTypeTpcCompanyVoltLevel(billDate);
        }
        if (null == info || info.isEmpty()) {return null;}
        int year = getYear(runDate);
        int month = getMonth(runDate);
        List<ReportLocFuelVoltVo> voList = new ArrayList<>();
        Set<String> keepCodes = new HashSet<>();
        BigDecimal keepAKwh = zeroBigDecimal;
        BigDecimal total = zeroBigDecimal;
        ReportLocFuelVoltVo vo = null;
        for (Map<String, Object> map : info) {
            String tpcCode = null == map.get("TPC_CODE")? "":map.get("TPC_CODE").toString();
            BigDecimal aKwh = null == map.get("A_KWH")? zeroBigDecimal: (BigDecimal) map.get("A_KWH");
            String fuelLevel = null == map.get("FUEL_LABEL")? "":map.get("FUEL_LABEL").toString();
            String voltLevel = null == map.get("EQUIP_VOLT_LEVEL")?"":map.get("EQUIP_VOLT_LEVEL").toString();
            String key = tpcCode + "~" + fuelLevel + "~" + voltLevel;
            if (voltLevel.equals(UNSURE_VOLTAGE_LEVEL)) {}
            else if (keepCodes.isEmpty() && keepCodes.add(key)) {
                keepAKwh = aKwh;
                vo = ReportLocFuelVoltVo.builder().billYear(Integer.toString(year)).billMonth(month).tpcCode(tpcCode)
                        .fuelLabel(fuelLevel).voltLevel(voltLevel).kwh("0").build();
            } else if (keepCodes.add(key)) {
                keepAKwh = bigDecimalShortZero(keepAKwh, null);
                vo.setKwh(keepAKwh.compareTo(zeroBigDecimal) > 0 ? keepAKwh.toPlainString(): "0");
                voList.add(vo);
                total = total.add(keepAKwh);
                keepAKwh = aKwh;
                vo = ReportLocFuelVoltVo.builder().billYear(Integer.toString(year)).billMonth(month).tpcCode(tpcCode)
                        .fuelLabel(fuelLevel).voltLevel(voltLevel).kwh("0").build();
            } else {
                keepAKwh = keepAKwh.add(aKwh);
            }
        }
        if (null != vo) {
            keepAKwh = bigDecimalShortZero(keepAKwh, null);
            vo.setKwh(keepAKwh.compareTo(zeroBigDecimal) > 0 ? keepAKwh.toPlainString(): keepAKwh.equals(zeroBigDecimal)? "": "0");
            voList.add(vo);
            //voList.add(ReportLocFuelVoltVo.builder().build());
            total = total.add(keepAKwh);
            voList.add(ReportLocFuelVoltVo.builder().billYear("總計").kwh(bigDecimalShortZero(total, null).toPlainString()).build());
            return voList;
        }
        return null;
    }

    // #16 調度處 發電月報 已契約編號為主 全列出, 不考慮 電號重複狀況
    public List<ReportGenComVo> getGeneratorCompanies(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> dd = applicationMonthlyCapacityRecordRepository.findSettleServiceDates(billDate);
        if (checkCurrentYearMonth(billDate))
            dd = applicationMonthlyCapacitySettlementRepository.findSettleServiceDates(billDate);
        if (null == dd || dd.isEmpty()) return new ArrayList<>();
        Date serviceStart = (Date) dd.get(0).get("serviceDate");
        Date serviceEnd = getNextMonthFirstDate((Date)dd.get(dd.size()-1).get("serviceDate"));

        List<Map<String, Object>> info = applicationRepository
                .findGeneratorContractInfo(serviceStart, serviceEnd);
        if (null == info || info.size() == 0) { return new ArrayList<>(); }
        List<ReportGenComVo> voList = new ArrayList<>();
        for (Map<String, Object> map : info) {
            String serviceId = null == map.get("SERVICE_ID")? "" : map.get("SERVICE_ID").toString();
            Long generatorEntityId = null == map.get("generatorEntityId")? null : Long.valueOf(map.get("generatorEntityId").toString());
            String GEN_ELEC_NO = null == map.get("GEN_ELEC_NO")?"":map.get("GEN_ELEC_NO").toString();
            voList.add(ReportGenComVo.builder().serviceId(serviceId)
                    .genElecNo(GEN_ELEC_NO)
                    .genName(null == map.get("GEN_NAME")? "":map.get("GEN_NAME").toString())
                    .deviceAddress(null == map.get("DEVICE_ADDRESS")?"":map.get("DEVICE_ADDRESS").toString())
                    .voltLevel(null == map.get("EQUIP_VOLT_LEVEL")?"":map.get("EQUIP_VOLT_LEVEL").toString())
                    .deviceCap(null == map.get("DEVICE_CAPACITY")?"":map.get("DEVICE_CAPACITY").toString())
                    .energyType(null == map.get("ENERGY_TYPE")?"":map.get("ENERGY_TYPE").toString())
                    .contractStart(null == map.get("CONTRACT_EFFE_DATE")?"":map.get("CONTRACT_EFFE_DATE").toString())
                    .contractEnd(null == map.get("TERMINATE_DATE")?"":map.get("TERMINATE_DATE").toString())
                    .generatorEntityId(generatorEntityId).build());
        }
        return voList;
    }

    /** #15 外部 調度處 每月轉直供服務各類度數
     * 度數應進位至整數再統計加總
     * @param runDate
     * @return
     * @throws Exception
     */
    public List<ReportInfoRateVo> getMonthExpRate(Date runDate) throws Exception {
        Date billD = regularDate(runDate);
        List<Map<String, Object>> info = applicationMonthlyCapacityRecordRepository.sumExpsByServiceDateBillDate(billD);
        if (checkCurrentYearMonth(billD))
            info = applicationMonthlyCapacitySettlementRepository.sumExpsByServiceDateBillDate(billD);
        if (null == info || info.isEmpty()) return new ArrayList<>();

        Map<String, BigDecimal> typeMatchMap = new HashMap<>();
        for (Map<String, Object> map : info) {
            String type = null == map.get("powerType")?"":map.get("powerType").toString();
            String mm = null == type || type.isEmpty()? "": type.equals("2") || type.equals("12")? "D": type.equals("1") ? "T": type.equals("3") ? "A": type.equals("Q")? "Q": "";
            BigDecimal kw = null == map.get("matchedKw")? zeroBigDecimal : (BigDecimal) map.get("matchedKw");
            if ((typeMatchMap.isEmpty() || null == typeMatchMap.get(mm)) && null != kw && !mm.isEmpty()) {
                if (type.equals("1")) typeMatchMap.put("T", kw);
                else if (type.equals("2")) typeMatchMap.put("D", kw);
                else if (type.equals("3")) {
                    typeMatchMap.put("A", kw);
                    typeMatchMap.put("S", kw);
                } else if (type.equals("Q")) typeMatchMap.put("Q", kw);
                else { //type.equals("12")
                    if (null == typeMatchMap.get("T")) typeMatchMap.put("T", kw);
                    else typeMatchMap.put("T", typeMatchMap.get("T").add(kw));
                    if (null == typeMatchMap.get("D")) typeMatchMap.put("D", kw);
                    else typeMatchMap.put("D", typeMatchMap.get("D").add(kw));
                }
            } else if (null != kw && !mm.isEmpty()) {
                if (type.equals("1")) typeMatchMap.put("T", typeMatchMap.get("T").add(kw));
                else if (type.equals("2")) typeMatchMap.put("D", typeMatchMap.get("D").add(kw));
                else if (type.equals("12")) {
                    if (null == typeMatchMap.get("T")) typeMatchMap.put("T", kw);
                    else typeMatchMap.put("T", typeMatchMap.get("T").add(kw));
                    if (null == typeMatchMap.get("D")) typeMatchMap.put("D", kw);
                    else typeMatchMap.put("D", typeMatchMap.get("D").add(kw));
                } else if (type.equals("3")) {
                    typeMatchMap.put("A", typeMatchMap.get("A").add(kw));
                    typeMatchMap.put("S", typeMatchMap.get("S").add(kw));
                } else typeMatchMap.put("Q", typeMatchMap.get("Q").add(kw)); // type.equals("Q")
            }
        }
        String[] rateInfo = {"輔助服務費", "配電進出費", "電力調度費", "輸電進出費", "自建綠電轉供費"}; // A,D,S,T,Q
        List<ReportInfoRateVo> vo = new ArrayList<>();
        if (null != typeMatchMap.get("A")) {
            BigDecimal kk = bigDecimalShortZero(typeMatchMap.get("A"), null);
            vo.add(ReportInfoRateVo.builder().info(rateInfo[0]).expRate(kk.toPlainString()).build());
        } else vo.add(ReportInfoRateVo.builder().info(rateInfo[0]).build());
        if (null != typeMatchMap.get("D")) {
            BigDecimal kk = bigDecimalShortZero(typeMatchMap.get("D"), null);
            vo.add(ReportInfoRateVo.builder().info(rateInfo[1]).expRate(kk.toPlainString()).build());
        } else vo.add(ReportInfoRateVo.builder().info(rateInfo[1]).build());
        if (null != typeMatchMap.get("S")) {
            BigDecimal kk = bigDecimalShortZero(typeMatchMap.get("S"), null);
            vo.add(ReportInfoRateVo.builder().info(rateInfo[2]).expRate(kk.toPlainString()).build());
        } else vo.add(ReportInfoRateVo.builder().info(rateInfo[2]).build());
        if (null != typeMatchMap.get("T")) {
            BigDecimal kk = bigDecimalShortZero(typeMatchMap.get("T"), null);
            vo.add(ReportInfoRateVo.builder().info(rateInfo[3]).expRate(kk.toPlainString()).build());
        } else vo.add(ReportInfoRateVo.builder().info(rateInfo[3]).build());
        if (null != typeMatchMap.get("Q")) {
            BigDecimal kk = bigDecimalShortZero(typeMatchMap.get("Q"), null);
            vo.add(ReportInfoRateVo.builder().info(rateInfo[4]).expRate(kk.toPlainString()).build());
        } else vo.add(ReportInfoRateVo.builder().info(rateInfo[4]).build());
        return vo;
    }

    /** #12 外部 業務處費率組 各電壓層級度數報表
     * 度數應進位至整數再統計加總
     * @param runDate 帳單年月
     * @return
     * @throws Exception
     */
    public List<ReportVoltVo> getTotalYearVoltKwh(Date runDate) throws Exception {
        List<String> voltLevel = voltageLevelRepository.findAllVoltInfo();

        return collectVoltageLevel(voltLevel, runDate);
    }

    // # 10 小額綠電契約
    public List<ReportGreenContractVo> getSmallGreenContractsInfo(Date startDate, Date endDate, String type) throws Exception {
        Date serviceStart = regularDate(startDate);
        Date serviceEnd = getNextMonthFirstDate(endDate);
        //String type = "Q"; //因沒有小額綠電 因此暫定 type = '1' -> 'Q'
        return getContractsInfoByFixType(type, serviceStart, serviceEnd);
    }

    /** #9 外部 業務處再購組 小額綠電契約4時段轉供度數
     * 1. KWH(轉供度數)應進位至整數。
     * 2. EXP(費用)應進位至整數。
     * 3. METER_TYPE_01~11應進位至整數。
     * 4. EXP_RATE(費率)應顯示至小數4位。
     * @param startDate
     * @param endDate
     * @param type
     * @return
     * @throws Exception
     */
    public List<ReportGreenVo> getSmallGreenSettleServiceDateMatched(Date startDate, Date endDate, String type) throws Exception {
        Date billStart = regularDate(startDate);
        Date billEnd = getNextMonthFirstDate(endDate);
        //String type = "Q"; //因沒有小額綠電 因此暫定 type = '1' -> 'Q'
        return getMonthlyGeneratorMatchReMatchInfo(type, billStart, billEnd);
    }

    // #6 遇到電表 多裝置容量(=多契約 這些契約 PMI 加起來是 100) 且同電號 + 同電表 此狀況裝置容量方面 呈現 radio*kw 最大的契約 找 發電端 channel = 3 -> 1
    public List<ReportGen15valueVo> getMonthsAmiSettlement15RecordLimitCapacity(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> dd = applicationMonthlyCapacityRecordRepository.findSettleServiceDates(billDate);
        if (checkCurrentYearMonth(billDate))
            dd = applicationMonthlyCapacitySettlementRepository.findSettleServiceDates(billDate);
        if (null == dd || dd.isEmpty()) return new ArrayList<>();
        List<Date> serviceDList = new ArrayList<>();
        for (int i =0; i< dd.size();i++)
            serviceDList.add((Date) dd.get(i).get("serviceDate"));

        List<ReportGen15valueVo> voList = new ArrayList<>();
        for (int i =0 ;i< serviceDList.size(); i++) {
            Date startDate = serviceDList.get(i);
            Date endDate = getNextMonthFirstDate(startDate);
            Integer channel = AMI_GEN_CHANNEL; // 發電端 3 -> 1
            List<String> elecNos = amiService.getAmiSettle15RecordElecNos(channel, startDate, endDate);

            Map<String, String> infos = collectElecNoFromApplicationType(elecNos, startDate, endDate);
            List<String> sElecNos = extractElecNo(infos.keySet());
            List<Map<String, Object>> ami15 = amiService.getAmiSettlement15Capacity(sElecNos, channel, startDate, endDate);

            List<ReportGen15valueVo> vo = collectAmiSettle15Record(ami15, startDate, infos);
            voList.addAll(vo);
        }
        return voList;
    }

    // #5 每月發電端購電(Rch3表)每15分鐘讀值 Surp
    public List<ReportGen15valueVo> getMonthsAmiSettlement15Record(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> dd = applicationMonthlyCapacityRecordRepository.findSettleServiceDates(billDate);
        if (checkCurrentYearMonth(billDate))
            dd = applicationMonthlyCapacitySettlementRepository.findSettleServiceDates(billDate);
        if (null == dd || dd.isEmpty()) return new ArrayList<>();
        List<Date> serviceDList = new ArrayList<>();
        for (int i =0; i< dd.size();i++)
            serviceDList.add((Date) dd.get(i).get("serviceDate"));

        List<ReportGen15valueVo> voList = new ArrayList<>();
        for (int i =0 ;i< serviceDList.size(); i++) {
            Date startDate = serviceDList.get(i);
            Date endDate = getNextMonthFirstDate(startDate);
            int channel = AMI_GEN_CHANNEL;
            List<String> elecNos = amiService.getAmiSettle15RecordElecNos(channel, startDate, endDate); // 發電端 3 -> 1

            Map<String, String> infos = collectElecNoFromApplicationType(elecNos, startDate, endDate);
            List<String> sElecNos = extractElecNo(infos.keySet());
            List<Map<String, Object>> ami15 = amiService.getAmiSettlement15Record(sElecNos, channel, startDate, endDate);
            List<ReportGen15valueVo> vo = collectAmiSettle15Record(ami15, startDate, null);
            voList.addAll(vo);
        }

        return voList;
    }

    /** #11 轉直供用戶之各月轉直供資料
     * 01~11應進位至整數
     * @param start
     * @param end
     * @return
     * @throws Exception
     */
    public List<ReportLoadVo> getMonthsApplicationLoadMatchedCn(Date start, Date end) throws Exception {
        Date billStart = regularDate(start);
        Date billEnd = getNextMonthFirstDate(end);

        List<Map<String, Object>> loadMatch = applicationMonthlyCapacityRecordRepository
                .findMonthlyApplicationLoadByBillDateRange(billStart, billEnd);
        if (checkCurrentYearMonth(getMonthFirstDate(end))) {
            loadMatch = applicationMonthlyCapacitySettlementRepository
                    .findMonthlyApplicationLoadByBillDateRange(billStart, billEnd);
        }
        if (null == loadMatch || loadMatch.isEmpty()) return new ArrayList<>();
        List<ReportLoadVo> voList = new ArrayList<>();
        for (int i =0; i<loadMatch.size(); i++) {
            Map<String, Object> load = loadMatch.get(i);
            Long appLoadId = (Long) load.get("applicationLoadId");
            Date serviceDate = (Date) load.get("serviceDate");
            List<String> dateList = getMonthStartDateEndDateString(serviceDate);
            String serviceStart = dateList.get(0);
            String serviceEnd = dateList.get(dateList.size()-1);
            int year = getYear(serviceDate);
            int month = getMonth(serviceDate);
            String KWH = load.get("KWH").toString();
            String pwDs = getPwDs(load, "PWDS");
            String SETTLEMENT_ID = load.get("SETTLEMENT_ID").toString();
            voList.add(ReportLoadVo.builder().custElecNo((String)load.get("CUST_ELEC_NO")).year(Integer.toString(year))
                    .month(Integer.toString(month)).startDate(serviceStart).endDate(serviceEnd).table1(KWH)
                    .contrType(load.get("CONTR_TYPE").toString()).timePriceStg(load.get("TIME_PRICE_STG").toString())
                    .pwDs(pwDs).applicationLoadId(appLoadId).settlementId(SETTLEMENT_ID).build());
            int keepIdx = i;
            int idx = voList.size() - 1;
            // 3
            load = loadMatch.get(keepIdx+1);
            if (((Long)load.get("applicationLoadId")).equals(appLoadId) && load.get("SETTLEMENT_ID").toString().equals(SETTLEMENT_ID)
                && load.get("ENERGY_CHARGE_SECTION_ID").toString().equals("3")) {
                ReportLoadVo vo = voList.get(idx);
                vo.setTable3(load.get("KWH").toString());
                i++;
            }
            // 9
            load = loadMatch.get(keepIdx+2);
            if (((Long)load.get("applicationLoadId")).equals(appLoadId) && load.get("SETTLEMENT_ID").toString().equals(SETTLEMENT_ID)) {
                if (load.get("ENERGY_CHARGE_SECTION_ID").toString().equals("9")) {
                    ReportLoadVo vo = voList.get(idx);
                    vo.setTable9(load.get("KWH").toString());
                    i++;
                } else if (load.get("ENERGY_CHARGE_SECTION_ID").toString().equals("11")) {
                    ReportLoadVo vo = voList.get(idx);
                    vo.setTable11(load.get("KWH").toString());
                    i++;
                }
            }
            // 11
            load = loadMatch.get(keepIdx+3);
            if (((Long)load.get("applicationLoadId")).equals(appLoadId) && load.get("SETTLEMENT_ID").toString().equals(SETTLEMENT_ID)
                    && load.get("ENERGY_CHARGE_SECTION_ID").toString().equals("11")) {
                ReportLoadVo vo = voList.get(idx);
                vo.setTable11(load.get("KWH").toString());
                i++;
            }
        }
        return voList;
    }

    // #8 再媒合 TransRelat2TH 有換表日 需要 DESC
    public List<ReportGenPowerVo> get2ThMonthlyGeneratorReMatchedRM(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> reMatch = applicationMonthlyCapacitySettlementRepository
                .findApplicationMonthlyGeneratorsReMatchInfoByBillDate(billDate);
        if (null == reMatch || reMatch.isEmpty()) return new ArrayList<>();
        List<String> inv13911 = List.of("1","3","9","11");
        List<ReportGenPowerVo> voList = new ArrayList<>();
        for (int i =0; i < reMatch.size(); i++) {
            Map<String, Object> m = reMatch.get(i);
            Long gId = (Long) m.get("applicationGeneratorId");
            Long settleId = (Long) m.get("SETTLEMENT_ID");
            Date serviceDate = (Date) m.get("serviceDate");
            List<String> dateList = getMonthStartDateEndDateString(serviceDate);
            String startDate = dateList.get(0);
            String endDate = dateList.get(dateList.size()-1);
            voList.add(ReportGenPowerVo.builder().genElecNo((String)m.get("GEN_ELEC_NO"))
                    .contractType((String)m.get("CONTRACT_TYPE")).meterNo((String)m.get("METER_NO"))
                    .meterReplacedDate(null == m.get("METER_REPLACED_DATE")? null:m.get("METER_REPLACED_DATE").toString().substring(0,16))
                    .serviceId((String)m.get("SERVICE_ID"))
                    .deviceCapacity(((BigDecimal)m.get("DEVICE_CAPACITY")).stripTrailingZeros().toPlainString())
                    .genPwPercent(((BigDecimal)m.get("GEN_PW_PERCENT")).stripTrailingZeros().toPlainString())
                    .terminateDate(null == m.get("TERMINATE_DATE")? null:m.get("TERMINATE_DATE").toString())
                    .startDate(startDate).endDate(endDate).table1(bigDecimalShortZero((BigDecimal) m.get("matchedRm"), null).toPlainString())
                    .pwDs(null == m.get("CONTRACT_TYPE")? "" : (m.get("CONTRACT_TYPE").toString().equals("2") || m.get("CONTRACT_TYPE").toString().equals("3")) ? "DS": "PW")
                    .applicationGeneratorId(gId).build());
            int keepIdx = i;
            Integer count = 0;
            // 3
            m = reMatch.get(keepIdx+1);
            Long settleIdN = (Long) m.get("SETTLEMENT_ID");
            String matchRm = bigDecimalShortZero((BigDecimal) m.get("matchedRm"), null).toPlainString();
            if (settleId.equals(settleIdN) && m.get("energyChangeSectionId").toString().equals(inv13911.get(1))) {
                ReportGenPowerVo vo = voList.get(voList.size()-1);
                vo.setTable3(matchRm);
                count ++;
                i ++;
            }
            // 9
            m = reMatch.get(keepIdx+2);
            settleIdN = (Long) m.get("SETTLEMENT_ID");
            matchRm = bigDecimalShortZero((BigDecimal) m.get("matchedRm"), null).toPlainString();
            if (settleId.equals(settleIdN)) {
                if (m.get("energyChangeSectionId").toString().equals(inv13911.get(2))) {
                    ReportGenPowerVo vo = voList.get(voList.size()-1);
                    vo.setTable9(matchRm);
                    count ++;
                    i ++;
                    // 11
                    m = reMatch.get(keepIdx+3);
                    settleIdN = (Long) m.get("SETTLEMENT_ID");
                    matchRm = bigDecimalShortZero((BigDecimal) m.get("matchedRm"), null).toPlainString();
                    if (settleId.equals(settleIdN) && m.get("energyChangeSectionId").toString().equals(inv13911.get(3))) {
                        vo = voList.get(voList.size()-1);
                        vo.setTable11(matchRm);
                        count ++;
                        i ++;
                    }
                } else { // 11
                    ReportGenPowerVo vo = voList.get(voList.size()-1);
                    vo.setTable11(matchRm);
                    count ++;
                    i ++;
                }
            }
            ReportGenPowerVo vo = voList.get(voList.size()-1);
            vo.setTimePriceStage(count.shortValue());
        }
        return voList;
    }

    // #7 每月發電端每15分鐘轉直供度數 TransRelatRaw 有換表日
    public List<ReportGen15powerVo> get15MinuteMonthlyGeneratorMatchedRM(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);
        List<Map<String, Object>> apGenIdDeviceCapMap = applicationMonthlyCapacityRecordRepository.find15MinutesGeneratorSettleInfoByServiceDate(billDate);
        if (checkCurrentYearMonth(billDate)) {
            apGenIdDeviceCapMap = applicationMonthlyCapacitySettlementRepository.find15MinutesGeneratorSettleInfoByServiceDate(billDate);
        }
        if (null == apGenIdDeviceCapMap || apGenIdDeviceCapMap.isEmpty()) return new ArrayList<>();
        List<ReportGen15powerVo> voList = new ArrayList<>();
        List<TypeDateMeterVo> tdm = new ArrayList<>();

        int count = 0;
        int idxCount = 0;
        Map<String, Integer> apGenIdStrIdxMap = new HashMap<>();
        Set<Long> settleIdSet = new HashSet<>();
        List<Long> settleIdList = new ArrayList<>();

        for (Map<String, Object> apGenIdDeviceCap : apGenIdDeviceCapMap) {
            Long apGenId = (Long) apGenIdDeviceCap.get("applicationGeneratorId");
            Date serviceDate = (Date) apGenIdDeviceCap.get("serviceDate");
            int monthDays = getDateOfMonth(serviceDate);
            String yearMonth = yearMonthStr(serviceDate);
            Long settleId = (Long) apGenIdDeviceCap.get("SETTLEMENT_ID");

            String genElecNo = null == apGenIdDeviceCap.get("GEN_ELEC_NO") ? "" : apGenIdDeviceCap.get("GEN_ELEC_NO").toString();
            String genMeterNo = null == apGenIdDeviceCap.get("oGMeterNext") ? null == apGenIdDeviceCap.get("GEN_METER_NO")
                    ? "" : apGenIdDeviceCap.get("GEN_METER_NO").toString(): apGenIdDeviceCap.get("oGMeterNext").toString();
            String serviceId = null == apGenIdDeviceCap.get("SERVICE_ID") ? "" : apGenIdDeviceCap.get("SERVICE_ID").toString();
            String pwDs = getPwDs(apGenIdDeviceCap, "appType");

            if (settleIdSet.add(settleId)) {
                settleIdList.add(settleId);
            }
            Date meterReplaceDate = null == apGenIdDeviceCap.get("genMeterReplaceDate") ? null : (Date) apGenIdDeviceCap.get("genMeterReplaceDate");
            if (null != meterReplaceDate) {
                tdm.add(TypeDateMeterVo.builder().date(meterReplaceDate).meterNo(apGenIdDeviceCap.get("oldGenMeterNo").toString()).build());
            }
            if (null == meterReplaceDate || !apGenIdDeviceCap.get("applicationGeneratorId").equals(apGenIdDeviceCapMap.get(idxCount+1).get("applicationGeneratorId"))) {
                if (tdm.isEmpty()) {
                    for (int i = 0; i < monthDays; i++) {
                        String ddd = yearMonth + String.format("%02d", i + 1);
                        voList.add(ReportGen15powerVo.builder().genElecNo(genElecNo).genMeterNo(genMeterNo).serviceId(serviceId)
                                .date(ddd).loads(comma95Str).pwDs(pwDs).applicationGeneratorId(apGenId).build());
                        apGenIdStrIdxMap.put(apGenId.toString()+"~"+ddd, count++);
                    }
                } else {
                    if (tdm.size() > 1) {
                        tdm.sort((t1,t2) -> t1.getDate().compareTo(t2.getDate()));
                    }
                    List<Integer> idx = new ArrayList<>();
                    for (int t =0; t<tdm.size();t++) {
                        idx.add(Integer.parseInt(new SimpleDateFormat(dateFormat).format(tdm.get(t).getDate()).split("-")[2]));
                        String meterNo = tdm.get(t).getMeterNo();
                        if (t ==0) {
                            for (int i = 0; i < idx.get(idx.size()-1); i++) {
                                String ddd = yearMonth + String.format("%02d", i + 1);
                                voList.add(ReportGen15powerVo.builder().genElecNo(genElecNo).genMeterNo(meterNo).serviceId(serviceId)
                                        .date(ddd).loads(comma95Str).pwDs(pwDs).applicationGeneratorId(apGenId).build());
                                apGenIdStrIdxMap.put(apGenId.toString()+"~"+ddd, count++);
                            }
                        } else {
                            for (int i = idx.get(idx.size()-2); i < idx.get(idx.size()-1); i++) {
                                String ddd = yearMonth + String.format("%02d", i + 1);
                                voList.add(ReportGen15powerVo.builder().genElecNo(genElecNo).genMeterNo(meterNo).serviceId(serviceId)
                                        .date(ddd).loads(comma95Str).pwDs(pwDs).applicationGeneratorId(apGenId).build());
                                apGenIdStrIdxMap.put(apGenId.toString()+"-"+(i+1), count++);
                            }
                        }
                    }
                    for (int i = idx.get(idx.size()-1); i < monthDays; i++) {
                        String ddd = yearMonth + String.format("%02d", i + 1);
                        voList.add(ReportGen15powerVo.builder().genElecNo(genElecNo).genMeterNo(genMeterNo).serviceId(serviceId)
                                .date(ddd).loads(comma95Str).pwDs(pwDs).applicationGeneratorId(apGenId).build());
                        apGenIdStrIdxMap.put(apGenId.toString()+"-"+(i+1), count++);
                    }
                }
                tdm.clear();
                tdm = new ArrayList<>();
            }
            idxCount ++;
        }
        return extract15MinGenMatchedRm(voList, settleIdList, apGenIdStrIdxMap);
    }

    // #3 #4 每月轉直供度數 有換表日
    public List<ReportGenPowerVo> getMonthlyGeneratorMatchedRM(Date runDate) throws Exception {
        Date billDate = regularDate(runDate);
        boolean checkMark = false;

        List<Map<String, Object>> info = applicationMonthlyCapacityRecordRepository.findApplicationGeneratorSettleInfo(billDate);
        if (checkCurrentYearMonth(billDate)) {
            info = applicationMonthlyCapacitySettlementRepository.findApplicationGeneratorSettleInfo(billDate);
            checkMark = true;
        }
        if (null == info || info.isEmpty()) return new ArrayList<>();
        List<Long> settleIdList = new ArrayList<>();
        if (checkMark)
            settleIdList = applicationMonthlyCapacitySettlementRepository.findAllSettlementId(billDate);
        else settleIdList = applicationMonthlyCapacityRecordRepository.findAllSettlementId(billDate);

        return collectMonthlyGeneratorMatchedRM(info, settleIdList);
    }

    // 原始資料: 有換表日 = 發電端換表日 用電端換表日
    public List<ReportVo> getOriginalBillingReport(Date start, Date end) throws Exception {
        Date billStart = regularDate(start);
        Date billEnd = getNextMonthFirstDate(end);
        List<Map<String, Object>> settlementList = repository.findAllSettlementInfoByBillingDateRange(billStart, billEnd);
        if (null == settlementList || settlementList.isEmpty()) return new ArrayList<>();
        Set<Long> setApplicationGeneratorId = new HashSet<>();
        Date firstServiceDate = null;
        Date lastServiceDate = null;

        List<ReportVo> voList = new ArrayList<>();
        Map<String, Integer> voListIdxMap = new HashMap<>();

        int count = 0;
        for (Map<String, Object> stm : settlementList) {
            Long apGenId = (Long) stm.get("applicationGeneratorId");
            if (apGenId != null && apGenId != 0 && setApplicationGeneratorId.add(apGenId)) {}

            int serviceYear = 0;
            int serviceMonth = 0;
            int billYear = 0;
            int billMonth = 0;
            Date tmp = (Date) stm.get("serviceDate");
            String serviceDate = tmp.toString();
            firstServiceDate = firstServiceDate == null || tmp.compareTo(firstServiceDate) <= 0 ? tmp : firstServiceDate;
            lastServiceDate = lastServiceDate == null || tmp.compareTo(lastServiceDate) >= 0 ? tmp : lastServiceDate;
            if (null != serviceDate && !serviceDate.isEmpty() && serviceDate.contains("-")) {
                String[] dd = serviceDate.split("-");
                if (dd.length > 1) {
                    serviceYear = Integer.parseInt(dd[0]);
                    serviceMonth = Integer.parseInt(dd[1]);
                }
            }
            String billDate = ((Date) stm.get("billDate")).toString();
            if (null != billDate && !billDate.isEmpty() && billDate.contains("-")) {
                String[] dd = billDate.split("-");
                if (dd.length > 1) {
                    billYear = Integer.parseInt(dd[0]);
                    billMonth = Integer.parseInt(dd[1]);
                }
            }
            BigDecimal dExp = bigDecimalShortZero((BigDecimal)stm.get("D_EXP"), null);
            BigDecimal tExp = bigDecimalShortZero((BigDecimal)stm.get("T_EXP"), null);
            BigDecimal aExp = bigDecimalShortZero((BigDecimal)stm.get("A_EXP"), null);
            BigDecimal sExp = bigDecimalShortZero((BigDecimal)stm.get("S_EXP"), null);
            BigDecimal totalExp = dExp.add(tExp).add(aExp).add(sExp);
            Integer powerType = null == stm.get("powerType")? null: Integer.parseInt(stm.get("powerType").toString());
            voList.add(ReportVo.builder().billYear(billYear).billMonth(billMonth)
                    .serviceYear(serviceYear).serviceMonth(serviceMonth).dExp(dExp.toPlainString()).tExp(tExp.toPlainString())
                    .aExp(aExp.toPlainString()).sExp(sExp.toPlainString()).totalExp(totalExp.toPlainString())
                    .serviceDate(stm.get("serviceDate").toString())
                    .applicationGeneratorId((Long)stm.get("applicationGeneratorId"))
                    .applicationLoadId((Long)stm.get("applicationLoadId"))
                    .apGenLoType(powerType).build());
            voListIdxMap.put(stm.get("serviceDate").toString()+"~"+stm.get("billDate").toString()+"~"
                    +stm.get("applicationGeneratorId").toString()+"~"+stm.get("applicationLoadId").toString(),count);
            count += 1;
        }
        return getMonthSettleGenLoadDetails(firstServiceDate, lastServiceDate, billEnd, voListIdxMap, voList);
    }

    public Long setApplicationGeneratorRepository(Long gId, Long generatorMeterId) {
        ApplicationGenerator apGen = getGenMeterId(gId);
        if(null != apGen) {
            apGen.setGeneratorMeterId(generatorMeterId);
            applicationGeneratorRepository.save(apGen);
            apGen = getGenMeterId(gId);
            if (null != apGen) return apGen.getGeneratorMeterId();
        }
        return null;
    }

    public Long setApplicationLoadRepository(Long lId, Long loadMeterId) {
        ApplicationLoad apLoad = getLoadMeterId(lId);
        if(null != apLoad) {
            apLoad.setLoadMeterId(loadMeterId);
            applicationLoadRepository.save(apLoad);
            apLoad = getLoadMeterId(lId);
            if (null != apLoad) return apLoad.getLoadMeterId();
        }
        return null;
    }

    // 以下處理 appType = '4' 彈性分配 = 沒有換表日
    private ReportContractSettlesMapDateListVo getMonthSettlementInfoForBSMI(List<Map<String, Object>> info
            , Set<String> oriContractSet, Map<Integer, Set<String>> dateOriContractMap) throws Exception {
        Map<String, String> sGIdLIdDateStrMap = new HashMap<>();
        Set<String> settleContractSet = new HashSet<>();
        Map<String, String> contractIdMap = new HashMap<>();
        Map<String, Map<String, String>> dateSettleContractIdxMap = new HashMap<>();
        List<ReportContractSettleOVo> voList = new ArrayList<>();
        int idx = 0;
        int voId = 0;
        String keepGenReplacedDate = "";
        String keepCustReplacedDate = "";
        for (Map<String, Object> m:info) {
            Date billDate = DateUtils.passStringToDate(m.get("billDate").toString());
            String contract = (String)m.get("CONTRACT_NO");
            Long appId = (Long)m.get("appId");
            Long appGenId = (Long)m.get("applicationGeneratorId");
            Long appLoadId = (Long)m.get("applicationLoadId");
            String voKey = m.get("applicationGeneratorId") +"~"+m.get("applicationLoadId");
            String appType = (String)m.get("appType");
            Date custMaterChangeDate = null == m.get("CUST_METER_CHANGE_DATE") ? null:(Date)m.get("CUST_METER_CHANGE_DATE");
            Date genMeterChangeDate = null == m.get("GEN_METER_CHANGE_DATE") ? null:(Date)m.get("GEN_METER_CHANGE_DATE");
            if (null != m.get("CUST_METER_CHANGE_DATE")) keepCustReplacedDate = getKeepReplacedDateString(m.get("CUST_METER_CHANGE_DATE").toString(), true);
            if (null != m.get("GEN_METER_CHANGE_DATE")) keepGenReplacedDate = getKeepReplacedDateString(m.get("GEN_METER_CHANGE_DATE").toString(), true);
            BSMIOutDateStrBillDateRangeVo dateStr = getMonthDayRangeString(billDate);
            String inDateStr = dateStr.getInDateStr();
            if (!appType.equals("4")) {// && !(null == custMaterChangeDate && null == genMeterChangeDate)) {
                if (sGIdLIdDateStrMap.isEmpty() && null != custMaterChangeDate) {
                    sGIdLIdDateStrMap.put(voKey, "c~" + keepCustReplacedDate + "~" + m.get("oldLoadMeterNo"));
                    keepCustReplacedDate = "";
                } else if (null != sGIdLIdDateStrMap.get(voKey) && null != custMaterChangeDate) {
                    String cc = "c~" + keepCustReplacedDate + "~" + m.get("oldLoadMeterNo");
                    String dd = null == sGIdLIdDateStrMap.get(voKey) ? cc : sGIdLIdDateStrMap.get(voKey) + "," + cc;
                    sGIdLIdDateStrMap.put(voKey, dd);
                    keepCustReplacedDate = "";
                }
                if (sGIdLIdDateStrMap.isEmpty() && null != genMeterChangeDate) {
                    sGIdLIdDateStrMap.put(voKey, "g~" + keepGenReplacedDate + "~" + m.get("oldGenMeterNo"));
                    keepGenReplacedDate = "";
                } else if (null != sGIdLIdDateStrMap.get(voKey) && null != genMeterChangeDate) {
                    String cc = "g~" + keepGenReplacedDate + "~" + m.get("oldGenMeterNo");
                    String dd = null == sGIdLIdDateStrMap.get(voKey) ? cc : sGIdLIdDateStrMap.get(voKey) + "," + cc;
                    sGIdLIdDateStrMap.put(voKey, dd);
                    keepGenReplacedDate = "";
                }
            }
            if (settleContractSet.isEmpty() || !settleContractSet.contains(contract)) {
                settleContractSet.add(contract);
            }
            if (sGIdLIdDateStrMap.isEmpty() || null == sGIdLIdDateStrMap.get(voKey)) { // 沒有換表日處理
                Map<String, Object> tt = info.get(idx == 0? 0: idx -1);
                if (idx > 0 && tt.get("applicationGeneratorId").equals(appGenId) && tt.get("applicationLoadId").equals(appLoadId)) {}
                else {
                    voList.add(ReportContractSettleOVo.builder().contractNo((String)m.get("SERVICE_ID")).billDateRange(dateStr.getBillDateRange())
                            .custElecNo(m.get("CUST_ELEC_NO").toString()).custMeterNo(null == m.get("CUST_METER_NO")?"":m.get("CUST_METER_NO").toString())
                            .genElecNo(m.get("GEN_ELEC_NO").toString()).genMeterNo(null == m.get("GEN_METER_NO")?"":m.get("GEN_METER_NO").toString())
                            .kwh(((BigDecimal)m.get("matchedKw")).stripTrailingZeros().toPlainString())
                            .tExp(((BigDecimal)m.get("tRate")).stripTrailingZeros().toPlainString())
                            .dExp(((BigDecimal)m.get("dRate")).stripTrailingZeros().toPlainString())
                            .sExp(((BigDecimal)m.get("sRate")).stripTrailingZeros().toPlainString())
                            .aExp(((BigDecimal)m.get("aRate")).stripTrailingZeros().toPlainString())
                            .cost(((BigDecimal)m.get("FEE")).stripTrailingZeros().toPlainString())
                            .applicationGeneratorId(appGenId).applicationLoadId(appLoadId).applicationId(appId).billDate(billDate).build());
                    contractIdMap = new HashMap<>();
                    if (!dateSettleContractIdxMap.isEmpty() && null != dateSettleContractIdxMap.get(inDateStr))
                        contractIdMap.putAll(dateSettleContractIdxMap.get(inDateStr));

                    String voIdStr = null == dateSettleContractIdxMap.get(inDateStr)? Integer.toString(voId): null != dateSettleContractIdxMap.get(inDateStr).get(contract)
                            ? dateSettleContractIdxMap.get(inDateStr).get(contract) + "~" + Integer.toString(voId): Integer.toString(voId);
                    contractIdMap.put(contract, voIdStr);
                    dateSettleContractIdxMap.put(inDateStr, contractIdMap);
                    voId ++;
                }
            } else {
                voId = voList.isEmpty()? 0: voList.size() - 1;
                voList = computeBSMIKwhsFees(voList, m, sGIdLIdDateStrMap.get(voKey), dateStr);
                String voIdStrTmp = Integer.toString(voId);
                for (int i = voId +1; i< voList.size(); i++)
                    voIdStrTmp += "~" + Integer.toString(i);
                contractIdMap = new HashMap<>();
                if (!dateSettleContractIdxMap.isEmpty() && null != dateSettleContractIdxMap.get(inDateStr))
                    contractIdMap.putAll(dateSettleContractIdxMap.get(inDateStr));

                String voIdStr = null == dateSettleContractIdxMap.get(inDateStr)? voIdStrTmp: null != dateSettleContractIdxMap.get(inDateStr).get(contract)
                        ? dateSettleContractIdxMap.get(inDateStr).get(contract) + "~" + voIdStrTmp: voIdStrTmp;
                contractIdMap.put(contract, voIdStr);
                dateSettleContractIdxMap.put(inDateStr, contractIdMap);
                voId = voList.size();
                sGIdLIdDateStrMap.clear();
                sGIdLIdDateStrMap = new HashMap<>();
            }
            idx ++;
        }
        return collectFatherContractWithSettleContract(settleContractSet, oriContractSet, dateOriContractMap
                , dateSettleContractIdxMap, voList);
    }

    private ReportContractSettlesMapDateListVo collectFatherContractWithSettleContract(Set<String> settleContract
            , Set<String> oriContractSet, Map<Integer, Set<String>> dateOriContractMap
            , Map<String, Map<String, String>> dateSettleContractIdxMap, List<ReportContractSettleOVo> voList) {
        List<String> settleContracts = new ArrayList<>(settleContract);
        Collections.sort(settleContracts);
        List<String> contractFa = new ArrayList<>(oriContractSet);
        Collections.sort(contractFa);
        List<Map<String, Object>> contractTable = applicationRepository.findParentIdByContractNos(settleContracts, contractFa);
        Map<String, String> contractFaMap = new HashMap<>();
        List<ContractFaNoVo> listContract = new ArrayList<>();
        Map<String, String> faToSettleContractMap = new HashMap<>();
        int count = 0;
        for (Map<String, Object> m:contractTable) {
            String contract = (String)m.get("CONTRACT_NO");
            String serviceId = (String)m.get("SERVICE_ID");
            String fatherContract = null == m.get("PARENT_ID")? "": null == m.get("PId2")? m.get("fatherContract").toString()
                    : null == m.get("PId3")? m.get("pContractNo3").toString(): null == m.get("PId4")
                    ? m.get("pContractNo4").toString() : null == m.get("PId5")? m.get("pContractNo5").toString(): "";
            if (!listContract.isEmpty() && count > 0 && listContract.get(count-1).getContract().equals(contract) && fatherContract.isEmpty()) {
                fatherContract = listContract.get(count-1).getFatherContract();
            }
            fatherContract = s1FatherContractTransformation(fatherContract, contract, serviceId);// 因應 s1 契約資訊不足, 從 pwoms 取得父契約後, 補上
            if (settleContract.contains(contract) && (contractFaMap.isEmpty() || null == contractFaMap.get(contract)) && !fatherContract.isEmpty()) {
                String fa = fatherContract.substring(0, fatherContract.length()-3);
                contractFaMap.put(contract, fa);
                faToSettleContractMap.put(fa, contract);
            }
            listContract.add(ContractFaNoVo.builder().id((Long)m.get("ID")).contract(contract).serviceId(serviceId).fatherContract(fatherContract).build());
            count ++;
        }
        // faToSettleContractMap fa vs settleContract Map
        return generatorBSMIOutName(faToSettleContractMap, dateOriContractMap, dateSettleContractIdxMap
                , voList);
    }

    private List<ReportGenPowerVo> collectMonthlyGeneratorMatchedRM(List<Map<String, Object>> info
            , List<Long> settleIdList) throws Exception {
        // sum15MinGenKwh
        List<ReportGenPowerVo> voList = new ArrayList<>();
        for (int i = 0; i < info.size(); i++) {
            Map<String, Object> m = info.get(i);
            Long apGenId = (Long) m.get("applicationGeneratorId");
            Date serviceDate = (Date) m.get("serviceDate");
            Short timePriceStg = null == m.get("TIME_PRICE_STG")? null: Short.valueOf(m.get("TIME_PRICE_STG").toString());
            String pwDs = getPwDs(m, "PWDS");
            List<String> dateList = getMonthStartDateEndDateString(serviceDate);
            String table1 = m.get("KWH").toString();
            String mTable3 = null;
            String mTable9 = null;
            String mTable11 = null;
            // 1
            if (null != m.get("GEN_METER_CHANGE_DATE")) {
                Kwhs13911Vo kwhS = sum15MinGenKwh(settleIdList, apGenId, serviceDate, (Date)m.get("GEN_METER_CHANGE_DATE"));
                String oldMeter = (String) m.get("oldGenMeterNo");
                voList.add(ReportGenPowerVo.builder().genElecNo((String)m.get("GEN_ELEC_NO"))
                        .contractType((String)m.get("CONTRACT_TYPE")).timePriceStage(timePriceStg)
                        .meterNo(oldMeter).serviceId((String)m.get("SERVICE_ID"))
                        .deviceCapacity(null == m.get("DEVICE_CAPACITY")? null: m.get("DEVICE_CAPACITY").toString())
                        .genPwPercent(null == m.get("GEN_PW_PERCENT")? null: m.get("GEN_PW_PERCENT").toString())
                        .terminateDate(null == m.get("TERMINATE_DATE")? null: m.get("TERMINATE_DATE").toString())
                        .startDate(dateList.get(0)).endDate(dateList.get(dateList.size()-1)).table1(kwhS.getTable1())
                                .table3(kwhS.getTable3()).table9(kwhS.getTable9()).table11(kwhS.getTable11())
                        .pwDs(pwDs).applicationGeneratorId(apGenId).build());
                mTable3 = kwhS.getTable3();
                mTable9 = kwhS.getTable9();
                mTable11 = kwhS.getTable11();
                table1 = null == kwhS.getTable1()? table1: (
                        new BigDecimal(table1).subtract(new BigDecimal(kwhS.getTable1()))).stripTrailingZeros().toPlainString();
                voList.add(ReportGenPowerVo.builder().genElecNo((String)m.get("GEN_ELEC_NO"))
                        .contractType((String)m.get("CONTRACT_TYPE")).timePriceStage(timePriceStg)
                        .meterNo((String)m.get("METER_NO")).meterReplacedDate(m.get("GEN_METER_CHANGE_DATE").toString().substring(0, 16))
                        .serviceId((String)m.get("SERVICE_ID"))
                        .deviceCapacity(null == m.get("DEVICE_CAPACITY")? null: m.get("DEVICE_CAPACITY").toString())
                        .genPwPercent(null == m.get("GEN_PW_PERCENT")? null: m.get("GEN_PW_PERCENT").toString())
                        .terminateDate(null == m.get("TERMINATE_DATE")? null: m.get("TERMINATE_DATE").toString())
                        .startDate(dateList.get(0)).endDate(dateList.get(dateList.size()-1)).table1(table1)
                        .pwDs(pwDs).applicationGeneratorId(apGenId).build());
            } else {
                voList.add(ReportGenPowerVo.builder().genElecNo((String)m.get("GEN_ELEC_NO"))
                        .contractType((String)m.get("CONTRACT_TYPE"))
                        .timePriceStage(timePriceStg)
                        .meterNo((String)m.get("METER_NO")).serviceId((String)m.get("SERVICE_ID"))
                        .deviceCapacity(null == m.get("DEVICE_CAPACITY")? null: m.get("DEVICE_CAPACITY").toString())
                        .genPwPercent(null == m.get("GEN_PW_PERCENT")? null: m.get("GEN_PW_PERCENT").toString())
                        .terminateDate(null == m.get("TERMINATE_DATE")? null: m.get("TERMINATE_DATE").toString())
                        .startDate(dateList.get(0)).endDate(dateList.get(dateList.size()-1)).table1(table1)
                        .pwDs(pwDs).applicationGeneratorId(apGenId).build());
            }
            int keepIdx = i;
            int idx = voList.size() - 1;
            // 3
            m = info.get(keepIdx+1);
            if (((Long)m.get("applicationGeneratorId")).equals(apGenId) && ((Date)m.get("serviceDate")).equals(serviceDate)
                    && m.get("ENERGY_CHARGE_SECTION_ID").toString().equals("3")) {
                ReportGenPowerVo vo = voList.get(idx);
                vo.setTable3(null == mTable3? m.get("KWH").toString()
                        : (((BigDecimal)m.get("KWH")).subtract(new BigDecimal(mTable3))).stripTrailingZeros().toPlainString());
                i ++;
            }
            // 9
            m = info.get(keepIdx+2);
            if (((Long)m.get("applicationGeneratorId")).equals(apGenId) && ((Date)m.get("serviceDate")).equals(serviceDate)) {
                if (m.get("ENERGY_CHARGE_SECTION_ID").toString().equals("9")) {
                    ReportGenPowerVo vo = voList.get(idx);
                    vo.setTable9(null == mTable9? m.get("KWH").toString()
                            : (((BigDecimal)m.get("KWH")).subtract(new BigDecimal(mTable9))).stripTrailingZeros().toPlainString());
                    i++;
                } else if (m.get("ENERGY_CHARGE_SECTION_ID").toString().equals("11")) {
                    ReportGenPowerVo vo = voList.get(idx);
                    vo.setTable11(null == mTable11? m.get("KWH").toString()
                            : (((BigDecimal)m.get("KWH")).subtract(new BigDecimal(mTable11))).stripTrailingZeros().toPlainString());
                    i++;
                }
            }
            // 11
            m = info.get(keepIdx+3);
            if (((Long)m.get("applicationGeneratorId")).equals(apGenId) && ((Date)m.get("serviceDate")).equals(serviceDate)
                    && m.get("ENERGY_CHARGE_SECTION_ID").toString().equals("11")) {
                ReportGenPowerVo vo = voList.get(idx);
                vo.setTable11(null == mTable11? m.get("KWH").toString()
                        : (((BigDecimal)m.get("KWH")).subtract(new BigDecimal(mTable11))).stripTrailingZeros().toPlainString());
                i ++;
            }
        }
        return voList;
    }

    private ReportContractSettlesMapDateListVo generatorBSMIOutName(Map<String, String> faToSettleContractMap
            , Map<Integer, Set<String>> dateOriContractMap, Map<String, Map<String, String>> dateSettleContractIdxMap
            , List<ReportContractSettleOVo> voList) {
        List<Integer> dates = new ArrayList<>(dateOriContractMap.keySet());
        Collections.sort(dates, Collections.reverseOrder());
        List<String> fileNames = new ArrayList<>();
        for (int i = 0; i < dates.size(); i++) {
            int dateInt = dates.get(i);
            String dateStr = Integer.toString(dateInt);
            String outDateCsv = dateStr.substring(0, dateStr.length()-2)+"_"+dateStr.substring(dateStr.length()-2, dateStr.length())+"月份.csv";
            List<String> faNos = new ArrayList<>(dateOriContractMap.get(dateInt));
            for (int j = 0; j< faNos.size(); j++) {
                String faNo = faNos.get(j);
                String faContract = faNo.substring(0, faNo.length()-2);
                faContract = faContract.endsWith("-")? faContract.substring(0, faContract.length()-1): faContract;
                String idxStr = "";
                if (null != faToSettleContractMap.get(faContract) && null != dateSettleContractIdxMap.get(dateStr)
                        && null != dateSettleContractIdxMap.get(dateStr).get(faToSettleContractMap.get(faContract))) {
                    idxStr = "~" + dateSettleContractIdxMap.get(dateStr).get(faToSettleContractMap.get(faContract));
                } else if (null != dateSettleContractIdxMap.get(dateStr)
                        && null != dateSettleContractIdxMap.get(dateStr).get(faContract)) idxStr = "~" + dateSettleContractIdxMap.get(dateStr).get(faContract);
                idxStr = idxStr.isEmpty()? idxStr: distinctIntervalSequence(idxStr, "~");
                fileNames.add(faNo+" "+outDateCsv+idxStr);
            }
        }
        return ReportContractSettlesMapDateListVo.builder().filesContent(voList).contractDateNames(fileNames).build();
    }

    public String distinctIntervalSequence(String listStr, String invStr) {
        if (!listStr.contains(invStr)) return listStr;
        String[] tmp = listStr.split(invStr);
        Set<String> dd = new HashSet<>();
        String outStr = "";
        for (int i = 0; i< tmp.length; i++) {
            if (dd.add(tmp[i])) outStr += tmp[i] + invStr;
        }
        return outStr.substring(0, outStr.length()-1);
    }

    public List<String> getOneBSMICSVString(String name, List<ReportContractSettleOVo> voList) {
        if (!name.contains("~")) return null;
        String[] dd = name.split("~");
        if (dd.length < 2) return null;
        List<String> detail = new ArrayList<>();
        String fa = dd[0].split(" ")[0];
        String contractFa = (fa.substring(0, fa.length()-2)+"-"+fa.substring(fa.length()-2)).replace("--", "-");
        for (int i = 1; i< dd.length; i++) {
            int kk = Integer.parseInt(dd[i]);
            ReportContractSettleOVo vo = voList.get(kk);
            vo.setIndex(i);
            vo.setFatherContract(contractFa);
            detail.add(vo.toString());
        }
        return detail;
    }

    private List<ReportADSTKwhVo> computeBeforeKwhs(Date bBillDate
            , BigDecimal tKwh, BigDecimal dKwh, BigDecimal tdKwh, BigDecimal aKwh) throws Exception {
        List<Map<String, Object>> info = applicationMonthlyCapacityRecordRepository.sumKwhADSTByBillingDate(bBillDate);
        List<ReportADSTKwhVo> voList = new ArrayList<>();
        String commaStr = ",,,,,";
        voList.add(ReportADSTKwhVo.builder().before(",,").item("服務收入,,").kwh(commaStr).kwhCompare(commaStr).build());
        String space = "      ";
        String[] items = {space+"轉供電能輸電服務收入",space+"轉供電能配電服務收入",space+"調度服務收入",space+"轉直供電能傳輸損失收入"
                , space+"輔助服務收入", space+"小                  計", space+"合                  計"};
        if (null == info || info.isEmpty()) {
            return addAllKwhs(voList, items, commaStr, tKwh, dKwh, tdKwh, aKwh);
        }

        for (Map<String, Object> map: info) {
            Integer powerType = (Integer) map.get("powerType");
            BigDecimal kwh = null == map.get("kwh")? zeroBigDecimal: (BigDecimal) map.get("kwh");
            if (powerType == 12) tdKwh = tdKwh.subtract(kwh);
            else if (powerType == 1) tKwh = tKwh.subtract(kwh);
            else if (powerType == 2) dKwh = dKwh.subtract(kwh);
            else aKwh = aKwh.subtract(kwh);// powerType = 3
        }
        return addAllKwhs(voList, items, commaStr, tKwh, dKwh, tdKwh, aKwh);
    }

    private List<ReportADSTKwhVo> addAllKwhs(List<ReportADSTKwhVo> voList, String[] items, String commaStr, BigDecimal tKwh
            , BigDecimal dKwh, BigDecimal tdKwh, BigDecimal aKwh) {
        tKwh = bigDecimalShortZero(tKwh.add(tdKwh), null);
        String kwhStr = tKwh.toPlainString()+commaStr;
        voList.add(ReportADSTKwhVo.builder().before(",,").item(items[0]+",,度").kwh(kwhStr).kwhCompare(kwhStr).build());
        dKwh = bigDecimalShortZero(dKwh.add(tdKwh), null);
        kwhStr = dKwh.toPlainString()+commaStr;
        voList.add(ReportADSTKwhVo.builder().before(",,").item(items[1]+",,度").kwh(kwhStr).kwhCompare(kwhStr).build());
        aKwh = bigDecimalShortZero(aKwh, null);
        kwhStr = aKwh.toPlainString()+commaStr;
        voList.add(ReportADSTKwhVo.builder().before(",,").item(items[2]+",,度").kwh(kwhStr).kwhCompare(kwhStr).build());
        voList.add(ReportADSTKwhVo.builder().before(",,").item(items[3]+",,度").kwh(kwhStr).kwhCompare(kwhStr).build());
        voList.add(ReportADSTKwhVo.builder().before(",,").item(items[4]+",,度").kwh(kwhStr).kwhCompare(kwhStr).build());
        String total = tKwh.add(dKwh).add(aKwh).add(aKwh).add(aKwh).toPlainString()+commaStr;
        voList.add(ReportADSTKwhVo.builder().before(",,").item(items[5]+",,度").kwh(total).kwhCompare(total).build());

        for (int i = 0; i< 11; i++)
            voList.add(ReportADSTKwhVo.builder().before(",,").item(",,").kwh(commaStr).kwhCompare(commaStr).build());
        voList.add(ReportADSTKwhVo.builder().before(",,").item(items[6]+",,度").kwh(total).kwhCompare(total).build());
        voList.add(ReportADSTKwhVo.builder().before(",,").item(",,").kwh(commaStr).kwhCompare(commaStr).build());
        voList.add(ReportADSTKwhVo.builder().before(",,").item("填表：,,覆核：").kwh(",,    組長：,,,").kwhCompare("    單位主管："+commaStr).build());
        return voList;
    }

    private List<ReportGenElecNoAppKwVo> collectGenElecNoAppAmiKwh(List<String> genElecNos, Date startDate, Date endDate
            ,int year, int month) {

        List<Map<String, Object>> info = applicationRepository
                .findGeneratorElecNoComApplicationId(genElecNos, startDate, endDate);
        List<ReportGenElecNoAppKwVo> voList = new ArrayList<>();
        Map<String, String> elecNoAppIdsMap = new HashMap<>();
        Map<String, Integer> elecIdx = new HashMap<>();
        int count = 0;
        for (Map<String, Object> map:info) {
            String elecNo = map.get("ELEC_NO").toString();
            String appId = map.get("appId").toString();
            if (null == elecNoAppIdsMap || null == elecNoAppIdsMap.get(elecNo)) {
                elecNoAppIdsMap.put(elecNo, appId);
                voList.add(ReportGenElecNoAppKwVo.builder().serviceYear(year).serviceMonth(month)
                        .groupGenName(null == map.get("GROUP_GEN_NAME")?"":map.get("GROUP_GEN_NAME").toString())
                        .genName(null == map.get("GEN_NAME")? "":map.get("GEN_NAME").toString()).genElecNo(elecNo).build());
                elecIdx.put(elecNo, count++);
            } else {
                String mId = elecNoAppIdsMap.get(elecNo)+"~"+appId;
                elecNoAppIdsMap.put(elecNo, mId);
            }
        }
        //List<String> elecNos = new ArrayList<>(elecIdx.keySet()); // int channel = AMI_GEN_CHANNEL; // 發電端 3
        List<Map<String, Object>> sumAmi15 = amiService.getSumAmi15PowerSummery(AMI_GEN_CHANNEL, startDate, endDate);
        Set<String> aId = new HashSet<>();
        for (Map<String, Object> s:sumAmi15) {
            String ee = s.get("elecNo").toString();
            BigDecimal load = (BigDecimal) s.get("load");
            if (null != elecNoAppIdsMap.get(ee)) {
                String[] ids = elecNoAppIdsMap.get(ee).split("~");
                for (int i =0;i< ids.length; i++) {
                    aId.add(ids[i]);
                }
                ReportGenElecNoAppKwVo vo = voList.get(elecIdx.get(ee));
                vo.setAppCount(aId.size());
                vo.setKwh(bigDecimalShortZero(load, null).toPlainString());
                aId = new HashSet<>();
            }
        }
        return voList;
    }

    private List<ReportLoadElecNoAppKwVo> collectLoadElecNoAppAmiKwh(List<String> loadElecNo
            , Date startDate, Date endDate, int year, int month) {

        List<Map<String, Object>> info = applicationRepository
                .findLoadElecNoComApplicationId(loadElecNo, startDate, endDate);
        List<ReportLoadElecNoAppKwVo> voList = new ArrayList<>();
        Map<String, String> elecNoAppIdsMap = new HashMap<>();
        Map<String, Integer> elecIdx = new HashMap<>();
        int count = 0;
        for (Map<String, Object> map:info) {
            String elecNo = map.get("ELEC_NO").toString();
            String appId = map.get("appId").toString();
            if (null == elecNoAppIdsMap || null == elecNoAppIdsMap.get(elecNo)) {
                elecNoAppIdsMap.put(elecNo, appId);
                voList.add(ReportLoadElecNoAppKwVo.builder().serviceYear(year).serviceMonth(month)
                        .groupCustName(null == map.get("GROUP_LOAD_NAME")?"":map.get("GROUP_LOAD_NAME").toString())
                        .custName(null == map.get("LOAD_NAME")? "":map.get("LOAD_NAME").toString()).custElecNo(elecNo).build());
                elecIdx.put(elecNo, count++);
            } else {
                String mId = elecNoAppIdsMap.get(elecNo)+"~"+appId;
                elecNoAppIdsMap.put(elecNo, mId);
            }
        }
        //List<String> elecNos = new ArrayList<>(elecIdx.keySet()); // int channel = AMI_LOAD_CHANNEL; // 用電端 1
        List<Map<String, Object>> sumAmi15 = amiService.getSumAmi15PowerSummery(AMI_LOAD_CHANNEL, startDate, endDate);
        Set<String> aId = new HashSet<>();
        for (Map<String, Object> s:sumAmi15) {
            String ee = s.get("elecNo").toString();
            BigDecimal load = (BigDecimal) s.get("load");
            if (null != elecNoAppIdsMap.get(ee)) {
                String[] ids = elecNoAppIdsMap.get(ee).split("~");
                for (int i =0;i< ids.length; i++) {
                    aId.add(ids[i]);
                }
                ReportLoadElecNoAppKwVo vo = voList.get(elecIdx.get(ee));
                vo.setAppCount(aId.size());
                vo.setKwh(bigDecimalShortZero(load, null).toPlainString());
                aId = new HashSet<>();
            }
        }
        return voList;
    }

    private List<ReportAppStatusGenLoadVo> collectAppStatusGenLoadElecNo(List<Map<String, Object>> infos, Date runDate) {
        Date monthEndDate = getMonthEndDate(runDate);
        Map<String, AppStatusGenLoadElecNoVo> onePart = new HashMap<>();
        Set<Long> appSet = new HashSet<>();
        Set<Long> genVComSet = new HashSet<>();
        Set<String> gElecSet = new HashSet<>();
        Set<Long> loadVComSet = new HashSet<>();
        Set<String> lElecSet = new HashSet<>();
        for (Map<String, Object> map:infos) {
            Integer status = (Integer)map.get("STATUS");
            Integer contractStatus = (Integer)map.get("CONTRACT_STATUS");
            Date contractedEnd = (Date)map.get("CONTRACTED_END");
            String currentStatus = computeAppCurrentStatus(status, contractStatus, contractedEnd, monthEndDate);
            if (currentStatus.equals("已終止") || currentStatus.equals("運轉中")) currentStatus = "轉供中";
            if (onePart.isEmpty() || null == onePart.get(currentStatus)) {
                if (null != map.get("appId")) appSet.add((Long)map.get("appId"));
                if (null != map.get("genVComId")) genVComSet.add((Long)map.get("genVComId"));
                if (null != map.get("gElecNo")) gElecSet.add((String)map.get("gElecNo"));
                if (null != map.get("loadVComId")) loadVComSet.add((Long)map.get("loadVComId"));
                if (null != map.get("lElecNo")) lElecSet.add((String)map.get("lElecNo"));
            } else {
                AppStatusGenLoadElecNoVo vo = onePart.get(currentStatus);
                appSet = vo.getAppSet();
                genVComSet = vo.getGenVComSet();
                gElecSet = vo.getGenElecSet();
                loadVComSet = vo.getLoadVComSet();
                lElecSet = vo.getLoadElecSet();
                if (null != map.get("appId")) appSet.add((Long)map.get("appId"));
                if (null != map.get("genVComId")) genVComSet.add((Long)map.get("genVComId"));
                if (null != map.get("gElecNo")) gElecSet.add((String)map.get("gElecNo"));
                if (null != map.get("loadVComId")) loadVComSet.add((Long)map.get("loadVComId"));
                if (null != map.get("lElecNo")) lElecSet.add((String)map.get("lElecNo"));
            }
            onePart.put(currentStatus, AppStatusGenLoadElecNoVo.builder().appSet(appSet).genVComSet(genVComSet)
                    .genElecSet(gElecSet).loadVComSet(loadVComSet).loadElecSet(lElecSet).build());
        }
        String[] hStatus = {"審查中", "已函覆", "申請中", "已簽約", "轉供中"};
        ReportAppStatusGenLoadVo total = ReportAppStatusGenLoadVo.builder().appStatus("總共").appCount(0)
                .genVirtualComCount(0).genElecNoCount(0).loadVirtualComCount(0).loadElecNoCount(0).build();
        List<ReportAppStatusGenLoadVo> voList = new ArrayList<>();
        for (String h:hStatus) {
            AppStatusGenLoadElecNoVo vo = onePart.get(h);
            int appCount = 0;
            int gVComCount = 0;
            int gElecCount = 0;
            int lVComCount = 0;
            int lElecCount = 0;
            if (null != vo) {
                appCount = vo.getAppSet().size();
                gVComCount = vo.getGenVComSet().size();
                gElecCount = vo.getGenElecSet().size();
                lVComCount = vo.getLoadVComSet().size();
                lElecCount = vo.getLoadElecSet().size();
            }
            voList.add(ReportAppStatusGenLoadVo.builder().appStatus(h).appCount(appCount).genVirtualComCount(gVComCount)
                    .genElecNoCount(gElecCount).loadVirtualComCount(lVComCount).loadElecNoCount(lElecCount).build());
            total.setAppCount(total.getAppCount()+appCount);
            total.setGenVirtualComCount(total.getGenVirtualComCount()+gVComCount);
            total.setGenElecNoCount(total.getGenElecNoCount()+gElecCount);
            total.setLoadVirtualComCount(total.getLoadVirtualComCount()+lVComCount);
            total.setLoadElecNoCount(total.getLoadElecNoCount()+lElecCount);
        }
        voList.add(total);
        return voList;
    }

    private List<ReportPowerGenLoadVo> collectYearMonthApplicationInfo(Map<Long, MatchedKwVo> appIdMatchedKwMap
            , Date runDate, Date yearFirstDay, Date endDate) throws Exception {
        List<Map<String, Object>> info = applicationRepository.findApplicationApplicantTpcCompany(yearFirstDay, endDate);
        if (null == info || info.isEmpty()) return new ArrayList<>();
        Date monthEndDate = getMonthEndDate(runDate);

        List<String> contractStatusList = List.of("刪除", "審查中", "已函覆", "申請中", "已簽約", "運轉中", "已終止");
        Map<String, Integer> contractStatusPWMap = new HashMap<>();
        Map<String, Integer> contractStatusDSMap = new HashMap<>();
        for (String c: contractStatusList) {
            contractStatusPWMap.put(c, 0);
            contractStatusDSMap.put(c, 0);
        }

        List<ReportPowerGenLoadVo> voList = new ArrayList<>();
        List<ReportPowerGenLoadVo> totalList = new ArrayList<>();
        Map<Long, Integer> appIdCount = new HashMap<>();
        int count = 0;
        String keepContract = "";
        for (Map<String, Object> map: info) {
            Long appId = (long)map.get("appId");
            String serviceId = null == map.get("SERVICE_ID")?"":map.get("SERVICE_ID").toString();
            String fatherContract = "";
            String contract = null == map.get("CONTRACT_NO")? "": map.get("CONTRACT_NO").toString();
            if (null != map.get("SERVICE_ID")) { // 補沒有 紀錄 PARENT_ID 的 fatherContract
                if (keepContract.equals(contract)) {
                    ReportPowerGenLoadVo vo = totalList.get(totalList.size()-1);
                    fatherContract = !vo.getFatherContract().isEmpty()? vo.getFatherContract(): fatherContract.isEmpty()? vo.getServiceId(): fatherContract;
                } else keepContract = contract;
                if (fatherContract.isEmpty()) {
                    fatherContract = null == map.get("PARENT_ID") ? "" : null == map.get("PId2") ? map.get("fatherContract").toString()
                            : null == map.get("PId3") ? map.get("pContractNo3").toString() : null == map.get("PId4")
                            ? map.get("pContractNo4").toString() : null == map.get("PId5") ? map.get("pContractNo5").toString() : "";
                }
                // 因應 s1 契約資訊不足, 從 pwoms 取得父契約後, 補上
                fatherContract = s1FatherContractTransformation(fatherContract, contract, serviceId);
            }
            totalList.add(ReportPowerGenLoadVo.builder().serviceId(serviceId).fatherContract(fatherContract).build());
            Date contractedEnd = (Date)map.get("CONTRACTED_END");
            if (null == contractedEnd || contractedEnd.compareTo(yearFirstDay) >= 0) {
                appIdCount.put(appId, count++);
                Integer status = (Integer)map.get("STATUS");
                Integer contractStatus = (Integer)map.get("CONTRACT_STATUS");

                String currentStatus = computeAppCurrentStatus(status, contractStatus, contractedEnd, monthEndDate);

                String pwDs = getPwDs(map, "PWDS");
                if (pwDs.equals("PW")) {
                    int cc = contractStatusPWMap.get(currentStatus);
                    contractStatusPWMap.put(currentStatus, cc + 1);
                } else {
                    int cc = contractStatusDSMap.get(currentStatus);
                    contractStatusDSMap.put(currentStatus, cc + 1);
                }
                MatchedKwVo kwVo = appIdMatchedKwMap.get(appId);
                String matchKw = null == kwVo? "-": null == kwVo.getMonthKw()? "-": bigDecimalShortZero(kwVo.getMonthKw(), false).toPlainString();
                String yearMatchKw = null == kwVo? "-": null == kwVo.getYearKw() ? "-": bigDecimalShortZero(kwVo.getYearKw(), false).toPlainString();
                voList.add(ReportPowerGenLoadVo.builder().index(count).no(null == map.get("NO")? "":map.get("NO").toString())
                        .serviceId(serviceId).tpcMain(null == map.get("TPC_MAIN")?"":map.get("TPC_MAIN").toString())
                        .contractType(null == map.get("CONTRACT_TYPE")?"":map.get("CONTRACT_TYPE").toString()).appGenLoadType("X")
                        .applicantType(null == map.get("APPLICANT_TYPE")?"":map.get("APPLICANT_TYPE").toString())
                        .applName(null == map.get("APPL_NAME")?"":map.get("APPL_NAME").toString()).genCount(0).loadCount(0)
                        .submittedAt(null == map.get("SUBMITTED_AT")? "":map.get("SUBMITTED_AT").toString().split(" ")[0])
                        .allConfirmDate(null == map.get("ALL_CONFIRM_DATE")?"":map.get("ALL_CONFIRM_DATE").toString().split(" ")[0])
                        .contractRequestAt(null == map.get("CONTRACT_REQUEST_AT")?"":map.get("CONTRACT_REQUEST_AT").toString().split(" ")[0])
                        .contractedStart(null == map.get("CONTRACTED_START")?"":map.get("CONTRACTED_START").toString())
                        .onlineAt(null == map.get("ONLINE_AT")?"":map.get("ONLINE_AT").toString())
                        .contractedEnd(null == contractedEnd? "": contractedEnd.toString())
                        .currentStatus(currentStatus).tpcContract(null == map.get("TPC_CONTRACT")?"X":map.get("TPC_CONTRACT").toString())
                        .deliveryMatchedKw("-").transMatchedKw("-").matchedKw(matchKw).yearDeliveryMatchedKw("-").yearTransMatchedKw("-")
                        .yearMatchedKw(yearMatchKw).fatherContract(fatherContract).appId(appId).build());
            }
        }
        voList.add(ReportPowerGenLoadVo.builder().build());
        voList.add(ReportPowerGenLoadVo.builder().contractCapacity("案件狀態").submittedAt("刪除").docsFinDate("審查中")
                .reviewDate("已函覆").allConfirmDate("申請中").contractRequestAt("已簽約").contractedStart("運轉中")
                .onlineAt("已終止").build());
        voList.add(ReportPowerGenLoadVo.builder().contractCapacity("轉供案件數量")
                .submittedAt(contractStatusPWMap.get("刪除").toString()).docsFinDate(contractStatusPWMap.get("審查中").toString())
                .reviewDate(contractStatusPWMap.get("已函覆").toString())
                .allConfirmDate(contractStatusPWMap.get("申請中").toString())
                .contractRequestAt(contractStatusPWMap.get("已簽約").toString())
                .contractedStart(contractStatusPWMap.get("運轉中").toString())
                .onlineAt(contractStatusPWMap.get("已終止").toString()).build());
        voList.add(ReportPowerGenLoadVo.builder().contractCapacity("直供案件數量")
                .submittedAt(contractStatusDSMap.get("刪除").toString()).docsFinDate(contractStatusDSMap.get("審查中").toString())
                .reviewDate(contractStatusDSMap.get("已函覆").toString())
                .allConfirmDate(contractStatusDSMap.get("申請中").toString())
                .contractRequestAt(contractStatusDSMap.get("已簽約").toString())
                .contractedStart(contractStatusDSMap.get("運轉中").toString())
                .onlineAt(contractStatusDSMap.get("已終止").toString()).build());
        return collectGenLoadTpcInfo(appIdCount, voList);
    }

    public String s1FatherContractTransformation(String fatherContract, String contract, String serviceId) {
        // 因應 s1 契約資訊不足, 從 pwoms 取得父契約後, 補上
        if (null != s1FatherContractMaps.get(contract) && !s1FatherContractMaps.get(contract).equals(serviceId)
                && !s1FatherContractMaps.get(contract).equals(fatherContract)) {
            if (fatherContract.isEmpty() || (!fatherContract.isEmpty()
                    && !fatherContract.equals(s1FatherContractMaps.get(contract))))
                fatherContract = s1FatherContractMaps.get(contract);
        }
        return fatherContract;
    }

    private String computeAppCurrentStatus(Integer status, Integer contractStatus, Date contractedEnd, Date monthEndDate) {
        String currentStatus = "審查中";
        if (null != contractStatus) {
            if (2 == contractStatus || 3 == contractStatus) currentStatus = "申請中";
            else if (4 == contractStatus) currentStatus= "已簽約";
            else if (contractStatus > 4) {
                currentStatus = "運轉中";
                if (null != contractedEnd && contractedEnd.compareTo(monthEndDate) < 0) currentStatus = "已終止";
            } else if (null != status) {
                if (-1 == status) currentStatus = "刪除";//else if (1 == status || 2 == status || 3 == status) currentStatus = "審查中";
                else if (4 == status) currentStatus = "已函覆";
            }
        } else if (null != status) {
            if (-1 == status) currentStatus = "刪除";//else if (1 == status || 2 == status || 3 == status) currentStatus = "審查中";
            else if (4 == status) currentStatus = "已函覆";
        }
        return currentStatus;
    }

    // VOLTAGE_LEVEL = (未確定) 輸配電類型 暫時分配為 (未定)
    private List<ReportPowerGenLoadVo> collectGenLoadTpcInfo(Map<Long, Integer> appIdCount
            , List<ReportPowerGenLoadVo> voList) throws Exception {
        List<Long> appIds = new ArrayList<>(appIdCount.keySet());

        int size = appIds.size();
        int subListNum = (size + sqlCheckSum - 1) / sqlCheckSum;
        List<List<String>> chunks = new ArrayList<>();
        List<Map<String, Object>> infos = new ArrayList<>();

        for (int i =0; i < subListNum; i++) {
            List<Long> subList = appIds.subList(i * sqlCheckSum, ((i + 1) * sqlCheckSum > size ? size : sqlCheckSum * (i+1)));
            if (infos.isEmpty()) {
                infos = applicationGeneratorRepository.findGenLoadCountInfo(subList);
            } else {
                List<Map<String, Object>> infos2 = applicationGeneratorRepository.findGenLoadCountInfo(subList);
                if (!infos2.isEmpty()) infos.addAll(infos2);
            }
        }

        Map<Long, Integer> pAddIdCountMap = new HashMap<>();
        for (Map<String, Object> m: infos) {
            Long appId = (Long)m.get("APPLICATION_ID");
            Long generatorId = (Long)m.get("GENERATOR_ID");
            Long loadId = (Long)m.get("LOAD_ID");
            Integer count = (Integer)m.get("count");
            String entityName = null == m.get("entityName")? "": m.get("entityName").toString();
            String codeType = null == m.get("codeType")? "": m.get("codeType").toString();
            String voltLevel = null == m.get("LABEL")?"": m.get("LABEL").toString();
            String tpcName = null == m.get("UNIT_NAME")?"": m.get("UNIT_NAME").toString();
            String dbCap = null == m.get("cap")? "": m.get("cap").toString();
            String cap = (null != dbCap && !dbCap.isEmpty())? bigDecimalShortZero(new BigDecimal(dbCap), false).toPlainString(): "";
            ReportPowerGenLoadVo vo = voList.get(appIdCount.get(appId));
            String ll = (null != voltLevel && !voltLevel.isEmpty())?voltLevel.equals(lowVoltageLevel)?"11.4":voltLevel.replace("kV", "").replace("不滿", ""):"";
            float llInt = ll.isEmpty()?0:ll.contains(UNSURE_VOLTAGE_LEVEL)?-1:Float.parseFloat(ll);
            String matchedKw = vo.getMatchedKw();
            String yearMatchedKw = vo.getYearMatchedKw();
            String genLoadCountStr = "";
            if (null != loadId) {
                String cc = null == vo.getGenLoadCountStr()? "":vo.getGenLoadCountStr();
                genLoadCountStr = "";
                int loadCount = 0;
                Integer genCount = vo.getGenCount();
                String dd = null == vo.getGenTransDelivery()?"":vo.getGenTransDelivery();
                String loadTransDelivery = llInt== 0? "": llInt<0? ll: (llInt -69) < 0? "配電":"輸電";
                if (null != count) {
                    loadCount = count;
                    if (1 == count) {
                        genLoadCountStr = cc+"對一";
                        if (loadTransDelivery.equals("輸電")) {
                            vo.setTransMatchedKw(matchedKw);
                            vo.setYearTransMatchedKw(yearMatchedKw);
                        } else if (loadTransDelivery.equals("配電")) {
                            vo.setDeliveryMatchedKw(matchedKw);
                            vo.setYearDeliveryMatchedKw(yearMatchedKw);
                        }
                        if (null != genCount && !loadTransDelivery.isEmpty() && !dd.isEmpty() && !loadTransDelivery.equals(dd)) {
                            vo.setTransMatchedKw(matchedKw);
                            vo.setDeliveryMatchedKw(matchedKw);
                            vo.setYearTransMatchedKw(yearMatchedKw);
                            vo.setYearDeliveryMatchedKw(yearMatchedKw);
                            if (loadTransDelivery.equals(UNSURE_VOLTAGE_LEVEL) || dd.equals(UNSURE_VOLTAGE_LEVEL))
                                vo.setAppGenLoadType(UNSURE_VOLTAGE_LEVEL);
                            else
                                vo.setAppGenLoadType("輸配混合");
                        }
                    } else if (count > 1) {
                        genLoadCountStr = cc+"對多";
                        pAddIdCountMap.put(appId, appIdCount.get(appId));
                        entityName = entityName.length() > 3 ? entityName+" 等": entityName;
                        codeType = codeType.length() > 2 ? codeType+" 等": codeType;
                        voltLevel = (null != voltLevel && voltLevel.length() > 2) ? voltLevel+" 等": voltLevel;
                        tpcName = tpcName.length() > 2 ? tpcName+" 等": tpcName;
                    }
                }
                vo.setLoadCount(loadCount);
                vo.setLoadTransDelivery(loadTransDelivery);
                vo.setCustName(entityName);
                vo.setIndustryName(codeType);
                vo.setLoadVoltLevel(voltLevel);
                vo.setLoadTpcName(tpcName);
                vo.setContractCapacity(cap);
                vo.setLoadId(loadId);
                vo.setAppGenLoadType(appGenLoadTransDeliverType(dd, loadTransDelivery));
            } else if (null != generatorId) {
                String cc = null == vo.getGenLoadCountStr()? "":vo.getGenLoadCountStr();
                Integer loadCount = vo.getLoadCount();
                String dd = null == vo.getLoadTransDelivery()?"":vo.getLoadTransDelivery();
                genLoadCountStr = "";
                int genCount = 0;
                String genTransDelivery = llInt== 0? "" : llInt<0? ll: (llInt -69) < 0? "配電":"輸電";
                if (null != count) {
                    genCount = count;
                    if (1 == count) {
                        genLoadCountStr = "一"+cc;
                        if (genTransDelivery.equals("輸電")) {
                            vo.setTransMatchedKw(matchedKw);
                            vo.setYearTransMatchedKw(yearMatchedKw);
                        } else if (genTransDelivery.equals("配電")) {
                            vo.setDeliveryMatchedKw(matchedKw);
                            vo.setYearDeliveryMatchedKw(yearMatchedKw);
                        }
                        if (null != loadCount && !genTransDelivery.isEmpty() && !dd.isEmpty() && !genTransDelivery.equals(dd)) {
                            vo.setTransMatchedKw(matchedKw);
                            vo.setDeliveryMatchedKw(matchedKw);
                            vo.setYearTransMatchedKw(yearMatchedKw);
                            vo.setYearDeliveryMatchedKw(yearMatchedKw);
                            if (genTransDelivery.equals(UNSURE_VOLTAGE_LEVEL) || dd.equals(UNSURE_VOLTAGE_LEVEL))
                                vo.setAppGenLoadType(UNSURE_VOLTAGE_LEVEL);
                            else
                                vo.setAppGenLoadType("輸配混合");
                        }
                    } else if (count > 1) {
                        genLoadCountStr = "多"+cc;
                        pAddIdCountMap.put(appId, appIdCount.get(appId));
                        entityName = entityName.length() > 3 ? entityName+" 等": entityName;
                        codeType = codeType.length() > 2 ? codeType+" 等": codeType;
                        voltLevel = (null != voltLevel && voltLevel.length() > 2) ? voltLevel+" 等": voltLevel;
                        tpcName = tpcName.length() > 2 ? tpcName+" 等": tpcName;
                    }
                }
                vo.setGenCount(genCount);
                vo.setGenTransDelivery(genTransDelivery);
                vo.setGenName(entityName);
                vo.setEnergyType(codeType);
                vo.setGenVoltLevel(voltLevel);
                vo.setGenTpcName(tpcName);
                vo.setLicenseCapacity(cap);
                vo.setGeneratorId(generatorId);
                vo.setAppGenLoadType(appGenLoadTransDeliverType(genTransDelivery, dd));
            }
            vo.setGenLoadCountStr(genLoadCountStr);
        }
        return collectApplicationSettleAllInfo(appIdCount, voList, pAddIdCountMap);
    }

    private List<ReportPowerGenLoadVo> collectApplicationSettleAllInfo(Map<Long, Integer> appIdCount
            , List<ReportPowerGenLoadVo> voList, Map<Long, Integer> pAddIdCountMap) throws Exception {

        List<Long> applicationIds = new ArrayList<>(pAddIdCountMap.keySet());
        List<Map<String, Object>> td = applicationGeneratorRepository
                .findMaxApplicationGeneratorLoadTypeByApplicationIds(applicationIds);
        for (Map<String, Object> tt:td) {
            Long appId = (Long)tt.get("ID");
            int ty = (int)tt.get("TYPE");
            ReportPowerGenLoadVo vo = voList.get(appIdCount.get(appId));
            int loadCount = vo.getLoadCount();
            int genCount = vo.getGenCount();
            String matchedKw = vo.getMatchedKw();
            String yearMatchedKw = vo.getYearMatchedKw();
            String transDelivery = "";
            if (1 == ty) {
                transDelivery = "輸電";
                vo.setTransMatchedKw(matchedKw);
                vo.setYearTransMatchedKw(yearMatchedKw);
            } else if (2 == ty) {
                transDelivery = "配電";
                vo.setDeliveryMatchedKw(matchedKw);
                vo.setYearDeliveryMatchedKw(yearMatchedKw);
            } else if (3 == ty) {
                transDelivery = "輸配混合";
                vo.setTransMatchedKw(matchedKw);
                vo.setDeliveryMatchedKw(matchedKw);
                vo.setYearTransMatchedKw(yearMatchedKw);
                vo.setYearDeliveryMatchedKw(yearMatchedKw);
            }
            if (loadCount > 1) vo.setLoadTransDelivery(transDelivery);
            if (genCount > 1) vo.setGenTransDelivery(transDelivery);
            vo.setAppGenLoadType(transDelivery);
        }
        return voList;
    }

    private List<ReportGen15TranReSummaryVo> collectAmi15PowerSummary(Map<String, Integer> elecNoCount
            , Map<Long, Integer> apGenIdCount, List<ReportGen15TranReSummaryVo> voList
            , Date startDate, Date endDate, Date billDate, boolean current) {
        //List<String> elecNos = new ArrayList<>(elecNoCount.keySet());
        List<Map<String, Object>> amiSum = amiService.getSumAmi15PowerSummery(AMI_GEN_CHANNEL, startDate, endDate); // 發電端 3 -> 1
        if (null == amiSum || amiSum.isEmpty()) return new ArrayList<>();

        for (Map<String, Object> el: amiSum) {
            String elecNo = el.get("elecNo").toString();
            BigDecimal load = (BigDecimal) el.get("load");
            if (null != elecNoCount.get(elecNo)) {
                ReportGen15TranReSummaryVo vo = voList.get(elecNoCount.get(elecNo));
                vo.setSumAmi(bigDecimalShortZero(load, null).toPlainString());
            }
        }

        List<Map<String, Object>> powerSum = !current? applicationMonthlyCapacityRecordRepository
                .sumGenMatchedRmUnmatchedRmByDate(billDate): applicationMonthlyCapacitySettlementRepository
                .sumGenMatchedRmUnmatchedRmByDate(billDate);
        if (null == powerSum || powerSum.isEmpty()) return voList;

        for(Map<String, Object> ps: powerSum) {
            Long apGenId = (long)ps.get("applicationGeneratorId");
            String unMatched = bigDecimalShortZero((BigDecimal) ps.get("unmatchedRm"), null).toPlainString();
            String matched = bigDecimalShortZero((BigDecimal) ps.get("matchedRm"), null).toPlainString();
            if (null != apGenIdCount.get(apGenId)) {
                ReportGen15TranReSummaryVo vo = voList.get(apGenIdCount.get(apGenId));
                String matchedKw = vo.getMatchedKw();
                if (!matched.isEmpty()) matchedKw = bigDecimalShortZero(new BigDecimal(matchedKw).subtract(new BigDecimal(matched)), null).toPlainString();
                vo.setMatchedKw(matchedKw);
                vo.setSumPWDS(matchedKw);
                vo.setUnmatchedRm(unMatched);
            }
        }
        return voList;
    }

    private List<ReportGenLoad15powerVo> collectAmi15PowerByMonthDate(List<String> elecNos, Integer channel
            , List<Date> runDates) throws Exception {

        String channelStr = channel == AMI_GEN_CHANNEL? "發電量加總": "用電量加總"; // 發電端 3 -> 1

        List<ReportGenLoad15powerVo> voList = new ArrayList<>();
        Map<String, Integer> dateCount = new HashMap<>();
        int idx = 0;
        Set<String> keepKeyStr = new HashSet<>();
        List<String> keepKeyList = new ArrayList<>();
        for (int j = 0; j < runDates.size(); j++) {
            int monthDays = getDateOfMonth(runDates.get(j));
            String yearMonth = yearMonthStr(runDates.get(j));
            for (int i = 0; i < monthDays; i++) {
                String ddd = yearMonth + String.format("%02d", i + 1);
                voList.add(ReportGenLoad15powerVo.builder().channel(channelStr).date(ddd).loads(comma95Str).build());
                dateCount.put(ddd, idx);
                idx ++;
            }
            Date start = runDates.get(j);
            Date end = getNextMonthFirstDate(start);
            List<Map<String, Object>> ami15 = amiService.getAmiLoad96InvTime(elecNos, channel, start, end);
            Set<String> dateStrSet = new HashSet<>();
            List<String> timeList = new ArrayList<>();
            List<String> loads = new ArrayList<>();
            String dd = "";

            for (Map<String, Object> ami : ami15) {
                String load = ((BigDecimal) ami.get("load")).stripTrailingZeros().toPlainString();
                Date time = (Date)ami.get("time");
                dd = ami.get("time").toString().split(" ")[0];
                String timeInv = new SimpleDateFormat(timeFormat).format(time);
                String dateTimeInv = dd + " " + timeInv;

                if (keepKeyStr.add(dd)) {
                    if (!loads.isEmpty()) {
                        ReportGenLoad15powerVo vo = voList.get(dateCount.get(keepKeyList.get(keepKeyList.size()-1)));
                        vo.setLoads(collectLoads(loads, timeList));
                        loads = new ArrayList<>();
                        timeList = new ArrayList<>();
                        dateStrSet.clear();
                    }
                    keepKeyList.add(dd);
                    timeList.add(timeInv);
                    loads.add(load);
                    dateStrSet.add(dateTimeInv);
                }
                else if (dateStrSet.add(dateTimeInv)) {
                    timeList.add(timeInv);
                    loads.add(load);
                }
            }
            ReportGenLoad15powerVo vo = voList.get(dateCount.get(dd));
            vo.setLoads(collectLoads(loads, timeList));
        }
        return voList;
    }

    // 目前僅支援單年度計算
    private List<ReportVoltVo> collectVoltageLevel(List<String> voltLevel, Date billDate) throws Exception {
        Map<String, BigDecimal[]> voList = new HashMap<>();
        List<String> vLevel = new ArrayList<>();
        for (String s: voltLevel) {
            if (!s.equals(UNSURE_VOLTAGE_LEVEL)) {
                BigDecimal[] rate = {zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,
                        zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal};
                voList.put(s, rate);
                vLevel.add(s);
            }
        }

        List<Map<String, Object>> info = applicationMonthlyCapacityRecordRepository.findExpVoltInfoByServiceDates(billDate);
        if (checkCurrentYearMonth(billDate)) {
            info = applicationMonthlyCapacitySettlementRepository.findExpVoltInfoByServiceDates(billDate);
        }
        if (null == info || info.isEmpty()) return new ArrayList<>();
        for (Map<String, Object> m : info) {
            Date serviceDate = (Date) m.get("serviceDate");
            String key = m.get("EQUIP_VOLT_LEVEL").toString();
            BigDecimal kwh = (BigDecimal) m.get("KWH");
            int monthIdx = getMonth(serviceDate) - 1;
            if (null != voList.get(key)) {
                BigDecimal[] addRate = voList.get(key);
                addRate[monthIdx] = addRate[monthIdx].add(kwh);
                voList.put(key, addRate);
            }
        }
        return sumYearVoltageLevel(vLevel, voList);
    }

    private List<ReportVoltVo> sumYearVoltageLevel(List<String> voltLevel, Map<String, BigDecimal[]> voList) throws Exception {
        BigDecimal[] rate = {zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,
                zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal,zeroBigDecimal};
        List<ReportVoltVo> ovoList = new ArrayList<>();
        for (String s : voltLevel) {
            BigDecimal voltKwh = zeroBigDecimal;
            String monthKwhStr = "";
            for (int i=0;i<rate.length;i++) { // one year = 12 months
                BigDecimal mRate = bigDecimalShortZero(voList.get(s)[i], null);
                voltKwh = voltKwh.add(mRate);
                rate[i] = rate[i].add(mRate);
                monthKwhStr = monthKwhStr + (mRate.toString().equals("0")? "," : bigDecimalShortZero(mRate, null).toPlainString()+",");
            }
            ovoList.add(ReportVoltVo.builder().voltageLevel(s)
                    .monthKwh(monthKwhStr.substring(0, monthKwhStr.length() - 1))
                    .total(bigDecimalShortZero(voltKwh, null).toPlainString()).build());
        }
        String monthKwhStr = "";
        BigDecimal voltKwh = zeroBigDecimal;
        for (int i=0;i<rate.length;i++) { // one year = 12 months
            voltKwh = rate[i].add(voltKwh);
            monthKwhStr = monthKwhStr + (rate[i].toString().equals("0")? "," : bigDecimalShortZero(rate[i], null).toPlainString()+",");
        }
        ovoList.add(ReportVoltVo.builder().voltageLevel("總計(度)")
                .monthKwh(monthKwhStr.substring(0, monthKwhStr.length() - 1))
                .total(bigDecimalShortZero(voltKwh, null).toPlainString()).build());
        return ovoList;
    }

    private List<ReportGreenContractVo> getContractsInfoByFixType(String type, Date serviceStart, Date serviceEnd) {
        List<Map<String, Object>> info = applicationMonthlyCapacityRecordRepository.findApplicationGeneratorLoadContractsByFixType(serviceStart, serviceEnd);
        if (null == info || info.isEmpty()) return new ArrayList<>();

        List<ReportGreenContractVo> voList = new ArrayList<>();
        Map<String, String> contractNosMap = new HashMap<>();
        Set<String> apGeLoIdSet = new HashSet<>();
        int count = 0;
        for (Map<String, Object> io : info) {
            Long apGenId = (Long) io.get("applicationGeneratorId");
            Long apLoadId = (Long) io.get("applicationLoadId");
            if (apGeLoIdSet.add(apGenId+"~"+apLoadId)) {
                String serviceId = (String) io.get("SERVICE_ID");
                String contractNo = (String) io.get("contractNo");
                String version = (String) io.get("version");
                if (!version.equals("00")) {
                    if (contractNosMap.containsKey(contractNo)) {
                        String ids = contractNosMap.get(contractNo)+"," +Integer.toString(count);
                        contractNosMap.put(contractNo, ids);
                    } else {
                        contractNosMap.put(contractNo, Integer.toString(count));
                    }
                }
                voList.add(ReportGreenContractVo.builder().serviceId(serviceId)
                        .contractExecUnit(null == io.get("CONTRACT_EXEC_UNIT")? "":io.get("CONTRACT_EXEC_UNIT").toString())
                        .contractEffeDate(null == io.get("CONTRACT_EFFE_DATE")? "":io.get("CONTRACT_EFFE_DATE").toString())
                        .terminateDate(null == io.get("TERMINATE_DATE")? "":io.get("TERMINATE_DATE").toString())
                        .fatherContract(serviceId).genElecNo(io.get("GEN_ELEC_NO").toString())
                        .genPwPercent(null == io.get("GEN_PW_PERCENT")? "":io.get("GEN_PW_PERCENT").toString())
                        .deviceCapacity(null == io.get("DEVICE_CAPACITY")? "":io.get("DEVICE_CAPACITY").toString())
                        .elecNo(null == io.get("ELEC_NO")? "":io.get("ELEC_NO").toString())
                        .custName(null == io.get("CUST_NAME")? "":io.get("CUST_NAME").toString())
                        .moContractKwh(null == io.get("MO_CONTRACT_KWH")? "":io.get("MO_CONTRACT_KWH").toString())
                        .yrContractKwh(null == io.get("YR_CONTRACT_KWH")? "":io.get("YR_CONTRACT_KWH").toString())
                        .applicationGeneratorId(apGenId).applicationLoadId(apLoadId)
                        .build());
                count ++;
            }
        }
        return getFatherContractList(contractNosMap, voList);
    }

    private List<ReportGreenContractVo> getFatherContractList(Map<String, String> contractNosMap, List<ReportGreenContractVo> voList) {
        List<String> contractNos = new ArrayList<>(contractNosMap.keySet());
        List<Map<String, Object>> fathers = applicationRepository.findFatherContractListByContractNos(contractNos);
        Set<String> contractSet = new HashSet<>();
        for (Map<String, Object> father : fathers) {
            String contractNo = (String) father.get("CONTRACT_NO");
            String ids = contractNosMap.get(contractNo);
            if (!ids.isEmpty() && contractSet.add(contractNo)) {
                String[] dd = ids.split(",");
                String fatherContract = null == father.get("FATHER_CONTRACT")? "": father.get("FATHER_CONTRACT").toString();
                String fatherContractTerminateDate = null == father.get("FATHER_CONTRACT_TERMINATE_DATE")? "": father.get("FATHER_CONTRACT_TERMINATE_DATE").toString();
                for (int i = 0; i < dd.length; i++) {
                    ReportGreenContractVo vo = voList.get(Integer.parseInt(dd[i]));
                    vo.setFatherContract(fatherContract);
                    vo.setFatherContractTerminateDate(fatherContractTerminateDate);
                }
            }
        }
        return voList;
    }

    private List<ReportGreenVo> getMonthlyGeneratorMatchReMatchInfo(String type, Date billStart, Date billEnd) throws Exception {
        List<Map<String, Object>> match = applicationMonthlyGeneratorRecordRepository
                .findMatchedByServiceDateRange(billStart, billEnd);
        List<ReportGreenVo> voList = new ArrayList<>();
        Map<String, Integer> memServiceDateGeLoIdx = new HashMap<>();
        String[] mValue = new String[]{"","","",""};
        int count = 0;
        for (Map<String, Object> m : match) {
            Date serviceDate = (Date) m.get("serviceDate");
            Long apGenId = (Long) m.get("applicationGeneratorId");
            Long apLoadId = (Long) m.get("applicationLoadId");
            Short energyChangeSectionId = Short.valueOf(m.get("energyChangeSectionId").toString());
            String mm = null == m.get("matchedRm")?"": bigDecimalShortZero((BigDecimal) m.get("matchedRm"), null).toPlainString();
            String key = serviceDate+"~"+apGenId+"~"+apLoadId;
            if (energyChangeSectionId.intValue() == 1) mValue[0] = mm;
            else if (energyChangeSectionId.intValue() == 3) mValue[1] = mm;
            else if (energyChangeSectionId.intValue() == 9) mValue[2] = mm;
            else mValue[3] = mm; // 11
            if (energyChangeSectionId.intValue() == 11) {
                String kwh = sumOne13911(Kwhs13911Vo.builder().table1(mValue[0]).table3(mValue[1]).table9(mValue[2]).table11(mValue[3]).build());
                voList.add(ReportGreenVo.builder().kwh(kwh).meterType01(mValue[0]).meterType03(mValue[1]).meterType09(mValue[2])
                        .meterType11(mValue[3]).serviceDate(serviceDate).applicationGeneratorId(apGenId).applicationLoadId(apLoadId).build());
                memServiceDateGeLoIdx.put(key, count++);
                Arrays.fill(mValue, "");
            }
        }
        if (voList.isEmpty()) return new ArrayList<>();
        return runMonthlySettlementBillingReport(type, voList, memServiceDateGeLoIdx, billStart, billEnd);
    }

    private List<ReportGreenVo> runMonthlySettlementBillingReport(String type, List<ReportGreenVo> voList
            , Map<String, Integer> memServiceDateGeLoIdx, Date billStart, Date billEnd) throws Exception {

        List<Map<String, Object>> settlementList = applicationMonthlyCapacityRecordRepository
                .findApplicationGeneratorLoadSettleInfoByFixType(billStart, billEnd);
        if (null == settlementList || settlementList.isEmpty()) return new ArrayList<>();

        Map<Integer, KwhsASDTVo> yearRate = getYearFuelCodeRate(getYear((Date)settlementList.get(0).get("serviceDate"))
                , getYear((Date)settlementList.get(settlementList.size()-1).get("serviceDate")));
        Date firstServiceDate = null;
        Date lastServiceDate = null;

        List<ReportGreenVo> ovoList = new ArrayList<>();
        Set<String> serviceDateSet = new HashSet<>();

        for (Map<String, Object> stm : settlementList) {
            Long apGenId = (Long) stm.get("applicationGeneratorId");
            Long apLoadId = (Long) stm.get("applicationLoadId");
            Date tmp = (Date) stm.get("serviceDate");
            String memoKey = tmp + "~" + apGenId + "~" + apLoadId;

            if (serviceDateSet.add(memoKey)) {
                int serviceYear = 0;
                int serviceMonth = 0;
                int billYear = 0;
                int billMonth = 0;
                String serviceDate = tmp.toString();
                firstServiceDate = firstServiceDate == null || tmp.compareTo(firstServiceDate) <= 0 ? tmp : firstServiceDate;
                lastServiceDate = lastServiceDate == null || tmp.compareTo(lastServiceDate) >= 0 ? tmp : lastServiceDate;
                if (null != serviceDate && !serviceDate.isEmpty() && serviceDate.contains("-")) {
                    String[] dd = serviceDate.split("-");
                    if (dd.length > 1) {
                        serviceYear = Integer.parseInt(dd[0]);
                        serviceMonth = Integer.parseInt(dd[1]);
                    }
                }
                String billDate = stm.get("billDate").toString();
                if (null != billDate && !billDate.isEmpty() && billDate.contains("-")) {
                    String[] dd = billDate.split("-");
                    if (dd.length > 1) {
                        billYear = Integer.parseInt(dd[0]);
                        billMonth = Integer.parseInt(dd[1]);
                    }
                }
                int monthEnd = getDateOfMonth(tmp);
                String expPeriod = serviceYear+"/"+serviceMonth+"/01 ~ "+serviceYear+"/"+serviceMonth+"/"+monthEnd;
                String nextReadDate = getAfterTwoMonthFirstDateStr(tmp);
                String serviceId = stm.get("SERVICE_ID").toString();
                String sinkElecNo = stm.get("SINK_ELEC_NO").toString();
                String sinkMeterNo = null == stm.get("oLMeterNext") ? stm.get("SINK_METER_NO").toString(): stm.get("oLMeterNext").toString();
                String sourceElecNo = stm.get("SOURCE_ELEC_NO").toString();
                String sourceMeterNo = stm.get("SOURCE_METER_NO").toString();
                String contractExecUnit = null == stm.get("CONTRACT_EXEC_UNIT")? "": stm.get("CONTRACT_EXEC_UNIT").toString();
                /*BigDecimal dExp = bigDecimalShortZero((BigDecimal)stm.get("D_EXP"), false);
                BigDecimal tExp = bigDecimalShortZero((BigDecimal)stm.get("T_EXP"), false);
                BigDecimal aExp = bigDecimalShortZero((BigDecimal)stm.get("A_EXP"), false);
                BigDecimal sExp = bigDecimalShortZero((BigDecimal)stm.get("S_EXP"), false);*/
                BigDecimal dExpVo = bigDecimalShortZero((BigDecimal)stm.get("D_EXP"), null);
                BigDecimal tExpVo = bigDecimalShortZero((BigDecimal)stm.get("T_EXP"), null);
                BigDecimal aExpVo = bigDecimalShortZero((BigDecimal)stm.get("A_EXP"), null);
                BigDecimal sExpVo = bigDecimalShortZero((BigDecimal)stm.get("S_EXP"), null);
                String KWH = null == stm.get("KWH")? null: bigDecimalShortZero((BigDecimal) stm.get("KWH"), null).toPlainString();
                if (null != memServiceDateGeLoIdx.get(memoKey)) {
                    ReportGreenVo vo = voList.get(memServiceDateGeLoIdx.get(memoKey));
                    KwhsASDTVo kVo = yearRate.get(serviceYear);
                    if (null == vo) {
                        ovoList.add(ReportGreenVo.builder().serviceId(serviceId).sinkElecNo(sinkElecNo).sinkMeterNo(sinkMeterNo)
                                .sourceElecNo(sourceElecNo).sourceMeterNo(sourceMeterNo).billYear(billYear).billMonth(billMonth)
                                .serviceYear(serviceYear).serviceMonth(serviceMonth).equipFuelType("1").expType("A").kwh(KWH)
                                .expRate(kVo.getAExp().toString()).exp(aExpVo.toPlainString()).expPeriod(expPeriod).nextReadDate(nextReadDate)
                                .mergeToServiceId(serviceId).contractExecUnit(contractExecUnit).serviceDate(tmp)
                                .applicationGeneratorId(apGenId).applicationLoadId(apLoadId).build()); //A
                        ovoList.add(ReportGreenVo.builder().serviceId(serviceId).sinkElecNo(sinkElecNo).sinkMeterNo(sinkMeterNo)
                                .sourceElecNo(sourceElecNo).sourceMeterNo(sourceMeterNo).billYear(billYear).billMonth(billMonth)
                                .serviceYear(serviceYear).serviceMonth(serviceMonth).equipFuelType("1").expType("D").kwh(KWH)
                                .expRate(kVo.getDExp().toString()).exp(dExpVo.toPlainString()).expPeriod(expPeriod).nextReadDate(nextReadDate)
                                .mergeToServiceId(serviceId).contractExecUnit(contractExecUnit).serviceDate(tmp)
                                .applicationGeneratorId(apGenId).applicationLoadId(apLoadId).build()); //D
                        ovoList.add(ReportGreenVo.builder().serviceId(serviceId).sinkElecNo(sinkElecNo).sinkMeterNo(sinkMeterNo)
                                .sourceElecNo(sourceElecNo).sourceMeterNo(sourceMeterNo).billYear(billYear).billMonth(billMonth)
                                .serviceYear(serviceYear).serviceMonth(serviceMonth).equipFuelType("1").expType("S").kwh(KWH)
                                .expRate(kVo.getSExp().toString()).exp(sExpVo.toPlainString()).expPeriod(expPeriod).nextReadDate(nextReadDate)
                                .mergeToServiceId(serviceId).contractExecUnit(contractExecUnit).serviceDate(tmp)
                                .applicationGeneratorId(apGenId).applicationLoadId(apLoadId).build()); //S
                        ovoList.add(ReportGreenVo.builder().serviceId(serviceId).sinkElecNo(sinkElecNo).sinkMeterNo(sinkMeterNo)
                                .sourceElecNo(sourceElecNo).sourceMeterNo(sourceMeterNo).billYear(billYear).billMonth(billMonth)
                                .serviceYear(serviceYear).serviceMonth(serviceMonth).equipFuelType("1").expType("T").kwh(KWH)
                                .expRate(kVo.getTExp().toString()).exp(tExpVo.toPlainString()).expPeriod(expPeriod).nextReadDate(nextReadDate)
                                .mergeToServiceId(serviceId).contractExecUnit(contractExecUnit).serviceDate(tmp)
                                .applicationGeneratorId(apGenId).applicationLoadId(apLoadId).build()); //T
                    } else {
                        String meterType01 = vo.getMeterType01();
                        String meterType03 = vo.getMeterType03();
                        String meterType09 = vo.getMeterType09();
                        String meterType11 = vo.getMeterType11();
                        ovoList.add(ReportGreenVo.builder().serviceId(serviceId).sinkElecNo(sinkElecNo).sinkMeterNo(sinkMeterNo)
                                .sourceElecNo(sourceElecNo).sourceMeterNo(sourceMeterNo).billYear(billYear).billMonth(billMonth)
                                .serviceYear(serviceYear).serviceMonth(serviceMonth).equipFuelType("1").expType("A").kwh(KWH)
                                .expRate(kVo.getAExp().toString()).exp(aExpVo.toPlainString()).expPeriod(expPeriod).nextReadDate(nextReadDate)
                                .mergeToServiceId(serviceId).contractExecUnit(contractExecUnit).meterType01(meterType01)
                                .meterType03(meterType03).meterType09(meterType09).meterType11(meterType11).serviceDate(tmp)
                                .applicationGeneratorId(apGenId).applicationLoadId(apLoadId).build()); //A
                        ovoList.add(ReportGreenVo.builder().serviceId(serviceId).sinkElecNo(sinkElecNo).sinkMeterNo(sinkMeterNo)
                                .sourceElecNo(sourceElecNo).sourceMeterNo(sourceMeterNo).billYear(billYear).billMonth(billMonth)
                                .serviceYear(serviceYear).serviceMonth(serviceMonth).equipFuelType("1").expType("D").kwh(KWH)
                                .expRate(kVo.getDExp().toString()).exp(dExpVo.toPlainString()).expPeriod(expPeriod).nextReadDate(nextReadDate)
                                .mergeToServiceId(serviceId).contractExecUnit(contractExecUnit).meterType01(meterType01)
                                .meterType03(meterType03).meterType09(meterType09).meterType11(meterType11).serviceDate(tmp)
                                .applicationGeneratorId(apGenId).applicationLoadId(apLoadId).build()); //D
                        ovoList.add(ReportGreenVo.builder().serviceId(serviceId).sinkElecNo(sinkElecNo).sinkMeterNo(sinkMeterNo)
                                .sourceElecNo(sourceElecNo).sourceMeterNo(sourceMeterNo).billYear(billYear).billMonth(billMonth)
                                .serviceYear(serviceYear).serviceMonth(serviceMonth).equipFuelType("1").expType("S").kwh(KWH)
                                .expRate(kVo.getSExp().toString()).exp(sExpVo.toPlainString()).expPeriod(expPeriod).nextReadDate(nextReadDate)
                                .mergeToServiceId(serviceId).contractExecUnit(contractExecUnit).meterType01(meterType01)
                                .meterType03(meterType03).meterType09(meterType09).meterType11(meterType11).serviceDate(tmp)
                                .applicationGeneratorId(apGenId).applicationLoadId(apLoadId).build()); //S
                        ovoList.add(ReportGreenVo.builder().serviceId(serviceId).sinkElecNo(sinkElecNo).sinkMeterNo(sinkMeterNo)
                                .sourceElecNo(sourceElecNo).sourceMeterNo(sourceMeterNo).billYear(billYear).billMonth(billMonth)
                                .serviceYear(serviceYear).serviceMonth(serviceMonth).equipFuelType("1").expType("T").kwh(KWH)
                                .expRate(kVo.getTExp().toString()).exp(tExpVo.toPlainString()).expPeriod(expPeriod).nextReadDate(nextReadDate)
                                .mergeToServiceId(serviceId).contractExecUnit(contractExecUnit).meterType01(meterType01)
                                .meterType03(meterType03).meterType09(meterType09).meterType11(meterType11).serviceDate(tmp)
                                .applicationGeneratorId(apGenId).applicationLoadId(apLoadId).build()); //T
                    }
                }
            }
        }
        return ovoList;
    }

    private List<ReportGen15TranReVo> collectAmiSettle15RecordDateSum(Map<String, String> elecNoContractPwDsMap
            , List<Date> serviceDateList, Map<Date, List<String>> serviceElecMap) throws Exception {

        List<ReportGen15TranReVo> voListAll = new ArrayList<>();
        for (int j = 0; j < serviceDateList.size(); j++) {
            Date startDate = serviceDateList.get(j);
            int monthDays = getDateOfMonth(startDate);
            String yearMonth = yearMonthStr(startDate);
            int count = 0;
            BigDecimal acc = zeroBigDecimal;
            String dd = "";
            Set<String> keepKeyStr = new HashSet<>();
            Set<String> dateStrSet = new HashSet<>();
            List<String> timeList = new ArrayList<>();
            List<String>dateStrList = new ArrayList<>();
            List<String> loads = new ArrayList<>();
            List<String> elecNos = serviceElecMap.get(startDate);
            List<Map<String, Object>> ami15 = amiService.getAmiSettlement15Capacity(elecNos, 1, startDate, getNextMonthFirstDate(startDate)); // 發電端 3 -> 1
            List<ReportGen15TranReVo> voList = new ArrayList<>();
            for (Map<String, Object> ami: ami15) {
                String genElecNo = ami.get("GEN_ELEC_NO").toString();
                String meterNo = ami.get("METER_NO").toString();
                String emNo = genElecNo + "~" + meterNo;
                Date time = (Date) ami.get("time");
                dd = dateStr(time);
                if (keepKeyStr.add(emNo)) {
                    if (loads.size() == daily15Inv) {
                        ReportGen15TranReVo vo = voList.get(get96DayInv(dateStrList.get(dateStrList.size() - 1), count, monthDays));
                        vo.setLoads(collectLoads(loads, timeList));
                        vo.setKwh(bigDecimalShortZero(acc, false).toPlainString());
                        loads = new ArrayList<>();
                        dateStrSet.clear();
                        dateStrList.clear();
                        timeList.clear();
                        acc = zeroBigDecimal;
                    }
                    String[] ccc = elecNoContractPwDsMap.get(genElecNo).split("~");
                    for (int i = 0; i < monthDays; i++) {
                        String ddd = yearMonth + String.format("%02d", i + 1);
                        voList.add(ReportGen15TranReVo.builder().serviceId(ccc[0]).elecNo(genElecNo).meterNo(meterNo)
                                .date(ddd).pwrOrUser("D").loads(comma95Str)
                                .pwDs(ccc[1]).applicationGeneratorId(ccc[2]).build());
                    }
                    count ++;
                }
                if (dateStrSet.add(dd)) {
                    timeList.add(new SimpleDateFormat(timeFormat).format(time));
                    if (loads.size() == daily15Inv) {
                        ReportGen15TranReVo vo = voList.get(get96DayInv(dateStrList.get(dateStrList.size()-1), count, monthDays));
                        vo.setLoads(collectLoads(loads, timeList));
                        vo.setKwh(bigDecimalShortZero(acc, false).toPlainString());
                        loads = new ArrayList<>();
                        acc = zeroBigDecimal;
                    }
                    dateStrList.add(dd);
                    BigDecimal ll = (BigDecimal)ami.get("load");
                    String tmp = bigDecimalShortZero(ll, false).toPlainString();
                    loads.add(tmp);
                    acc = acc.add(ll);
                } else {
                    timeList.add(new SimpleDateFormat(timeFormat).format(time));
                    BigDecimal ll = (BigDecimal)ami.get("load");
                    String tmp = bigDecimalShortZero(ll, false).toPlainString();
                    loads.add(tmp);
                    acc = acc.add(ll);
                }
            }
            ReportGen15TranReVo vo = voList.get(get96DayInv(dd, count, monthDays));
            vo.setLoads(collectLoads(loads, timeList));
            vo.setKwh(bigDecimalShortZero(acc, false).toPlainString());
            voListAll.addAll(voList);
        }

        return voListAll;
    }

    private List<ReportGen15valueVo> collectAmiSettle15Record(List<Map<String, Object>> ami15, Date runDate
            , Map<String, String> infos) throws Exception {
        Map<String, List<MeterUseDateVo>> elecNoMeterChangeDate = getElecNoMeterChangeDate(runDate);
        int monthDays = getDateOfMonth(runDate);
        Set<String> keepKeyStr = new HashSet<>();
        Set<String> dateStrSet = new HashSet<>();
        List<String> timeList = new ArrayList<>();
        List<String> dateStrList = new ArrayList<>();
        List<String> loads = new ArrayList<>();
        String dd = "";
        int idxS = 0;
        int idxE = monthDays;
        int keepCount = 0;
        int idx = 0;

        List<ReportGen15valueVo> voList = new ArrayList<>();
        for (Map<String, Object> ami : ami15) {
            String genElecNo = ami.get("GEN_ELEC_NO").toString();
            String meterNo = ami.get("METER_NO").toString();
            String emNo = genElecNo + "~" + meterNo;
            String combineCap = "";
            String pwDs = "";
            String appId = "";
            if (null != infos) {
                String value = infos.get(genElecNo);
                if (null != value && !value.isEmpty()) {
                    String[] cut = value.split("~");
                    combineCap = cut[0];
                    pwDs = cut[1];
                    appId = cut[2];
                }
            }
            Date time = (Date) ami.get("time");
            dd = dateStr(time);
            int dDay = Integer.parseInt(dd.split("-")[2]);
            if (keepKeyStr.add(emNo)) {
                idxS = 0;
                idxE = monthDays;
                if (null != elecNoMeterChangeDate.get(genElecNo)) {
                    List<MeterUseDateVo> cc = elecNoMeterChangeDate.get(genElecNo);
                    for (int i = 0; i< cc.size(); i++) {
                        if (cc.get(i).getMeterNo().equals(meterNo)) {
                            idxS = 0 == i? 0: cc.get(i-1).getUseDate()-1;
                            idxE = cc.get(i).getUseDate();
                            break;
                        }
                    }
                }
                keepCount = voList.isEmpty()?0 : voList.size() -1;
                voList = getInitVoList(voList, runDate, idxS, idxE, ami, genElecNo, meterNo, pwDs, appId);
                if (loads.size() == daily15Inv || (idx > 1 && loads.size() > 0 && !ami15.get(idx-1).get("time").toString().equals(ami15.get(idx).get("time").toString()))) {
                    for (int k =loads.size(); k < daily15Inv; k++)
                        loads.add("");
                    ReportGen15valueVo vo = voList.get(keepCount);
                    vo.setLoads(collectLoads(loads, timeList));
                    loads = new ArrayList<>();
                    dateStrSet.clear();
                    dateStrList.clear();
                    timeList.clear();
                    keepCount ++;
                }
            }
            if (dDay >= idxS && dDay <= idxE) {
                if (dateStrSet.add(dd)) {
                    timeList.add(new SimpleDateFormat(timeFormat).format(time));
                    if (loads.size() == daily15Inv || (idx > 1 && loads.size() > 0 && !ami15.get(idx-1).get("time").toString().equals(ami15.get(idx).get("time").toString()))) {
                        ReportGen15valueVo vo = voList.get(keepCount + dateStrList.size() - 1);
                        vo.setLoads(collectLoads(loads, timeList));
                        loads = new ArrayList<>();
                    }
                    dateStrList.add(dd);
                    while (loads.size() < day15Str.length && !ami.get("time").toString().contains(" "+day15Str[loads.size()]+":")) loads.add("");
                    String tmp = "";
                    if (null != ami.get("load")) {
                        tmp = bigDecimalShortZero((BigDecimal)ami.get("load"), false).toPlainString();
                        if (!combineCap.isEmpty() && !tmp.isEmpty()) {
                            if (new BigDecimal(combineCap).compareTo(new BigDecimal(tmp)) < 0) tmp = combineCap;
                        }
                    }
                    loads.add(tmp);
                } else {
                    timeList.add(new SimpleDateFormat(timeFormat).format(time));
                    while (loads.size() < day15Str.length && !ami.get("time").toString().contains(" "+day15Str[loads.size()]+":")) loads.add("");
                    String tmp = "";
                    if (null != ami.get("load")) {
                        tmp = bigDecimalShortZero((BigDecimal)ami.get("load"), false).toPlainString();
                        if (!combineCap.isEmpty() && !tmp.isEmpty()) {
                            if (new BigDecimal(combineCap).compareTo(new BigDecimal(tmp)) < 0) tmp = combineCap;
                        }
                    }
                    loads.add(tmp);
                }
            }
            idx ++;
        }
        ReportGen15valueVo vo = voList.get(voList.size()-1);
        for (int k = loads.size(); k < daily15Inv; k++)
            loads.add("");
        vo.setLoads(collectLoads(loads, timeList));
        return voList;
    }

    private List<ReportGen15valueVo> getInitVoList(List<ReportGen15valueVo> voList
            , Date runDate, int idxS, int idxE, Map<String, Object> ami, String genElecNo, String meterNo, String pwDs
            , String appId) throws Exception {
        String yearMonth = yearMonthStr(runDate);
        for (int i = idxS; i < idxE; i++) {
            String ddd = yearMonth + String.format("%02d", i + 1);
            String radio = null == ami.get("RATIO")? null:ami.get("RATIO").toString().equals("1")? "1": bigDecimalShortZero(
                    (BigDecimal)ami.get("RATIO"), false).toPlainString();
            voList.add(ReportGen15valueVo.builder().genElecNo(genElecNo).meterNo(meterNo)
                    .ratio(radio)
                    .channel(1).date(ddd).loads(comma95Str)
                    .pwDs(pwDs).appId(appId).build());
        }
        return voList;
    }

    private Map<String, List<MeterUseDateVo>> getElecNoMeterChangeDate(Date runDate) throws Exception {
        Date start = regularDate(runDate);
        Date end = getMonthEndDate(runDate);
        int monthDays = getDateOfMonth(runDate);
        List<Map<String, Object>> cMeter = generatorEntityMeterRepository.findGenMeterChangeDate(start, end);
        int month = getMonth(runDate);
        int year = getYear(runDate);
        String monthEndDay = String.format("%d-%02d-", year, month)+String.valueOf(monthDays);
        Map<String, List<MeterUseDateVo>> elecNoMeterChangeDate = new HashMap<>();
        Map<String, MeterUseDateVo> elDate = new HashMap<>();
        for (Map<String, Object> m: cMeter) {
            String elecNo = m.get("ELEC_NO").toString();
            Date date = (Date)m.get("GEN_METER_CHANGE_DATE");
            Integer dIdx = Integer.parseInt(date.toString().split("-")[2]);
            String oldGenMeterNo = m.get("oldGenMeterNo").toString();
            String GEN_METER_NO = m.get("GEN_METER_NO").toString();
            elDate.put(elecNo, MeterUseDateVo.builder().meterNo(GEN_METER_NO).useDate(monthDays).build());
            List<MeterUseDateVo> cc = new ArrayList<>();
            if (!elecNoMeterChangeDate.isEmpty() || null != elecNoMeterChangeDate.get(elecNo)) {
                List<MeterUseDateVo> dd = elecNoMeterChangeDate.get(elecNo);
                if (null != dd) cc.addAll(dd);
            }
            cc.add(MeterUseDateVo.builder().meterNo(oldGenMeterNo).useDate(dIdx).build());
            elecNoMeterChangeDate.put(elecNo, cc);
            if (date.toString().equals(monthEndDay)) elDate.remove(elecNo);
        }
        for (Map.Entry<String, MeterUseDateVo> entry : elDate.entrySet()) {
            String elecNo = entry.getKey();
            List<MeterUseDateVo> dd = elecNoMeterChangeDate.get(elecNo);
            List<MeterUseDateVo> cc = new ArrayList<>(dd);
            cc.add(MeterUseDateVo.builder().meterNo(entry.getValue().getMeterNo()).useDate(entry.getValue().getUseDate())
                    .build());
            elecNoMeterChangeDate.put(elecNo, cc);
        }
        return elecNoMeterChangeDate;
    }

    private Map<String, String> collectElecNoFromApplicationType(List<String> elecNos, Date startDate, Date endDate) throws Exception {
        int size = elecNos.size();
        int subListNum = (size + sqlCheckSum - 1) / sqlCheckSum;
        List<List<String>> chunks = new ArrayList<>();
        List<Map<String, Object>> infos = new ArrayList<>();

        for (int i =0; i < subListNum; i++) {
            List<String> subList = elecNos.subList(i * sqlCheckSum, ((i + 1) * sqlCheckSum > size ? size : sqlCheckSum * (i+1)));
            if (infos.isEmpty()) {
                infos = applicationGeneratorRepository.findApplicationTypeByElecNo(subList, startDate, endDate);
            } else {
                List<Map<String, Object>> infos2 = applicationGeneratorRepository.findApplicationTypeByElecNo(subList, startDate, endDate);
                if (!infos2.isEmpty()) infos.addAll(infos2);
            }
        }

        Map<String, String> map = new HashMap<>();
        Set<String> elecSet = new HashSet<>();
        Set<String> contractKeys = new HashSet<>();
        for (Map<String, Object> info : infos) {
            String SERVICE_ID = info.get("SERVICE_ID").toString();
            String GEN_ELEC_NO = info.get("GEN_ELEC_NO").toString();
            //String METER_NO = info.get("METER_NO").toString();
            String[] ss = SERVICE_ID.split("-");
            String elecMeterKey = GEN_ELEC_NO;// +"~"+METER_NO;
            String key = ss[0]+"-"+ss[1]+"-"+ss[2]+"-"+ss[3]+"~"+GEN_ELEC_NO;
            if (!contractKeys.isEmpty() && contractKeys.contains(key)) {}
            else {
                contractKeys.add(key);

                if (elecSet.add(elecMeterKey)) {
                    String combineCap = null == info.get("combineCap")? "" : info.get("combineCap").toString();
                    String PWDS = getPwDs(info, "PWDS");
                    String appId = null == info.get("appId")? "" : info.get("appId").toString();
                    map.put(elecMeterKey, combineCap + "~" + PWDS + "~" + appId + "~" + SERVICE_ID);
                }
                //if (map.get(GEN_ELEC_NO).split("~").length < 4) log.info("[elecNo]:"+GEN_ELEC_NO+", value:"+map.get(GEN_ELEC_NO));
            }
        }
        return map;
    }

    private String appGenLoadTransDeliverType(String genTransDelivery, String loadTransDelivery) {
        if (null != genTransDelivery && !genTransDelivery.isEmpty() && null != loadTransDelivery && !loadTransDelivery.isEmpty()) {
            if (genTransDelivery.equals(loadTransDelivery)) return genTransDelivery;
            else if (genTransDelivery.equals(UNSURE_VOLTAGE_LEVEL)) return genTransDelivery;
            else if (loadTransDelivery.equals(UNSURE_VOLTAGE_LEVEL)) return loadTransDelivery;
            else return "輸配混合";
        }
        return null;
    }

    private int get96DayInv(String dd, int count, int monthDays) {
        return (Integer.parseInt(dd.split("-")[2])) + (count -1) * monthDays - 1;
    }

    private String getPwDs(Map<String, Object> info, String PWDS) {
        if (null == info) return "";
        return null == info.get(PWDS)? "" : (info.get(PWDS).toString().equals("2") || info.get(PWDS).toString().equals("3")) ? "DS": "PW";
    }

    private String sumOne13911(Kwhs13911Vo one) {
        BigDecimal table1 = null == one.getTable1() || one.getTable1().isEmpty()? zeroBigDecimal: new BigDecimal(one.getTable1());
        BigDecimal table3 = null == one.getTable3() || one.getTable3().isEmpty()? zeroBigDecimal: new BigDecimal(one.getTable3());
        BigDecimal table11 = null == one.getTable11() || one.getTable11().isEmpty()? zeroBigDecimal: new BigDecimal(one.getTable11());
        BigDecimal table9 = new BigDecimal(null == one.getTable9() || one.getTable9().isEmpty() ? "0" : one.getTable9());
        return table1.add(table3).add(table9).add(table11).toString();
    }

    private Kwhs13911Vo sumTwoKwhs13911Vo(Kwhs13911Vo one, Kwhs13911Vo two) {
        BigDecimal table1 = new BigDecimal(one.getTable1());
        BigDecimal table3 = new BigDecimal(one.getTable3());
        BigDecimal table11 = new BigDecimal(one.getTable11());
        BigDecimal ot1 = new BigDecimal(two.getTable1());
        BigDecimal ot3 = new BigDecimal(two.getTable3());
        BigDecimal ot11 = new BigDecimal(two.getTable11());
        String o9 = "";
        if ((null == one.getTable9() || one.getTable9().isEmpty()) && (null == two.getTable9() || two.getTable9().isEmpty())) o9 = null;
        else if ((null == one.getTable9() || one.getTable9().isEmpty()) && !two.getTable9().isEmpty()) o9 = two.getTable9();
        else if (!one.getTable9().isEmpty() && (null == two.getTable9() || two.getTable9().isEmpty())) o9 = one.getTable9();
        else //if (!one.getTable9().isEmpty() && !two.getTable9().isEmpty())
            o9 = new BigDecimal(one.getTable9()).add(new BigDecimal(two.getTable9())).toString();
        return Kwhs13911Vo.builder().table1(table1.add(ot1).toString()).table3(table3.add(ot3).toString())
                .table9(o9).table11(table11.add(ot11).toString()).build();
    }

    private Map<String, PartLoadEntityVo> collectPartLoadEntity(List<Map<String, Object>> loadInfo) {
        Map<PartLoadEntityVo, String> loadMap = new HashMap<>();
        Map<String, PartLoadEntityVo> loadIdMap = new HashMap<>();
        for (Map<String, Object> load : loadInfo) {
            String appLoadId = load.get("applicationLoadId").toString();
            String pwDs = getPwDs(load, "PWDS");
            String elecNo = load.get("CUST_ELEC_NO").toString();
            String contrType = load.get("CONTR_TYPE").toString();
            String priceStg = load.get("TIME_PRICE_STG").toString();
            PartLoadEntityVo key = PartLoadEntityVo.builder().elecNo(elecNo).contrType(contrType).priceStg(priceStg).pwDs(pwDs).build();
            if (loadMap.containsKey(key)) {
                String old = loadMap.get(key).toString();
                String add = old+"~"+appLoadId;
                loadMap.put(key, add);
            } else loadMap.put(key, appLoadId);
        }
        for (Map.Entry<PartLoadEntityVo, String> ee : loadMap.entrySet()) {
            PartLoadEntityVo key = ee.getKey();
            String appLoadId = ee.getValue();
            PartLoadEntityVo oo = PartLoadEntityVo.builder().elecNo(key.getElecNo()).contrType(key.getContrType())
                    .priceStg(key.getPriceStg()).pwDs(key.getPwDs()).idxes(appLoadId).build();
            if (!appLoadId.contains("~"))
                loadIdMap.put(appLoadId, oo);
            else {
                String[] ss = appLoadId.split("~");
                loadIdMap.put(ss[0], oo);
            }
        }
        return loadIdMap;
    }

    private ApplicationGenerator getGenMeterId(Long gId) {
        return applicationGeneratorRepository.findById(gId).orElse(null);
    }

    private ApplicationLoad getLoadMeterId(Long lId) {
        return applicationLoadRepository.findById(lId).orElse(null);
    }

    private String addMatchInfo(String match1, String match2) {
        if ((null == match1 || match1.isEmpty()) && (null == match2 || match2.isEmpty())) return "";
        else if ((null == match1 || match1.isEmpty()) && !match2.isEmpty()) return match2;
        else if (!match1.isEmpty() && (null == match2 || match2.isEmpty())) return match1;
        else {//if (!ecm.get((short)1).isEmpty() && !rem.get((short)1).isEmpty()) tt = new BigDecimal()
            return new BigDecimal(match1).add(new BigDecimal(match2)).toString();
        }
    }

    private String getKeepReplacedDateString(String tmpD, boolean fullMark) {
        return 10 == tmpD.length()? tmpD: tmpD.contains(" 00:00:00")? tmpD.substring(0, 10)
                : fullMark? tmpD.substring(0, tmpD.length()-2) : tmpD.substring(0, tmpD.length()-5);
    }

    private String getDateOrDateMinuteStr(Date reD, String keepReplacedDateStr) throws Exception {
        if (keepReplacedDateStr.isEmpty()) return new SimpleDateFormat(dateFormat).format(reD);
        else return keepReplacedDateStr;
    }

    private List<String> serviceYearMonthDates(String serviceDate) throws Exception {
        List<String> YMDateStr = new ArrayList<>();
        if (serviceDate.contains("-")) {
            String[] dd = serviceDate.toString().split("-");
            if (dd.length > 1) {
                YMDateStr.add(dd[0]);
                YMDateStr.add(dd[1].replace("0", ""));
            }
        }
        List<String> dd = getMonthStartDateEndDateString(dateStrToDate(serviceDate));
        YMDateStr.add(dd.get(0));
        YMDateStr.add(dd.get(1));
        return YMDateStr;
    }

    // 取得整個月份 15分鐘電量  **單純 list 不需要日內換表
    private List<ReportGen15powerVo> extract15MinGenMatchedRm(List<ReportGen15powerVo> voList
            , List<Long> settleIdList, Map<String, Integer> apGenIdIdxMap) throws Exception {

        int count = 0;
        String dd = "";

        for (int i =0 ;i< settleIdList.size(); i++) {
            List<Map<String, Object>> apTimeGen = applicationTimelyGeneratorRecordRepository
                    .findGen15MinutesMatchedRmByDate(settleIdList.get(i));

            Set<Long> apGenIdSet = new HashSet<>();
            Set<Date> datetimeSet = new HashSet<>();
            Set<String> dateStrSet = new HashSet<>();
            List<String> timeList = new ArrayList<>();
            List<String> loads = new ArrayList<>();
            BigDecimal[] rateValue = new BigDecimal[2];
            long keepGenId = 0;

            for (Map<String, Object> apt : apTimeGen) {
                Long apGenId = (Long) apt.get("applicationGeneratorId");
                Date time = (Date) apt.get("time");
                dd = dateStr(time);
                if (apGenId != null && apGenId != 0 && apGenIdSet.add(apGenId)) {
                    if (!loads.isEmpty() && null != apGenIdIdxMap.get(keepGenId+"~"+dd)) {
                        ReportGen15powerVo vo = voList.get(apGenIdIdxMap.get(keepGenId+"~"+dd));
                        vo.setLoads(collectLoads(loads, timeList));
                        datetimeSet.clear();
                        dateStrSet.clear();
                        timeList.clear();
                        timeList = new ArrayList<>();
                    }
                    keepGenId = apGenId;
                    datetimeSet.add(time);
                    timeList.add(new SimpleDateFormat(timeFormat).format(time));
                    dateStrSet.add(dd);
                    count = 1;
                    loads = new ArrayList<>();
                    loads.add(matchedPowerInfo((BigDecimal) apt.get("dirMatched"), (BigDecimal) apt.get("matchedRm")));
                } else if (dateStrSet.add(dd)) {
                    if (null != apGenIdIdxMap.get(keepGenId+"~"+dd)) {
                        ReportGen15powerVo vo = voList.get(apGenIdIdxMap.get(keepGenId + "~" + dd));
                        vo.setLoads(collectLoads(loads, timeList));
                    }
                    datetimeSet.clear();
                    timeList.clear();
                    timeList = new ArrayList<>();
                    datetimeSet.add(time);
                    timeList.add(new SimpleDateFormat(timeFormat).format(time));
                    count = 1;
                    loads = new ArrayList<>();
                    loads.add(matchedPowerInfo((BigDecimal) apt.get("dirMatched"), (BigDecimal) apt.get("matchedRm")));
                } else {
                    if (datetimeSet.add(time)) {
                        timeList.add(new SimpleDateFormat(timeFormat).format(time));
                        rateValue[0] = apt.get("matchedRm") == null ? (BigDecimal)apt.get("dirMatched") : (BigDecimal)apt.get("matchedRm");
                        loads.add(matchedPowerInfo((BigDecimal) apt.get("dirMatched"), (BigDecimal) apt.get("matchedRm")));
                        count++;
                    } else {
                        rateValue[1] = apt.get("matchedRm") == null ? (BigDecimal)apt.get("dirMatched") : (BigDecimal)apt.get("matchedRm");
                        loads.set(count-1, matchedPowerInfo(null, rateValue[0].add(rateValue[1])));
                    }
                }
            }
            if (null != apGenIdIdxMap.get(keepGenId+"~"+dd)) {
                ReportGen15powerVo vo = voList.get(apGenIdIdxMap.get(keepGenId + "~" + dd));
                vo.setLoads(collectLoads(loads, timeList));
            }
        }
        return voList;
    }

    private Map<String, Map<Short, String>> getEcChangeSectionMatchedInfo(List<Map<String, Object>> flexMatchRm
            , Boolean mark, boolean genLoad) {
        Map<String, Map<Short, String>> match = new HashMap<>();
        Set<String> genIdDateSet = new HashSet<>();
        String keep = null;
        String genIdDate = null;
        String mm = null;
        Map<Short, String> rate = new HashMap<>();
        for (Map<String, Object> m : flexMatchRm) {
            Short ecId = Short.valueOf(m.get("energyChangeSectionId").toString());
            if (genLoad) {
                genIdDate = m.get("applicationGeneratorId").toString()+"~"+m.get("serviceDate").toString();
                mm = null == m.get("matchedRm") ? "": bigDecimalShortZero((BigDecimal)m.get("matchedRm"), mark).toPlainString();
            } else {// (!genLoad)
                genIdDate =  m.get("applicationLoadId").toString()+"~"+m.get("serviceDate").toString();
                mm = null == m.get("matchedCn") ? "": bigDecimalShortZero((BigDecimal)m.get("matchedCn"), mark).toPlainString();
            }
            if (rate.isEmpty() && genIdDateSet.add(genIdDate)) keep = genIdDate;
            else if (keep != genIdDate && genIdDateSet.add(genIdDate)) {
                match.put(keep, rate);
                keep = genIdDate;
                rate = new HashMap<>();
            }
            rate.put(ecId, mm);
        }
        if (!rate.isEmpty() && null != genIdDate) {
            match.put(genIdDate, rate);
        }
        return match;
    }

    private List<ReportVo> getMonthSettleGenLoadDetails(Date serviceStart, Date serviceEnd, Date billEnd
            , Map<String, Integer> voListIdxMap, List<ReportVo> voList) throws Exception {
        List<ReportVo> cvoList = getSumMatchRmToOriginalData(serviceStart, getMonthEndDate(serviceEnd), voList);

        Map<Integer, KwhsASDTVo> yearRate = getYearFuelCodeRate(getYear(serviceStart), getYear(serviceEnd));

        List<Map<String, Object>> detail = repository.findGeneratorsLoadsByServiceDates(serviceStart, billEnd);
        List<ReportVo> ovoList = new ArrayList<>();
        Map<String, String> sGIdLIdDateStrMap = new HashMap<>();
        String keepVoKey = "";
        int count = 0;
        for (Map<String, Object> stm:detail) {
            Date custMaterChangeDate = null == stm.get("CUST_METER_CHANGE_DATE") ? null:(Date)stm.get("CUST_METER_CHANGE_DATE");
            Date genMeterChangeDate = null == stm.get("GEN_METER_CHANGE_DATE") ? null:(Date)stm.get("GEN_METER_CHANGE_DATE");
            Date serviceDate = (Date)stm.get("serviceDate");
            Date billDate = (Date)stm.get("billDate");
            Long appGenId = (Long)stm.get("applicationGeneratorId");
            Long appLoadId = (Long)stm.get("applicationLoadId");
            String appType = stm.get("appType").toString();
            Boolean mark = compareServiceDateChangeDate(serviceDate, custMaterChangeDate, genMeterChangeDate);
            String voKey = serviceDate.toString()+"~"+billDate.toString()+"~"+appGenId.toString()+"~"+appLoadId.toString();
            if (null != voListIdxMap.get(voKey)) {
                int idx = voListIdxMap.get(voKey);
                if (null != mark && mark && !appType.equals("4")) {// && !(null == custMaterChangeDate && null == genMeterChangeDate)) {
                    if (sGIdLIdDateStrMap.isEmpty() && null != custMaterChangeDate) sGIdLIdDateStrMap.put(voKey, "c~" + custMaterChangeDate + "~" + stm.get("oldLoadMeterNo"));
                    else if (null != sGIdLIdDateStrMap.get(voKey) && null != custMaterChangeDate) {
                        String cc = "c~" + custMaterChangeDate + "~" + stm.get("oldLoadMeterNo");
                        String dd = null == sGIdLIdDateStrMap.get(voKey)? cc: sGIdLIdDateStrMap.get(voKey) + "," + cc;
                        sGIdLIdDateStrMap.put(voKey, dd);
                    }
                    if (sGIdLIdDateStrMap.isEmpty() && null != genMeterChangeDate) sGIdLIdDateStrMap.put(voKey, "g~" + genMeterChangeDate + "~" + stm.get("oldGenMeterNo"));
                    else if (null != sGIdLIdDateStrMap.get(voKey) && null != genMeterChangeDate) {
                        String cc = "g~" + genMeterChangeDate + "~" + stm.get("oldGenMeterNo");
                        String dd = null == sGIdLIdDateStrMap.get(voKey)? cc: sGIdLIdDateStrMap.get(voKey) + "," + cc;
                        sGIdLIdDateStrMap.put(voKey, dd);
                    }
                }
                if (appType.equals("4")) {
                    if (null != mark && mark) {
                        ReportVo vv = cvoList.get(idx);
                        vv.setBillDate(billDate.toString());
                        ovoList.add(collectGenLoReportVo(stm, vv, null, null, null, null));
                    } else {
                        ReportVo vo = ovoList.get(ovoList.size() - 1);
                        String bbKey = vo.getServiceYear()+"-"+String.format("%02d", vo.getServiceMonth())+"-01~"+vo.getBillYear()+"-"+String.format("%02d", vo.getBillMonth())+"-01~"+vo.getApplicationGeneratorId().toString()+"~"+vo.getApplicationLoadId().toString();
                        if (!bbKey.equals(voKey)) {
                            ReportVo vv = cvoList.get(idx);
                            vv.setBillDate(billDate.toString());
                            ovoList.add(collectGenLoReportVo(stm, vv, null, null, null, null));
                        }
                    }
                } else {
                    boolean mm = false;
                    if (ovoList.isEmpty()) mm = true;
                    else if (idx != 0 && idx < voListIdxMap.size()) {
                        ReportVo beVo = ovoList.get(ovoList.size()-1);
                        String beVoKey = beVo.getServiceDate()+"~"+ beVo.getBillDate()+"~"+beVo.getApplicationGeneratorId()+"~"+beVo.getApplicationLoadId();
                        Map<String, Object> stm2 = count < detail.size() - 1 ? detail.get(count+1): null;
                        if (null != stm2) {
                            String nextVoKey = stm2.get("serviceDate") + "~" + stm2.get("billDate") + "~" + stm2.get("applicationGeneratorId") + "~" + stm2.get("applicationLoadId");
                            mm = (!voKey.equals(nextVoKey) && !beVoKey.equals(nextVoKey)) || (voKey.equals(nextVoKey) && !keepVoKey.equals(voKey)
                                    && null == stm2.get("CUST_METER_CHANGE_DATE") && null == stm2.get("GEN_METER_CHANGE_DATE"));
                        } else mm = idx == voListIdxMap.size() -1;
                    }
                    if (null != mark && mark == false) {
                        if (mm && ((null != genMeterChangeDate && !genMeterChangeDate.equals(custMaterChangeDate))
                                || (null != custMaterChangeDate && !custMaterChangeDate.equals(genMeterChangeDate)))) {
                            ReportVo vv = collectGenLoReportVo(stm, cvoList.get(idx), null, null, null, null);
                            vv.setGenMeterChangeDate("");
                            vv.setCustMeterChangeDate("");
                            vv.setBillDate(billDate.toString());
                            ovoList.add(collectGenLoReportVo(stm, vv, null, null, null, null));
                        }
                    } else if (mm) {
                        if (sGIdLIdDateStrMap.isEmpty() || null == sGIdLIdDateStrMap.get(voKey)) {
                            if ((null != genMeterChangeDate && genMeterChangeDate.equals(custMaterChangeDate))
                                    || (null != custMaterChangeDate && custMaterChangeDate.equals(genMeterChangeDate)) ||
                                    (keepVoKey.equals(voKey))) {}
                            else {
                                ReportVo vv = cvoList.get(idx);
                                vv.setBillDate(billDate.toString());
                                ovoList.add(collectGenLoReportVo(stm, vv, null, null, null, null));
                            }
                        } else if (!sGIdLIdDateStrMap.isEmpty() && !sGIdLIdDateStrMap.get(voKey).contains(",")) {
                            String[] cg = sGIdLIdDateStrMap.get(voKey).split("~");
                            TypeDateMeterVo tdm = TypeDateMeterVo.builder().type(cg[0]).date(dateStrToDate(cg[1])).meterNo(cg[2]).build();
                            ReportVo vv = cvoList.get(idx);
                            vv.setBillDate(billDate.toString());
                            ovoList = setKwhtoVoList(appGenId, appLoadId, serviceDate, tdm, yearRate.get(vv.getServiceYear()), vv, stm, ovoList, true);
                            sGIdLIdDateStrMap.clear();
                            sGIdLIdDateStrMap = new HashMap<>();
                        } else {
                            String[] cgCase = sGIdLIdDateStrMap.get(voKey).split(",");
                            List<TypeDateMeterVo> tdm = new ArrayList<>();
                            for (int i = 0; i < cgCase.length; i++) {
                                String[] cg = cgCase[i].split("~");
                                tdm.add(TypeDateMeterVo.builder().type(cg[0]).date(dateStrToDate(cg[1])).meterNo(cg[2]).build());
                            }
                            tdm.sort((t1,t2) -> t1.getDate().compareTo(t2.getDate()));
                            ReportVo vv = cvoList.get(idx);
                            vv.setBillDate(billDate.toString());
                            for (int i = 0; i<tdm.size(); i++) {
                                TypeDateMeterVo tt = tdm.get(i);
                                if (i == 0)
                                    ovoList = setKwhtoVoList(appGenId, appLoadId, serviceDate, tt, yearRate.get(vv.getServiceYear()), vv, stm, ovoList, false);
                                else {
                                    ovoList = setKwhtoVoList(appGenId, appLoadId, tdm.get(i-1).getDate(), tt, yearRate.get(vv.getServiceYear()), vv, stm, ovoList, false);
                                }
                            }
                            Date monthDate = getMonthEndDate(serviceDate);
                            Date last = tdm.get(tdm.size()-1).getDate();
                            if (last.before(monthDate)) {
                                ReportVo bb = ovoList.get(ovoList.size()-1);
                                if (!bb.getDKwh().isEmpty()) {
                                    KwhsASDTVo ss = KwhsASDTVo.builder().dKwh(new BigDecimal(bb.getDKwh()))
                                            .tKwh(new BigDecimal(bb.getTKwh())).sAKwh(new BigDecimal(bb.getSAKwh()))
                                            .aExp(new BigDecimal(bb.getAExp())).sExp(new BigDecimal(bb.getSExp()))
                                            .dExp(new BigDecimal(bb.getDExp())).tExp(new BigDecimal(bb.getTExp())).build();
                                    KwhsASDTVo nn = KwhsASDTVo.builder().dKwh(new BigDecimal(vv.getDKwh()))
                                            .tKwh(new BigDecimal(vv.getTKwh())).sAKwh(new BigDecimal(vv.getSAKwh()))
                                            .aExp(new BigDecimal(vv.getAExp())).sExp(new BigDecimal(vv.getSExp()))
                                            .dExp(new BigDecimal(vv.getDExp())).tExp(new BigDecimal(vv.getTExp())).build();
                                    KwhsASDTVo sub = subtractTwoKwh(ss, nn);
                                    ovoList.add(collectGenLoReportVo(stm, vv, sub, null, null, null));
                                } else {
                                    ovoList.add(collectGenLoReportVo(stm, vv, null, null, null, null));
                                }
                            }
                            sGIdLIdDateStrMap.clear();
                            sGIdLIdDateStrMap = new HashMap<>();
                        }
                    }
                }
                keepVoKey = voKey;
                count ++;
            }
        }
        return ovoList;
    }

    private List<ReportVo> getSumMatchRmToOriginalData(Date firstServiceDate, Date lastServiceDate, List<ReportVo> voList) {
        List<Map<String, Object>> matchRm = repository.findSettlementInfoMatchedRmByServiceDate(firstServiceDate, lastServiceDate);
        Map<String, BigDecimal> mRm = new HashMap<>();
        for (Map<String, Object> m: matchRm) {
            String kk = m.get("serviceDate").toString()+"~"+m.get("billDate").toString()+"~"+m.get("applicationGeneratorId").toString() + "~" +m.get("applicationLoadId").toString();
            BigDecimal vv = null == m.get("matchedRm")? zeroBigDecimal: (BigDecimal)m.get("matchedRm");
            if (mRm.containsKey(kk)) { //  && (null != mRm.get(kk))
                mRm.put(kk, vv.add(mRm.get(kk)));
            } else {
                mRm.put(kk, vv);
            }
        }
        for (ReportVo vo: voList) {
            String kk = vo.getServiceDate()+"~"+vo.getBillYear()+"-"+String.format("%02d", vo.getBillMonth())+"-01~"+vo.getApplicationGeneratorId().toString()+"~"+vo.getApplicationLoadId().toString();
            if (mRm.containsKey(kk)) { // && null != mRm.get(kk)
                String rmVal = bigDecimalShortZero(mRm.get(kk), null).toPlainString();
                if (null == vo.getApGenLoType()) {}
                else if (vo.getApGenLoType() == 1) {
                    vo.setTKwh(rmVal);
                    vo.setDKwh("0");
                }
                else if (vo.getApGenLoType() == 2) {
                    vo.setTKwh("0");
                    vo.setDKwh(rmVal);
                }
                else {
                    vo.setTKwh(rmVal);
                    vo.setDKwh(rmVal);
                }
                vo.setSAKwh(rmVal);
            }
        }
        return voList;
    }

    // #3 #4 timely 計算 15 分鐘1,3,9,11 電量 endDate
    private Kwhs13911Vo sum15MinGenKwh(List<Long> settleIds, Long appGenId, Date start, Date end) {
        List<IMatchedRmEcChangeId> sums = applicationTimelyGeneratorRecordRepository
                .sumMatchedRm13911ByApplicationGeneratorIdBetweenDateRange(settleIds, appGenId, start, end);
        if (sums.isEmpty()) return Kwhs13911Vo.builder().table1("").table3("").table9("").table11("").build();
        // 1
        String table1 = null;
        String table3 = null;
        String table9 = null;
        String table11 = null;
        for (int i = 0; i < sums.size(); i++) {
            IMatchedRmEcChangeId m = sums.get(i);
            if (m.getEcChangeId().toString().equals("1")) {
                if (null == table1)
                    table1 = null == m.getMatchedRm() ? null == m.getDirMatched() ? null
                        : m.getDirMatched().stripTrailingZeros().toPlainString() : null == m.getDirMatched() ?
                        m.getMatchedRm().stripTrailingZeros().toPlainString()
                        : (m.getMatchedRm().add(m.getMatchedRm())).stripTrailingZeros().toPlainString();
                else {
                    BigDecimal tt = new BigDecimal(table1);
                    table1 = null == m.getMatchedRm() ? null == m.getDirMatched() ? table1
                            : (tt.add(m.getDirMatched())).stripTrailingZeros().toPlainString() : null == m.getDirMatched()
                            ? (tt.add(m.getMatchedRm())).stripTrailingZeros().toPlainString()
                            : (tt.add(m.getMatchedRm()).add(m.getMatchedRm())).stripTrailingZeros().toPlainString();
                }
            } else if (m.getEcChangeId().toString().equals("3")) {
                if (null == table3)
                    table3 = null == m.getMatchedRm() ? null == m.getDirMatched() ? null
                        : m.getDirMatched().stripTrailingZeros().toPlainString() : null == m.getDirMatched() ?
                        m.getMatchedRm().stripTrailingZeros().toPlainString()
                        : (m.getMatchedRm().add(m.getMatchedRm())).stripTrailingZeros().toPlainString();
                else {
                    BigDecimal tt = new BigDecimal(table3);
                    table3 = null == m.getMatchedRm() ? null == m.getDirMatched() ? table3
                            : (tt.add(m.getDirMatched())).stripTrailingZeros().toPlainString() : null == m.getDirMatched()
                            ? (tt.add(m.getMatchedRm())).stripTrailingZeros().toPlainString()
                            : (tt.add(m.getMatchedRm()).add(m.getMatchedRm())).stripTrailingZeros().toPlainString();
                }
            } else if (m.getEcChangeId().toString().equals("9")) {
                if (null == table9)
                    table9 = null == m.getMatchedRm() ? null == m.getDirMatched() ? null
                        : m.getDirMatched().stripTrailingZeros().toPlainString() : null == m.getDirMatched() ?
                        m.getMatchedRm().stripTrailingZeros().toPlainString()
                        : (m.getMatchedRm().add(m.getMatchedRm())).stripTrailingZeros().toPlainString();
                else {
                    BigDecimal tt = new BigDecimal(table9);
                    table9 = null == m.getMatchedRm() ? null == m.getDirMatched() ? table9
                            : (tt.add(m.getDirMatched())).stripTrailingZeros().toPlainString() : null == m.getDirMatched()
                            ? (tt.add(m.getMatchedRm())).stripTrailingZeros().toPlainString()
                            : (tt.add(m.getMatchedRm()).add(m.getMatchedRm())).stripTrailingZeros().toPlainString();
                }
            } else {// 11
                if (null == table11)
                    table11 = null == m.getMatchedRm() ? null == m.getDirMatched() ? null
                        : m.getDirMatched().stripTrailingZeros().toPlainString() : null == m.getDirMatched() ?
                        m.getMatchedRm().stripTrailingZeros().toPlainString()
                        : (m.getMatchedRm().add(m.getMatchedRm())).stripTrailingZeros().toPlainString();
                else {
                    BigDecimal tt = new BigDecimal(table11);
                    table11 = null == m.getMatchedRm() ? null == m.getDirMatched() ? table11
                            : (tt.add(m.getDirMatched())).stripTrailingZeros().toPlainString() : null == m.getDirMatched()
                            ? (tt.add(m.getMatchedRm())).stripTrailingZeros().toPlainString()
                            : (tt.add(m.getMatchedRm()).add(m.getMatchedRm())).stripTrailingZeros().toPlainString();
                }
            }
        }

        return Kwhs13911Vo.builder().table1(table1).table3(table3).table9(table9).table11(table11).build();
    }

    // timely 15分鐘電量累加 endDate 都多加 5 分鐘
    private BigDecimal sumMatchedRmByApplicationGeneratorIdApplicationLoadId(Long appGenId, Long appLoadId
            , Date start, Date end, Date billDate, Long appId) {
        return applicationTimelyGeneratorLoadRecordRepository
                .sumMatchedRmByApplicationGeneratorIdApplicationLoadIdBetweenDateRange(appGenId, appLoadId, start
                        , getNext5Min(end), billDate, appId);
    }

    private List<ReportContractSettleOVo> computeBSMIKwhsFees(List<ReportContractSettleOVo> voList
            , Map<String, Object> m, String changeInfo, BSMIOutDateStrBillDateRangeVo dateStr) throws Exception {
        String[] cgCase = changeInfo.split(",");
        List<TypeDateMeterVo> tdm = new ArrayList<>();
        for (int i =0; i < cgCase.length; i++) {
            String[] cg = cgCase[i].split("~");
            tdm.add(TypeDateMeterVo.builder().type(cg[0]).date(dateStrToDate(cg[1])).meterNo(cg[2]).build());
        }
        if (tdm.size() > 1)
            tdm.sort((t1,t2) -> t1.getDate().compareTo(t2.getDate()));
        Date firstDate = getMonthFirstDate(dateStrToDate(changeInfo.split("~")[1]));
        Long appGenId = (Long)m.get("applicationGeneratorId");
        Long appLoadId = (Long)m.get("applicationLoadId");
        Long appId = (Long)m.get("appId");
        Date billDate = DateUtils.passStringToDate(m.get("billDate").toString());
        BigDecimal tRate = (BigDecimal) m.get("tRate");
        BigDecimal dRate = (BigDecimal) m.get("dRate");
        BigDecimal sRate = (BigDecimal) m.get("sRate");
        BigDecimal aRate = (BigDecimal) m.get("aRate");
        BigDecimal keepKwh = BigDecimal.ZERO;
        BigDecimal keepCost = BigDecimal.ZERO;
        for (int i =0; i< tdm.size(); i++) {
            TypeDateMeterVo tt = tdm.get(i);
            BigDecimal kwh = BigDecimal.ZERO;
            if (i == 0) {
                kwh = sumMatchedRmByApplicationGeneratorIdApplicationLoadId(appGenId, appLoadId, firstDate, tt.getDate(), billDate, appId);
            } else {
                kwh = sumMatchedRmByApplicationGeneratorIdApplicationLoadId(appGenId, appLoadId, tdm.get(i-1).getDate(), tt.getDate(), billDate, appId);
            }
            BigDecimal cost = bigDecimalShortZero(kwh.multiply(tRate).add(kwh.multiply(dRate)).add(kwh.multiply(sRate)).add(kwh.multiply(aRate)), null);
            kwh = bigDecimalShortZero(kwh, null);
            keepKwh = keepKwh.add(kwh);
            keepCost = keepCost.add(cost);
            String custM = "";
            String genM = "";
            if (tt.getType().equals("c")) {
                custM = tt.getMeterNo();
                genM = null == m.get("GEN_METER_NO")?"":m.get("GEN_METER_NO").toString();
            } else {
                custM = null == m.get("CUST_METER_NO")?"":m.get("CUST_METER_NO").toString();
                genM = tt.getMeterNo();
            }
            voList.add(ReportContractSettleOVo.builder().contractNo((String)m.get("SERVICE_ID")).billDateRange(dateStr.getBillDateRange())
                    .custElecNo(m.get("CUST_ELEC_NO").toString()).custMeterNo(custM).genElecNo(m.get("GEN_ELEC_NO").toString()).genMeterNo(genM)
                    .kwh(kwh.stripTrailingZeros().toPlainString()).tExp(tRate.stripTrailingZeros().toString())
                    .dExp(dRate.stripTrailingZeros().toString()).sExp(sRate.stripTrailingZeros().toString())
                    .aExp(aRate.stripTrailingZeros().toString()).cost(cost.stripTrailingZeros().toPlainString())
                    .applicationGeneratorId(appGenId).applicationLoadId(appLoadId).build());
        }
        Date monthDate = getMonthEndDate(firstDate);
        Date last = tdm.get(tdm.size()-1).getDate();
        if (last.before(monthDate) || last.equals(monthDate)) {
            int kwh = ((BigDecimal) m.get("matchedKw")).subtract(keepKwh).intValue();
            int cost = ((BigDecimal) m.get("FEE")).subtract(keepCost).intValue();
            if (!last.equals(monthDate)) {
                voList.add(ReportContractSettleOVo.builder().contractNo((String)m.get("SERVICE_ID")).billDateRange(dateStr.getBillDateRange())
                        .custElecNo(m.get("CUST_ELEC_NO").toString()).custMeterNo(null == m.get("CUST_METER_NO")?"":m.get("CUST_METER_NO").toString())
                        .genElecNo(m.get("GEN_ELEC_NO").toString()).genMeterNo(null == m.get("GEN_METER_NO")?"":m.get("GEN_METER_NO").toString())
                        .kwh(Integer.toString(kwh)).tExp(tRate.stripTrailingZeros().toString())
                        .dExp(dRate.stripTrailingZeros().toString()).sExp(sRate.stripTrailingZeros().toString())
                        .aExp(aRate.stripTrailingZeros().toString()).cost(Integer.toString(cost))
                        .applicationGeneratorId(appGenId).applicationLoadId(appLoadId).build());
            } else {
                ReportContractSettleOVo vo = voList.get(voList.size()-1);
                kwh += Integer.parseInt(vo.getKwh());
                cost += Integer.parseInt(vo.getCost());
                vo.setKwh(Integer.toString(kwh));
                vo.setCost(Integer.toString(cost));
            }
        }
        return voList;
    }

    private KwhsASDTVo sum15MinGenLoadKwh(Long appGenId, Long appLoadId, Date start, Date end, KwhsASDTVo yearRate
            , int apGenLoType) {
        BigDecimal match = sumMatchedRmByApplicationGeneratorIdApplicationLoadId(appGenId, appLoadId, start, end, null, null);
        // <- 預設 billDate=mull 跟 applicationId=null 等之後 修復原始報表 再一起改
        if (match == BigDecimal.ZERO) return null;
        BigDecimal aExp = match.multiply(yearRate.getAExp());
        BigDecimal sExp = match.multiply(yearRate.getSExp());
        BigDecimal tExp = match.multiply(yearRate.getTExp());
        BigDecimal dExp = match.multiply(yearRate.getDExp());
        BigDecimal total = aExp.add(sExp).add(tExp).add(dExp);
        KwhsASDTVo ans = KwhsASDTVo.builder().sAKwh(match).aExp(aExp).sExp(sExp).tExp(tExp).dExp(dExp).totalExp(total).build();
        if (apGenLoType == 1) {
            ans.setTKwh(match);
            ans.setDKwh(zeroPointBigDecimal);
        } else if (apGenLoType == 2) {
            ans.setTKwh(zeroPointBigDecimal);
            ans.setDKwh(match);
        } else {
            ans.setTKwh(match);
            ans.setDKwh(match);
        }
        return ans;
    }

    private List<ReportVo> setKwhtoVoList(Long appGenId, Long appLoadId, Date serviceDate, TypeDateMeterVo tdm
            , KwhsASDTVo yearRate, ReportVo vv, Map<String, Object> stm, List<ReportVo> ovoList, boolean mark) {
        KwhsASDTVo kwhs = sum15MinGenLoadKwh(appGenId, appLoadId, serviceDate, tdm.getDate(), yearRate, vv.getApGenLoType());
        if (null != kwhs && tdm.getType().equals("c")) {
            String custMeter = tdm.getMeterNo().isEmpty() ? null : tdm.getMeterNo();
            ovoList.add(collectGenLoReportVo(stm, vv, kwhs, custMeter, null, tdm.getDate()));
        } else if (null != kwhs) {
            String genMeter = tdm.getMeterNo().isEmpty() ? null : tdm.getMeterNo();
            ovoList.add(collectGenLoReportVo(stm, vv, kwhs, null, genMeter, tdm.getDate()));
        } else {
            KwhsASDTVo nn = KwhsASDTVo.builder().sAKwh(new BigDecimal("-1")).build();
            if (tdm.getType().equals("c"))
                ovoList.add(collectGenLoReportVo(stm, vv, nn, tdm.getMeterNo(), null, tdm.getDate()));
            else // tdm.getType().equals("g")
                ovoList.add(collectGenLoReportVo(stm, vv, nn, null, tdm.getMeterNo(), tdm.getDate()));
            if (mark)
                ovoList.add(collectGenLoReportVo(stm, vv, null, null, null, null));
        }
        if (mark && null != kwhs && null != vv.getDKwh()) {
            KwhsASDTVo nn = KwhsASDTVo.builder().dKwh(new BigDecimal(vv.getDKwh()))
                    .tKwh(new BigDecimal(vv.getTKwh())).sAKwh(new BigDecimal(vv.getSAKwh()))
                    .aExp(new BigDecimal(vv.getAExp())).sExp(new BigDecimal(vv.getSExp()))
                    .dExp(new BigDecimal(vv.getDExp())).tExp(new BigDecimal(vv.getTExp())).build();
            KwhsASDTVo sub = subtractTwoKwh(kwhs, nn);
            ovoList.add(collectGenLoReportVo(stm, vv, sub, "ZERO", "ZERO", null));
        }
        return ovoList;
    }

    private ReportVo collectGenLoReportVo(Map<String, Object> stm, ReportVo vv, KwhsASDTVo kwhs
            , String custMeterNo, String genMeterNo, Date meterChangeDate) {
        String dKwh = vv.getDKwh();
        String tKwh = vv.getTKwh();
        String sAKwh = vv.getSAKwh();
        String aExp = vv.getAExp();
        String sExp = vv.getSExp();
        String dExp = vv.getDExp();
        if ((null == dKwh || dKwh.isEmpty()) && (null != tKwh && !tKwh.isEmpty())) dExp = null;
        String tExp = vv.getTExp();
        if ((null != dKwh && !dKwh.isEmpty()) && (null == tKwh || tKwh.isEmpty())) tExp = null;
        String totalExp = vv.getTotalExp();
        String mCustMeterNo = null;
        String custMeterChangeDate = "";
        String genMeterChangeDate = "";
        String mGenMeterNo = null;

        if (null == custMeterNo && null != stm.get("CUST_METER_NO")) {
            mCustMeterNo = null != stm.get("oLMeterNext") ? stm.get("oLMeterNext").toString(): stm.get("CUST_METER_NO").toString();
        } else if (null != custMeterNo) {
            mCustMeterNo = custMeterNo;
            if (!custMeterNo.equals("ZERO")) custMeterChangeDate = new SimpleDateFormat(dateFormat).format(meterChangeDate);
            else mCustMeterNo = stm.get("CUST_METER_NO").toString();
        }
        if (null == genMeterNo && null != stm.get("GEN_METER_NO")) {
            mGenMeterNo = null != stm.get("oGMeterNext") ? stm.get("oGMeterNext").toString(): stm.get("GEN_METER_NO").toString();
        } else if (null != genMeterNo) {
            mGenMeterNo = genMeterNo;
            if (!genMeterNo.equals("ZERO"))  genMeterChangeDate = new SimpleDateFormat(dateFormat).format(meterChangeDate);
            else mGenMeterNo = stm.get("GEN_METER_NO").toString();
        }
        if (null != custMeterNo && null != genMeterNo && custMeterNo.equals(genMeterNo) && genMeterNo.equals("ZERO")) {}
        else {
            custMeterChangeDate = !custMeterChangeDate.isEmpty()?custMeterChangeDate: null == stm.get("CUST_METER_CHANGE_DATE") ? "":new SimpleDateFormat(dateFormat)
                    .format(stm.get("CUST_METER_CHANGE_DATE")).compareTo(new SimpleDateFormat(dateFormat).format(
                            stm.get("serviceDate"))) < 0? "": new SimpleDateFormat(dateFormat)
                    .format(stm.get("CUST_METER_CHANGE_DATE"));
            genMeterChangeDate = !genMeterChangeDate.isEmpty()?genMeterChangeDate:null == stm.get("GEN_METER_CHANGE_DATE")? "":new SimpleDateFormat(dateFormat)
                    .format(stm.get("GEN_METER_CHANGE_DATE")).compareTo(new SimpleDateFormat(dateFormat).format(
                            stm.get("serviceDate"))) < 0 ? "":new SimpleDateFormat(dateFormat)
                    .format(stm.get("GEN_METER_CHANGE_DATE"));
        }

        if (null != kwhs) {
            if (kwhs.getSAKwh().compareTo(BigDecimal.ZERO) < 0) {
                dKwh = "";
                tKwh = "";
                sAKwh = "";
                aExp = "";
                sExp = "";
                dExp = "";
                tExp = "";
                totalExp = "";
            } else {
                aExp = new BigDecimal(aExp).compareTo(kwhs.getAExp()) > 0 ? kwhs.getAExp().stripTrailingZeros().toPlainString(): aExp;
                sExp = new BigDecimal(sExp).compareTo(kwhs.getSExp()) > 0 ? kwhs.getSExp().stripTrailingZeros().toPlainString(): sExp;
                dExp = new BigDecimal(dExp).compareTo(kwhs.getDExp()) > 0 ? kwhs.getDExp().stripTrailingZeros().toPlainString(): dExp;
                tExp = new BigDecimal(tExp).compareTo(kwhs.getTExp()) > 0 ? kwhs.getTExp().stripTrailingZeros().toPlainString(): tExp;

                if (aExp.equals(zeroPointZero)) {
                    dKwh = zeroPointZero;
                    tKwh = zeroPointZero;
                    sAKwh = zeroPointZero;
                } else {
                    dKwh = dExp.equals(zeroPointZero) || dExp.equals("0") ? zeroPointZero: new BigDecimal(dKwh).compareTo(kwhs.getDKwh()) > 0 ? kwhs.getDKwh().stripTrailingZeros().toPlainString(): dKwh;
                    tKwh = tExp.equals(zeroPointZero) || tExp.equals("0") ? zeroPointZero: new BigDecimal(tKwh).compareTo(kwhs.getTKwh()) > 0 ? kwhs.getTKwh().stripTrailingZeros().toPlainString(): tKwh;
                    sAKwh = sAKwh.equals(zeroPointZero)? zeroPointZero: new BigDecimal(sAKwh).compareTo(kwhs.getSAKwh()) > 0 ? kwhs.getSAKwh().stripTrailingZeros().toPlainString(): sAKwh;
                }
                totalExp = aExp.equals(zeroPointZero) ? zeroPointZero: kwhs.getTotalExp().stripTrailingZeros().toPlainString();
            }
        } else {
            if (aExp.equals(zeroPointZero)) {
                dKwh = zeroPointZero;
                tKwh = zeroPointZero;
                sAKwh = zeroPointZero;
            } else {
                dKwh = dExp.equals(zeroPointZero) || dExp.equals("0") ? zeroPointZero: dKwh;
                tKwh = tExp.equals(zeroPointZero) || tExp.equals("0") ? zeroPointZero: tKwh;
                sAKwh = sAKwh.equals(zeroPointZero)? zeroPointZero: sAKwh;
            }
        }
        return ReportVo.builder().serviceId((String)stm.get("SERVICE_ID")).pwdsContractType((String)stm.get("PWDS_CONTRACT_TYPE"))
                .applId((String)stm.get("APPL_ID")).applName((String)stm.get("APPL_NAME"))
                .groupApplName(null == stm.get("GROUP_APPL_NAME") ? "": (String)stm.get("GROUP_APPL_NAME"))
                .applicantType((String)stm.get("APPLICANT_TYPE")).custElecNo((String)stm.get("CUST_ELEC_NO"))
                .custName((String)stm.get("CUST_NAME"))
                .groupCustName(null == stm.get("GROUP_CUST_NAME") ? "": (String)stm.get("GROUP_CUST_NAME"))
                .industryName(null == stm.get("INDUSTRY_NAME") ? "":(String)stm.get("INDUSTRY_NAME")).custResp("")
                .custVoltLevel((String)stm.get("CUST_VOLT_LEVEL"))
                .moContractKwh(((BigDecimal)stm.get("MO_CONTRACT_KWH")).stripTrailingZeros().toPlainString())
                .yrContractKwh(((BigDecimal)stm.get("YR_CONTRACT_KWH")).stripTrailingZeros().toPlainString())
                .custGroupType(null == stm.get("CUST_GROUP_TYPE") ? "":(String)stm.get("CUST_GROUP_TYPE")).custMeterNo(mCustMeterNo)
                .custMeterChangeDate(custMeterChangeDate)
                .genElecNo((String)stm.get("GEN_ELEC_NO")).genName((String)stm.get("GEN_NAME"))
                .groupGenName(null == stm.get("GROUP_GEN_NAME") ? "":(String)stm.get("GROUP_GEN_NAME"))
                .genResp((String)stm.get("GEN_RESP")).energyType((String)stm.get("ENERGY_TYPE"))
                .genGroupType(null == stm.get("GEN_GROUP_TYPE") ? "":(String)stm.get("GEN_GROUP_TYPE")).genMeterNo(mGenMeterNo)
                .equipVoltLevel((String)stm.get("EQUIP_VOLT_LEVEL"))
                .deviceCapacity(null == stm.get("DEVICE_CAPACITY") ? "":stm.get("DEVICE_CAPACITY").toString())
                .genPwPercent(((BigDecimal)stm.get("GEN_PW_PERCENT")).stripTrailingZeros().toPlainString())
                .genMeterChangeDate(genMeterChangeDate)
                .billYear(vv.getBillYear()).billMonth(vv.getBillMonth()).serviceYear(vv.getServiceYear())
                .serviceMonth(vv.getServiceMonth()).dKwh(bigDecimalStrShortZero(dKwh, null)).tKwh(bigDecimalStrShortZero(tKwh, null))
                .sAKwh(bigDecimalStrShortZero(sAKwh, null)).dExp(bigDecimalStrShortZero(dExp, null))
                .tExp(bigDecimalStrShortZero(tExp, null)).aExp(bigDecimalStrShortZero(aExp, null))
                .sExp(bigDecimalStrShortZero(sExp, null))
                .totalExp(bigDecimalStrShortZero(totalExp, null)).serviceDate(vv.getServiceDate()).billDate(vv.getBillDate())
                .applicationGeneratorId(vv.getApplicationGeneratorId()).applicationLoadId(vv.getApplicationLoadId()).build();
    }

    private KwhsASDTVo subtractTwoKwh(KwhsASDTVo small, KwhsASDTVo big) {
        BigDecimal aExp = big.getAExp().compareTo(small.getAExp()) > 0 ? big.getAExp().subtract(small.getAExp()): big.getAExp().compareTo(zeroBigDecimal) > 0? big.getAExp(): zeroPointBigDecimal;
        BigDecimal dKwh = zeroBigDecimal;
        BigDecimal tKwh = zeroBigDecimal;
        BigDecimal sAKwh = zeroBigDecimal;
        if (!aExp.equals(zeroBigDecimal)) {
            dKwh = big.getDKwh().compareTo(small.getDKwh()) > 0 ? bigDecimalShortZero(big.getDKwh().subtract(small.getDKwh()), null): big.getDKwh();
            tKwh = big.getTKwh().compareTo(small.getTKwh()) > 0 ? bigDecimalShortZero(big.getTKwh().subtract(small.getTKwh()), null): big.getTKwh();
            sAKwh = big.getSAKwh().compareTo(small.getSAKwh()) > 0 ? bigDecimalShortZero(big.getSAKwh().subtract(small.getSAKwh()), null): big.getSAKwh();
        }
        BigDecimal sExp = big.getSExp().compareTo(small.getSExp()) > 0 ? bigDecimalShortZero(big.getSExp().subtract(small.getSExp()), null): big.getSExp();
        BigDecimal dExp = big.getDExp().compareTo(small.getDExp()) > 0 ? bigDecimalShortZero(big.getDExp().subtract(small.getDExp()), null): big.getDExp();
        BigDecimal tExp = big.getTExp().compareTo(small.getTExp()) > 0 ? bigDecimalShortZero(big.getTExp().subtract(small.getTExp()), null): big.getTExp();
        BigDecimal totalExp = bigDecimalShortZero(aExp.add(sExp).add(dExp).add(tExp), null);
        return KwhsASDTVo.builder().dKwh(dKwh).tKwh(tKwh).sAKwh(sAKwh).aExp(aExp).sExp(sExp).dExp(dExp).tExp(tExp)
                .totalExp(totalExp).build();
    }

    public Map<Integer, KwhsASDTVo> getYearFuelCodeRate(int yearStart, int yearEnd) {
        List<IFuelCodeRate> base = pwFuelRateRepository.getFuelRateByYears(yearStart, yearEnd);
        Map<Integer, KwhsASDTVo> yearRate = new HashMap<>();
        for (IFuelCodeRate b:base) {
            int year = b.getYear();
            if (yearRate.containsKey(year)) {
                KwhsASDTVo vo = yearRate.get(year);
                if (b.getCode().equals("S")) vo.setSExp(b.getRate());
                else if (b.getCode().equals("D")) vo.setDExp(b.getRate());
                else if (b.getCode().equals("T")) vo.setTExp(b.getRate());
            } else { // if (b.getCode().equals("A"))
                yearRate.put(year, KwhsASDTVo.builder().aExp(b.getRate()).build());
            }
        }
        return yearRate;
    }

    private String matchedPowerInfo(BigDecimal dirMatched, BigDecimal matchedRm) {
        BigDecimal tmp = matchedRm == null ? dirMatched : matchedRm;
        if (null == tmp) return null;
        return tmp.stripTrailingZeros().toPlainString();
    }

    private String bigDecimalStrShortZero(String bigDecimal, Boolean mark) {
        if (null == bigDecimal || bigDecimal.isEmpty()) return bigDecimal;
        return new BigDecimal(bigDecimal).setScale(0, RoundingMode.HALF_UP).toPlainString();
    }

    public BigDecimal bigDecimalShortZero(BigDecimal bigDecimal, Boolean mark) {
        if (null == bigDecimal) return null;
        else if (null == mark) {
            return bigDecimal.setScale(0, RoundingMode.HALF_UP).stripTrailingZeros(); // 四捨五入進位到整數
        }
        else if (mark)
            return bigDecimal.stripTrailingZeros().equals(BigDecimal.ZERO) ? zeroPointBigDecimal: bigDecimal.stripTrailingZeros(); // 0.0
        else return bigDecimal.stripTrailingZeros();
    }

    private String regularDateStr(Date date) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
        return formatter.format(date);
    }

    public Date regularDate(Date date) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
        String dateStr = formatter.format(date);
        return formatter.parse(dateStr);//return DateUtils.getTruncatedDate(date);
    }

    private Boolean compareServiceDateChangeDate(Date serviceDate, Date custMaterChangeDate, Date genMeterChangeDate) {
        if (null == custMaterChangeDate && null == genMeterChangeDate) return null;
        Integer serviceInt = Integer.parseInt(serviceDate.toString().split("-")[1]);
        if (null != custMaterChangeDate) {
            Integer custInt = Integer.parseInt(custMaterChangeDate.toString().split("-")[1]);
            if (custInt != serviceInt) return false;
        }
        if (null != genMeterChangeDate) {
            Integer genInt = Integer.parseInt(genMeterChangeDate.toString().split("-")[1]);
            if (genInt != serviceInt) return false;
        }
        return true;
    }

    private List<String> extractElecNo(Set<String> elecMeterList) {
        List<String> elecNos = new ArrayList<>();
        for (String k:elecMeterList) {
            String[] kk = k.split("~");
            elecNos.add(kk[0]);
        }
        return elecNos;
    }

    private Date dateStrToDate(String dateStr) throws Exception {
        if (dateStr.length() > 10) {
            SimpleDateFormat formatter = new SimpleDateFormat(dateFormatFull);
            return formatter.parse(dateStr);
        }
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
        return formatter.parse(dateStr);
    }

    public String dateStr(Date date) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
        return formatter.format(date);
    }

    private String yearMonthStr(Date date) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
        String dateStr = formatter.format(date);
        if (!dateStr.isEmpty() && dateStr.contains("-")) {
            String[] inv = dateStr.split("-");
            return inv[0] + "-" + inv[1] + '-';
        }
        return null;
    }

    public int getYear(Date date) {
        Calendar after = Calendar.getInstance();
        after.setTime(date);
        return after.get(Calendar.YEAR);
    }

    public int getMonth(Date date) {
        Calendar after = Calendar.getInstance();
        after.setTime(date);
        return after.get(Calendar.MONTH) + 1;
    }

    public int getDay(Date date) {
        Calendar after = Calendar.getInstance();
        after.setTime(date);
        return after.get(Calendar.DAY_OF_MONTH);
    }

    private int getDateOfMonth(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        return after.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 取得該月 首日 與 結束日 idx 為提前祭天結束
     * @param runDate
     * @return java.util.List
     * @throws Exception
     */
    private List<String> getMonthStartDateEndDateString(Date runDate) throws Exception {
        List<String> sDateEDate = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
        sDateEDate.add(formatter.format(runDate));
        Date after = getMonthEndDate(runDate);
        sDateEDate.add(formatter.format(new Date(after.getTime() - 1)));
        return sDateEDate;
    }

    private BSMIOutDateStrBillDateRangeVo getMonthDayRangeString(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
        String[] ddStr = formatter.format(calendar.getTime()).split("-");
        String twY = Integer.toString(Integer.parseInt(ddStr[0])-1911);
        String dd = ddStr[1];
        calendar.add(Calendar.YEAR, 0);
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);//月初
        long time = calendar.getTimeInMillis();
        Date startDate = new Date(time);
        String startDateStr = simpleDateFormat.format(startDate);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));//月底
        time = calendar.getTimeInMillis();
        Date endDate = new Date(time);
        String endDateStr = simpleDateFormat.format(endDate); //.outDateStr(twY+"_"+dd+"月份")
        return BSMIOutDateStrBillDateRangeVo.builder().inDateStr(twY+dd).billDateRange(startDateStr+" ~ "+endDateStr).build();
    }

    private Date getNextMinutes(Date date, int min) {
        Calendar after = Calendar.getInstance();
        after.setTime(date);
        after.add(Calendar.MINUTE, min);
        return after.getTime();
    }

    private Date getNext5Min(Date date) {
        Calendar after = Calendar.getInstance();
        after.setTime(date);
        after.add(Calendar.MINUTE, 5);
        return after.getTime();
    }

    public Date getNextDate(Date date) {
        Calendar after = Calendar.getInstance();
        after.setTime(date);
        after.add(Calendar.HOUR_OF_DAY, 1);
        return after.getTime();
    }

    private String getAfterTwoMonthFirstDateStr(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        after.add(Calendar.MONTH, 2);
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
        return formatter.format(after.getTime());
    }

    public Date getNextMonthFirstDate(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        after.add(Calendar.MONTH, 1);
        return after.getTime();
    }

    private Date getBeforeMonthFirstDate(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        after.add(Calendar.MONTH, -1);
        return after.getTime();
    }

    public Date getMonthFirstDate(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        return new GregorianCalendar(after.get(Calendar.YEAR), after.get(Calendar.MONTH), 1).getTime();
    }

    private Date getMonthEndDate(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        after.add(Calendar.MONTH, 1);
        after.add(Calendar.SECOND, -1);
        return after.getTime();
    }

    private Date getBeforeYearDecemberFirstDay(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        return new GregorianCalendar(after.get(Calendar.YEAR) - 1, Calendar.DECEMBER, 1).getTime();
    }

    public boolean checkCurrentYearMonth(Date runDate) {
        Date tt = getMonthFirstDate(new Date());
        Date nn = getNextMonthFirstDate(runDate);
        Integer cc = applicationMonthlyCapacitySettlementRepository.countCurrentErpMode(tt, nn);
        return tt.compareTo(runDate) == 0 && cc == 0;
    }

    public Date getNextYearFirstDay(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        return new GregorianCalendar(after.get(Calendar.YEAR) + 1, Calendar.JANUARY, 1).getTime();
    }

    public Date getYearFirstDay(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        return new GregorianCalendar(after.get(Calendar.YEAR), Calendar.JANUARY, 1).getTime();
    }

    public Date getYearLastMonthFirstDay(Date runDate) {
        Calendar after = Calendar.getInstance();
        after.setTime(runDate);
        return new GregorianCalendar(after.get(Calendar.YEAR), Calendar.DECEMBER, 1).getTime();
    }

    private String collectLoads(List<String> loads, List<String> timeList) {
        String loadsStr = "";
        int count = 0;
        if (day15Str.length == loads.size()) {
            for (int i = 0; i < day15Str.length; i++)
                loadsStr += loads.get(i) +",";
        } else {
            for (int i = 0; i < day15Str.length; i++) {
                if (count < timeList.size() && timeList.get(count).equals(day15Str[i])) {
                    loadsStr += count < loads.size()? loads.get(count) + ",": ",";
                    count ++;
                    if (count < timeList.size() && timeList.get(count-1).equals(timeList.get(count))) count ++;
                } else loadsStr += ",";
            }
        }

        return loadsStr.substring(0, loadsStr.length() - 1);
    }
}