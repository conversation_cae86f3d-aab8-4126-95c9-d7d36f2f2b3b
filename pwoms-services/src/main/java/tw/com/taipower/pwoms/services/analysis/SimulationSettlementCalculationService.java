package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.services.enumclass.SettlementMethodEnum;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Log4j2
@Service
public class SimulationSettlementCalculationService {

    @Autowired
    private SimulationSettlementCalculationRepository calculationRepository;

    @Autowired
    private SimulationSettlementCalculationRecordRepository recordRepository;

    @Autowired
    private SimulationSettlementCapacityCalculationRepository capacityCalculationRepository;

    @Autowired
    private SimulationSettlementSumCapacityCalculationRepository sumCapacityCalculationRepository;

    @Autowired
    private SimulationSettlementCalculationFailureRecordRepository failureRecordRepository;

    public long saveCalculation(SimulationSettlementCalculation sc, List<Long> appIdList, String reCalReason) {
        try {
            List<SimulationSettlementCalculationRecord> calculationRecordList = new ArrayList<>();

            //settlement calculation record SETTLEMENT_CALCULATION_RECORDE
            for (Long appId : appIdList) {
                calculationRecordList.add(SimulationSettlementCalculationRecord.builder()
                        .settlementId(sc.getSettlementId())
                        .applicationId(appId)
                        .reCalReason(reCalReason)
                        .build());
            }
            recordRepository.saveAll(calculationRecordList);

            //capacity SETTLEMENT_CAPACITY_CALCULATION
            capacityCalculationRepository.saveAllByDateAndSettlementIdAndAppIdIn(sc.getServiceDate(), sc.getSettlementId(), appIdList);
            //sum capacity SETTLEMENT_SUM_CAPACITY_CALCULATION
            sumCapacityCalculationRepository.saveAllByDateAndSettlementIdAndUpdatePercentage(sc.getServiceDate(), sc.getSettlementId());

            return sc.getSettlementId();
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return -1;
    }

    public SimulationSettlementCalculation saveCalculation(SimulationSettlementCalculation calculation) {
        try {
            return calculationRepository.saveAndFlush(calculation);
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return null;
    }

    public Long getLatestIdCalculation(SettlementMethodEnum methodEnum) {
        return calculationRepository.findTop1CalculationMethod(methodEnum.getId());
    }

    public void deleteAllRecordBySettlementId(Long settlementId) {
        calculationRepository.deleteAllRecordBySettlementId(settlementId);
    }

    public boolean updateCalculationMethodAndLog(Long settlementId, short calMethod, String progressLog, BigDecimal percentage) {
        try {
            calculationRepository.updateCalculationMethodAndProgressBySettlementId(settlementId, calMethod, progressLog, percentage);
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return false;
    }

    public Map<Long, Long> getApplicationIdAndRecordId(Long settlementId, List<Long> appIdList) {
        Map<Long, Long> appRecordIdMap = new HashMap<>();

        List<Map<String, Object>> appRecordIdList = recordRepository.findBySettlementIdAndAppIdList(settlementId, appIdList);

        if (CollectionUtils.isNotEmpty(appRecordIdList)) {
            for (Map<String, Object> appRecordId : appRecordIdList) {
                appRecordIdMap.put((Long) appRecordId.get("APPLICATION_ID"), (Long) appRecordId.get("ID"));
            }
        }
        return appRecordIdMap;
    }

    public boolean saveCalculationFailureRecord(List<SimulationSettlementCalculationFailureRecord> failureRecordList) {
        try {
            failureRecordRepository.saveAllAndFlush(failureRecordList);
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return false;
    }

    public boolean updateRecord(List<Long> recordIdList, boolean success, String reason) {
        try {
            recordRepository.updateExecution(recordIdList, success, reason);
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return false;
    }

    public Map<Long, List<Long>> getLatestIdCalculationInOrder(Date serviceDate, List<Long> appIdList, List<Short> calMethodList, Long exceptSettlementId) {
        List<Long> remainAppIdList = new ArrayList<>(appIdList);

        //1. sent ERP without cancellation
        Map<Long, Long> appIdSettlementIdMap = new HashMap<>();
        Map<Long, Long> erpMap = convertObjectToAppIdAndSettlementIdMap(calculationRepository.findLatestIdByServiceDateAndApplicationIdInAndErpSentDateIsNotNull(serviceDate, remainAppIdList, calMethodList));
        if (MapUtils.isNotEmpty(erpMap)) {
            appIdSettlementIdMap.putAll(erpMap);
            remainAppIdList.removeAll(erpMap.keySet());
        }

        //2. sent ERP and cancellation
        if (CollectionUtils.isNotEmpty(remainAppIdList)) {
            Map<Long, Long> erpCancelMap = convertObjectToAppIdAndSettlementIdMap(calculationRepository.findLatestIdByServiceDateAndApplicationIdInAndErpCancellationDateIsNotNull(serviceDate, remainAppIdList, calMethodList));
            if (MapUtils.isNotEmpty(erpCancelMap)) {
                appIdSettlementIdMap.putAll(erpCancelMap);
                remainAppIdList.removeAll(erpCancelMap.keySet());
            }
        }

        //3. not send to ERP
        if (CollectionUtils.isNotEmpty(remainAppIdList)) {
            Map<Long, Long> notInErpMap = convertObjectToAppIdAndSettlementIdMap(calculationRepository.findLatestIdByServiceDateAndApplicationIdInAndNotInErpAndNotSettlementId(exceptSettlementId, serviceDate, remainAppIdList, calMethodList));
            if (MapUtils.isNotEmpty(notInErpMap)) {
                appIdSettlementIdMap.putAll(notInErpMap);
                remainAppIdList.removeAll(notInErpMap.keySet());
            }
        }
        return convertToSettlementIdAndAppIdMap(appIdSettlementIdMap);
    }

    private Map<Long, Long> convertObjectToAppIdAndSettlementIdMap(List<Map<String, Object>> nameValeMapList) {
        Map<Long, Long> appIdSettlementIdMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(nameValeMapList)) {
            for (Map<String, Object> nameValue : nameValeMapList) {
                //SC.SETTLEMENT_ID, SCR.APPLICATION_ID
                Long appId = (Long) nameValue.get("APPLICATION_ID");
                if (!appIdSettlementIdMap.containsKey(appId)) {
                    Long settlementId = (Long) nameValue.get("SETTLEMENT_ID");
                    appIdSettlementIdMap.put(appId, settlementId);
                }
            }
        }
        return appIdSettlementIdMap;
    }

    private Map<Long, List<Long>> convertToSettlementIdAndAppIdMap(Map<Long, Long> setIdAndAppIdMap) {
        if (MapUtils.isNotEmpty(setIdAndAppIdMap)) {
            return setIdAndAppIdMap.entrySet().stream().collect(groupingBy(Map.Entry::getValue
                    , Collectors.mapping(Map.Entry::getKey, Collectors.toList())));
        }
        return new HashMap<>();
    }

    public boolean updateCalculationAndRecordResult(Long settlementId, List<Long> appIdList, boolean success) {
        try {
            calculationRepository.updateResultBySettlementIdAndApplicationIdIn(settlementId, appIdList, success);
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return false;
    }

    public boolean updateResultBySettlementIdAndExecutionResultIsNull(Long settlementId, boolean success) {
        try {
            calculationRepository.updateResultBySettlementIdAndExecutionResultIsNull(settlementId, success);
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return false;
    }

    public boolean updateCalculationResult(Long settlementId) {
        try {
            calculationRepository.updateResultBySettlementId(settlementId);
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return false;
    }

    public SimulationSettlementCalculation getCalculationById(Long settlementId) {
        return calculationRepository.findBySettlementId(settlementId);
    }

}
