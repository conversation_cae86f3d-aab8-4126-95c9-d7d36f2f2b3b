package tw.com.taipower.pwoms.services.report;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.pwoms.services.account.TaipowerCompanyUnitService;
import tw.com.taipower.pwoms.services.tpc.RemsService;
import tw.com.taipower.pwoms.services.vo.report.*;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static tw.com.taipower.pwoms.services.constant.Constants.*;

@Log4j2
@Service
public class PublishService {

    @Autowired
    ReportService service;

    @Autowired
    FileService fileService;

    @Autowired
    CsvService csvService;

    @Autowired
    ZipService zipService;

    @Autowired
    RemsService remsService;

    @Autowired
    TaipowerCompanyUnitService companyUnitService;

    public List<String> triggerSyncSFTP(Date runDate) throws Exception {
        csvService.beforeTriggerSftp(TMP_SFTP_FOLDER);
        Date billDate = service.regularDate(runDate);
        int twY = service.getYear(runDate) - 1911;
        int month = service.getMonth(runDate);
        List<String> outFileNames = new ArrayList<>();
        // #13 調度處發電餘電總計
        String oName = putYearPwDsPowerInfo(billDate, twY);
        if (null == oName) return null;
        outFileNames.add(oName);
        // #14 調度處發轉餘電表
        List<ReportGen15TranReVo> genPower = service.getAmi15PowerContractNo(billDate); // 發電量
        List<ReportGen15TranReSummaryVo> genPowerSummary = service.getAmi15PowerContractNoSummary(billDate); // 統計
        oName = TMP_SFTP_FOLDER+String.format("附件一、調度處發轉餘電表-%d年%02d月.xlsx", twY, month);
        fileService.loadFile(genPowerSummary, oName, ".xlsx", "ami15PowerContractSummary"
                , genPower, null);
        outFileNames.add(oName);
        // #15 調度處轉直供服務各類度數
        List<ReportInfoRateVo> eRate = service.getMonthExpRate(billDate);
        oName = TMP_SFTP_FOLDER+String.format("附件二、%d年%02d月-轉直供服務各類度數.xlsx", twY, month);
        fileService.loadFile(eRate, oName, ".xlsx", String.format("expRate~%d年%d月", twY, month), null, null);
        outFileNames.add(oName);
        // #16 調度處發電月報
        List<ReportGenComVo> genRate = service.getGeneratorCompanies(billDate);
        oName = TMP_SFTP_FOLDER+String.format("附件三、提供調度處發電月報(%d年%02d月).xlsx", twY, month);
        fileService.loadFile(genRate, oName, ".xlsx", "genCom~發電月報", null, null);
        outFileNames.add(oName);
        // #17 調度處線損小組報表
        List<ReportLocFuelVoltVo> genFuelVolt = service.getTpcLocationFuelVoltKwh(billDate, true);
        List<ReportLocFuelVoltVo> loadFuelVolt = service.getTpcLocationFuelVoltKwh(billDate, false);
        List<ReportTpcCodeUnitVo> tpcCom = companyUnitService.findTpcCodeUnitName();
        oName = TMP_SFTP_FOLDER+String.format("提供調度處線損計算(%d年%02d月).xlsx", twY, month);
        fileService.loadFile(genFuelVolt, oName, ".xlsx", "genLoadTpcCom~線損計算", loadFuelVolt, tpcCom);
        outFileNames.add(oName);
        // #18 調度處高雄報表
        List<ReportGenCapacityVo> expRate = service.getGenDeviceCapacities(billDate);
        String name = TMP_SFTP_FOLDER+String.format("轉直供資料_民國%d年%02d月", twY, month);
        oName = name+".xlsx";
        fileService.loadFile(expRate, oName, ".xlsx", "genDeviceComCapacities", null, null);
        String tmpFolder = csvService.tmpFolder();
        String zipPath = tmpFolder+name+".zip";
        zipService.compressOneFileWithMode(tmpFolder+oName, zipPath, ZIP_KAOHSIUNG_GEN_DEVICE_MODE, true);
        outFileNames.add(name+".zip");
        // #19 調度處15分鐘發用電量報表
        List<ReportGenLoad15powerVo> gPower = service.getAmi15GeneratorLoadEndPowers(billDate, true);
        List<ReportGenLoad15powerVo> lPower = service.getAmi15GeneratorLoadEndPowers(billDate, false);
        oName = TMP_SFTP_FOLDER+String.format("調度處15分鐘發用電量報表(%d年%02d月).xlsx", twY, month);
        fileService.loadFile(gPower, oName, ".xlsx", "ami15GenLoadPowers", lPower, null);
        outFileNames.add(oName);
        return outFileNames;
    }

    public String putYearPwDsPowerInfo(Date billDate, int twY) throws Exception {
        // #13 調度處發電餘電總計
        List<ReportGenTransReYearVo> pwDsPower = service.getYearPwDsPowerInfo(billDate);
        if (null == pwDsPower || pwDsPower.isEmpty()) return null;
        String filename = String.format("%d年-每月發轉餘總計V2.csv", twY);
        String oName = TMP_SFTP_FOLDER+filename;
        ByteArrayInputStream res = fileService.loadFile(pwDsPower, oName, ".csv", "YearPwDsPower"
                , null, null);
        remsService.putPDCFileToSftp(new String(res.readAllBytes()), filename, SFTP_PDC_DATE_REMOTE_FOLDER_DATA, SFTP_PDC_MODE); // Boolean bb =
        return oName;
    }
}
