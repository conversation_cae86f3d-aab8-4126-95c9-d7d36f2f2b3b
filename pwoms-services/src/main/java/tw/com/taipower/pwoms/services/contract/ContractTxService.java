package tw.com.taipower.pwoms.services.contract;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lowagie.text.Document;
import com.lowagie.text.Font;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import jakarta.transaction.Transactional;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.Application;
import tw.com.taipower.data.entity.pwoms.ApplicationWorkflow;
import tw.com.taipower.data.entity.pwoms.WorkflowStepMapping;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.logger.CustomLog;
import tw.com.taipower.pwoms.services.BaseService;
import tw.com.taipower.pwoms.services.entitymanage.GeneratorService;
import tw.com.taipower.pwoms.services.entitymanage.LoadService;
import tw.com.taipower.pwoms.services.enumclass.ApplicationStage3Status;
import tw.com.taipower.pwoms.services.enumclass.ApplicationStage9Status;
import tw.com.taipower.pwoms.services.enumclass.ApplicationWorkflowStatus;
import tw.com.taipower.pwoms.services.enumclass.ErrorCode;
import tw.com.taipower.pwoms.services.flowcontrol.WorkflowService;
import tw.com.taipower.pwoms.services.flowcontrol.WorkflowStage;
import tw.com.taipower.pwoms.services.vo.flowcontrol.MainUnitAccountVO;
import tw.com.taipower.pwoms.services.vo.generated.ApplicationVO;
import tw.com.taipower.pwoms.services.vo.generated.ApplicationWorkflowVO;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static tw.com.taipower.pwoms.services.flowcontrol.WorkflowGroup.*;

/**
 * 案件契約管理服務
 *
 * @class: ApplicantEntityService
 * @author: linyu-sheng
 * @version: 0.0.0
 * @since: 2024/5/3
 * @see:
 */
@Service
@CustomLog
public class ContractTxService extends BaseService {

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private ApplicationGeneratorRepository applicationGeneratorRepository;

    @Autowired
    private ApplicationLoadRepository applicationLoadRepository;

    @Autowired
    private GeneratorService generatorService;

    @Autowired
    private WorkflowStepMappingRepository workflowStepMappingRepository;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private ApplicationWorkflowRepository applicationWorkflowRepository;

    @Autowired
    private LoadService loadService;

    @Autowired
    private TaipowerCompanyUnitRepository taipowerCompanyUnitRepository;

    /**
     * 新建契約流程開始
     *
     * @param applicationId
     * @param userId
     */
    @Transactional
    public void startNewContract(Long applicationId, Long userId) {
        var application = applicationRepository.findById(applicationId).orElseThrow();
        var submitAt = application.getSubmittedAt();
        var steps = workflowStepMappingRepository.findByCustom1(PLAN, submitAt, WorkflowStage.CONTRACT);
        var step1 = steps.stream().findFirst().orElseThrow();
        var mainUnitVO = workflowService.getMainUnitAndAccounts(step1, application);

        /**
         * 紀錄審核作業
         */
        var workflowJob = ApplicationWorkflow.builder().applicationId(application.getId()).status(
                        ApplicationWorkflowStatus.WAITING.getValue()) // 待審核
                .stepId(step1.getId()).tpcDeptId(mainUnitVO.getMainUnit()).createdAt(new Date()).build();

        applicationWorkflowRepository.save(workflowJob);
        /**
         * 依據審核作業敘述發通知
         */
        String subject = step1.getStepDesc();
        String context = String.format("契約編號: %s 待審查，請儘速查看", contractNo(application));
        application.setStage3Status(ApplicationStage3Status.SUBMITTED.getValue());
        saveApplication(application);
        // applicationRepository.updateStage3Status(application.getId(),
        // ApplicationStage3Status.SUBMITTED.getValue());
        this.sendNotificationWithAccount(userId, mainUnitVO.getReceivers(), subject, context, workflowJob.getId(),
                WorkflowStage.CONTRACT);
    }

    @Transactional
    public void reviewApplicationWorkflow(ApplicationWorkflowVO applicationWorkflowVO, ApplicationWorkflowStatus status,
                                          Long userId) throws JsonProcessingException {
        var applicationWorkflowId = applicationWorkflowVO.getId();
        /**
         * 根據id查詢資料庫中的紀錄，目前VO沒有效果
         */
        var applicationWorkflow = applicationWorkflowRepository.findById(applicationWorkflowId).orElseThrow();

        if (applicationWorkflow.getStatus() == 1) {
            if (!status.equals(ApplicationWorkflowStatus.COMPLETE)) {
                throwException(ErrorCode.APPLICATION_WORKFLOW_HAS_SUBMITTED);
            }
        } else if (applicationWorkflow.getStatus() != 0) {
            throwException(ErrorCode.APPLICATION_WORKFLOW_HAS_SUBMITTED);
        }

        applicationWorkflow.setStatus(status.getValue());
        applicationWorkflow.setModifiedBy(userId);
        applicationWorkflow.setComment(applicationWorkflowVO.getComment());
        applicationWorkflowRepository.save(applicationWorkflow);
        /**
         * 特殊情境，如果狀態從半完成變成完成的話，不會增設額外作業。
         * 應對簽約作業時，需要將半完成的作業完成。
         */
        if (status.equals(ApplicationWorkflowStatus.COMPLETE)) {
            /**
             * 本步驟完成，看下一個步驟要做什麼處理
             */
            var currentStep = workflowStepMappingRepository.findById(applicationWorkflow.getStepId()).orElseThrow();
            processStep(applicationWorkflow, currentStep, applicationWorkflow.getApplicationId(), userId);
        } else if (status.equals(ApplicationWorkflowStatus.WAIT_FOR_DOCUMENTS)) {
            /**
             * 本步驟完成，看下一個步驟要做什麼處理
             */
            /**
             * 寄送電子郵件給申請者，但是待補件的通知訊息要通知申請者哪些訊息還沒決定
             */
            var application = applicationRepository.findById(applicationWorkflowVO.getApplicationId()).orElseThrow();
            String subject2 = String.format("轉直供計畫書提交確認信（契約編號%s）待補件", contractNo(application));
            String context2 = "請儘速補齊文件。";
            try {
                context2 = context2 + "\n" + applicationWorkflowVO.getComment();
                // 補件要多紀錄原因
                application.appendNotes(applicationWorkflowVO.getComment());
                applicationRepository.save(application);
            } catch (Throwable ex) {
                log.error(ex, ex);
            }

            workflowService.sendAppliationSubjectToApplicant(application.getId(), userId, subject2, context2,
                    applicationWorkflowVO.getId(), 3);
        } else {
            throwException(ErrorCode.WORKFLOW_UNDEFINED);
        }

    }

    /**
     * 審查下一步驟
     *
     * @param preWorkflow
     * @param step
     * @param applicationId
     * @param userId
     * @throws JsonProcessingException
     */
    public void processStep(ApplicationWorkflow preWorkflow, WorkflowStepMapping step, Long applicationId,
                            Long userId) throws JsonProcessingException {
        var application = applicationRepository.findById(applicationId).orElseThrow();
        var processMethod = step.getProcessMethod();
        var reviewTotalSteps = workflowStepMappingRepository.findByCustom1(step.getStepGroup(),
                application.getSubmittedAt(),
                WorkflowStage.CONTRACT);
        WorkflowStepMapping nextStep = null;
        if (step.getSubSeq() != null) {
            try {
                /**
                 * 這邊步驟不是太肯定，如果有subSeq代表是子流程，先以子流程subSeq+1找下一步
                 * 子流程都找不到再看看有沒有主流程stepSeq+1的步驟
                 */
                nextStep = reviewTotalSteps.stream().filter(
                                a -> (a.getStepSeq().equals(step.getStepSeq()) && a.getSubSeq() != null && a.getSubSeq().equals(
                                        step.getSubSeq() == null ? null : step.getSubSeq() + 1)))
                        .findFirst().orElse(null);
                if (nextStep == null) {
                    nextStep = reviewTotalSteps.stream().filter(
                            a -> (a.getStepSeq().equals(step.getStepSeq() + 1))).findFirst().orElse(null);
                }
            } catch (Exception e) {

            }
        } else {
            nextStep = reviewTotalSteps.stream().filter(
                    a -> (a.getStepSeq().equals(step.getStepSeq() + 1))).findFirst().orElse(null);
        }

        if (processMethod.equals("DISPATCH_WORKFLOW2")) {
            var allSteps = reviewTotalSteps.stream().filter(a -> a.getStepSeq().equals(2)).filter(
                    a -> a.getFrontHandleMethod().equals("#2a")).toList();
            for (WorkflowStepMapping currentStep : allSteps) {
                var vo = getMainUnitAndAccounts(currentStep, application);
                commonWorkFlow(preWorkflow, currentStep, vo, application, userId);
            }
            application.setStage3Status(ApplicationStage3Status.SUBMITTED.getValue());
            saveApplication(application);
        } else if (processMethod.equals("DISPATCH_WORKFLOW3")) {
            if (isApplicationNeed2bSign(application)) {
                var allSteps = reviewTotalSteps.stream().filter(a -> a.getStepSeq().equals(2)).filter(
                        a -> a.getFrontHandleMethod().equals("#2b")).toList();
                for (WorkflowStepMapping currentStep : allSteps) {
                    var vo = getMainUnitAndAccounts(currentStep, application);
                    commonWorkFlow(preWorkflow, currentStep, vo, application, userId);
                }
            } else {
                contract2to3flow(preWorkflow, applicationId, userId, reviewTotalSteps, application);
            }

        } else if (processMethod.equals("DISPATCH_WORKFLOW3S")) {
            contract2to3flow(preWorkflow, applicationId, userId, reviewTotalSteps, application);
            generateTransferInformation(application);
            //TODO generate pdf and send notification and email
        } else if (processMethod.equals("DISPATCH_WORKFLOW#3AFTER")) {

            /**
             * #3 作業因為有狀態變更，所以額外拉出作業
             */
            dispatchWorkflow6(preWorkflow, applicationId, userId, reviewTotalSteps, application);
            application.setStage3Status(ApplicationStage3Status.CONTRACT_START.getValue());
            saveApplication(application);
        } else if (processMethod.equals("DISPATCH_WORKFLOW#6")) {

            /**
             * 檢查當前契約，是否所有stepSeq3的審查作業都已經結束
             */
            dispatchWorkflow6(preWorkflow, applicationId, userId, reviewTotalSteps, application);

        } else if (processMethod.equals("DISPATCH_WORKFLOW#7")) {
            var allSteps = reviewTotalSteps.stream().filter(a -> a.getStepSeq().equals(step.getStepSeq() + 1)).toList();
            for (WorkflowStepMapping currentStep : allSteps) {
                var vo = getMainUnitAndAccounts(currentStep, application);
                commonWorkFlow(preWorkflow, currentStep, vo, application, userId);
            }

        } else if (processMethod.equals("FINISH")) {
            /**
             * 如果有父契約的話
             */
            if (application.getParentId() != null) {
                var onlineAt = application.getOnlineAt();
                /**
                 * 有父契約的情形，需要將父契約打上結束日期
                 * 結束日期為上線日期前一日
                 */
                var parentApplication = applicationRepository.findById(application.getParentId()).orElseThrow();
                var contractEnd = DateUtils.addDays(onlineAt, -1);
                parentApplication.setContractedEnd(contractEnd);
                parentApplication.setStage9Status(ApplicationStage9Status.TERMINATED.getValue());
                saveApplication(parentApplication);
            }
            application.setStage3Status(ApplicationStage3Status.COMPLETED.getValue());
            saveApplication(application);
        } else if (processMethod.equals("NONE")) {

        }

    }

    /**
     * 流程 #2 to #3
     *
     * @param preWorkflow
     * @param applicationId
     * @param userId
     * @param reviewTotalSteps
     * @param application
     */
    private void contract2to3flow(ApplicationWorkflow preWorkflow, Long applicationId, Long userId,
                                  List<WorkflowStepMapping> reviewTotalSteps, Application application) {
        var allSteps = reviewTotalSteps.stream().filter(a -> a.getStepSeq().equals(3)).toList();
        for (WorkflowStepMapping currentStep : allSteps) {
            // 移除#4 特殊處理
            var vo = getMainUnitAndAccounts(currentStep, application);
            commonWorkFlow(preWorkflow, currentStep, vo, application, userId);

        }
        application.setStage3Status(ApplicationStage3Status.CONTRACT_REVIEWING.getValue());
        saveApplication(application);
    }

    /**
     * 判斷契約是否需要經過總經理簽核
     *
     * @param application
     * @return
     */
    public boolean isApplicationNeed2bSign(Application application) {
        var contractDeptId = application.getContractDeptId();
        return need2bsignDept(contractDeptId);
    }

    /**
     * 判斷契約是否需要經過總經理簽核
     *
     * @param application
     * @return
     */
    public boolean isApplicationNeed2bSign(ApplicationVO application) {
        var contractDeptId = application.getContractDeptId();
        return need2bsignDept(contractDeptId);
    }

    /**
     * 需要送總經理簽核的單位
     *
     * @param contractDeptId
     * @return
     */
    private boolean need2bsignDept(Integer contractDeptId) {
        var contractDept = taipowerCompanyUnitRepository.findById(contractDeptId).orElseThrow();
        List<String> codes = List.of("007", "470", "475", "480", "485", "490", "495");
        return codes.contains(contractDept.getCode());
    }

    /**
     * 進展到#6的作業統一拉出，原因是因為各作業階段不同導致
     *
     * @param preWorkflow
     * @param applicationId
     * @param userId
     * @param reviewTotalSteps
     * @param application
     */
    private void dispatchWorkflow6(ApplicationWorkflow preWorkflow, Long applicationId, Long userId,
                                   List<WorkflowStepMapping> reviewTotalSteps, Application application) {
        var step3s = reviewTotalSteps.stream().filter(a -> a.getStepSeq().equals(3)).map(a -> a.getId()).toList();

        var aws = applicationWorkflowRepository.findAllByApplicationIdAndStepIdIn(applicationId, step3s);
        var completeCount = aws.stream().filter(
                a -> a.getStatus().equals(ApplicationWorkflowStatus.COMPLETE.getValue())).count();
        if (step3s.size() == completeCount) {
            var allSteps = reviewTotalSteps.stream().filter(a -> a.getStepSeq().equals(4)).toList();
            for (WorkflowStepMapping currentStep : allSteps) {
                var vo = getMainUnitAndAccounts(currentStep, application);
                commonWorkFlow(preWorkflow, currentStep, vo, application, userId);
            }

        }
    }

    public void autoCloseWorkFlow(ApplicationWorkflow preWorkflow, WorkflowStepMapping nextStep, MainUnitAccountVO vo,
                                  Application application, Long userId) {

        var nextWorkflow = ApplicationWorkflow.builder().stepId(nextStep.getId()).applicationId(application.getId())
                .status(
                        ApplicationWorkflowStatus.COMPLETE.getValue()) // 待審核
                .tpcDeptId(vo.getMainUnit()).createdAt(new Date()).stepMapping(nextStep).message("").receivers(
                        vo.getReceivers())
                .approvedFrom(preWorkflow.getId()).build();
        applicationWorkflowRepository.save(nextWorkflow);
    }

    public void commonWorkFlow(ApplicationWorkflow preWorkflow, WorkflowStepMapping nextStep, MainUnitAccountVO vo,
                               Application application, Long userId) {

        var nextWorkflow = ApplicationWorkflow.builder().stepId(nextStep.getId()).applicationId(application.getId())
                .status(
                        ApplicationWorkflowStatus.WAITING.getValue()) // 待審核
                .tpcDeptId(vo.getMainUnit()).createdAt(new Date()).stepMapping(nextStep).message("").receivers(
                        vo.getReceivers())
                .approvedFrom(preWorkflow.getId()).build();
        applicationWorkflowRepository.save(nextWorkflow);
        sendNotificationWithAccount(userId, nextWorkflow.getReceivers(), nextWorkflow.getStepMapping().getStepDesc(),
                String.format("契約編號：%s %s 簽約作業待確認", contractNo(application), nextWorkflow.getMessage()),
                nextWorkflow.getId(), WorkflowStage.CONTRACT);
    }

    /**
     * 通知半完成的作業，請對應用戶去完成
     *
     * @param applicationId
     */
    public long notifyHalfCompleteApplicationWorkflowProgress(Long applicationId, Long userId) {

        var application = applicationRepository.findById(applicationId).orElseThrow();
        var submittedAt = application.getSubmittedAt();

        List fidnStep = List.of("#3a");
        List<WorkflowStepMapping> totalSteps = new ArrayList<>();
        totalSteps.addAll(workflowStepMappingRepository.findByCustom1(GENERATOR, submittedAt, 2));
        totalSteps.addAll(workflowStepMappingRepository.findByCustom1(LOAD, submittedAt, 2));
        /**
         * 只要3a 3b 3c
         */
        totalSteps = totalSteps.stream().filter(a -> fidnStep.contains(a.getFrontHandleMethod())).toList();
        var totalStepIds = totalSteps.stream().map(a -> a.getId()).toList();
        var workflows = applicationWorkflowRepository.findAllByApplicationIdAndStepIdIn(applicationId, totalStepIds);

        workflows = workflows.stream().filter(
                a -> a.getStatus().equals(ApplicationWorkflowStatus.HALF_COMPLETE.getValue())).toList();
        List<WorkflowStepMapping> finalTotalSteps = totalSteps;
        workflows.forEach(wf -> {
            try {
                if (wf.getEntityType().equals("G")) {
                    var step = finalTotalSteps.stream().filter(
                            a -> a.getId().equals(wf.getStepId())).findFirst().orElseThrow();
                    var gen = generatorService.getTempGeneratorEntity(wf.getEntityId());
                    var receivers = workflowService.findAccountsByUpperLevelTpcDeptIdAndRoleId(gen.getTpcDeptId(),
                            step.getRoleId());
                    wf.setStepMapping(step);
                    wf.setMessage(String.format("%s %s", gen.getNbsCustomerNumber(), gen.getName()));
                    wf.setReceivers(receivers);
                } else {
                    var step = finalTotalSteps.stream().filter(
                            a -> a.getId().equals(wf.getStepId())).findFirst().orElseThrow();
                    var load = loadService.getTempLoadEntity(wf.getEntityId());
                    var receivers = workflowService.findAccountsByUpperLevelTpcDeptIdAndRoleId(load.getTpcDeptId(),
                            step.getRoleId());
                    wf.setStepMapping(step);
                    wf.setMessage(String.format("%s %s", load.getNbsCustomerNumber(), load.getName()));
                    wf.setReceivers(receivers);
                }
            } catch (Throwable ex) {
                log.error(ex, ex);
            }
        });

        String contractNo = this.contractNo(application);
        workflows.forEach(wf -> {
            sendNotificationWithAccount(userId, wf.getReceivers(), wf.getStepMapping().getStepDesc(),
                    String.format("契約編號：%s %s 審查作業待確認", contractNo, wf.getMessage()), wf.getId(),
                    WorkflowStage.REVIEW);
        });
        return workflows.size();
    }

    public byte[] generateTransferInformation(Application application) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();

        Document document = new Document(PageSize.A4.rotate());
        try {
//            FileOutputStream fos = new FileOutputStream("change.pdf");
//            PdfWriter.getInstance(document, fos);
            PdfWriter.getInstance(document, bos);
            document.open();
            addTextCenter(document, "保證金繳納方式及金額", Font.BOLD);
            PdfPTable table = new PdfPTable(6);
            table.setWidthPercentage(new float[]{1f, 1f, 1f, 1f, 2f, 2f}, document.getPageSize());
            table.setSpacingBefore(10);
            table.setSpacingAfter(10);
            table.setTotalWidth(document.getPageSize().getWidth() - document.leftMargin() - document.rightMargin());
            table.setLockedWidth(true);
            String[] content = {"契約編號", "履約保證金變更前已繳納金額", "履約保證金變更後金額", "補繳金額"};
            for (String s : content) {
                addTextCenter(table, s, Font.NORMAL);
            }
            addTextColRowSpan(table, "繳款帳號\n（本公司提供台銀及台企銀帳戶，匯款資訊如下）", Font.NORMAL, 2, 1);

            addTextColRowSpan(table, application.getContractNo(), Font.NORMAL, 1, 3);

            if (application.getParentId() != null) {
                applicationRepository.findById(application.getParentId()).ifPresentOrElse(p -> {
                    if (p.getDepositRealAmount() == null) {
                        p.setDepositRealAmount(BigDecimal.ZERO);
                    }
                    try {
                        addTextColRowSpan(table, p.getDepositRealAmount().toString(), Font.NORMAL, 1, 3);
                        addTextColRowSpan(table, application.getDepositAmount().toString(), Font.NORMAL, 1, 3);
                        addTextColRowSpan(table,
                                application.getDepositAmount().subtract(p.getDepositRealAmount()).toString(),
                                Font.NORMAL, 1, 3);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }, () -> {
                    try {
                        addTextColRowSpan(table, "0", Font.NORMAL, 1, 3);
                        addTextColRowSpan(table, String.format("%,d", application.getDepositAmount().longValue()),
                                Font.NORMAL, 1, 3);
                        addTextColRowSpan(table, String.format("%,d", application.getDepositAmount().longValue()),
                                Font.NORMAL, 1, 3);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
            } else {
                addTextColRowSpan(table, "0", Font.NORMAL, 1, 3);
                addTextColRowSpan(table, String.format("%,d", application.getDepositAmount().longValue()), Font.NORMAL,
                        1, 3);
                addTextColRowSpan(table, String.format("%,d", application.getDepositAmount().longValue()), Font.NORMAL,
                        1, 3);
            }

            String unitCode =
                    taipowerCompanyUnitRepository.findById(application.getContractDeptId()).get().getUnitCode();

            addText(table, "收款銀行：臺灣銀行（銀行代碼：004) 公館分行", Font.NORMAL);
            addText(table, "收款銀行：臺灣中小企業銀行（銀行代碼： 050) 營業部", Font.NORMAL);
            addText(table, "收款帳號： 9304" + unitCode + application.getContractNo().replaceAll("-", "").substring(4),
                    Font.NORMAL);
            addText(table, "收款帳號： 8278" + unitCode + application.getContractNo().replaceAll("-", "").substring(4),
                    Font.NORMAL);
            addText(table, "帳戶戶名：台灣電力股份有限公司", Font.NORMAL);
            addText(table, "帳戶戶名：台灣電力股份有限公司", Font.NORMAL);

            document.add(table);

            addText(document, """
                    備註：
                    請將變更後履約保證金匯至指定台銀或台企銀帳戶。
                    並請於繳款後以電子郵件通知***********************，方便辦理後續契約變更作業。
                    如有履約保證金計算之疑慮，請洽業務處行銷組陳瑩瑩（電話：02-2366-6669)
                    本公司地址：10016 台北市中正區羅斯福路三段242號6樓
                    收件人：業務處總務組事務課陳旺志（電語02-2366-6666)""", Font.NORMAL);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        document.close();
        return bos.toByteArray();
//        return null;
    }

    void addText(Document document, String text, int fontStyle) throws IOException {
        Font font = new Font(getBaseFont(), 12, fontStyle);
        Paragraph paragraph = new Paragraph(text, font);
        document.add(paragraph);
    }

    void addTextCenter(Document document, String text, int fontStyle) throws IOException {
        Font font = new Font(getBaseFont(), 12, fontStyle);
        Paragraph paragraph = new Paragraph(text, font);
        paragraph.setAlignment(Paragraph.ALIGN_CENTER);
        document.add(paragraph);
    }

    void addText(PdfPTable table, String text, int fontStyle) throws IOException {
        Font font = new Font(getBaseFont(), 12, fontStyle);
        Paragraph paragraph = new Paragraph(text, font);
        PdfPCell cell = new PdfPCell(paragraph);
        cell.setPadding(5);
        table.addCell(cell);
    }

    void addTextColRowSpan(PdfPTable table, String text, int fontStyle, int colspan, int rowspan) throws IOException {
        Font font = new Font(getBaseFont(), 12, fontStyle);
        Paragraph paragraph = new Paragraph(text, font);
        PdfPCell cell = new PdfPCell(paragraph);
        cell.setColspan(colspan);
        cell.setRowspan(rowspan);
        cell.setPadding(5);
        cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
        table.addCell(cell);
    }

    void addTextCenter(PdfPTable table, String text, int fontStyle) throws IOException {
        Font font = new Font(getBaseFont(), 12, fontStyle);
        Paragraph paragraph = new Paragraph(text, font);
        PdfPCell cell = new PdfPCell(paragraph);
        cell.setPadding(5);
        cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
        table.addCell(cell);
    }

    BaseFont bfChinese = null;

    private BaseFont getBaseFont() throws IOException {
        if (bfChinese == null)
            bfChinese = BaseFont.createFont("NotoSerifTC-VariableFont_wght.ttf", BaseFont.IDENTITY_H,
                    BaseFont.NOT_EMBEDDED, true, IOUtils.toByteArray(new ClassPathResource("fonts/NotoSerifTC" +
                            "-VariableFont_wght.ttf").getInputStream()),
                    null);
        return bfChinese;
    }
}
