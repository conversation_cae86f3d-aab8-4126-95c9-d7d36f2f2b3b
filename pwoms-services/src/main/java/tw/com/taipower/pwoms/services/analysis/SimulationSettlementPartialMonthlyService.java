package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.EnergyChargeSection;
import tw.com.taipower.data.entity.pwoms.SimulationTempApplicationMonthlyCapacityRecord;
import tw.com.taipower.data.entity.pwoms.TempApplicationMonthlyCapacityRecord;
import tw.com.taipower.data.repository.pwoms.SimulationTempApplicationMonthlyCapacityRecordRepository;
import tw.com.taipower.data.repository.pwoms.SimulationTempApplicationMonthlyCapacitySettlementRepository;
import tw.com.taipower.data.repository.pwoms.TempApplicationMonthlyCapacityRecordRepository;
import tw.com.taipower.data.repository.pwoms.TempApplicationMonthlyCapacitySettlementRepository;
import tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum;
import tw.com.taipower.pwoms.services.settlement.*;
import tw.com.taipower.pwoms.services.system.EnergyChargeService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.utils.TimeIntervalVo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@Service
public class SimulationSettlementPartialMonthlyService {

    @Autowired
    private SimulationSettlementTempService tempService;

    @Autowired
    private SimulationSettlementNiService settlementNiService;

    @Autowired
    private EnergyChargeService energyChargeService;

    @Autowired
    private SimulationSettlementTrialDirectService trialDirectService;

    @Autowired
    private SimulationSettlementTrialPowerWheelingService trialPowerWheelingService;

    @Autowired
    private SimulationSettlementUtilsService settlementUtilsService;

    @Autowired
    private SimulationTempApplicationMonthlyCapacityRecordRepository tempMonthlyCapacityRecordRepository;

    @Autowired
    private SimulationTempApplicationMonthlyCapacitySettlementRepository tempMonthlyCapacitySettlementRepository;


    public Map<Long, Boolean> calculate(TimeIntervalVo timeIntervalVo
            , Long settlementId
            , Map<ApplicationTypeEnum, Map<Long, BigDecimal>> typeAnnualYni
            , Integer userId
            , SettlementCalculationInterface caller
    ) throws Exception{

        Map<Long, Boolean> appIdResultMap = new HashMap<>();

        Map<ApplicationTypeEnum, List<Long>> appTypeAppIdMap = tempService.getComputableApplicationAndTypeByDateIntervalAndSettlementId(timeIntervalVo.getStartTime().getTime(), timeIntervalVo.getEndTime().getTime(), settlementId);
//        Map<ApplicationTypeEnum, List<Long>> appTypeAppIdMap = new HashMap<>();
//        appTypeAppIdMap.put(APPLICATION_TYPE_POWER_WHEELING, Arrays.asList(120L, 102L));

        Map<ApplicationTypeEnum, Map<Long, BigDecimal>> typeMonthlyNi = settlementNiService.getMonthlyNi(timeIntervalVo, DateUtils.getMonthInterval(timeIntervalVo.getStartTime().getTimeInMillis()), settlementId, typeAnnualYni);

        if(MapUtils.isNotEmpty(appTypeAppIdMap)) {
            List<EnergyChargeSection> chargeSectionList = energyChargeService.getAllSection();
            int curTypeCnt = 0;
            int totalCalTypeCnt = appTypeAppIdMap.size();
            for (Map.Entry<ApplicationTypeEnum, List<Long>> typeEnumIdEntry : appTypeAppIdMap.entrySet()) {
                curTypeCnt++;
                switch (typeEnumIdEntry.getKey()) {
                    case APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE:
                        appIdResultMap.putAll(trialDirectService.calculateMonthly(timeIntervalVo
                                , settlementId
                                , chargeSectionList
                                , typeEnumIdEntry
                                , typeMonthlyNi.get(typeEnumIdEntry.getKey())));
                        break;
                    case APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING:
                        appIdResultMap.putAll(trialDirectService.calculateMonthly(timeIntervalVo
                                , settlementId
                                , chargeSectionList
                                , typeEnumIdEntry
                                , typeMonthlyNi.get(typeEnumIdEntry.getKey())));
                        appIdResultMap.putAll(trialPowerWheelingService.calculateMonthly(timeIntervalVo
                                , settlementId
                                , chargeSectionList
                                , typeEnumIdEntry
                                , typeMonthlyNi.get(typeEnumIdEntry.getKey())));
                        break;
                    case APPLICATION_TYPE_POWER_WHEELING:
                    case APPLICATION_TYPE_POWER_WHEELING_OWN_USE:
                    case APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION:
                        appIdResultMap.putAll(trialPowerWheelingService.calculateMonthly(timeIntervalVo
                                , settlementId
                                , chargeSectionList
                                , typeEnumIdEntry
                                , typeMonthlyNi.get(typeEnumIdEntry.getKey())));
                        break;
                    default:
                        log.error("{} not support", typeEnumIdEntry.getKey().getDescription());
                        break;
                }
                settlementUtilsService.sendToFront(userId, settlementId, totalCalTypeCnt, curTypeCnt, caller);
            }
        }

        if(appIdResultMap.values().stream().anyMatch(value->value)){
            //initial TEMP_APPLICATION_MONTHLY_CAPACITY_SETTLEMENT
            tempMonthlyCapacityRecordRepository.saveAllExceptFlexibleDistributionBeforeAdjustMatchedKw(settlementId);

            //Adjust MatchedKw
            adjustMatchedKw(settlementId);

            //calculate fee
            tempMonthlyCapacityRecordRepository.updateAllExceptFlexibleDistributionAfterAdjustMatchedKw(settlementId);

            //sum matched_kw and fee
            tempMonthlyCapacitySettlementRepository.saveAllRecordBySettlementId(settlementId);

            tempMonthlyCapacityRecordRepository.updateRelatedTableAfterAdjustMatchedKw(settlementId);
        }

        return appIdResultMap;
    }

    private void adjustMatchedKw(Long settlementId){

        //LOAD_CUSTOMER_NUMBER, SUM_MATCHED_KW, SUM_ADJUSTED_KW
        List<Map<String, Object>> recordList =  tempMonthlyCapacityRecordRepository.findNeedAdjustMatchedKw(settlementId);
        if(CollectionUtils.isNotEmpty(recordList)){
            List<SimulationTempApplicationMonthlyCapacityRecord> modifiedRecordeList = new ArrayList<>();
            for(Map<String, Object> recordMap : recordList){
                String customerNumber = (String)recordMap.get(("LOAD_CUSTOMER_NUMBER"));
                BigDecimal matchedRm = (BigDecimal)recordMap.get(("SUM_MATCHED_KW"));
                BigDecimal adjustedRm = (BigDecimal)recordMap.get(("SUM_ADJUSTED_KW"));

                log.info("{} {} {} ", customerNumber, matchedRm, adjustedRm);

                List<SimulationTempApplicationMonthlyCapacityRecord> monthlyRecordList =  tempMonthlyCapacityRecordRepository.findByDateAndSettlementIdAndLoadCustomerNumber(
                        settlementId, customerNumber);

                List<SimulationTempApplicationMonthlyCapacityRecord> sortedMonthlyRecordList = monthlyRecordList.stream().sorted((cmp1st, cmp2nd) -> {

                    BigDecimal fractionalPart1st = cmp1st.getMatchedKw().remainder(BigDecimal.ONE);
                    BigDecimal fractionalPart2nd = cmp2nd.getMatchedKw().remainder(BigDecimal.ONE);
                    return fractionalPart2nd.compareTo(fractionalPart1st);
                }).toList();

                int gap = matchedRm.subtract(adjustedRm).intValue();
                for(SimulationTempApplicationMonthlyCapacityRecord monthlyRecord : sortedMonthlyRecordList){

                    monthlyRecord.setAdjustedKw(monthlyRecord.getAdjustedKw().add(BigDecimal.ONE));
                    modifiedRecordeList.add(monthlyRecord);
                    gap -= 1;
                    if(0 == gap){
                        break;
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(modifiedRecordeList)){
//                log.info(modifiedRecordeList.toString());
                tempMonthlyCapacityRecordRepository.saveAll(modifiedRecordeList);
            }
        }
    }

}
