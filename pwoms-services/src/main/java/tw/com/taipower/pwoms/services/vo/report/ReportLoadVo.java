package tw.com.taipower.pwoms.services.vo.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @class: ReportLoadVo
 * @author: jingfungchen
 * @version:
 * @since: 2024-09-17 00:05
 * @see:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportLoadVo implements Serializable {

    @Schema(description = "CUST_ELEC_NO 用電端電號, LOAD_ENTITY.NBS_CUSTOMER_NUMBER")
    private String custElecNo;

    @Schema(description = "YEAR 用電端運作年, APPLICATION_MONTHLY_SETTLEMENT.SERVICE_DATE.YEAR")
    private String year;

    @Schema(description = "MONTH 用電端運作年, APPLICATION_MONTHLY_SETTLEMENT.SERVICE_DATE.MONTH")
    private String month;

    @Schema(description = "START_DATE 計費起日, APPLICATION_MONTHLY_SETTLEMENT.BILLING_DATE - month start date")
    private String startDate;

    @Schema(description = "END_DATE 計費迄日, APPLICATION_MONTHLY_SETTLEMENT.BILLING_DATE - month end date")
    private String endDate;

    @Schema(description = "1 1表(01), 半尖峰媒合電量 APPLICATION_MONTHLY_GENERATOR_RECORD.MATCHED_RM - ENERGY_CHARGE_SECTION_ID=1")
    private String table1;

    @Schema(description = "3 3表(03), 離峰媒合電量 APPLICATION_MONTHLY_GENERATOR_RECORD.MATCHED_RM - ENERGY_CHARGE_SECTION_ID=3")
    private String table3;

    @Schema(description = "9 9表(09), 尖峰媒合電量 APPLICATION_MONTHLY_GENERATOR_RECORD.MATCHED_RM - ENERGY_CHARGE_SECTION_ID=9")
    private String table9;

    @Schema(description = "11 11表(11), 週六半尖峰媒合電量 APPLICATION_MONTHLY_GENERATOR_RECORD.MATCHED_RM - ENERGY_CHARGE_SECTION_ID=11")
    private String table11;

    @Schema(description = "CONTR_TYPE 用電端契約類型 LOAD_ENTITY.CONTRACT_STG")
    private String contrType;

    @Schema(description = "TIME_PRICE_STG 用電端段別 0:不分段 2:二段式 3:三段式 LOAD_ENTITY.TIME_STG")
    private String timePriceStg;
    @Schema(description = "PW_DS 轉供/直供, PW[轉供]: APPLICATION.TYPE 1,4,5,Q, DS[直供]: 2,3")
    private String pwDs;

    // --- 以下提供 合併報表資料表 使用 ---
    @Schema(description = "applicationLoadId 用電端ID")
    private Long applicationLoadId;

    @Schema(description = "settlementId SETTLEMENT_ID")
    private String settlementId;
}
