package tw.com.taipower.pwoms.services.re;

import jakarta.persistence.Tuple;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.ReSettlement;
import tw.com.taipower.data.entity.pwoms.ReSettlementStep;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.services.utils.DataException;
import tw.com.taipower.pwoms.services.vo.generated.ReSettlementStepVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RE30結算模組
 *
 * @class: ReSettlementService
 * @author: charlene
 * @version: 1.0
 * @since: 2025-03-28 10:21
 * @see:
 **/

@Log4j2
@Service
public class ReSettlementService {
     static final int AMI_LOAD_CHANNEL = 1 ;
    static final int AMI_GENERATOR_CHANNEL = 3 ;

    @Autowired
    private ReSettlementRepository repository;
    @Autowired
    private ReSettlementStepRepository stepRepo;
    @Autowired
    private ReSettlementRepository reSettlementRepository;
    @Autowired
    private ReDistributionService reDistributionService;
    @Autowired
    private AmiStagingService amiStagingService;
    @Autowired
    private ReLoadRepository reLoadRepository;
    @Autowired
    private ReGeneratorRepository reGeneratorRepository;


    // step1 output: 發電端能源別分配統計
    public Map<String, Object> stepOneOutput(LocalDate yymm)  {
//        ReSettlement reSettlement= getReSettlementById(reSttlementId) ;
        ReSettlement reSettlement=  this.getReSettlementId(yymm) ;
        List<Tuple> tuples = repository.step1GeneratorSummary(reSettlement.getId());
        List<String> groupByFields = List.of("fuelType");
        List<String> values = List.of( "section", "kwh", "distPct");
        List<Map<String, Object>> data = ReDataProcessor.transformData(tuples, groupByFields, values,"kwh","合計(度)");
        Map<String, BigDecimal> sectionSums = ReDataProcessor.calcTotalByListOfMap(data, "details", "section", "kwh" ,"總計");

        List<Object> list = new ArrayList<>(data);
        list.add(sectionSums);

        Map<String, Object> ret = new HashMap<>();
        ret.put("list", data);
        ret.put("total", sectionSums);
        return ret ;
    }

    // step2 output: 用電端分配上限統計—用戶時段補充量
    // todo : reKw not correct : new
    public Map<String, Object> stepTwoOutput(LocalDate yymm)  {
        ReSettlement reSettlement=  this.getReSettlementId(yymm) ;
        List<Tuple> tuples = repository.step2ReDsPw(reSettlement.getId());
        List<String> groupByFields = List.of("name", "nbsCustomerNumber");
//        List<String> values = List.of( "section", "nbsCustomerNumber", "directMatchedRm", "directAdjustedMatchedRm", "pwMatchedRm", "pwAdjustedMatchedRm", "flexible", "allMatched", "consumedKwh","reKwh");
        List<String> values = List.of( "section", "distKwh", "directAdjustedMatchedRm", "pwAdjustedMatchedRm", "flexible", "allMatched", "consumedSectionPct", "consumedKwh","reKwh", "consumedKwhTotal","reKwhPlan", "preRemaining","distKwhTotal");

        List<Map<String, Object>> data = ReDataProcessor.transformData(tuples, groupByFields, values,null,null);
        Map<String, BigDecimal> sectionSums = ReDataProcessor.calcTotalByListOfMap(data, "details", "section", "distKwh" ,"總計");

        Map<String, Object> ret = new HashMap<>();
        ret.put("list", data);
        ret.put("total", sectionSums);

        return ret;
    }

    // step3 output: 用戶能源別時段補充量
    public Map<String, Object> stepThreeOutputCancel(LocalDate yymm)  {
        ReSettlement reSettlement=  this.getReSettlementId(yymm) ;
        List<Tuple> tuples = repository.step3LoadDistribution(reSettlement.getId());
        List<String> groupByFields = List.of("name", "nbsCustomerNumber");
        List<String> values = List.of("section","annualQuota", "preSum", "demandKwh", "distKwh");

        List<Map<String, Object>> data = ReDataProcessor.transformData(tuples, groupByFields, values,"distKwh","合計(度)");
        Map<String, BigDecimal> sectionSums = ReDataProcessor.calcTotalByListOfMap(data, "details", "section", "distKwh" ,"總計");

        Map<String, Object> ret = new HashMap<>();
        ret.put("list", data);
        ret.put("total", sectionSums);
        return ret;

    }

    // step4 output: 能源別分配量統計 -> step3
   public Map<String, Object> stepThreeOutput(LocalDate yymm)  {
        ReSettlement reSettlement=  this.getReSettlementId(yymm) ;
        List<Tuple> tuples = repository.step3AllocationBySectionByFueltype(reSettlement.getId());
        List<String> groupByFields = List.of("name", "nbsCustomerNumber","loadKwh");
        List<Map<String, Object>> data = ReDataProcessor.convertWithTwoLevel(tuples, groupByFields, "section", "fuelType","sectionFuelKwh", true,"合計(度)", null);
        Map<String, BigDecimal> sectionSums = ReDataProcessor.calcTotalByListOfTuple(tuples, "section", "sectionFuelKwh" ,"總計");

        Map<String, Object> ret = new HashMap<>();
        ret.put("list", data);
        ret.put("total", sectionSums);
        return ret;


   }

    // step 4 - RE綠電補充表
    public List<Map<String, Object>> stepFourOutput(LocalDate yymm)  {
        ReSettlement reSettlement=  this.getReSettlementId(yymm) ;
        List<Tuple> tuples = repository.step4Allocation(reSettlement.getId());
        List<String> groupByFields = List.of("yymm","leName", "leNbsNumber","geName", "geNbsNumber","distKwh");
        List<Map<String, Object>> data = ReDataProcessor.transformListOfTupleToMap(tuples);

        return data;
    }

    // query
    public List<ReSettlementStepVO>stepsQuery(LocalDate yymm)  {
        ReSettlement reSettlement = getReSettlementId(yymm) ;
        Long settlementId =reSettlement.getId() ; // toDo : getSettleMentId(ld)
        List<ReSettlementStep> list = stepRepo.findReSettlementStepByReSettlementId(settlementId);
        List<ReSettlementStepVO> ret = list.stream().map(ent -> {
            ReSettlementStepVO vo = new ReSettlementStepVO();
            BeanUtils.copyProperties(ent, vo);
            vo.setAccountName(ent.getAccount()==null?null:ent.getAccount().getName());
            vo.setReSettlementDate(reSettlement.getYymm());
            return vo;
        }).toList();
        return ret;
    }

    public ReSettlement getReSettlementId(LocalDate yymm) {
        log.info("== oout 123 getReSettlementId. =" +yymm );
        ReSettlement reSettlement =  reDistributionService.getOrCreateSettlement(yymm);
        if ( reSettlement == null ) throw new DataException("沒有RE結算資料!");
//        return reSettlementRepository.findByYymm(yymm).orElseThrow(()-> new DataException("沒有 Re 結算資料!"));
        return reSettlement;
    }

    private ReSettlement getReSettlementById(Long id) {
        return reSettlementRepository.findById(id).orElseThrow(()-> new DataException("沒有RE結算資料!"));
    }

//    @Async
//    public CompletableFuture<Void> distributeGreenEnergyAsync(LocalDate date, Consumer<Integer> progressCallback) {
//        reDistributionService.distributeGreenEnergy(LocalDate.now(), percent -> {
//            log.info("Progress: " + percent + "%");
//        });
//        return CompletableFuture.completedFuture(null);
//    }


   @Async
    public void distribute(LocalDate yymm, ReProgressListener listener) {
        log.info("== oout distribute. =" + yymm);
        ReSettlement reSettlement = getReSettlementId(yymm) ;
        try {
            insertAmiGenerator(reSettlement);
            insertAmiLoad(reSettlement);
            reDistributionService.distribute(yymm, listener);
        } catch (Exception e) {
            log.error("== oooout distribute. error. =" + yymm, e);
            e.printStackTrace();
        }

    }

    public void insertAmiGenerator(ReSettlement reSettlement ) {
        log.info("== oout insertAmiGenerator. =" + reSettlement);
        List<String> custIds = reLoadRepository.findNbsCustomerNumberOfAvailableReLoad() ;
        amiStagingService.loadAmiStagingData(reSettlement,custIds,AMI_LOAD_CHANNEL );
        log.info("== oout insertAmiGenerator. done. =" + reSettlement);
    }
    public void insertAmiLoad(ReSettlement reSettlement ) {
        log.info("== oout insertAmiLoad. =" + reSettlement);
        List<String> custIds = reGeneratorRepository.findNbsCustomerNumberOfAvailableReGenerator() ;
        amiStagingService.loadAmiStagingData(reSettlement,custIds,AMI_GENERATOR_CHANNEL );
        log.info("== oout insertAmiLoad. done. =" + reSettlement);
    }


}
