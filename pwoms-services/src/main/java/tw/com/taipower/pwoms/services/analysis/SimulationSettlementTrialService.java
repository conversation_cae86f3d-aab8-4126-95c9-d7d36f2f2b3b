package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.EnergyChargeSection;
import tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum;
import tw.com.taipower.pwoms.services.settlement.SettlementContractVer2Service;
import tw.com.taipower.pwoms.services.settlement.SettlementTempService;
import tw.com.taipower.pwoms.services.vo.settlement.LoadAppMeterVo;
import tw.com.taipower.pwoms.services.vo.utils.TimeIntervalVo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Log4j2
@Service
public abstract class SimulationSettlementTrialService {

    @Autowired
    protected SimulationSettlementContractVer2Service contractServiceVer2;

    @Autowired
    protected SimulationSettlementTempService tempService;

    public abstract void deleteAllRecordBySettlementId(Long settlementId) throws Exception;

    protected abstract Map<ApplicationTypeEnum, Map<Long, BigDecimal>> getAnnualYni(TimeIntervalVo timeIntervalVo, TimeIntervalVo annualTimeIntervalVo, Map<Long, LoadAppMeterVo> appLoadVoMap, List<Long> resetAppIdList);
    protected abstract Map<ApplicationTypeEnum, Map<Long, BigDecimal>> getMonthlyNi(TimeIntervalVo timeIntervalVo, Long settlementId, Map<Long, LoadAppMeterVo> appLoadVoMap);

    public abstract void saveDailyRecordByDateIntervalAndSettlementId(Date startDate, Date endDate, Long settlementId) throws Exception;

    protected abstract Boolean calculateDaily(TimeIntervalVo timeIntervalVo, Long settlementId, Long applicationId, List<Long> relationAppId, Map<Long, BigDecimal> appLoadIdNiMap);

    protected abstract Map<Long, Boolean> calculateMonthly(TimeIntervalVo timeIntervalVo
            , Long settlementId
            , List<EnergyChargeSection> chargeSectionList
            , Map.Entry<ApplicationTypeEnum, List<Long>> typeEnumIdEntry
            , Map<Long, BigDecimal> currentNiMap);


}
