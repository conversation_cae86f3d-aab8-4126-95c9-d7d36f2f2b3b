package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum;
import tw.com.taipower.pwoms.services.enumclass.MeterChannelEnum;
import tw.com.taipower.pwoms.services.enumclass.TimeSlotEnum;
import tw.com.taipower.pwoms.services.vo.settlement.GeneratorAppMeterVo;
import tw.com.taipower.pwoms.services.vo.settlement.LoadAppMeterVo;
import tw.com.taipower.pwoms.services.vo.settlement.SumGmiKwVo;
import tw.com.taipower.pwoms.services.vo.utils.TimeIntervalVo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;
import static tw.com.taipower.data.constant.Constants.FIELD_NAME_APPLICATION_GENERATOR_ID;
import static tw.com.taipower.data.constant.Constants.FIELD_NAME_APPLICATION_LOAD_ID;
import static tw.com.taipower.pwoms.services.constant.Constants.BIGDECIMAL_KW_PRECISION;

@Log4j2
@Service
public class SimulationSettlementTempService {

    @Autowired
    private SimulationTempTempApplicationGeneratorKwRepository tempAppGenKwRepository;

    @Autowired
    private SimulationTempTempApplicationLoadKwRepository tempAppLoadKwRepository;

    @Autowired
    private SimulationTempDateApplicationGeneratorLoadRepository appGenLoadRepository;

    @Autowired
    private SimulationTempDateMeterComputableSettlementRepository meterComputableSettlementRepository;

    @Autowired
    private SimulationTempDateApplicationSelfComputableSettlementRepository appSelfComputableSettlementRepository;

    @Autowired
    private SimulationTempDateApplicationComputableSettlementRepository appComputableSettlementRepository;

    @Autowired
    private SimulationTempApplicationDailyDirectLoadRecordRepository dailyDirectLoadRepository;

    @Autowired
    private SimulationTempApplicationDailyLoadRecordRepository dailyLoadRepository;

    @Autowired
    private SimulationTempDateApplicationMeterRepository appMeterRepository;

    public void deleteTempTempApplicationKw() throws Exception{
        try {
            tempAppGenKwRepository.deleteAll();
            tempAppLoadKwRepository.deleteAll();
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new Exception(exception.getMessage());
        }
    }

    public void saveDateApplicationMeter(Date startDate, Date endDate, Long settlementId, List<String> contractNoList) throws Exception {

        try {
            for(String contractNo : contractNoList) {
                appGenLoadRepository.saveAllByDateIntervalAndSettlementId(startDate, endDate, settlementId, contractNo);
            }
            meterComputableSettlementRepository.saveAllByDateIntervalAndSettlementId(startDate, endDate, settlementId);
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new Exception(exception.getMessage());
        }
    }

    public List<String> getDateMeterIdBySettlementId(Date date, Long settlementId, MeterChannelEnum channelEnum) {
        if (MeterChannelEnum.METER_CHANNEL_GENERATOR.equals(channelEnum)) {
            return meterComputableSettlementRepository.findGeneratorMeterIdBySettlementId(date, settlementId);
        }
        return meterComputableSettlementRepository.findLoadMeterIdBySettlementId(date, settlementId);
    }

    public boolean updateDateMeterComputable(Date date, Long settlementId, MeterChannelEnum meterChannel, List<String> custIdList, List<String> meterNoList, boolean computable) {
        try {
            if (meterChannel.equals(MeterChannelEnum.METER_CHANNEL_GENERATOR)) {
                meterComputableSettlementRepository.updateGeneratorComputableBySettlementIdAndMeterNoIn(date, settlementId, custIdList, meterNoList, computable);
            } else {
                meterComputableSettlementRepository.updateLoadComputableBySettlementIdAndMeterNoIn(date, settlementId, custIdList, meterNoList, computable);
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public void saveDateApplicationComputable(Date startDate, Date endDate, Long settlementId) throws Exception{
        try {
            appSelfComputableSettlementRepository.saveAllByDateIntervalAndSettlementId(startDate, endDate, settlementId);
            appComputableSettlementRepository.saveAllByDateIntervalAndSettlementId(startDate, endDate, settlementId);
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new Exception(exception.getMessage());
        }
    }

    public List<Map<String, Object>> getIncomputableApplicationGeneratorIdAndDate(Long settlementId, List<Long> appIdList){
        Map<Long, String> appDateMap = new HashMap<>();
        return meterComputableSettlementRepository.findInComputableGeneratorBySettlementIdAndApplicationIn(settlementId, appIdList);
    }

    public List<Map<String, Object>> getIncomputableApplicationLoadIdAndDate(Long settlementId, List<Long> appIdList){

        return meterComputableSettlementRepository.findInComputableLoadBySettlementIdAndApplicationIn(settlementId, appIdList);
    }

    public Map<Long, LoadAppMeterVo> findComputableApplicationByDateIntervalAndSettlementId(Date startDate, Date endDate, Long settlementId) {
        //AL.APPLICATION_ID, AL.ID AS APPLICATION_LOAD_ID, A.CONTRACT_NO, MONTHLY_CONTRACT_CAP, ANNUAL_CONTRACT_CAP
        final int PER_TIME = 100;
        Map<Long, LoadAppMeterVo> idAppMap = new HashMap<>();
        List<?> appLoadList = appComputableSettlementRepository.findComputableApplicationByDateIntervalAndSettlementId(startDate, endDate, settlementId);
        if (CollectionUtils.isNotEmpty(appLoadList)) {
            int start = 0;
            int end = 0;
            while (start < appLoadList.size()) {
                end += Math.min(PER_TIME, appLoadList.size() - start);
                List<?> loadSubList = appLoadList.subList(start, end);

                for (Object appLoadObject : loadSubList) {
                    Map<?, ?> appLoadMap = (Map<?, ?>) appLoadObject;
                    Long appLoadId = ((Long) appLoadMap.get("APPLICATION_LOAD_ID"));

                    idAppMap.put(appLoadId, LoadAppMeterVo.builder()
                            .applicationId((Long) appLoadMap.get("APPLICATION_ID"))
                            .appLoadId(appLoadId)
                            .isDirect((Boolean) appLoadMap.get("IS_DIRECT"))
                            .contractNo((String) appLoadMap.get("CONTRACT_NO"))
                            .type((String) appLoadMap.get("TYPE"))
                            .monthlyContractCap((BigDecimal) appLoadMap.get("MONTHLY_CONTRACT_CAP"))
                            .annualContractCap((BigDecimal) appLoadMap.get("ANNUAL_CONTRACT_CAP"))
                            .build());
                }
                start = end;
            }
        }
        return idAppMap;
    }

    public List<Long> getComputableApplicationIdByDateIntervalAndTrialDate(TimeIntervalVo timeIntervalVo, Long settlementId){
        return appComputableSettlementRepository.findComputableApplicationIdByDateIntervalAndSettlementId(timeIntervalVo.getStartTime().getTime()
                , timeIntervalVo.getEndTime().getTime(), settlementId);
    }

    public Map<Long, ApplicationTypeEnum> getComputableApplication(Date date, Long settlementId, List<Long> computableAppInMonth) {
        Map<Long, ApplicationTypeEnum> idTypeMap = new HashMap<>();
        List<?> appIdList = appComputableSettlementRepository.findIdAndTypeByDateAndSettlementId(date, settlementId, true);
        if (CollectionUtils.isNotEmpty(appIdList)) {
            for (Object appIdObject : appIdList) {
                Map<?, ?> appIdMap = (Map<?, ?>) appIdObject;
                if(computableAppInMonth.contains((Long) appIdMap.get("ID"))) {
                    idTypeMap.put((Long) appIdMap.get("ID"), ApplicationTypeEnum.get((String) appIdMap.get("TYPE")));
                }
            }
        }
        return idTypeMap;
    }

    public List<String> getComutableLoadMeterNo(Long settlementId, Date date){
        return meterComputableSettlementRepository.findMeterNoBySettlementIdAndDateAndLoadMeterIdIsNotNullAndComputableTrue(settlementId, date);
    }

    public List<String> getComutableGeneratorMeterNo(Long settlementId, Date date){
        return meterComputableSettlementRepository.findMeterNoBySettlementIdAndDateAndGeneratorMeterIdIsNotNullAndComputableTrue(settlementId, date);
    }

    public Map<Long, List<GeneratorAppMeterVo>> getApplicationGeneratorMeterNo(Date date, Long settlementId, List<Long> appIdList) {
        //APPLICATION_GENERATOR_ID, NBS_CUSTOMER_NUMBER, METER_NO, PMI_EXCEPT_OP, LICENSE_CAPACITY, TRIAL_RUN_CAPACITY
        //因為有換表問題，所以會有多個values
        Map<Long, List<GeneratorAppMeterVo>> idMeterMap = new HashMap<>();
        List<?> appMeterList = meterComputableSettlementRepository.findComputableGeneratorBySettlementIdAndApplicationIn(date, settlementId, appIdList);
        if (CollectionUtils.isNotEmpty(appMeterList)) {
            for (Object appMeterObject : appMeterList) {
                Map<?, ?> appMeterMap = (Map<?, ?>) appMeterObject;
                Long appGenId = (Long) appMeterMap.get("APPLICATION_GENERATOR_ID");
                String relMeterId = null;
                if (!idMeterMap.containsKey(appGenId)) {
                    idMeterMap.put(appGenId, new ArrayList<>());
                }else{
                    idMeterMap.get(appGenId).get(0).setRelMeterNo((String) appMeterMap.get("METER_NO"));
                    relMeterId = idMeterMap.get(appGenId).get(0).getMeterNo();
                }
                List<GeneratorAppMeterVo> appMeterVoList = idMeterMap.get(appGenId);
                appMeterVoList.add(GeneratorAppMeterVo.builder()
                        .applicationId((Long) appMeterMap.get("APPLICATION_ID"))
                        .appGeneratorId(appGenId)
                        .nbsCustomer((String) appMeterMap.get("NBS_CUSTOMER_NUMBER"))
                        .meterNo((String) appMeterMap.get("METER_NO"))
                        .relMeterNo(relMeterId)
                        .pmiExceptOp((BigDecimal) appMeterMap.get("PMI_EXCEPT_OP"))
                        .computableCapacity((BigDecimal) appMeterMap.get("COMPUTABLE_CAPACITY"))
//                        .trialRunCapacity((BigDecimal) appMeterMap.get("LICENSE_CAPACITY"))
//                        .licenseCapacity((BigDecimal) appMeterMap.get("TRIAL_RUN_CAPACITY"))
                        .build());
            }
        }
        return idMeterMap;
    }

    public boolean saveTempTempApplicationGeneratorKw(List<SimulationTempTempApplicationGeneratorKw> kwList) {
        try {
            if (CollectionUtils.isNotEmpty(kwList)) {
                tempAppGenKwRepository.saveAll(kwList);
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public boolean duplicateTempTempApplicationGeneratorKw(Date startTime, Date endTime, Long sourceAppGenId, List<Long> destIdList, BigDecimal capacity) {
        try {
            String commaList = StringUtils.join(destIdList.stream().map(Object::toString).toList(), ",");
            tempAppGenKwRepository.saveByApplicationGenerator(startTime, endTime, sourceAppGenId, commaList, capacity);
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public Map<Long, List<LoadAppMeterVo>> getApplicationLoadMeterNo(Date date, Long settlementId, List<Long> appIdList) {
        //APPLICATION_ID, APPLICATION_LOAD_ID, NBS_CUSTOMER_NUMBER, METER_NO, VOLTAGE_LEVEL.VOLTAGE_ID, TIME_STG, LOAD_ENTITY.CONTRACT_STG
        //因為有換表問題，所以會有多個values
        Map<Long, List<LoadAppMeterVo>> idMeterMap = new HashMap<>();
        List<Map<String, Object>> appMeterMapList = meterComputableSettlementRepository.findComputableLoadBySettlementIdAndApplicationIn(date, settlementId, appIdList);
        if (CollectionUtils.isNotEmpty(appMeterMapList)) {
            for (Map<String, Object> appMeterMap : appMeterMapList) {

                Long appLoadId = (Long) appMeterMap.get("APPLICATION_LOAD_ID");
                String relMeterId = null;
                if (!idMeterMap.containsKey(appLoadId)) {
                    idMeterMap.put(appLoadId, new ArrayList<>());
                }else{
                    idMeterMap.get(appLoadId).get(0).setRelMeterNo((String) appMeterMap.get("METER_NO"));
                    relMeterId = idMeterMap.get(appLoadId).get(0).getMeterNo();
                }
                List<LoadAppMeterVo> appMeterVoList = idMeterMap.get(appLoadId);
                Short timeStg = null != appMeterMap.get("TIME_STG") ? ((Integer)appMeterMap.get("TIME_STG")).shortValue() : null;
                appMeterVoList.add(LoadAppMeterVo.builder()
                        .applicationId((Long) appMeterMap.get("APPLICATION_ID"))
                        .appLoadId(appLoadId)
                        .nbsCustomer((String) appMeterMap.get("NBS_CUSTOMER_NUMBER"))
                        .meterNo((String) appMeterMap.get("METER_NO"))
//                        .voltageEnum(VoltageClassificationEnum.get((String) appMeterMap.get("VOLTAGE_ID")))
                        .timeSlotEnum(null != timeStg ? TimeSlotEnum.get(timeStg) : null)
                        .contractStg((String) appMeterMap.get("CONTRACT_STG"))
                        .relMeterNo(relMeterId)
                        .build());
            }
        }
        return idMeterMap;
    }

    public boolean saveTempTempApplicationLoadKw(List<SimulationTempTempApplicationLoadKw> kwList) {
        try {
            if (CollectionUtils.isNotEmpty(kwList)) {
                tempAppLoadKwRepository.saveAll(kwList);
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public boolean duplicateTempTempApplicationLoadKw(Date startTime, Date endTime, Long sourceAppLoadId, List<Long> destIdList) {
        try {
            String commaList = StringUtils.join(destIdList.stream().map(Object::toString).toList(), ",");
            tempAppLoadKwRepository.saveByApplicationLoad(startTime, endTime, sourceAppLoadId, commaList);
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public Map<ApplicationTypeEnum, List<Long>> getComputableApplicationAndTypeByDateIntervalAndSettlementId(Date startDate, Date endDate, Long settlementId){

        Map<ApplicationTypeEnum, List<Long>> typeEnumIdMap = new HashMap<>();

        List<?> appTypeList = appComputableSettlementRepository.findComputableApplicationAndTypeByDateIntervalAndSettlementId(startDate, endDate, settlementId);

        //  APPLICATION_ID, TYPE
        if(CollectionUtils.isNotEmpty(appTypeList)) {
            for (Object appTypeObject : appTypeList) {
                Map<?, ?> appTypeMap = (Map<?, ?>) appTypeObject;
                ApplicationTypeEnum typeEnum = ApplicationTypeEnum.get((String)appTypeMap.get("TYPE"));
                if (!typeEnumIdMap.containsKey(typeEnum)) {
                    typeEnumIdMap.put(typeEnum, new ArrayList<>());
                }
                typeEnumIdMap.get(typeEnum).add((Long) appTypeMap.get("APPLICATION_ID"));
            }
        }
        return typeEnumIdMap;
    }

    public Map<Long, BigDecimal> getDirectMonthlyNi(Date startTime, Date endTime, Long settlementId, Map<Long, LoadAppMeterVo> idLoadAppVoMap){
        Map<Long, BigDecimal> idYniMap = new HashMap<>();

        for(Map.Entry<Long, LoadAppMeterVo> idLoadAppVo : idLoadAppVoMap.entrySet()) {
            LoadAppMeterVo loadAppMeterVo = idLoadAppVo.getValue();
            BigDecimal monthlyNi = dailyDirectLoadRepository.findMonthlyContractResidualCapacity(startTime, endTime, settlementId
                    , loadAppMeterVo.getAppLoadId()
                    , loadAppMeterVo.getContractNo()
                    , loadAppMeterVo.getMonthlyContractCap().setScale(BIGDECIMAL_KW_PRECISION, RoundingMode.DOWN)); //若不加小數點，jpa就會自動取整

            if(null == monthlyNi){ //表示沒有daily record, 所以剩餘量就是monthly capacity
                idYniMap.put(idLoadAppVo.getKey(), loadAppMeterVo.getMonthlyContractCap());
            }else {
                idYniMap.put(idLoadAppVo.getKey(), monthlyNi);
            }
        }
        return idYniMap;
    }

    public  Map<String, List<Long>> getApplicationGenLoadIdList(Date date, Long settlementId, Long appId){

        Map<String, List<Long>> appGenLoadMap = new HashMap<>();
        List<TempDateApplicationGeneratorLoad> appGenLoadList = appGenLoadRepository.findDateAndSettlementIdAndApplicationIdIn(date, settlementId, Collections.singletonList(appId));

        appGenLoadMap.put(FIELD_NAME_APPLICATION_GENERATOR_ID, appGenLoadList.stream().map(TempDateApplicationGeneratorLoad::getAppGeneratorId).distinct().toList());
        appGenLoadMap.put(FIELD_NAME_APPLICATION_LOAD_ID, appGenLoadList.stream().map(TempDateApplicationGeneratorLoad::getAppLoadId).distinct().toList());

        return appGenLoadMap;
    }

    public Map<Long, BigDecimal> getSumGmiByDatetimeIntervalAndApplicationGeneratorIdIn(Date startTime, Date endTime, List<Long> appGenIdList){

        Map<Long, BigDecimal> timeSumGmiMap = new HashMap<>();
        List<?> sumGmiList = tempAppGenKwRepository.sumGmiByDatetimeIntervalAndApplicationGeneratorIdIn(startTime, endTime, appGenIdList);

        //  DATETIME, SUM(GMI) AS SUM_GMI
        if(CollectionUtils.isNotEmpty(sumGmiList)) {
            for (Object sumGmiObject : sumGmiList) {
                Map<?, ?> sumGmiMap = (Map<?, ?>) sumGmiObject;
                timeSumGmiMap.put(((Date)sumGmiMap.get("DATETIME")).getTime(), (BigDecimal)sumGmiMap.get("SUM_GMI"));
            }
        }
        return timeSumGmiMap;
    }

    public Map<Long, Map<Long, TempTempApplicationLoadKw>> getApplicationLoadKw(Date startTime, Date endTime, List<Long> appLoadIdList){
        Map<Long, Map<Long, TempTempApplicationLoadKw>> idTimeKwMap = new HashMap<>();

        List<TempTempApplicationLoadKw> kwList = tempAppLoadKwRepository.findByDatetimeIntervalAndApplicationLoadIdIn(startTime, endTime, appLoadIdList);
        for(Long appLoadId : appLoadIdList){
            List<TempTempApplicationLoadKw> filteredKwList = kwList.stream().filter(kw->kw.getAppLoadId().equals(appLoadId)).toList();
            if(CollectionUtils.isNotEmpty(filteredKwList)){
                idTimeKwMap.put(appLoadId, filteredKwList.stream().collect(toMap(kw->kw.getDatetime().getTime(), Function.identity())));
            }else {
                idTimeKwMap.put(appLoadId, new HashMap<>());
            }
        }
        return idTimeKwMap;
    }

    public Map<Long, Map<Long, TempTempApplicationGeneratorKw>> getApplicationGeneratorKw(Date startTime, Date endTime, List<Long> appGendList){
        Map<Long, Map<Long, TempTempApplicationGeneratorKw>> idTimeKwMap = new HashMap<>();

        List<TempTempApplicationGeneratorKw> kwList = tempAppGenKwRepository.findByDatetimeIntervalAndApplicationLoadIdIn(startTime, endTime, appGendList);
        for(Long appGenId : appGendList){
            List<TempTempApplicationGeneratorKw> filteredKwList = kwList.stream().filter(kw->kw.getAppGeneratorId().equals(appGenId)).toList();
            if(CollectionUtils.isNotEmpty(filteredKwList)){
                idTimeKwMap.put(appGenId, filteredKwList.stream().collect(toMap(kw->kw.getDatetime().getTime(), Function.identity())));
            }else {
                idTimeKwMap.put(appGenId, new HashMap<>());
            }
        }
        return idTimeKwMap;
    }

    public Map<Long, BigDecimal> getPWMonthlyNi(Date startTime, Date endTime, Long settlementId, Map<Long, LoadAppMeterVo> idLoadAppVoMap){
        Map<Long, BigDecimal> idYniMap = new HashMap<>();

        for(Map.Entry<Long, LoadAppMeterVo> idLoadAppVo : idLoadAppVoMap.entrySet()) {
            LoadAppMeterVo loadAppMeterVo = idLoadAppVo.getValue();
            //取得服務月目前剩餘量
            BigDecimal monthlyNi = dailyLoadRepository.findMonthlyContractResidualCapacity(startTime, endTime, settlementId
                    , loadAppMeterVo.getAppLoadId()
                    , loadAppMeterVo.getContractNo()
                    , loadAppMeterVo.getMonthlyContractCap().setScale(BIGDECIMAL_KW_PRECISION, RoundingMode.DOWN)); //若不加小數點，jpa就會自動取整

            if(null == monthlyNi){ //表示沒有daily record, 所以剩餘量就是monthly capacity
                idYniMap.put(idLoadAppVo.getKey(), loadAppMeterVo.getMonthlyContractCap());
            }else {
                idYniMap.put(idLoadAppVo.getKey(), monthlyNi);
            }
        }
        return idYniMap;
    }

    public Map<Long, Map<String, List<Long>>> getApplicationGenLoadIdList(Date date, Long settlementId, List<Long> appIdList){

        Map<Long, Map<String, List<Long>>> appIdGenLoadMap = new HashMap<>();

        List<TempDateApplicationGeneratorLoad> appGenLoadList = appGenLoadRepository.findDateAndSettlementIdAndApplicationIdIn(date, settlementId, appIdList);
        Map<Long, List<TempDateApplicationGeneratorLoad>> appIdRecordMap = appGenLoadList.stream().collect(groupingBy(TempDateApplicationGeneratorLoad::getApplicationId
                , Collectors.mapping(Function.identity(), toList())));

        for(Map.Entry<Long, List<TempDateApplicationGeneratorLoad>> appIdRecord : appIdRecordMap.entrySet()){
            Map<String, List<Long>> genLoadIdMap = new HashMap<>();
            appIdGenLoadMap.put(appIdRecord.getKey(), genLoadIdMap);
            genLoadIdMap.put(FIELD_NAME_APPLICATION_GENERATOR_ID, appIdRecord.getValue().stream().map(TempDateApplicationGeneratorLoad::getAppGeneratorId).distinct().toList());
            genLoadIdMap.put(FIELD_NAME_APPLICATION_LOAD_ID, appIdRecord.getValue().stream().map(TempDateApplicationGeneratorLoad::getAppLoadId).distinct().toList());
        }
        return appIdGenLoadMap;
    }

    public Map<Long, Map<String, List<Long>>> getApplicationGenLoadIdList(Date startDate, Date endDate, Long settlementId, List<Long> appIdList){

        Map<Long, Map<String, List<Long>>> appIdGenLoadMap = new HashMap<>();

        List<?> appGenLoadList = appGenLoadRepository.findDateAndSettlementIdIntervalAndApplicationIdIn(startDate, endDate, settlementId, appIdList);

        if(CollectionUtils.isNotEmpty(appGenLoadList)){
            for(Object appGenLoadObject : appGenLoadList){
                Map<?, ?> appGenLoadMap = (Map<?, ?>) appGenLoadObject;
                Long appId = (Long)appGenLoadMap.get("APPLICATION_ID");
                Long appGenId = (Long)appGenLoadMap.get("APPLICATION_GENERATOR_ID");
                Long appLoadId = (Long)appGenLoadMap.get("APPLICATION_LOAD_ID");
                if(!appIdGenLoadMap.containsKey(appId)) {
                    appIdGenLoadMap.put(appId, new HashMap<>());
                    appIdGenLoadMap.get(appId).put(FIELD_NAME_APPLICATION_GENERATOR_ID, new ArrayList<>());
                    appIdGenLoadMap.get(appId).put(FIELD_NAME_APPLICATION_LOAD_ID, new ArrayList<>());
                }
                if(!appIdGenLoadMap.get(appId).get(FIELD_NAME_APPLICATION_GENERATOR_ID).contains(appGenId)) {
                    appIdGenLoadMap.get(appId).get(FIELD_NAME_APPLICATION_GENERATOR_ID).add(appGenId);
                }
                if(!appIdGenLoadMap.get(appId).get(FIELD_NAME_APPLICATION_LOAD_ID).contains(appLoadId)) {
                    appIdGenLoadMap.get(appId).get(FIELD_NAME_APPLICATION_LOAD_ID).add(appLoadId);
                }
            }
        }
        return appIdGenLoadMap;
    }

    public Map<Long, List<Long>> getRelationApplicationGeneratorIdByApplicationLoadId(Date date
            , Long settlementId
            , Long selfAppId
            , Map<Long, Map<String, List<Long>>> appIdGenLoadMap){
        //<ApplicationLoadId, List<ApplicationId>>
        Map<Long, List<Long>> appLoadIdGenIdMap = new HashMap<>();
        List<Long> selfLoadIdList = appIdGenLoadMap.get(selfAppId).get(FIELD_NAME_APPLICATION_LOAD_ID);

        List<List<Long>> relAppLoadIdListList = appIdGenLoadMap.values().stream().map(entry->entry.get(FIELD_NAME_APPLICATION_LOAD_ID)).toList();
        List<Long> appLoadIdList  = relAppLoadIdListList.stream().flatMap(Collection::stream).distinct().toList();

        List<?> appRecordList = appMeterRepository.findLoadIdAndMeterIdByDateAndSettlementIdAndApplicationLoadIdIn(date, settlementId, appLoadIdList);
        Map<Long, LoadAppMeterVo> loadMeterMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(appRecordList)){
            for (Object appRecordObject : appRecordList) {
                Map<?, ?> appRecordMap = (Map<?, ?>) appRecordObject;

                //TDAGL.APPLICATION_ID, TDAGL.APPLICATION_LOAD_ID, METER_NO
                Long appLoadId = (Long) appRecordMap.get("APPLICATION_LOAD_ID");
                loadMeterMap.put(appLoadId, LoadAppMeterVo.builder().appLoadId(appLoadId)
                        .applicationId((Long) appRecordMap.get("APPLICATION_ID"))
                        .meterNo((String) appRecordMap.get("METER_NO")).build());

            }
        }

        for(Long selfLoadId : selfLoadIdList){
            String meter = loadMeterMap.get(selfLoadId).getMeterNo();

            List<Long> appIdList = loadMeterMap.values().stream().filter(vo->vo.getMeterNo().equals(meter)
                    && !vo.getAppLoadId().equals(selfLoadId)).map(LoadAppMeterVo::getApplicationId).distinct().toList();

            if(CollectionUtils.isNotEmpty(appIdList)){

                List<List<Long>> relAppGenIdListList = appIdGenLoadMap.entrySet().stream().filter(entry->appIdList.contains(entry.getKey()))
                        .map(entry->entry.getValue().get(FIELD_NAME_APPLICATION_GENERATOR_ID)).toList();
                appLoadIdGenIdMap.put(selfLoadId, relAppGenIdListList.stream().flatMap(Collection::stream).distinct().toList());
            }else {
                appLoadIdGenIdMap.put(selfLoadId, new ArrayList<>());
            }
        }
        return appLoadIdGenIdMap;
    }

    public boolean updateDirectGmiEqualToTimelyUnmatchedRm(Date startTime, Date endTime, Long settlementId, List<Long> appGenIdList){
        try{
            tempAppGenKwRepository.updateDirectGmiByUnmatchedRmAndDatetimeIntervalAndApplicationGeneratorIdIn(startTime, endTime, settlementId, appGenIdList);
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public boolean updateZeroGmiByDatetimeAndApplicationGeneratorIdIn(Long settlementId, Date startTime, Date endTime, List<Long> appGenIdList){
        try{
            tempAppGenKwRepository.updateZeroGmiByDatetimeAndApplicationGeneratorIdIn(settlementId, startTime, endTime, appGenIdList);
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public Map<Long, SumGmiKwVo> getSumGmiAndKwByDatetimeIntervalAndApplicationGeneratorIdIn(Date startTime, Date endTime, List<Long> appGenIdList){
        Map<Long, SumGmiKwVo> timeSumMap = new HashMap<>();
        List<?> sumGmiList = tempAppGenKwRepository.sumGmiAndKwByDatetimeIntervalAndApplicationGeneratorIdIn(startTime, endTime, appGenIdList);

        //  DATETIME, SUM(GMI) AS SUM_GMI, SUM(KW_RATIO) AS SUM_KW_RATIO
        if(CollectionUtils.isNotEmpty(sumGmiList)) {
            for (Object sumGmiObject : sumGmiList) {
                Map<?, ?> sumGmiMap = (Map<?, ?>) sumGmiObject;
                timeSumMap.put(((Date)sumGmiMap.get("DATETIME")).getTime(), SumGmiKwVo.builder()
                        .gmi((BigDecimal)sumGmiMap.get("SUM_GMI"))
                        .kwRatio((BigDecimal)sumGmiMap.get("SUM_KW_RATIO"))
                        .build());
            }
        }
        return timeSumMap;

    }

    public boolean updateGmiToZeroByDatetimeAndApplicationGeneratorIdIn(Date startTime, Date endTime, List<Long> appGenIdList){
        try{
            tempAppGenKwRepository.updateGmiToZeroByDatetimeAndApplicationGeneratorIdIn(startTime, endTime, appGenIdList);
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    public Map<Long, List<Long>> getRelationApplicationGeneratorIdByApplicationLoadId(Date startDate
            , Date endDate
            , Long settlementId
            , Long selfAppId
            , Map<Long, Map<String, List<Long>>> appIdGenLoadMap){
        //<ApplicationLoadId, List<ApplicationId>>
        Map<Long, List<Long>> appLoadIdGenIdMap = new HashMap<>();
        List<Long> selfLoadIdList = appIdGenLoadMap.get(selfAppId).get(FIELD_NAME_APPLICATION_LOAD_ID);

        List<List<Long>> relAppLoadIdListList = appIdGenLoadMap.values().stream().map(entry->entry.get(FIELD_NAME_APPLICATION_LOAD_ID)).toList();
        List<Long> appLoadIdList  = relAppLoadIdListList.stream().flatMap(Collection::stream).distinct().toList();

        List<?> appRecordList = appMeterRepository.findLoadIdAndMeterIdByDateIntervalAndSettlementIdAndApplicationLoadIdIn(startDate, endDate, settlementId, appLoadIdList);
        Map<Long, LoadAppMeterVo> loadMeterMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(appRecordList)){
            for (Object appRecordObject : appRecordList) {
                Map<?, ?> appRecordMap = (Map<?, ?>) appRecordObject;

                //TDAGL.APPLICATION_ID, TDAGL.APPLICATION_LOAD_ID, METER_NO
                Long appLoadId = (Long) appRecordMap.get("APPLICATION_LOAD_ID");
                loadMeterMap.put(appLoadId, LoadAppMeterVo.builder().appLoadId(appLoadId)
                        .applicationId((Long) appRecordMap.get("APPLICATION_ID"))
                        .meterNo((String) appRecordMap.get("METER_NO")).build());

            }
        }

        for(Long selfLoadId : selfLoadIdList){
            String meter = loadMeterMap.get(selfLoadId).getMeterNo();

            List<Long> appIdList = loadMeterMap.values().stream().filter(vo->vo.getMeterNo().equals(meter)
                    && !vo.getAppLoadId().equals(selfLoadId)).map(LoadAppMeterVo::getApplicationId).distinct().toList();

            if(CollectionUtils.isNotEmpty(appIdList)){

                List<List<Long>> relAppGenIdListList = appIdGenLoadMap.entrySet().stream().filter(entry->appIdList.contains(entry.getKey()))
                        .map(entry->entry.getValue().get(FIELD_NAME_APPLICATION_GENERATOR_ID)).toList();
                appLoadIdGenIdMap.put(selfLoadId, relAppGenIdListList.stream().flatMap(Collection::stream).distinct().toList());
            }else {
                appLoadIdGenIdMap.put(selfLoadId, new ArrayList<>());
            }
        }
        return appLoadIdGenIdMap;
    }

}
