package tw.com.taipower.pwoms.services.applicant;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.dhatim.fastexcel.reader.ExcelReaderException;
import org.dhatim.fastexcel.reader.ReadableWorkbook;
import org.dhatim.fastexcel.reader.Row;
import org.dhatim.fastexcel.reader.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.data.entity.pwoms.Application;
import tw.com.taipower.data.entity.pwoms.ApplicationDocument;
import tw.com.taipower.data.entity.pwoms.ApplicationGenerator;
import tw.com.taipower.data.entity.pwoms.ApplicationLoad;
import tw.com.taipower.data.enums.ALGKeepEnum;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.logger.CustomLog;
import tw.com.taipower.pwoms.services.BaseService;
import tw.com.taipower.pwoms.services.applicant.vo.ExternalApplicationGeneratorVO;
import tw.com.taipower.pwoms.services.applicant.vo.ExternalApplicationLoadVO;
import tw.com.taipower.pwoms.services.applicant.vo.ExternalApplicationVO;
import tw.com.taipower.pwoms.services.entitymanage.GeneratorService;
import tw.com.taipower.pwoms.services.entitymanage.LoadService;
import tw.com.taipower.pwoms.services.enumclass.ErrorCode;
import tw.com.taipower.pwoms.services.flowcontrol.ApplicationService;
import tw.com.taipower.pwoms.services.flowcontrol.ApplicationTxService;
import tw.com.taipower.pwoms.services.tpc.RemsService;
import tw.com.taipower.pwoms.services.tpc.SelectorITService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.utils.FileTypeValidator;
import tw.com.taipower.pwoms.services.utils.VoUtils;
import tw.com.taipower.pwoms.services.vo.generated.*;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 外部申請者計畫書契約相關服務
 * 雖然是計畫書契約相關服務，依據分類放到申請者模組中
 *
 * @class: ExternalApplicationService
 * @author: linyu-sheng
 * @version: 0.0.0
 * @since: 2024/5/3
 * @see:
 */
@Service
@CustomLog
public class ExternalApplicationService extends BaseService {

    @Autowired
    private EntityDocumentRequiredRepository entityDocumentRequiredRepository;

    @Autowired
    private WorkflowDocumentRepository workflowDocumentRepository;

    @Autowired
    private GeneratorService generatorService;

    @Autowired
    private LoadService loadService;

    @Autowired
    private RemsService remsService;

    @Autowired
    private ApplicantEntityRepository applicantEntityRepository;

    @Autowired
    private ApplicantEntityContactRepository applicantEntityContactRepository;

    @Autowired
    private ApplicationTxService applicationTxService;

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private ApplicationGeneratorRepository applicationGeneratorRepository;

    @Autowired
    private ApplicationLoadRepository applicationLoadRepository;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private ApplicationDocumentRepository applicationDocumentRepository;

    @Autowired
    private SelectorITService selectorITService;

    /**
     * 利用檔案匯入查詢大量用電端
     *
     * @param fileInputStream excel file
     * @return List<LoadEntityVO>
     */
    public List<ExternalApplicationGeneratorVO> searchGeneratorFromExcel(InputStream fileInputStream, Long userId) {
        List<ApplicationGeneratorVO> vos = new ArrayList<>();
        List<String> nbsList = new ArrayList<>();
        try {
            try (ReadableWorkbook wb = new ReadableWorkbook(fileInputStream)) {
                Sheet sheet = wb.getFirstSheet();
                try (Stream<Row> rows = sheet.openStream()) {
                    rows.forEach(r -> {
                        String nbsNo = null;
                        Integer order = null;
                        if (r.getCellCount() < 4) return;
                        if (r.getCell(1) != null && r.getCell(1).getRawValue() != null) {
                            try {
                                nbsNo = r.getCell(1).getRawValue().replaceAll("-", "").trim();
                                Long.valueOf(nbsNo); // 無法轉換代表非有效資料
                                nbsList.add(nbsNo);
                            } catch (ExcelReaderException e) {
                                // skip this value
                                log.error(e.getMessage());
                            } catch (Throwable e) {
                                // skip this value
                                log.error(e.getMessage());
                            }
                        }
                        // 要加上處理order給前端判斷
                        if (r.getCell(0) != null && r.getCell(0).getRawValue() != null) {
                            try {
                                order = Integer.valueOf(r.getCell(0).getRawValue());
                            } catch (ExcelReaderException e) {
                                // skip this value
                                log.error(e.getMessage());
                            } catch (Throwable e) {
                                // skip this value
                                log.error(e.getMessage());
                            }
                        }
                        if (nbsNo != null && order != null) {
                            var tempLoad = GeneratorEntityVO.builder().nbsCustomerNumber(nbsNo).build();
                            var vo =
                                    ApplicationGeneratorVO.builder().order(order).generatorEntity(tempLoad).outterName(
                                            r.getCellText(2)).outterAddress(r.getCellText(3)).build();
                            vos.add(vo);
                        }
                    });
                }
            }
            var entityVos = generatorService.getTempGeneratorEntitysByCustomerNos(nbsList);
            vos.forEach(vo -> {
                var optFirst = entityVos.stream().filter(a -> a.getNbsCustomerNumber().equals(
                        vo.getGeneratorEntity().getNbsCustomerNumber())).findFirst();
                if (optFirst.isPresent()) {
                    vo.setGeneratorEntity(optFirst.get());
                } else {
                    vo.getGeneratorEntity().setId(-1L);
                    vo.getGeneratorEntity().setName("Unknown");
                }
            });
            try {
                var newNoList = vos.stream().filter(a -> a.getGeneratorEntity().getId() < 0).map(
                        a -> a.getGeneratorEntity().getNbsCustomerNumber()).collect(Collectors.toList());
                remsService.syncRNISWithCustomNumbers(newNoList, userId);
                var newEntityVos = generatorService.getTempGeneratorEntitysByCustomerNos(newNoList);
                vos.stream().filter(a -> a.getGeneratorEntity().getId() < 0).forEach(vo -> {
                    var optFirst = newEntityVos.stream().filter(a -> a.getNbsCustomerNumber().equals(
                            vo.getGeneratorEntity().getNbsCustomerNumber())).findFirst();
                    optFirst.ifPresent(vo::setGeneratorEntity);
                });
            } catch (Throwable ex) {
                log.error(ex, ex);
            }
            return vos.stream().map(a -> VoUtils.toVO(a, ExternalApplicationGeneratorVO.class)).collect(
                    Collectors.toList());
        } catch (Throwable ex) {
            throwException(ErrorCode.FILE_IS_INVALID);
        }
        return null;
    }

    /**
     * 利用檔案匯入查詢大量用電端
     *
     * @param fileInputStream excel file
     * @return List<LoadEntityVO>
     */
    public List<ExternalApplicationLoadVO> searchLoadsFromExcel(InputStream fileInputStream, Long userId) {
        List<ApplicationLoadVO> vos = new ArrayList<>();
        List<String> nbsList = new ArrayList<>();
        try {
            try (ReadableWorkbook wb = new ReadableWorkbook(fileInputStream)) {
                Sheet sheet = wb.getFirstSheet();
                try (Stream<Row> rows = sheet.openStream()) {
                    rows.forEach(r -> {
                        String nbsNo = null;
                        Integer order = null;
                        if (r.getCellCount() < 4) return;
                        if (r.getCell(1) != null && r.getCell(1).getRawValue() != null) {
                            try {
                                nbsNo = r.getCell(1).getRawValue().replaceAll("-", "").trim();
                                Long.valueOf(nbsNo); // 無法轉換代表非有效資料
                                nbsList.add(nbsNo);
                            } catch (ExcelReaderException e) {
                                // skip this value
                                log.error(e.getMessage());
                            } catch (Throwable e) {
                                // skip this value
                                log.error(e.getMessage());
                            }
                        }
                        // 要加上處理order給前端判斷
                        if (r.getCell(0) != null && r.getCell(0).getRawValue() != null) {
                            try {
                                order = Integer.valueOf(r.getCell(0).getRawValue());
                            } catch (ExcelReaderException e) {
                                // skip this value
                                log.error(e.getMessage());
                            } catch (Throwable e) {
                                // skip this value
                                log.error(e.getMessage());
                            }
                        }
                        if (nbsNo != null && order != null) {
                            var tempLoad = LoadEntityVO.builder().nbsCustomerNumber(nbsNo).build();
                            var vo =
                                    ApplicationLoadVO.builder().order(order).loadEntity(tempLoad).outterName(
                                            r.getCellText(2)).outterAddress(r.getCellText(3)).build();
                            vos.add(vo);
                        }
                    });
                }
            }
            var entityVos = loadService.getTempLoadEntitysByCustomerNos(nbsList);
            vos.forEach(vo -> {
                var optFirst = entityVos.stream().filter(
                        a -> a.getNbsCustomerNumber().equals(vo.getLoadEntity().getNbsCustomerNumber())).findFirst();
                if (optFirst.isPresent()) {
                    vo.setLoadEntity(optFirst.get());
                } else {

                    vo.getLoadEntity().setId(-1L);
                    vo.getLoadEntity().setName("Unknown");
                }
            });
            try {
                var newNoList = vos.stream().filter(a -> a.getLoadEntity().getId() < 0).map(
                        a -> a.getLoadEntity().getNbsCustomerNumber()).collect(Collectors.toList());
                remsService.syncNBSWithCustomNumbers(newNoList, userId);
                var newEntityVos = loadService.getTempLoadEntitysByCustomerNos(newNoList);
                vos.stream().filter(a -> a.getLoadEntity().getId() < 0).forEach(vo -> {
                    var optFirst = newEntityVos.stream().filter(a -> a.getNbsCustomerNumber().equals(
                            vo.getLoadEntity().getNbsCustomerNumber())).findFirst();
                    optFirst.ifPresent(vo::setLoadEntity);
                });
            } catch (Throwable ex) {
                log.error(ex, ex);
            }
            return vos.stream().map(a -> VoUtils.toVO(a, ExternalApplicationLoadVO.class)).collect(Collectors.toList());
        } catch (Throwable ex) {
            throwException(ErrorCode.FILE_IS_INVALID);
        }
        return null;
    }

    /**
     * 案件選擇申請人及聯絡人以後，儲存草稿。
     *
     * @param applicationVO
     * @param userId
     * @return
     */
    @Transactional
    public Long saveDraft(ExternalApplicationVO applicationVO, Long userId) {

        var applicant = findByAccountId(userId);
        /**
         * init applicationLoad & applicationGenerator
         */
        applicationVO.getApplicationGeneratorList().forEach(ag -> {
            if (ag.getKeep() == null) {
                ag.setKeep(ALGKeepEnum.KEEP_USING);
            }
        });
        applicationVO.getApplicationLoadList().forEach(al -> {

            if (al.getKeep() == null) {
                al.setKeep(ALGKeepEnum.KEEP_USING);
            }
        });
        // checkDraftNeededColumn(applicationVO);

        applicationVO.setModifiedAt(null);

        /**
         * 1. 轉換VO成model
         */
        var model = VoUtils.toEntity(applicationVO, Application.class);
        /**
         * 2. 設定編輯人員
         */
        model.setModifiedBy(userId);
        /**
         * 3.
         * 查詢申請人，取得統編
         */
        if (model.getApplicantId() == null) {
            throwException(ErrorCode.APPLICATION_APPLICANT_IS_NEED);
        }
        if (model.getApplicantContactId() == null) {
            throwException(ErrorCode.APPLICATION_APPLICANT_CONTACT_IS_NEED);
        }

        var optApplicantContact = applicantEntityContactRepository.findById(model.getApplicantContactId());
        if (!optApplicantContact.isPresent()) {
            throw new RuntimeException("ApplicantEntityContact not found");
        }

        if (model.getId() == null) {
            var taxId = applicant.getTaxId();
            var yearMonth = DateUtils.getYearMonth();

            //
            // 如果沒有遞件編號，產生遞件編號
            model.setNo(applicationTxService.applicationNo(taxId, yearMonth));
            /**
             * 存入資料庫
             */
            var dbModel = applicationRepository.save(model);
            /**
             * 回傳案件ID
             */
            saveRelations(model, dbModel.getId(), true);

            return dbModel.getId();
        } else {
            var optdbModel = applicationRepository.findById(model.getId());
            if (!optdbModel.isPresent()) {
                throw new RuntimeException("Application not found");
            }
            model.setModifiedAt(null);
            var dbModel = optdbModel.get();
            VoUtils.mergeObjectWithNullIgnore(model, dbModel);

            applicationRepository.save(dbModel);
            saveRelations(model, model.getId(), false);
            return dbModel.getId();
        }

    }

    void saveRelations(Application model, Long applicationId, boolean isNew) {
        var genRefs = model.getApplicationGeneratorList();
        var loadRefs = model.getApplicationLoadList();
        /**
         * 新建草稿，不用比對，直接新建
         */
        if (isNew) {
            if (!genRefs.isEmpty()) {
                genRefs.stream().forEach(a -> {
                    a.setApplicationId(applicationId);
                    if (a.getPmi() == null) {
                        a.setPmi(BigDecimal.ZERO);
                    }
                });
                applicationGeneratorRepository.saveAll(genRefs);
            }
            if (!loadRefs.isEmpty()) {
                loadRefs.stream().forEach(a -> {
                    a.setApplicationId(applicationId);
                    if (a.getAnnualContractCap() == null) {
                        a.setAnnualContractCap(0L);
                    }
                    if (a.getMonthlyContractCap() == null) {
                        a.setMonthlyContractCap(0L);
                    }
                });
                applicationLoadRepository.saveAll(loadRefs);
            }
        } else {
            var queryGen = ApplicationGenerator.builder().applicationId(model.getId()).build();
            var allDbGens = applicationGeneratorRepository.findAll(Example.of(queryGen));
            var queryLoad = ApplicationLoad.builder().applicationId(model.getId()).build();
            var allDbLoads = applicationLoadRepository.findAll(Example.of(queryLoad));

            var saveGenRefs = genRefs.stream().map(genRef -> {
                var optFrist =
                        allDbGens.stream().filter(a -> a.getGeneratorId().equals(genRef.getGeneratorId())).findFirst();
                if (optFrist.isPresent()) {
                    VoUtils.mergeObjectWithNullIgnore(genRef, optFrist.get());
                    return optFrist.get();
                } else {
                    genRef.setApplicationId(applicationId);
                    if (genRef.getPmi() == null) {
                        genRef.setPmi(BigDecimal.ZERO);
                    }
                    return genRef;
                }
            }).collect(Collectors.toList());
            var saveLoadRefs = loadRefs.stream().map(loadRef -> {
                var optFrist = allDbLoads.stream().filter(a -> a.getLoadId().equals(loadRef.getLoadId())).findFirst();
                if (optFrist.isPresent()) {
                    VoUtils.mergeObjectWithNullIgnore(loadRef, optFrist.get());
                    return optFrist.get();
                } else {
                    loadRef.setApplicationId(applicationId);
                    if (loadRef.getAnnualContractCap() == null) {
                        loadRef.setAnnualContractCap(0L);
                    }
                    if (loadRef.getMonthlyContractCap() == null) {
                        loadRef.setMonthlyContractCap(0L);
                    }
                    return loadRef;
                }
            }).collect(Collectors.toList());

            /**
             * 這邊是要儲存的
             */
            saveGenRefs = applicationGeneratorRepository.saveAll(saveGenRefs);
            saveLoadRefs = applicationLoadRepository.saveAll(saveLoadRefs);

            /**
             * 要刪除的
             */
            var saveGenIds = saveGenRefs.stream().map(a -> a.getId()).collect(Collectors.toList());
            var saveLoadIds = saveLoadRefs.stream().map(a -> a.getId()).collect(Collectors.toList());
            var deleteGens =
                    allDbGens.stream().filter(a -> !saveGenIds.contains(a.getId())).collect(Collectors.toList());
            var deleteLoads =
                    allDbLoads.stream().filter(a -> !saveLoadIds.contains(a.getId())).collect(Collectors.toList());

            applicationGeneratorRepository.deleteAll(deleteGens);
            applicationLoadRepository.deleteAll(deleteLoads);
        }
    }

    /**
     * 計畫書/案件送出審查申請
     *
     * @param applicationid
     * @param userId
     */
    public void submitApplication(Long applicationid, ExternalApplicationVO applicationVO,
                                  Long userId) throws JsonProcessingException {
        saveDraft(applicationVO, userId);
        // validApplication(applicationVO);
        applicationService.checkApplicationDocumentStage1(applicationVO.getId());
        applicationTxService.submitToWorkflow(applicationid, userId);
    }

    /**
     * 作廢計畫書
     *
     * @param applicationid
     * @param userId
     */
    public void abandon(Long applicationid, Long userId) {
        applicationService.abandon(applicationid, userId);
    }

    /**
     * 取得計畫書相關應附文件
     *
     * @param applicationId
     * @return
     */
    public List<ApplicationDocumentVO> findUploadedDocumentsByApplicationId(Long applicationId, Long userId) {
        checkApplicationAccessForAccount(applicationId, userId);
        // 改成使用同一方法，只差在上面那段檢查權限
        return applicationService.findUploadedDocumentsByApplicationId(applicationId);
    }

    /**
     * 上傳計畫書相關文件
     *
     * @param applicationId
     * @param documentId
     * @param file
     * @param unit
     * @param serialNumber
     * @param operationMode
     * @param validDate
     * @param issueDate
     * @param comment
     * @param userId
     * @return
     * @throws IOException
     */
    public Long uploadDocumentByApplicationIdAndDocumentId(Long applicationId, Integer documentId, MultipartFile file,
                                                           String unit, String serialNumber, String operationMode,
                                                           Date validDate, Date issueDate, String comment,
                                                           Long userId) throws Exception {
        FileTypeValidator.checkValid(file);
        selectorITService.checkWithoutResult(file);
        checkApplicationAccessForAccount(applicationId, userId);
        // 改成使用同一方法，只差在上面那段檢查權限
        return applicationService.uploadDocumentByApplicationIdAndDocumentId(applicationId, documentId, file, unit,
                serialNumber, operationMode, validDate, issueDate, comment, userId);

    }

    /**
     * 取得檔案下載內容
     *
     * @param id
     * @param userId
     * @return
     */
    public ApplicationDocumentVO getUploadedDocumentById(Long id, Long userId) {
        ApplicationDocument model = (ApplicationDocument) this.applicationDocumentRepository.findById(id).get();
        checkApplicationAccessForAccount(model.getApplicationId(), userId);
        ApplicationDocumentVO result = (ApplicationDocumentVO) VoUtils.toVO(model, ApplicationDocumentVO.class);
        result.setContent(model.getContent());
        return result;
    }
}
