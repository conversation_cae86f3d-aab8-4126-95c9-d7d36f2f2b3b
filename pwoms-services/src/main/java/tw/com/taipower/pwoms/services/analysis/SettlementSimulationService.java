package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum;
import tw.com.taipower.pwoms.services.enumclass.MeterChannelEnum;
import tw.com.taipower.pwoms.services.enumclass.SettlementMethodEnum;
import tw.com.taipower.pwoms.services.settlement.*;
import tw.com.taipower.pwoms.services.system.VoltageLevelService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.analysis.*;
import tw.com.taipower.pwoms.services.vo.settlement.FailGeneratorVo;
import tw.com.taipower.pwoms.services.vo.settlement.FailLoadVo;
import tw.com.taipower.pwoms.services.vo.settlement.SettlementResultVo;
import tw.com.taipower.pwoms.services.vo.utils.TimeIntervalVo;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;
import static tw.com.taipower.pwoms.services.enumclass.MeterChannelEnum.METER_CHANNEL_GENERATOR;
import static tw.com.taipower.pwoms.services.enumclass.MeterChannelEnum.METER_CHANNEL_LOAD;
import static tw.com.taipower.pwoms.services.enumclass.SettlementFailureTypeEnum.*;
import static tw.com.taipower.pwoms.services.enumclass.SettlementFailureTypeEnum.SETTLEMENT_FAILURE_LOAD_KW;
import static tw.com.taipower.pwoms.services.enumclass.SettlementMethodEnum.*;

/**
 * 結算模擬分析模組
 *
 * @class: SettlementSimulationService
 * @author: SilverYang
 * @version: 0.1.0
 * @since: 2025-06-09 13:56
 * @see:
 **/
@Log4j2
@Service
public class SettlementSimulationService {

    @Autowired
    private SimulationSettlementUtilsService simulationSettlementUtilsService;

    @Autowired
    private SimulationSettlementCalculationService settlementCalService;

    @Autowired
    private SimulationSettlementContractVer2Service contractVer2Service;

    @Autowired
    private SimulationSettlementTempService tempService;

    @Autowired
    private SimulationSettlementTrialPowerWheelingService trialPowerWheelingService;

    @Autowired
    private SimulationSettlementTrialDirectService trialDirectService;

    @Autowired
    private SimulationTempApplicationMonthlyCapacitySettlementRepository tempMonthlyCapacitySettlementRepository;

    @Autowired
    private SimulationTempDateApplicationMeterRepository tempDateAppRepository;

    @Autowired
    private SimulationAmiService amiService;

    @Autowired
    private SimulationSettlementNiService settlementNiService;

    @Autowired
    private SimulationSettlementPartialDailyService dailyService;

    @Autowired
    private SimulationSettlementPartialMonthlyService monthlyService;

    @Autowired
    private SimulationTempApplicationMonthlyTrialSettlementRepository trialSettlementRepository;

    @Autowired
    private SimulationMeterChangeRecordRepository meterChangeRecordRepository;

    @Autowired
    private SimulationSettlementUtilsService settlementUtilsService;

    @Autowired
    private SimulationGeneratorEntityCombinedCapacityRepository simulationGeneratorEntityCombinedCapacityRepository;

    @Autowired
    private SimulationApplicationGeneratorRepository simulationApplicationGeneratorRepository;

    @Autowired
    private SimulationGeneratorEntityRepository simulationGeneratorEntityRepository;

    @Autowired
    private SimulationApplicationLoadRepository simulationApplicationLoadRepository;

    @Autowired
    private SimulationLoadEntityRepository simulationLoadEntityRepository;

    @Autowired
    private SimulationApplicationRelationRepository simulationApplicationRelationRepository;

    @Autowired
    VoltageLevelRepository voltageLevelRepository;




    private static final int PROCESS_COUNT_PER_TIME = 200;

    public SettlementResultVo calculateByContractNo(Date date, List<String> contractNoList, String reCalReason, Integer userId, SettlementCalculationInterface caller) {
        log.info("start -- trail-calculate by Contract No.");
        simulationSettlementUtilsService.setSettlementCalculation(SETTLEMENT_METHOD_TRIAL_CALCULATION);
        SettlementResultVo settlementResultVo = calculateByContractNo(SETTLEMENT_METHOD_TRIAL_CALCULATION, date, contractNoList, reCalReason, userId, caller);
        simulationSettlementUtilsService.cleanSettlementCalculation();
        log.info("end -- trail-calculate by Contract No.");
        return settlementResultVo;
    }

    public void saveSimulationInfo(int year,int month, List<AssociatedContractsVo> associatedContracts, List<GenerationsVo> generations, List<LoadsVo> loads){

        simulationApplicationGeneratorRepository.deleteAll();
        simulationApplicationLoadRepository.deleteAll();
        simulationApplicationRelationRepository.deleteAll();
        simulationGeneratorEntityCombinedCapacityRepository.deleteAll();
        simulationGeneratorEntityRepository.deleteAll();
        simulationLoadEntityRepository.deleteAll();

        List<SimulationApplicationRelation>  simulationApplicationRelationList = generateRelations(year, month, associatedContracts);
        simulationApplicationRelationRepository.saveAll(simulationApplicationRelationList);

        if(!generations.isEmpty()){

            for(GenerationsVo generationsVo : generations){
                SimulationGeneratorEntity simulationGeneratorEntity = new SimulationGeneratorEntity();
                simulationGeneratorEntity.setId(generationsVo.getGeneratorId());
                simulationGeneratorEntity.setIsSaleInTrialRun(generationsVo.getIsSaleInTrialRun());

                VoltageLevel voltageLevel = voltageLevelRepository.findByLabel(generationsVo.getResponsibilityVoltage());

                simulationGeneratorEntity.setResponsibilityVoltage(voltageLevel.getId());

                simulationGeneratorEntityRepository.save(simulationGeneratorEntity);

                SimulationApplicationGenerator simulationApplicationGenerator = new SimulationApplicationGenerator();
                simulationApplicationGenerator.setApplicationId(generationsVo.getApplicationId());
                simulationApplicationGenerator.setGeneratorId(generationsVo.getGeneratorId());
                simulationApplicationGenerator.setPmi(generationsVo.getPmi());
                simulationApplicationGeneratorRepository.save(simulationApplicationGenerator);

                if(!generationsVo.getGeneratorEntityMeterList().isEmpty()){

                    for(GeneratorEntityMeterVo  generatorEntityMeterVo : generationsVo.getGeneratorEntityMeterList()){
                        SimulationGeneratorEntityCombinedCapacity simulationGeneratorEntityCombinedCapacity = new SimulationGeneratorEntityCombinedCapacity();
                        simulationGeneratorEntityCombinedCapacity.setGeneratorEntityId(generationsVo.getGeneratorId());
                        simulationGeneratorEntityCombinedCapacity.setCapacity(generatorEntityMeterVo.getCapacity());
                        simulationGeneratorEntityCombinedCapacity.setCombinedDate(generatorEntityMeterVo.getCombinedDate());
                        simulationGeneratorEntityCombinedCapacity.setLicenseDate(generatorEntityMeterVo.getLicenseDate());
                        simulationGeneratorEntityCombinedCapacityRepository.save(simulationGeneratorEntityCombinedCapacity);

                    }

                }

            }

        }


        if(!loads.isEmpty()){

            for(LoadsVo loadsVo : loads){
                SimulationLoadEntity simulationLoadEntity = new SimulationLoadEntity();
                simulationLoadEntity.setId(loadsVo.getLoadId());

                VoltageLevel voltageLevel = voltageLevelRepository.findByLabel(loadsVo.getResponsibilityVoltage());

                simulationLoadEntity.setResponsibilityVoltage(voltageLevel.getId());

                simulationLoadEntityRepository.save(simulationLoadEntity);

                SimulationApplicationLoad simulationApplicationLoad = new SimulationApplicationLoad();
                simulationApplicationLoad.setApplicationId(loadsVo.getApplicationId());
                simulationApplicationLoad.setLoadId(loadsVo.getLoadId());
                simulationApplicationLoad.setAnnualContractCap(loadsVo.getAnnualContractCap().longValue());
                simulationApplicationLoad.setMonthlyContractCap(loadsVo.getMonthlyContractCap().longValue());
                simulationApplicationLoad.setIsDirect(loadsVo.getIsDirect());

                simulationApplicationLoadRepository.save(simulationApplicationLoad);

            }

        }


    }

    public List<SimulationApplicationRelation> generateRelations(
            int year,
            int month,
            List<AssociatedContractsVo> associatedContracts) {


        YearMonth ym = YearMonth.of(year, month);
        int daysInMonth = ym.lengthOfMonth();
        List<LocalDate> allDates = new ArrayList<>();

        for (int day = 1; day <= daysInMonth; day++) {
            allDates.add(LocalDate.of(year, month, day));
        }


        List<SimulationApplicationRelation> result = new ArrayList<>();


        if (associatedContracts == null || associatedContracts.isEmpty()) {
            return result;
        }


        ZoneId zoneId = ZoneId.systemDefault();


        for (LocalDate localDate : allDates) {

            Date utilDate = Date.from(localDate.atStartOfDay(zoneId).toInstant());


            for (AssociatedContractsVo source : associatedContracts) {
                for (AssociatedContractsVo target : associatedContracts) {

//                    if (source.getApplicationId().equals(target.getApplicationId())) {
//                        continue;
//                    }

                    SimulationApplicationRelation relation = new SimulationApplicationRelation();
                    relation.setDate(utilDate);
                    relation.setApplicationId(source.getApplicationId());
                    relation.setAppRelationId(target.getApplicationId());

                    result.add(relation);
                }
            }
        }
        return result;
    }

    public SettlementResultVo calculateByContractNo(SettlementMethodEnum methodEnum
            , Date date
            , List<String> contractNoList
            , String reCalReason
            , Integer userId
            , SettlementCalculationInterface caller) {

        SettlementResultVo settlementResultVo = SettlementResultVo.builder()
                .methodEnum(methodEnum)
                .pass(true)
                .serviceDate(date)
                .build();

        SimulationSettlementCalculation sc = initialCalculation(methodEnum, date, userId, caller);
        if(sc.getSettlementId() > 0) {
            settlementResultVo.setSettlementId(sc.getSettlementId());
            List<Long> appIdList = contractVer2Service.getApplicationIdByContractNo(contractNoList);

            try {
                tempService.deleteTempTempApplicationKw(); //temporary remove
                if (CollectionUtils.isNotEmpty(contractNoList)) {
                    TimeIntervalVo timeIntervalVo = DateUtils.getMonthInterval(date.getTime());
                    //delete all related records
                    //當只有部分結算才要都刪除
                    //全部結算會用部分結算方式，所以，不用刪資料
                    deleteAllRecordByApplicationId(methodEnum, date, appIdList);

                    if(CollectionUtils.isNotEmpty(appIdList)) {
                        startCalculation(sc, appIdList, reCalReason, userId, caller);

                        tempService.saveDateApplicationMeter(timeIntervalVo.getStartTime().getTime(), timeIntervalVo.getEndTime().getTime(), sc.getSettlementId(), contractNoList);
                        updateMeterComputable(timeIntervalVo.getStartTime().getTime(), timeIntervalVo.getEndTime().getTime(), sc.getSettlementId());
                        List<Long> incmpAppIdList = findIncomputableApplication(sc, appIdList, userId, caller);
                        calculate(timeIntervalVo, sc, incmpAppIdList, userId, caller);
                    }else{
                        settlementResultVo.setPass(false);
                        settlementResultVo.setErrorMsg("無可計算契約");
                    }
                }
            } catch (Exception exception) {
                log.error(exception.getMessage());
                settlementResultVo.setPass(false);
                settlementResultVo.setErrorMsg(simulationSettlementUtilsService.translateExceptionToReadableSentence(methodEnum, exception.getMessage()));
                simulationSettlementUtilsService.sendToFront(userId, sc.getSettlementId(), false, settlementResultVo.getErrorMsg(), new BigDecimal("100"), new Date(), caller);
            }
            closeSettlementCalculation(sc.getSettlementId(), settlementResultVo);
        }else{
            log.error("無法取得Settlement Calculation");
            settlementResultVo.setPass(false);
            settlementResultVo.setErrorMsg("無法取得Settlement Calculation");
        }
        return settlementResultVo;
    }

    private SimulationSettlementCalculation initialCalculation(SettlementMethodEnum methodEnum, Date date, Integer userId, SettlementCalculationInterface caller) {
        SettlementMethodEnum initMethod = methodEnum.equals(SETTLEMENT_METHOD_TRIAL_CALCULATION) ? SETTLEMENT_METHOD_INITIAL_TRIAL_CALCULATION : SETTLEMENT_METHOD_INITIAL_ALL_CALCULATION;
        SimulationSettlementCalculation calculation = SimulationSettlementCalculation.builder()
                .serviceDate(date)
                .executionStart(new Date())
                .calculationMethod(initMethod.getId())
                .executionLog("結算初始化")
                .executionPercentage(BigDecimal.ZERO)
                .userId(userId)
                .build();
        SimulationSettlementCalculation sc = settlementCalService.saveCalculation(calculation);
        log.info(sc);
        simulationSettlementUtilsService.sendToFront(userId, sc, caller);
        return sc;
    }

    private void deleteAllRecordByApplicationId(SettlementMethodEnum methodEnum, Date date, List<Long> applicationIdList) throws Exception{

        //部分結算
        if(methodEnum.equals(SETTLEMENT_METHOD_TRIAL_CALCULATION)) {
//            Long delSettlementId = settlementCalService.getLatestIdCalculation(SETTLEMENT_METHOD_TRIAL_CALCULATION, date);
            Long delSettlementId = settlementCalService.getLatestIdCalculation(SETTLEMENT_METHOD_TRIAL_CALCULATION);
            if (null != delSettlementId) {
                deleteAllRecordBySettlementId(delSettlementId);
            }
        }else{
            //全部結算(刪除還要包含採用部分結算)
            //SC.SETTLEMENT_ID, SCR.APPLICATION_ID

        }
    }

    public void deleteAllRecordBySettlementId(Long settlementId){

        try {
            trialDirectService.deleteAllRecordBySettlementId(settlementId);
            trialPowerWheelingService.deleteAllRecordBySettlementId(settlementId);

            tempMonthlyCapacitySettlementRepository.deleteAllRecordBySettlementId(settlementId);
            tempDateAppRepository.deleteAllRecordBySettlementId(settlementId);
            settlementCalService.deleteAllRecordBySettlementId(settlementId);
            amiService.deleteSettlementTempRecordBySettlementId(settlementId);
        }catch (Exception exception){
            log.error(exception.getMessage());
        }
    }

    private long startCalculation(SimulationSettlementCalculation sc, List<Long> appIdList, String reCalReason, Integer userId, SettlementCalculationInterface caller) {
        simulationSettlementUtilsService.sendToFront(userId, sc.getSettlementId(), new BigDecimal("100"), caller);
        SettlementMethodEnum methodEnum = SETTLEMENT_METHOD_INITIAL_ALL_CALCULATION.getId() == sc.getCalculationMethod() ? SETTLEMENT_METHOD_ALL_CALCULATION : SETTLEMENT_METHOD_TRIAL_CALCULATION;
        settlementCalService.updateCalculationMethodAndLog(sc.getSettlementId()
                , methodEnum.getId()
                , "檢查可結算契約"
                , BigDecimal.ZERO);
        settlementCalService.saveCalculation(sc, appIdList, reCalReason);
        simulationSettlementUtilsService.sendToFront(userId, sc, caller);
        return sc.getSettlementId();
    }

    protected void updateMeterComputable(Date startDate, Date endDate, Long settlementId) throws Exception{
        long aDayMillis = TimeUnit.DAYS.toMillis(1);
        for(long datetime = startDate.getTime(); datetime <= endDate.getTime(); datetime += aDayMillis) {
            Date curDate = new Date(datetime);
            updateMeterComputable(curDate, settlementId, METER_CHANNEL_GENERATOR);
            updateMeterComputable(curDate, settlementId, METER_CHANNEL_LOAD);
        }
        tempService.saveDateApplicationComputable(startDate, endDate, settlementId);
    }

    private void updateMeterComputable(Date date, Long settlementId, MeterChannelEnum meterChannel){

        List<String> meterList = tempService.getDateMeterIdBySettlementId(date, settlementId, meterChannel);
        if(CollectionUtils.isNotEmpty(meterList)){
            int start = 0;
            int end = 0;
            while(start < meterList.size()){
                end += Math.min(PROCESS_COUNT_PER_TIME, meterList.size() - start);
                List<String> meterSubList = meterList.subList(start, end);
                List<Map<String, Object>> custMeterList = amiService.getComputableMeter(date, meterChannel, meterSubList);

                if(CollectionUtils.isNotEmpty(custMeterList)) {
                    List<String> meterIdList = new ArrayList<>();
                    List<String> custIdList = new ArrayList<>();

                    for (Map<String, Object> custMeterMap : custMeterList) {
                        custIdList.add((String)custMeterMap.get("custId"));
                        meterIdList.add((String)custMeterMap.get("meterId"));
                    }

                    if (CollectionUtils.isNotEmpty(meterIdList)) {
                        tempService.updateDateMeterComputable(date, settlementId, meterChannel, custIdList, meterIdList, true);
                    }
                }
                start = end;
            }
        }
    }

    public List<Long> findIncomputableApplication(SimulationSettlementCalculation sc, List<Long> appIdList, Integer userId, SettlementCalculationInterface caller){
        simulationSettlementUtilsService.sendToFront(userId, sc.getSettlementId(), new BigDecimal("50"), caller);

        List<Long> incmpAppIdList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(appIdList)){
            List<SimulationSettlementCalculationFailureRecord> failureRecordList = new ArrayList<>();
            int start = 0;
            int end = 0;
            while(start < appIdList.size()){
                end += Math.min(PROCESS_COUNT_PER_TIME, appIdList.size() - start);
                List<Long> appSubList = appIdList.subList(start, end);

                Map<Long, FailGeneratorVo> failGeneratorVoMap = findIncomputableApplicationGenerator(sc.getSettlementId(), appSubList);
                Map<Long, FailLoadVo> failLoadVoMap = findIncomputableApplicationLoad(sc.getSettlementId(), appSubList);
                if(MapUtils.isNotEmpty(failGeneratorVoMap) || MapUtils.isNotEmpty(failLoadVoMap)) {
                    Map<Long, Long> appIdRecordIdMap = settlementCalService.getApplicationIdAndRecordId(sc.getSettlementId(), appIdList);

                    if (MapUtils.isNotEmpty(failGeneratorVoMap)) {
                        for (Map.Entry<Long, FailGeneratorVo> failGeneratorVoEntry : failGeneratorVoMap.entrySet()) {
                            FailGeneratorVo failGeneratorVo = failGeneratorVoEntry.getValue();
                            if (!incmpAppIdList.contains(failGeneratorVo.getAppId())) {
                                incmpAppIdList.add(failGeneratorVo.getAppId());
                            }
                            String failReason = failGeneratorVo.isPmi() ? SETTLEMENT_FAILURE_PW_RATIO.getDescription() : "";
                            failReason = failGeneratorVo.isCapacity() ? failReason.concat(SETTLEMENT_FAILURE_CAPACITY.getDescription()) : failReason;
                            failReason = StringUtils.hasLength(failGeneratorVo.getDate()) ? failReason.concat(String.format(SETTLEMENT_FAILURE_GENERATOR_KW.getDescription(), failGeneratorVo.getDate())) : failReason;

                            failureRecordList.add(SimulationSettlementCalculationFailureRecord.builder()
                                    .generatorId(failGeneratorVoEntry.getKey())
                                    .settlementRecordId(appIdRecordIdMap.get(failGeneratorVo.getAppId()))
                                    .reasonOfFailure(failReason)
                                    .build());
                        }
                    }

                    if (MapUtils.isNotEmpty(failLoadVoMap)) {
                        for (Map.Entry<Long, FailLoadVo> failLoadVoEntry : failLoadVoMap.entrySet()) {
                            FailLoadVo failLoadVo = failLoadVoEntry.getValue();
                            if (!incmpAppIdList.contains(failLoadVoEntry.getValue().getAppId())) {
                                incmpAppIdList.add(failLoadVoEntry.getValue().getAppId());
                            }
                            String failReason = failLoadVo.isMonthlyCap() ? SETTLEMENT_FAILURE_ANNUAL_CAPACITY.getDescription() : "";
                            failReason = failLoadVo.isAnnualCap() ? failReason.concat(SETTLEMENT_FAILURE_MONTH_CAPACITY.getDescription()) : failReason;
                            failReason = StringUtils.hasLength(failLoadVo.getDate()) ? failReason.concat(String.format(SETTLEMENT_FAILURE_LOAD_KW.getDescription(), failLoadVo.getDate())) : failReason;

                            failureRecordList.add(SimulationSettlementCalculationFailureRecord.builder()
                                    .loadId(failLoadVoEntry.getKey())
                                    .settlementRecordId(appIdRecordIdMap.get(failLoadVo.getAppId()))
                                    .reasonOfFailure(failReason)
                                    .build());
                        }
                    }
                }
                start = end;
            }
            if(CollectionUtils.isNotEmpty(failureRecordList)){
                settlementCalService.saveCalculationFailureRecord(failureRecordList);
                List<Long> recordIdList = failureRecordList.stream().map(SimulationSettlementCalculationFailureRecord::getSettlementRecordId).distinct().toList();
                settlementCalService.updateRecord(recordIdList, false, "契約內容有缺");
            }
        }
        return incmpAppIdList.stream().sorted().toList();
    }

    private Map<Long, FailGeneratorVo> findIncomputableApplicationGenerator(Long settlementId, List<Long> appIdList){
        Map<Long, FailGeneratorVo> idFailVoMap = new HashMap<>();
        //app generator id/ app id
        Map<Long, Long> pmiGenAppMap = contractVer2Service.getApplicationGeneratorIdByAppIdInAndPmiIsNull(appIdList);
        //app generator id/ app id
        Map<Long, Long> capGenAppMap = contractVer2Service.getApplicationGeneratorIdByAppIdInAndCapacityIsNull(appIdList);

        List<Map<String, Object>> idValueMapList = tempService.getIncomputableApplicationGeneratorIdAndDate(settlementId, appIdList);

        if(MapUtils.isNotEmpty(pmiGenAppMap)){
            for(Map.Entry<Long, Long> pmiGenApp : pmiGenAppMap.entrySet()){
                idFailVoMap.put(pmiGenApp.getKey(), FailGeneratorVo.builder()
                        .appId(pmiGenApp.getValue())
                        .isPmi(true)
                        .build());
            }
        }

        if(MapUtils.isNotEmpty(capGenAppMap)){
            for(Map.Entry<Long, Long> capGenApp : capGenAppMap.entrySet()){
                if(!idFailVoMap.containsKey(capGenApp.getKey())){
                    idFailVoMap.put(capGenApp.getKey(), FailGeneratorVo.builder()
                            .appId(capGenApp.getValue())
                            .isCapacity(true)
                            .build());
                }else{
                    idFailVoMap.get(capGenApp.getKey()).setCapacity(true);
                }
            }
        }

        if(CollectionUtils.isNotEmpty(idValueMapList)){
            for(Map<String, Object> idValueMap : idValueMapList) {
                Long appGenId = (Long)idValueMap.get("APPLICATION_GENERATOR_ID");
                if(!idFailVoMap.containsKey(appGenId)) {
                    idFailVoMap.put(appGenId, FailGeneratorVo.builder()
                            .appId((Long)idValueMap.get("APPLICATION_ID"))
                            .build());
                }
                FailGeneratorVo failGenVo = idFailVoMap.get(appGenId);
                String strDate = (String)idValueMap.get("STRING_DATE");
                if(StringUtils.hasLength(failGenVo.getDate())){
                    failGenVo.setDate(failGenVo.getDate().concat("、"));
                }
                failGenVo.setDate(failGenVo.getDate().concat(strDate));
            }
        }
        return idFailVoMap;
    }

    private Map<Long, FailLoadVo> findIncomputableApplicationLoad(Long settlementId, List<Long> appIdList){
        List<ApplicationLoad> appLoadList = contractVer2Service.getApplicationLoadByAppIdInAndContractCapIsNull(appIdList);
        List<Map<String, Object>> dateIdValueMapList = tempService.getIncomputableApplicationLoadIdAndDate(settlementId, appIdList);
        Map<Long, FailLoadVo> idFailVoMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(appLoadList)){
            for(ApplicationLoad appLoad : appLoadList){
                idFailVoMap.put(appLoad.getId(), FailLoadVo.builder()
                        .appId(appLoad.getApplicationId())
                        .isMonthlyCap(null == appLoad.getMonthlyContractCap())
                        .isAnnualCap(null == appLoad.getAnnualContractCap())
                        .build());
            }
        }

        if(CollectionUtils.isNotEmpty(dateIdValueMapList)){
            for(Map<String, Object> idValueMap : dateIdValueMapList) {
                Long appLoadId = (Long)idValueMap.get("APPLICATION_LOAD_ID");
                if(!idFailVoMap.containsKey(appLoadId)) {
                    idFailVoMap.put(appLoadId, FailLoadVo.builder()
                            .appId((Long)idValueMap.get("APPLICATION_ID"))
                            .build());
                }
                FailLoadVo failLoadVo = idFailVoMap.get(appLoadId);
                String strDate = (String)idValueMap.get("STRING_DATE");
                if(StringUtils.hasLength(failLoadVo.getDate())) {
                    failLoadVo.setDate(failLoadVo.getDate().concat("、"));
                }
                failLoadVo.setDate(failLoadVo.getDate().concat(strDate));
            }
        }
        return idFailVoMap;
    }

    public void calculate(TimeIntervalVo timeIntervalVo
            , SimulationSettlementCalculation sc
            , List<Long> incmpAppIdList
            , Integer userId
            , SettlementCalculationInterface caller) throws Exception{
        Long settlementId = sc.getSettlementId();
        long aDayMillis = TimeUnit.DAYS.toMillis(1);

        Map<ApplicationTypeEnum,Map<Long, BigDecimal>> typeAnnualYni = settlementNiService.getAnnualYni(timeIntervalVo, settlementId, incmpAppIdList);
        List<Long> computableAppInMonth = tempService.getComputableApplicationIdByDateIntervalAndTrialDate(timeIntervalVo, settlementId);
        //remove incomputable AppId
//        computableAppInMonth.removeAll(incmpAppIdList);
        Map<Long, Map<Long, Boolean>> dateAppResultMap = new HashMap<>();

        for(long datetime = timeIntervalVo.getStartTime().getTimeInMillis(); datetime <= timeIntervalVo.getEndTime().getTimeInMillis(); datetime += aDayMillis) {
            startDailyCalculation(sc, datetime, userId, caller);
            long executeTime = System.currentTimeMillis();

            Date curDate = new Date(datetime);
            log.info(curDate);
            dateAppResultMap.put(datetime, new HashMap<>());

            computableAppInMonth = computableApplication(curDate, curDate, computableAppInMonth, incmpAppIdList);
            //here daily calculation
            Map<Long, Boolean> appResultMap = dailyService.calculate(curDate
                    , settlementId
                    , typeAnnualYni
                    , computableAppInMonth
                    , userId
                    , caller);
            if(MapUtils.isNotEmpty(appResultMap)) {
                dateAppResultMap.get(datetime).putAll(appResultMap);
                trialDirectService.saveDailyRecordByDateIntervalAndSettlementId(curDate, timeIntervalVo.getEndTime().getTime(), settlementId);
                trialPowerWheelingService.saveDailyRecordByDateIntervalAndSettlementId(curDate, timeIntervalVo.getEndTime().getTime(), settlementId);
            }else{
                log.info("沒有可計算契約");
                simulationSettlementUtilsService.sendToFront(userId, settlementId, datetime, "沒有可計算契約", new BigDecimal("100"), caller);
            }
            log.info("executeTime = {}", System.currentTimeMillis() - executeTime);
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        String serviceDate = simpleDateFormat.format(timeIntervalVo.getStartTime().getTime());
        if(CollectionUtils.isNotEmpty(dateAppResultMap.values())) {
            simulationSettlementUtilsService.sendToFront(userId, settlementId, "日結完成", new BigDecimal("100"), caller);
            simulationSettlementUtilsService.sendToFront(userId, settlementId, "月結開始", new BigDecimal("0"), caller);

            monthlyService.calculate(timeIntervalVo
                    , settlementId
                    , typeAnnualYni
                    , userId
                    , caller);

            saveTrialSettlement(timeIntervalVo.getStartTime().getTime(), sc, computableAppInMonth);
            saveMeterChangeRecord(settlementId);
            simulationSettlementUtilsService.sendToFront(userId, settlementId, true, serviceDate + " 結算完成", new BigDecimal("100"), new Date(), caller);
        }else{
            log.info("本月，沒有可計算契約");
            simulationSettlementUtilsService.sendToFront(userId, settlementId, false, serviceDate + " 沒有可計算契約", new BigDecimal("100"), new Date(), caller);
        }
        updateSettlementCalculationResult(settlementId, dateAppResultMap);
    }

    private void startDailyCalculation(SimulationSettlementCalculation sc, long datetime, Integer userId, SettlementCalculationInterface caller){
        simulationSettlementUtilsService.sendToFront(userId, sc.getSettlementId(), new BigDecimal("100"), caller);
        simulationSettlementUtilsService.sendToFront(userId, sc.getSettlementId(), datetime, "日結算計算中" , BigDecimal.ZERO, caller);
    }

    private List<Long> computableApplication(Date startDate, Date endDate, List<Long> idList, List<Long> incomputableIdList){
        if(CollectionUtils.isNotEmpty(incomputableIdList)){
            List<Long> computableIdList = new ArrayList<>(idList);
            List<Long> relationIdList = contractVer2Service.getSelfAndRelationApplicationId(startDate, endDate, incomputableIdList);
            computableIdList.removeAll(relationIdList);
            return computableIdList;
        }
        return idList;
    }

    public void saveTrialSettlement(Date serviceDate, SimulationSettlementCalculation sc, List<Long> appIdList){

        if(sc.getCalculationMethod().equals(SETTLEMENT_METHOD_TRIAL_CALCULATION.getId())
                || sc.getCalculationMethod().equals(SETTLEMENT_METHOD_INITIAL_TRIAL_CALCULATION.getId())) {
            Long settlementId = sc.getSettlementId();
            Map<Long, List<Long>> settlementAppIdMap = settlementCalService.getLatestIdCalculationInOrder(serviceDate, appIdList, Arrays.asList(
                            SETTLEMENT_METHOD_ALL_CALCULATION.getId()
                            , SETTLEMENT_METHOD_ACCEPT_TRIAL_CALCULATION.getId())
                    , settlementId);

            if (MapUtils.isNotEmpty(settlementAppIdMap)) {
                List<Long> remainAppIdList = new ArrayList<>(appIdList);
                for (Map.Entry<Long, List<Long>> settlementAppId : settlementAppIdMap.entrySet()) {
                    trialSettlementRepository.saveBySettlementIdAndAppIdIn(settlementAppId.getKey(), settlementId, settlementAppId.getValue());
                    remainAppIdList.removeAll(settlementAppId.getValue());
                }

                if (CollectionUtils.isNotEmpty(remainAppIdList)) {
                    trialSettlementRepository.saveBySettlementIdAndAppIdIn(settlementId, remainAppIdList);
                }
            } else {
                trialSettlementRepository.saveBySettlementIdAndAppIdIn(settlementId, appIdList);
            }
        }
    }

    public void saveMeterChangeRecord(Long settlementId){
        try{
            meterChangeRecordRepository.saveBySettlementId(settlementId);
            List<SimulationMeterChangeRecord> genMeterUseFromList = meterChangeRecordRepository.findBySettlementIdAndApplicationGeneratorIdIsNotNullAndUseFrom(settlementId);
            List<SimulationMeterChangeRecord> genMeterUseToList = meterChangeRecordRepository.findBySettlementIdAndApplicationGeneratorIdIsNotNullAndUseTo(settlementId);
            List<SimulationMeterChangeRecord> loadMeterUseFromList = meterChangeRecordRepository.findBySettlementIdAndApplicationLoadIdIsNotNullAndUseFrom(settlementId);
            List<SimulationMeterChangeRecord> loadsMeterUseToList = meterChangeRecordRepository.findBySettlementIdAndApplicationLoadIdIsNotNullAndUseTo(settlementId);

            List<SimulationMeterChangeRecord> savedMeterRecordList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(genMeterUseFromList)) {
                savedMeterRecordList.addAll(updateMeterChangeRecordUseFromTo(settlementId, METER_CHANNEL_GENERATOR, genMeterUseFromList, genMeterUseToList));
            }
            if(CollectionUtils.isNotEmpty(loadMeterUseFromList)){
                savedMeterRecordList.addAll(updateMeterChangeRecordUseFromTo(settlementId, METER_CHANNEL_LOAD, loadMeterUseFromList, loadsMeterUseToList));
            }
            if(CollectionUtils.isNotEmpty(savedMeterRecordList)){
                meterChangeRecordRepository.saveAll(savedMeterRecordList);
            }
        }catch (Exception exception){
            log.error(exception.getMessage());
        }
    }

    private List<SimulationMeterChangeRecord> updateMeterChangeRecordUseFromTo(Long settlementId, MeterChannelEnum channelEnum, List<SimulationMeterChangeRecord> meterUseFromList, List<SimulationMeterChangeRecord> meterUseToList){
        List<SimulationMeterChangeRecord> savedMeterList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(meterUseFromList)) {
            Map<Date, Map<String, SimulationMeterChangeRecord>> useFromMeterMap = meterUseFromList.stream().collect(groupingBy(SimulationMeterChangeRecord::getUseFrom
                    , Collectors.toMap(SimulationMeterChangeRecord::getMeterNo, Function.identity())));
            //Application generator ID, MeterChangeRecord
            Map<Long, SimulationMeterChangeRecord>  useToRecordMap;
            if(channelEnum.equals(METER_CHANNEL_GENERATOR)) {
                useToRecordMap = meterUseToList.stream().collect(toMap(SimulationMeterChangeRecord::getGeneratorId, Function.identity()));
            }else {
                useToRecordMap = meterUseToList.stream().collect(toMap(SimulationMeterChangeRecord::getLoadId, Function.identity()));
            }

            for (Map.Entry<Date, Map<String, SimulationMeterChangeRecord>> useFromMeter : useFromMeterMap.entrySet()) {
                TimeIntervalVo dayTimeIntervalVo = DateUtils.getDayInterval(useFromMeter.getKey().getTime());
                Map<String, Date> meterDateMap = amiService.getTop1MeterIdAndFirstDateAndKwAndRatioGreaterThanZero(dayTimeIntervalVo.getStartTime().getTime()
                        , dayTimeIntervalVo.getEndTime().getTime()
                        , settlementId
                        , new ArrayList<>(useFromMeter.getValue().keySet())
                        , channelEnum);

                if(MapUtils.isNotEmpty(meterDateMap)) {
                    for (Map.Entry<String, Date> meterDate : meterDateMap.entrySet()) {
                        SimulationMeterChangeRecord meterChangeRecord = useFromMeter.getValue().get(meterDate.getKey());
                        meterChangeRecord.setUseFrom(meterDate.getValue());
                        savedMeterList.add(meterChangeRecord);

                        SimulationMeterChangeRecord previewRecord;
                        if(channelEnum.equals(METER_CHANNEL_GENERATOR)) {
                            previewRecord = useToRecordMap.get(meterChangeRecord.getGeneratorId());
                        }else{
                            previewRecord = useToRecordMap.get(meterChangeRecord.getLoadId());
                        }
                        previewRecord.setUseTo(meterChangeRecord.getUseFrom());
                        savedMeterList.add(previewRecord);
                    }
                }
            }
        }
        return savedMeterList;
    }

    private void updateSettlementCalculationResult(Long settlementId, Map<Long, Map<Long, Boolean>> dateAppResultMap){
        //update result to true
        Set<Long> failAppIdList = new HashSet<>();
        for (Map<Long, Boolean> valueMap : dateAppResultMap.values()) {
            Set<Long> failIdList = valueMap.entrySet().stream().filter(entry->entry.getValue().equals(false)).map(Map.Entry::getKey).collect(Collectors.toSet());
            failAppIdList.addAll(failIdList);
        }
        if(CollectionUtils.isNotEmpty(failAppIdList)){
            settlementCalService.updateCalculationAndRecordResult(settlementId, new ArrayList<>(failAppIdList), false);
        }
        settlementCalService.updateResultBySettlementIdAndExecutionResultIsNull(settlementId, true);
        settlementCalService.updateCalculationResult(settlementId);
    }

    private void closeSettlementCalculation(Long settlementId, SettlementResultVo settlementResultVo){
        SimulationSettlementCalculation sc = settlementCalService.getCalculationById(settlementId);
        SettlementMethodEnum curMethodEnum = SettlementMethodEnum.get(sc.getCalculationMethod());

        sc.setCalculationMethod(convertToOriginalSettlementCalculation(curMethodEnum).getId());
        sc.setExecutionResult(settlementResultVo.isPass());
        if(!settlementResultVo.isPass()){
            sc.setExecutionLog(settlementUtilsService.translateExceptionToReadableSentence(curMethodEnum, settlementResultVo.getErrorMsg()));
        }else{
            sc.setExecutionLog("結算完成");
        }
        sc.setExecutionPercentage(new BigDecimal(100));
        sc.setExecutionEnd(new Date());
        settlementCalService.saveCalculation(sc);
    }

    private SettlementMethodEnum convertToOriginalSettlementCalculation(SettlementMethodEnum curMethodEnum){
        if(curMethodEnum.equals(SETTLEMENT_METHOD_INITIAL_ALL_CALCULATION)){
            curMethodEnum = SETTLEMENT_METHOD_ALL_CALCULATION;
        }else if(curMethodEnum.equals(SETTLEMENT_METHOD_INITIAL_TRIAL_CALCULATION)){
            curMethodEnum = SETTLEMENT_METHOD_TRIAL_CALCULATION;
        }
        return curMethodEnum;
    }

}
