package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum;
import tw.com.taipower.pwoms.services.settlement.SettlementContractVer2Service;
import tw.com.taipower.pwoms.services.settlement.SettlementTempService;
import tw.com.taipower.pwoms.services.settlement.SettlementTrialDirectService;
import tw.com.taipower.pwoms.services.settlement.SettlementTrialPowerWheelingService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.settlement.LoadAppMeterVo;
import tw.com.taipower.pwoms.services.vo.settlement.MonthlyCapAndNi;
import tw.com.taipower.pwoms.services.vo.utils.TimeIntervalVo;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toMap;
import static tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum.APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING;
import static tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum.APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE;

@Log4j2
@Service
public class SimulationSettlementNiService {

    @Autowired
    private SimulationSettlementTempService tempService;

    @Autowired
    private SimulationSettlementContractVer2Service ver2Service;

    @Autowired
    private SimulationSettlementTrialDirectService directService;

    @Autowired
    private SimulationSettlementTrialPowerWheelingService powerWheelingService;

    public Map<ApplicationTypeEnum, Map<Long, BigDecimal>> getAnnualYni(TimeIntervalVo timeIntervalVo, Long settlementId, List<Long> incmpAppIdList){

        Map<Long, LoadAppMeterVo> loadMap = tempService.findComputableApplicationByDateIntervalAndSettlementId(timeIntervalVo.getStartTime().getTime()
                , timeIntervalVo.getEndTime().getTime(), settlementId);

        Map<ApplicationTypeEnum, Map<Long, BigDecimal>> typeAnnualYniMap = new HashMap<>();

        if(MapUtils.isNotEmpty(loadMap)){
            //filter out incmputable application
            loadMap = loadMap.entrySet().stream().filter(entry->!incmpAppIdList.contains(entry.getValue().getApplicationId())).collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            TimeIntervalVo annualTimeIntervalVo = getYearToEnd(timeIntervalVo.getStartTime());
            List<Long> resetAppIdList = ver2Service.getResetAnnualNiApplicationId(annualTimeIntervalVo);
            //direct
            Map<Long, LoadAppMeterVo> directLoadMap = loadMap.entrySet().stream().filter(
                    entry -> APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE.getId().equals(entry.getValue().getType())
                            || ((APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING.getId().equals(entry.getValue().getType()))
                            && entry.getValue().getIsDirect())).collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
            typeAnnualYniMap = new HashMap<>(directService.getAnnualYni(timeIntervalVo, annualTimeIntervalVo, directLoadMap, resetAppIdList));
            //other
            Map<Long, LoadAppMeterVo> directPwLoadMap = loadMap.entrySet().stream().filter(entry->!directLoadMap.containsKey(entry.getKey()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            Map<ApplicationTypeEnum, Map<Long, BigDecimal>> pwTypeAnnualYniMap = powerWheelingService.getAnnualYni(timeIntervalVo, annualTimeIntervalVo, directPwLoadMap, resetAppIdList);

            for(Map.Entry<ApplicationTypeEnum, Map<Long, BigDecimal>> pwTypeAnnualYni : pwTypeAnnualYniMap.entrySet()){
                if(typeAnnualYniMap.containsKey(pwTypeAnnualYni.getKey())){
                    typeAnnualYniMap.get(pwTypeAnnualYni.getKey()).putAll(pwTypeAnnualYni.getValue());
                }else{
                    typeAnnualYniMap.put(pwTypeAnnualYni.getKey(), pwTypeAnnualYni.getValue());
                }
            }
        }else{
//            for (ApplicationTypeEnum applicationTypeEnum : ApplicationTypeEnum.values()) {
//                typeAnnualYniMap.put(applicationTypeEnum, new HashMap<>());
//            }
        }
        return typeAnnualYniMap;
    }

    protected TimeIntervalVo getYearToEnd(Calendar calEnd){
        Calendar startCal = Calendar.getInstance();
        startCal.setTimeInMillis(calEnd.getTimeInMillis());
        startCal.set(Calendar.MONTH, Calendar.JANUARY);
        startCal.set(Calendar.DAY_OF_MONTH, 1);

        Calendar endCal = Calendar.getInstance();
        endCal.set(Calendar.YEAR, calEnd.get(Calendar.YEAR));
        endCal.set(Calendar.MONTH, calEnd.get(Calendar.MONTH));
        endCal.set(Calendar.DAY_OF_MONTH, 1);
        endCal.add(Calendar.DAY_OF_MONTH,  -1);

        return TimeIntervalVo.builder().startTime(startCal).endTime(DateUtils.getLastQuarter(endCal.getTimeInMillis())).build();
    }

    public Map<ApplicationTypeEnum, Map<Long, BigDecimal>> getMonthlyNi(TimeIntervalVo timeIntervalVo, TimeIntervalVo niTimeIntervalVo, Long settlementId, Map<ApplicationTypeEnum, Map<Long, BigDecimal>> typeAnnualYniMap){

//        Map<ApplicationTypeEnum,Map<Long, BigDecimal>> initialMonthlyNiMap = getMonthlyNi(timeIntervalVo, settlementId);
        Map<ApplicationTypeEnum, Map<Long, MonthlyCapAndNi>> initialMonthlyNiMap = getMonthlyNi(timeIntervalVo, niTimeIntervalVo, settlementId);

        Map<ApplicationTypeEnum,Map<Long, BigDecimal>> monthlyNiMap = new HashMap<>();

        for(Map.Entry<ApplicationTypeEnum, Map<Long, MonthlyCapAndNi>> initialMonthlyNi : initialMonthlyNiMap.entrySet()){
            //年剩餘量
            Map<Long, BigDecimal> annualYniMap = typeAnnualYniMap.get(initialMonthlyNi.getKey());
            Map<Long, BigDecimal> idNiMap = new HashMap<>();
            monthlyNiMap.put(initialMonthlyNi.getKey(), idNiMap);

            for(Map.Entry<Long, MonthlyCapAndNi> initialIdNi : initialMonthlyNi.getValue().entrySet()){
                Long appLoadId = initialIdNi.getKey();
                //月剩餘量
                BigDecimal monthlyNi = initialIdNi.getValue().getNi();
                BigDecimal annualNi = annualYniMap.getOrDefault(appLoadId, BigDecimal.ZERO);
                //月剩餘大於年剩餘 月剩餘 = 年剩餘, 否就是月剩餘
                if(0 > annualNi.compareTo(monthlyNi)){
                    //目前媒合量
                    BigDecimal monthlyMatchedCn = initialIdNi.getValue().getContractCap().subtract(initialIdNi.getValue().getNi());
                    //月剩餘量 = 年剩餘 - 目前媒合量
                    idNiMap.put(appLoadId, annualNi.subtract(monthlyMatchedCn));
                }else {
                    //月剩餘量
                    idNiMap.put(appLoadId, monthlyNi.min(0 < monthlyNi.compareTo(BigDecimal.ZERO) ? monthlyNi : BigDecimal.ZERO));
                }
            }
        }
        return monthlyNiMap;
    }

    private Map<ApplicationTypeEnum, Map<Long, MonthlyCapAndNi>> getMonthlyNi(TimeIntervalVo timeIntervalVo, TimeIntervalVo niTimeIntervalVo, Long settlementId){

        Map<Long, LoadAppMeterVo> loadMap = tempService.findComputableApplicationByDateIntervalAndSettlementId(timeIntervalVo.getStartTime().getTime()
                , timeIntervalVo.getEndTime().getTime(), settlementId);

//        Map<ApplicationTypeEnum, Map<Long, BigDecimal>> typeMonthlyYniMap = new HashMap<>();
        Map<ApplicationTypeEnum, Map<Long, MonthlyCapAndNi>> typeMonthlyCapNiMap = new HashMap<>();

        if(MapUtils.isNotEmpty(loadMap)){
            //direct
            Map<Long, LoadAppMeterVo> directLoadMap = loadMap.entrySet().stream().filter(
                    entry -> APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE.getId().equals(entry.getValue().getType())
                            || ((APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING.getId().equals(entry.getValue().getType()))
                            && entry.getValue().getIsDirect())).collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
            Map<ApplicationTypeEnum, Map<Long, BigDecimal>> typeMonthlyYniMap = new HashMap<>(directService.getMonthlyNi(niTimeIntervalVo, settlementId, directLoadMap));

            if(MapUtils.isNotEmpty(typeMonthlyYniMap)){
                for(Map.Entry<ApplicationTypeEnum, Map<Long, BigDecimal>> typeMonthlyYni : typeMonthlyYniMap.entrySet()){
                    typeMonthlyCapNiMap.put(typeMonthlyYni.getKey(), new HashMap<>());
                    for(Map.Entry<Long, BigDecimal> monthlyYni : typeMonthlyYni.getValue().entrySet()){
                        typeMonthlyCapNiMap.get(typeMonthlyYni.getKey()).put(monthlyYni.getKey(), MonthlyCapAndNi.builder()
                                .contractCap(directLoadMap.get(monthlyYni.getKey()).getMonthlyContractCap())
                                .ni(monthlyYni.getValue()).build());
                    }
                }
            }

            //other
            Map<Long, LoadAppMeterVo> pwLoadMap = loadMap.entrySet().stream().filter(entry->!directLoadMap.containsKey(entry.getKey()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
            Map<ApplicationTypeEnum, Map<Long, BigDecimal>> pwTypeMonthlyNiMap = powerWheelingService.getMonthlyNi(niTimeIntervalVo, settlementId, pwLoadMap);

            if(MapUtils.isNotEmpty(pwTypeMonthlyNiMap)){
                for(Map.Entry<ApplicationTypeEnum, Map<Long, BigDecimal>> typeMonthlyYni : pwTypeMonthlyNiMap.entrySet()){
                    typeMonthlyCapNiMap.put(typeMonthlyYni.getKey(), new HashMap<>());
                    for(Map.Entry<Long, BigDecimal> monthlyYni : typeMonthlyYni.getValue().entrySet()){
                        typeMonthlyCapNiMap.get(typeMonthlyYni.getKey()).put(monthlyYni.getKey(), MonthlyCapAndNi.builder()
                                .contractCap(pwLoadMap.get(monthlyYni.getKey()).getMonthlyContractCap())
                                .ni(monthlyYni.getValue()).build());
                    }
                }
            }
        }
        return typeMonthlyCapNiMap;
    }

}
