package tw.com.taipower.pwoms.services.applicant;

import lombok.extern.log4j.Log4j2;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.data.entity.pwoms.ApplicantEntityContact;
import tw.com.taipower.data.entity.pwoms.ApplicantEntityDocument;
import tw.com.taipower.data.repository.pwoms.ApplicantEntityContactRepository;
import tw.com.taipower.data.repository.pwoms.ApplicantEntityDocumentRepository;
import tw.com.taipower.data.repository.pwoms.ApplicantEntityRepository;
import tw.com.taipower.pwoms.logger.CustomLog;
import tw.com.taipower.pwoms.services.BaseService;
import tw.com.taipower.pwoms.services.applicant.vo.ExternalApplicantEntityVO;
import tw.com.taipower.pwoms.services.enumclass.ErrorCode;
import tw.com.taipower.pwoms.services.tpc.SelectorITService;
import tw.com.taipower.pwoms.services.utils.FileTypeValidator;
import tw.com.taipower.pwoms.services.utils.VoUtils;
import tw.com.taipower.pwoms.services.vo.generated.ApplicantEntityContactVO;
import tw.com.taipower.pwoms.services.vo.generated.ApplicantEntityDocumentVO;

/**
 * 外部申請者服務
 * 主要處理申請者資料的維護，包含聯絡人資料的新增修改
 * 對應14.6的功能
 */
@CustomLog
@Service
public class ExternalApplicantService extends BaseService {

    @Autowired
    private ApplicantEntityRepository applicantEntityRepository; // 申請者資料存取

    @Autowired
    private ApplicantEntityContactRepository applicantEntityContactRepository; // 申請者聯絡人資料存取

    @Autowired
    private ApplicantEntityDocumentRepository applicantEntityDocumentRepository; // 申請者文件資料存取

    @Autowired
    private SelectorITService selectorITService;
    /**
     * 透過使用者帳號ID取得申請者資料
     * 
     * @param accountId
     *            使用者帳號ID
     * @return 申請者資料VO
     */
    public ExternalApplicantEntityVO findVOById(Long accountId) {
        var applicant = findByAccountId(accountId);
        return VoUtils.toVO(applicant, ExternalApplicantEntityVO.class);
    }

    /**
     * 根據申請人查詢所有相關聯絡人
     *
     * @param accountId
     *            使用者帳號ID
     * @return 聯絡人資料清單
     */
    public List<ApplicantEntityContactVO> findAllContactsByAccountId(Long accountId) {
        var applicant = findByAccountId(accountId);
        var list = applicantEntityContactRepository.findAllByApplicantEntityId(applicant.getId());
        return list.stream().map(a -> VoUtils.toVO(a, ApplicantEntityContactVO.class))
                .collect(Collectors.toList());
    }

    /**
     * 儲存申請人資料
     *
     * @param vo
     *            申請者資料VO
     * @param accountId
     *            使用者帳號ID
     */
    public void save(ExternalApplicantEntityVO vo, Long accountId) {
        var applicant = findByAccountId(accountId);
        VoUtils.mergeObjectWithNullIgnore(vo, applicant);
        applicant.setModifiedBy(accountId);
        applicantEntityRepository.save(applicant);
    }

    /**
     * 新增申請人自己的聯絡人
     *
     * @param vo
     *            聯絡人資料VO
     * @param accountId
     *            使用者帳號ID
     */
    public void addApplicantContact(ApplicantEntityContactVO vo, Long accountId) {
        var applicant = findByAccountId(accountId);
        var entity = VoUtils.toEntity(vo, ApplicantEntityContact.class);
        entity.setApplicantEntityId(applicant.getId());
        entity.setModifiedBy(accountId);
        applicantEntityContactRepository.save(entity);
    }

    /**
     * 修改申請者自己的聯絡人
     *
     * @param vo
     *            聯絡人資料VO
     * @param contactId
     *            聯絡人ID
     * @param accountId
     *            使用者帳號ID
     * @throws RuntimeException
     *             當找不到聯絡人或聯絡人不屬於該申請者時拋出例外
     */
    public void updateApplicantContact(ApplicantEntityContactVO vo, Long contactId, Long accountId) {
        var applicant = findByAccountId(accountId);
        var opt = applicantEntityContactRepository.findById(contactId);
        if (!opt.isPresent()) {
            throwException(ErrorCode.APPLICANT_ENTITY_CONTACT_NOT_FOUND);
        }

        var model = opt.get();
        if (!model.getApplicantEntityId().equals(applicant.getId())) {
            throwException(ErrorCode.APPLICANT_ENTITY_CONTACT_NOT_FOUND);
        }
        VoUtils.mergeObjectWithNullIgnore(vo, model);
        model.setModifiedBy(accountId);
        applicantEntityContactRepository.save(model);
    }

    /**
     * 取得當前申請者已上傳之文件
     *
     * @param accountId
     * @return
     */
    public List<ApplicantEntityDocumentVO> findUploadedDocumentsByEntityId(Long accountId) {
        var applicant = findByAccountId(accountId);
        var list = applicantEntityDocumentRepository.findAllByApplicantEntityId(applicant.getId()).stream().map(
                a -> VoUtils.toVO(a, ApplicantEntityDocumentVO.class)).collect(Collectors.toList());
        list.forEach(a -> a.setContent(null));
        return list;
    }

    /**
     * 取得申請者單一對應文件
     *
     * @param accountId
     * @return
     */
    public ApplicantEntityDocumentVO getUploadedDocumentByEntityIdAndId(Long fileId, Long accountId) {
        var applicant = findByAccountId(accountId);
        var model = applicantEntityDocumentRepository.findById(fileId).get();
        if (!model.getApplicantEntityId().equals(applicant.getId())) {
            throwException(ErrorCode.APPLICANT_ENTITY_DOCUMENT_NOT_FOUND);
        }
        var result = VoUtils.toVO(model, ApplicantEntityDocumentVO.class);
        result.setContent(model.getContent());
        return result;
    }

    /**
     * 儲存檔案並且回報檔案ＩＤ
     * 
     * @param documentId
     *            文件ID
     * @param file
     *            檔案
     * @param unit
     *            單位
     * @param serialNumber
     *            序號
     * @param operationMode
     *            操作模式
     * @param validDate
     *            有效日期
     * @param comment
     *            備註
     * @param issueDate
     *            發行日期
     * @param accountId
     *            使用者帳號ID
     * @return 檔案ID
     * @throws IOException
     */
    public Long uploadDocumentByEntityIdAndDocumentId(Integer documentId, MultipartFile file,
            String unit, String serialNumber, String operationMode,
            Date validDate, String comment, Date issueDate,
            Long accountId) throws Exception {
        FileTypeValidator.checkValid(file);
        selectorITService.checkWithoutResult(file);

        var applicant = findByAccountId(accountId);
        checkFileUploadColumnValid(documentId, unit, serialNumber, operationMode, validDate, comment, issueDate);
        var model = ApplicantEntityDocument.builder().applicantEntityId(applicant.getId()).documentId(documentId)
                .build();
        var dbDocument = applicantEntityDocumentRepository.findOne(Example.of(model));
        if (dbDocument.isPresent()) {
            model = dbDocument.get();
            if (file != null && !file.isEmpty()) {
                model.setContent(file.getBytes());
                model.setExt(getFileExtension(file));
            }

            model.setModifiedBy(accountId);
            model.setUnit(unit);
            model.setSerialNumber(serialNumber);
            model.setOperationMode(operationMode);
            model.setValidDate(validDate);
            model.setComment(comment);
            var result = applicantEntityDocumentRepository.save(dbDocument.get());
            return result.getId();
        } else {
            model.setModifiedBy(accountId);
            if (file != null && !file.isEmpty()) {
                model.setContent(file.getBytes());
                model.setExt(getFileExtension(file));
            }
            model.setUnit(unit);
            model.setSerialNumber(serialNumber);
            model.setOperationMode(operationMode);
            model.setValidDate(validDate);
            model.setComment(comment);
            var result = applicantEntityDocumentRepository.save(model);
            return result.getId();
        }
    }
}
