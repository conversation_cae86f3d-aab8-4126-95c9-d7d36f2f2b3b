package tw.com.taipower.pwoms.services.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @class: TpcRemsConfig
 * @author: daniel
 * @version:
 * @since: 2024-06-06 12:10
 * @see:
 **/

@Data
@Configuration
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "tpc.rems")
public class TpcRemsConfig {
    String nbs1URL;
    String nbs2URL;
    String nbs3URL;
    String rnis1URL;
    String rnis2URL;
    String rnis3URL;

    String SFTPKnownHost;

    String rnisSFTPRemoteDir;
    String rnisSFTPHost;
    String rnisSFTPUsername;
    String rnisSFTPPassword;

    String nbsSFTPRemoteDir;
    String nbsSFTPHost;
    String nbsSFTPUsername;
    String nbsSFTPPassword;

    /**
     * 如果rnis有異動，要通知的角色
     */
    Integer rnisFTPNotifyRole;

    String bsmiSFTPRemoteDir;
    String bsmiSFTPHost;
    String bsmiSFTPUsername;
    String bsmiSFTPPassword;
    String bsmiSFTPUploadPrefix;

    String pdcSFTPHost;
    String pdcSFTPUsername;
    String pdcSFTPPassword;
    String pdcSFTPRemoteDirData;
    String pdcSFTPRemoteDirLostTable;
    String pdcSFTPRemoteDirKaoData;
    String pdcSFTPRemoteDirADData;
    String pdcSFTPRemoteDirVLValue;
    String pdcSFTPUploadPrefix;
}
