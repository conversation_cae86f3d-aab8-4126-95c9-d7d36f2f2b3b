package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.ami.SettlementAmiPwr15RecordColumn;
import tw.com.taipower.data.entity.ami.SettlementTempAmiPwr15Record;
import tw.com.taipower.data.entity.pwoms.SimulationTempTempApplicationGeneratorKw;
import tw.com.taipower.data.entity.pwoms.SimulationTempTempApplicationLoadKw;
import tw.com.taipower.data.entity.pwoms.TempTempApplicationGeneratorKw;
import tw.com.taipower.data.entity.pwoms.TempTempApplicationLoadKw;
import tw.com.taipower.pwoms.services.ami.AmiService;
import tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum;
import tw.com.taipower.pwoms.services.enumclass.MeterChannelEnum;
import tw.com.taipower.pwoms.services.enumclass.TimeSlotEnum;
import tw.com.taipower.pwoms.services.settlement.*;
import tw.com.taipower.pwoms.services.settlement.SettlementUtilsService;
import tw.com.taipower.pwoms.services.system.EnergyChargeService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.vo.settlement.GeneratorAppMeterVo;
import tw.com.taipower.pwoms.services.vo.settlement.LoadAppMeterVo;
import tw.com.taipower.pwoms.services.vo.utils.TimeIntervalVo;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static java.util.stream.Collectors.toMap;
import static tw.com.taipower.pwoms.services.constant.Constants.DATETIME_FORMAT_YYYYMMDD_WITH_DASH;
import static tw.com.taipower.pwoms.services.settlement.SettlementUtilsService.SETTLEMENT_ERROR_HEADER;

@Service
@Log4j2
public class SimulationSettlementPartialDailyService {

    @Autowired
    private SimulationSettlementTempService tempService;

    @Autowired
    private SimulationAmiService amiService;

    @Autowired
    private EnergyChargeService energyChargeService;

    @Autowired
    private SimulationSettlementNiService settlementNiService;

    @Autowired
    private SimulationSettlementContractVer2Service contractServiceVer2;

    @Autowired
    private SimulationSettlementTrialDirectService directService;

    @Autowired
    private SimulationSettlementTrialPowerWheelingService powerWheelingService;

    @Autowired
    private SimulationSettlementUtilsService settlementUtilsService;

    private static final String CONTRACT_STG_LV = "4";
    private static final String CONTRACT_STG_UH_UHV = "6";

    public Map<Long, Boolean> calculate(Date date
            , Long settlementId
            , Map<ApplicationTypeEnum, Map<Long, BigDecimal>> typeAnnualYni
            , List<Long> computableAppInMonth
            , Integer userId
            , SettlementCalculationInterface caller) throws Exception{
        Map<Long, Boolean> appResultMap = new HashMap<>();

        Map<Long, ApplicationTypeEnum> idTypeEnumMap = tempService.getComputableApplication(date, settlementId, computableAppInMonth);
//        Map<Long, ApplicationTypeEnum> idTypeEnumMap = new HashMap<>();
//        idTypeEnumMap.put(3L, ApplicationTypeEnum.APPLICATION_TYPE_POWER_WHEELING);
//        idTypeEnumMap.put(16L, APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION);
//        idTypeEnumMap.put(1L, APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE);

        TimeIntervalVo timeIntervalVo = DateUtils.getDayInterval(date.getTime());
        if(MapUtils.isNotEmpty(idTypeEnumMap)){
            long executeStart = System.currentTimeMillis();
//            if(amiService.saveAmiPwr15MdesToKwHistory(new Date(), timeIntervalVo.getStartTime().getTime(), timeIntervalVo.getEndTime().getTime(), settlementId)) {
            saveAllFromAmiPwr15Mdes(settlementId, date);
            log.info("ami use time {}", System.currentTimeMillis() - executeStart);
            //assemble temp_temp_application_generator_kw meter kw
            assembleTempApplicationGeneratorKw(date, settlementId, idTypeEnumMap); //temporary remove
            assembleTempApplicationLoadKw(date, settlementId, idTypeEnumMap); //temporary remove
            Map<ApplicationTypeEnum, Map<Long, BigDecimal>> typeMonthlyNi = settlementNiService.getMonthlyNi(timeIntervalVo, getMonthToEnd(timeIntervalVo.getEndTime()), settlementId, typeAnnualYni);

            Map<Long, List<Long>> idRelationIdMap = contractServiceVer2.getRelationApplicationId(date, settlementId);
            int curCalApCnt = 0;
            int totalCalApCnt = idTypeEnumMap.size();
            for (Map.Entry<Long, ApplicationTypeEnum> idTypeEnum : idTypeEnumMap.entrySet()) {
                curCalApCnt++;
                log.info("start with application Id = {}", idTypeEnum.getKey());
                boolean result = false;
                switch (idTypeEnum.getValue()) {
                    case APPLICATION_TYPE_POWER_WHEELING:
                    case APPLICATION_TYPE_POWER_WHEELING_OWN_USE:
                    case APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION:
                        result = powerWheelingService.calculateDaily(timeIntervalVo
                                , settlementId
                                , idTypeEnum.getKey()
                                , idRelationIdMap.get(idTypeEnum.getKey())
                                , typeMonthlyNi.get(idTypeEnum.getValue()));
                        break;
                    case APPLICATION_TYPE_DIRECT_SUPPLY_WHOLESALE:
                        result = directService.calculateDaily(timeIntervalVo
                                , settlementId
                                , idTypeEnum.getKey()
                                , idRelationIdMap.get(idTypeEnum.getKey())
                                , typeMonthlyNi.get(idTypeEnum.getValue()));
                        break;
                    case APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING:
                        result = calculateDailyDirectSupplyAndPowerWheeling(timeIntervalVo
                                , settlementId
                                , idTypeEnum.getKey()
                                , idRelationIdMap.get(idTypeEnum.getKey())
                                , typeMonthlyNi.get(idTypeEnum.getValue()));
                        break;
                    case APPLICATION_TYPE_POWER_WHEELING_FLEXIBLE_DISTRIBUTION:
                        log.error("彈性分配結算不支援");
                        continue;
                }
                appResultMap.put(idTypeEnum.getKey(), result);
                log.info("end with application Id = {}, result = {}", idTypeEnum.getKey(), result);
                settlementUtilsService.sendToFront(userId, settlementId, totalCalApCnt, curCalApCnt, caller);
            }
        }
        return appResultMap;
    }

    private void saveAllFromAmiPwr15Mdes(Long settlementId, Date date) throws Exception{
        List<String> loadMeterIdList = tempService.getComutableLoadMeterNo(settlementId, date);
        List<String> genMeterIdList = tempService.getComutableGeneratorMeterNo(settlementId, date);

        amiService.saveAllFromAmiPwr15Mdes(settlementId, date, genMeterIdList, loadMeterIdList);
    }

    private void assembleTempApplicationGeneratorKw(Date date, Long settlementId, Map<Long, ApplicationTypeEnum> idTypeEnumMap) throws Exception{
//        Map<TimeSlotEnum, TimeEnergyChargeVo> slotChargeMap = energyChargeService.get(date.getTime());
        Map<Long, Integer> timeSectionIdMap = energyChargeService.getByEnergyTableName(TimeSlotEnum.TIME_SLOT_THREE, "高壓及特高壓電力", date);
        if(MapUtils.isEmpty(timeSectionIdMap)){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATETIME_FORMAT_YYYYMMDD_WITH_DASH);
            String errorMsg = "電量時間帶" + simpleDateFormat.format(date) +"有缺";
            log.error(errorMsg);
            throw new Exception(SETTLEMENT_ERROR_HEADER + errorMsg);
        }

        //因為有換表問題，所以會有多個values
        //Map<APPLICATION_GENERATOR_ID, List<GeneratorAppMeterVo>>
        //若發電端一個meter可能有多個機組，若所有幾組皆為試營運，且發電端為不記帳且，這樣就不計算了
        Map<Long, List<GeneratorAppMeterVo>> genMeterMap = tempService.getApplicationGeneratorMeterNo(date, settlementId, new ArrayList<>(idTypeEnumMap.keySet()));
        final int PER_TIME = 10;

        if(MapUtils.isNotEmpty(genMeterMap)) {
            List<String> meterIdList = new ArrayList<>();
            TimeIntervalVo timeIntervalVo = DateUtils.getDayInterval(date.getTime());
            Map<String, List<Long>> meterAppIdMap =  new HashMap<>();
            for(Map.Entry<Long, List<GeneratorAppMeterVo>> idVo : genMeterMap.entrySet()){
                //若為換表取一個做代表便可
                meterIdList.add(idVo.getValue().get(0).getMeterNo());
                for(GeneratorAppMeterVo appMeterVo : idVo.getValue()){
                    if(!meterAppIdMap.containsKey(appMeterVo.getMeterNo())){
                        meterAppIdMap.put(appMeterVo.getMeterNo(), new ArrayList<>());
                    }
                    meterAppIdMap.get(appMeterVo.getMeterNo()).add(idVo.getKey());
                }
            }
            meterIdList = meterIdList.stream().distinct().toList();
//            List<String> meterIdList = genMeterMap.values().stream().flatMap(vo->vo.stream().map(GeneratorAppMeterVo::getMeterNo)).distinct().toList();
            long executeStart = System.currentTimeMillis();
            int start = 0;
            int end = 0;
            while (start < meterIdList.size()) {
                end += Math.min(PER_TIME, meterIdList.size() - start);
                List<String> meterSubList = meterIdList.subList(start, end);
                Map<String, List<Long>> meterAppSubMap = meterAppIdMap.entrySet().stream().filter(entry->meterSubList.contains(entry.getKey())).collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
                List<String> custSubList = genMeterMap.values().stream().flatMap(vo->vo.stream().filter(flatVo->meterSubList.contains(flatVo.getMeterNo())).map(GeneratorAppMeterVo::getNbsCustomer)).distinct().toList();
                //因為換表時，只取一個代表號，所以加上換表
                List<String> relMeterIdList = genMeterMap.values().stream().flatMap(vo->vo.stream().filter(flatVo->flatVo.getRelMeterNo() != null && meterSubList.contains(flatVo.getMeterNo())).map(GeneratorAppMeterVo::getRelMeterNo)).toList();

                List<String> amiMeterIdList = new ArrayList<>(List.copyOf(meterSubList));
                if(CollectionUtils.isNotEmpty(relMeterIdList)){
                    amiMeterIdList.addAll(relMeterIdList);
                }
                List<SettlementTempAmiPwr15Record> pwr15RecordList = amiService.getPwr15MdesFromKwHistory(timeIntervalVo.getStartTime().getTime()
                        , timeIntervalVo.getEndTime().getTime()
                        , settlementId
                        , custSubList
                        , amiMeterIdList
                        , MeterChannelEnum.METER_CHANNEL_GENERATOR);

                if(CollectionUtils.isNotEmpty(pwr15RecordList)) {
                    assembleTempApplicationGeneratorKw(timeIntervalVo, meterSubList, meterAppSubMap, genMeterMap, pwr15RecordList, timeSectionIdMap);
                }
                start = end;
            }
//            tempService.updateGmiByTrialOPFeeNotCalculated(settlementId);
            log.info("generator use time {}", System.currentTimeMillis() - executeStart);
        }
    }

    public void assembleTempApplicationGeneratorKw(TimeIntervalVo timeIntervalVo
            , List<String> meterSubList
            , Map<String, List<Long>> meterNoAppId
            , Map<Long, List<GeneratorAppMeterVo>> genMeterMap
            , List<SettlementTempAmiPwr15Record> pwr15RecordList
            , Map<Long, Integer> timeSectionIdMap){

        List<SimulationTempTempApplicationGeneratorKw> tempAppGenKwList = new ArrayList<>();

        for(String meterNo : meterSubList){
            List<Long> appGenIdList = meterNoAppId.get(meterNo);
            Long appGenId = appGenIdList.get(0);
            GeneratorAppMeterVo genAppMeterVo = genMeterMap.get(appGenId).get(0);

            BigDecimal capacity = genAppMeterVo.getComputableCapacity();
            BigDecimal quarterCap = capacity.multiply(new BigDecimal("0.25"));
//            BigDecimal pmiPercent = genAppMeterVo.getPmiExceptOp().multiply(new BigDecimal("0.01"));
            List<SettlementTempAmiPwr15Record> amiRecordList = pwr15RecordList.stream().filter(record->record.getMeterId().equals(meterNo)).toList();
            if(null == genAppMeterVo.getRelMeterNo()) {
                for (SettlementTempAmiPwr15Record filterRecord : amiRecordList) {
                    BigDecimal kwRatio = filterRecord.getRatio().multiply(filterRecord.getKw());
                    BigDecimal gmi = BigDecimal.ZERO;
                    if(0 < kwRatio.compareTo(BigDecimal.ZERO)){
                        gmi = kwRatio.multiply(genAppMeterVo.getPmiExceptOp()).min(quarterCap);
                    }
                    tempAppGenKwList.add(SimulationTempTempApplicationGeneratorKw.builder()
                            .datetime(filterRecord.getPwrTime())
                            .appGeneratorId(appGenId)
                            .energyChargeSectionId(timeSectionIdMap.get(filterRecord.getPwrTime().getTime()))
                            .kwRatio(kwRatio)
//                            .gmi(0 < kwRatio.compareTo(BigDecimal.ZERO) ? kwRatio.min(quarterCap).multiply(genAppMeterVo.getPmiExceptOp()) : BigDecimal.ZERO)
                            .gmi(gmi)
                            .kwUpdateTime(filterRecord.getUpdateTime())
                            .build());
                }
            }else{
                List<SettlementTempAmiPwr15Record> relRecordList = pwr15RecordList.stream().filter(record->record.getMeterId().equals(genAppMeterVo.getRelMeterNo())).toList();
                Map<Date, BigDecimal> relDateKwMap = relRecordList.stream().collect(toMap(SettlementAmiPwr15RecordColumn::getPwrTime, record->record.getRatio().multiply(record.getKw())));
                for (SettlementTempAmiPwr15Record filterRecord : amiRecordList) {
                    BigDecimal kwRatio = filterRecord.getRatio().multiply(filterRecord.getKw());
                    kwRatio = kwRatio.add(relDateKwMap.get(filterRecord.getPwrTime()));
                    BigDecimal gmi = BigDecimal.ZERO;
                    if(0 < kwRatio.compareTo(BigDecimal.ZERO)){
                        gmi = kwRatio.multiply(genAppMeterVo.getPmiExceptOp()).min(quarterCap);
                    }
                    tempAppGenKwList.add(SimulationTempTempApplicationGeneratorKw.builder()
                            .datetime(filterRecord.getPwrTime())
                            .appGeneratorId(appGenId)
                            .energyChargeSectionId(timeSectionIdMap.get(filterRecord.getPwrTime().getTime()))
                            .kwRatio(kwRatio)
                            .gmi(gmi)
//                            .gmi(0 < kwRatio.compareTo(BigDecimal.ZERO) ? kwRatio.min(quarterCap).multiply(genAppMeterVo.getPmiExceptOp()) : BigDecimal.ZERO)
                            .kwUpdateTime(filterRecord.getUpdateTime())
                            .build());
                }
            }
        }
        if (tempService.saveTempTempApplicationGeneratorKw(tempAppGenKwList)) {
            Map<String, List<Long>> filteredMeterNoAppId = meterNoAppId.entrySet().stream().filter(entry->entry.getValue().size() > 1).collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
            if(MapUtils.isNotEmpty(filteredMeterNoAppId)) {
                for (Map.Entry<String, List<Long>> keyValue : filteredMeterNoAppId.entrySet()) {
                    Long appGenId = keyValue.getValue().get(0);
                    GeneratorAppMeterVo dupGenAppMeterVo = genMeterMap.get(appGenId).get(0);
                    tempService.duplicateTempTempApplicationGeneratorKw(timeIntervalVo.getStartTime().getTime()
                            , timeIntervalVo.getEndTime().getTime()
                            , appGenId
                            , keyValue.getValue().subList(1, keyValue.getValue().size())
                            , dupGenAppMeterVo.getComputableCapacity());
//                for (int cnt = 1; cnt < keyValue.getValue().size(); cnt++) {
//                    Long dupAppGenId = keyValue.getValue().get(cnt);
//                    GeneratorAppMeterVo dupGenAppMeterVo = genMeterMap.get(dupAppGenId).get(0);
//                    BigDecimal dupCapacity = dupGenAppMeterVo.getComputableCapacity();
//                    tempService.duplicateTempTempApplicationGeneratorKw(timeIntervalVo.getStartTime().getTime(), timeIntervalVo.getEndTime().getTime(), appGenId, dupAppGenId, dupCapacity, dupGenAppMeterVo.getPmi());
//                }
                }
            }
        }
    }

    private void assembleTempApplicationLoadKw(Date date, Long settlementId, Map<Long, ApplicationTypeEnum> idTypeEnumMap) throws Exception{

        //因為有換表問題，所以會有多個values
        Map<Long, List<LoadAppMeterVo>> loadMeterMap = tempService.getApplicationLoadMeterNo(date, settlementId, new ArrayList<>(idTypeEnumMap.keySet()));

        final int PER_TIME = 10;

        if(MapUtils.isNotEmpty(loadMeterMap)) {
            List<String> contractStgList = loadMeterMap.values().stream().flatMap(Collection::stream).map(LoadAppMeterVo::getContractStg).toList();
            Map<String, Map<Long, Integer>> stgTimeSectionIdMap = energyChargeService.getByContractStg(TimeSlotEnum.TIME_SLOT_THREE, contractStgList, CONTRACT_STG_LV, CONTRACT_STG_UH_UHV, date);

            if(MapUtils.isEmpty(stgTimeSectionIdMap)){
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATETIME_FORMAT_YYYYMMDD_WITH_DASH);
                String errorMsg = "電量時間帶" + simpleDateFormat.format(date) +"有缺";
                log.error(errorMsg);
                throw new Exception(SETTLEMENT_ERROR_HEADER + errorMsg);
            }

            List<String> meterIdList = new ArrayList<>();
            TimeIntervalVo timeIntervalVo = DateUtils.getDayInterval(date.getTime());

            Map<String, List<Long>> meterAppIdMap =  new HashMap<>();
            for(Map.Entry<Long, List<LoadAppMeterVo>> idVo : loadMeterMap.entrySet()){
                //若為換表取一個做代表便可
                meterIdList.add(idVo.getValue().get(0).getMeterNo());
                for(LoadAppMeterVo loadAppMeterVo : idVo.getValue()){
                    if(!meterAppIdMap.containsKey(loadAppMeterVo.getMeterNo())){
                        meterAppIdMap.put(loadAppMeterVo.getMeterNo(), new ArrayList<>());
                    }
                    meterAppIdMap.get(loadAppMeterVo.getMeterNo()).add(idVo.getKey());
                }
            }
            meterIdList = meterIdList.stream().distinct().toList();
//            List<String> meterIdList = loadMeterMap.values().stream().flatMap(vo->vo.stream().map(LoadAppMeterVo::getMeterNo)).distinct().toList();

            long executeStart = System.currentTimeMillis();
            int start = 0;
            int end = 0;
            while (start < meterIdList.size()) {
                end += Math.min(PER_TIME, meterIdList.size() - start);
                List<String> meterSubList = meterIdList.subList(start, end);
                Map<String, List<Long>> meterAppSubMap = meterAppIdMap.entrySet().stream().filter(entry->meterSubList.contains(entry.getKey())).collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
                List<String> custSubList = loadMeterMap.values().stream().flatMap(vo->vo.stream().filter(flatVo->meterSubList.contains(flatVo.getMeterNo())).map(LoadAppMeterVo::getNbsCustomer)).distinct().toList();
                Map<String, String> meterStgMap = loadMeterMap.values().stream().flatMap(vo->vo.stream().filter(flatVo->meterSubList.contains(flatVo.getMeterNo()))).collect(toMap(LoadAppMeterVo::getMeterNo, LoadAppMeterVo::getContractStg, (first, second)->first));

                //因為換表時，只取一個代表號，所以加上換表
                List<String> relMeterIdList = loadMeterMap.values().stream().flatMap(vo->vo.stream().filter(flatVo->flatVo.getRelMeterNo() != null && meterSubList.contains(flatVo.getMeterNo())).map(LoadAppMeterVo::getRelMeterNo)).toList();
                Map<String, String> meterRelMap = new HashMap<>();
                List<String> amiMeterIdList = new ArrayList<>(List.copyOf(meterSubList));
                if(CollectionUtils.isNotEmpty(relMeterIdList)){
                    meterRelMap = loadMeterMap.values().stream().flatMap(vo->vo.stream().filter(flatVo->flatVo.getRelMeterNo() != null && meterSubList.contains(flatVo.getMeterNo()))).collect(toMap(LoadAppMeterVo::getMeterNo, LoadAppMeterVo::getRelMeterNo));
                    amiMeterIdList.addAll(relMeterIdList);
                }
                List<SettlementTempAmiPwr15Record> pwr15RecordList = amiService.getPwr15MdesFromKwHistory(timeIntervalVo.getStartTime().getTime()
                        , timeIntervalVo.getEndTime().getTime()
                        , settlementId
                        , custSubList
                        , amiMeterIdList
                        , MeterChannelEnum.METER_CHANNEL_LOAD);

                if(CollectionUtils.isNotEmpty(pwr15RecordList)) {
                    assembleTempApplicationLoadKw(timeIntervalVo, meterSubList, meterAppSubMap, meterRelMap, pwr15RecordList, stgTimeSectionIdMap, meterStgMap);
                }
                start = end;
            }
            log.info("load use time {}", System.currentTimeMillis() - executeStart);
        }
    }

    public void assembleTempApplicationLoadKw(TimeIntervalVo timeIntervalVo
            , List<String> meterSubList
            , Map<String, List<Long>> meterNoAppId
            , Map<String, String> meterRelMap
            , List<SettlementTempAmiPwr15Record> pwr15RecordList
            , Map<String, Map<Long, Integer>> stgTimeSectionIdMap
            , Map<String, String> meterStgMap){

        List<SimulationTempTempApplicationLoadKw> tempAppLoadKwList = new ArrayList<>();

        for(String meterNo : meterSubList){
            List<Long> appLoadIdList = meterNoAppId.get(meterNo);
            List<SettlementTempAmiPwr15Record> filterRecordList = pwr15RecordList.stream().filter(record->record.getMeterId().equals(meterNo)).toList();
            Map<Long, Integer> timeSectionIdMap = stgTimeSectionIdMap.get(meterStgMap.get(meterNo));

            if(null == meterRelMap.get(meterNo)) {
                for (SettlementTempAmiPwr15Record filterRecord : filterRecordList) {
                    BigDecimal kwRatio = filterRecord.getRatio().multiply(filterRecord.getKw());
                    tempAppLoadKwList.add(SimulationTempTempApplicationLoadKw.builder()
                            .datetime(filterRecord.getPwrTime())
                            .appLoadId(appLoadIdList.get(0))
                            .energyChargeSectionId(timeSectionIdMap.get(filterRecord.getPwrTime().getTime()))
                            .kwRatio(kwRatio)
                            .kwUpdateTime(filterRecord.getUpdateTime())
                            .build());
                }
            }else{
                List<SettlementTempAmiPwr15Record> relRecordList = pwr15RecordList.stream().filter(record->record.getMeterId().equals(meterRelMap.get(meterNo))).toList();
                Map<Date, BigDecimal> relDateKwMap = relRecordList.stream().collect(toMap(SettlementAmiPwr15RecordColumn::getPwrTime, record->record.getRatio().multiply(record.getKw())));
                for (SettlementTempAmiPwr15Record filterRecord : filterRecordList) {
                    BigDecimal kwRatio = filterRecord.getRatio().multiply(filterRecord.getKw());
                    kwRatio = kwRatio.add(relDateKwMap.get(filterRecord.getPwrTime()));

                    tempAppLoadKwList.add(SimulationTempTempApplicationLoadKw.builder()
                            .datetime(filterRecord.getPwrTime())
                            .appLoadId(appLoadIdList.get(0))
                            .energyChargeSectionId(timeSectionIdMap.get(filterRecord.getPwrTime().getTime()))
                            .kwRatio(kwRatio)
                            .kwUpdateTime(filterRecord.getUpdateTime())
                            .build());
                }
            }
        }
        if (tempService.saveTempTempApplicationLoadKw(tempAppLoadKwList)) {
            Map<String, List<Long>> filteredMeterNoAppId = meterNoAppId.entrySet().stream().filter(entry->entry.getValue().size() > 1).collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(filteredMeterNoAppId)) {
                for (Map.Entry<String, List<Long>> keyValue : filteredMeterNoAppId.entrySet()) {
                    Long appLoadId = keyValue.getValue().get(0);
                    tempService.duplicateTempTempApplicationLoadKw(timeIntervalVo.getStartTime().getTime()
                            , timeIntervalVo.getEndTime().getTime()
                            , appLoadId, keyValue.getValue().subList(1, keyValue.getValue().size()));
//                for (int cnt = 1; cnt < keyValue.getValue().size(); cnt++) {
//                    Long dupAppLoadId = keyValue.getValue().get(cnt);
//                    tempService.duplicateTempTempApplicationLoadKw(timeIntervalVo.getStartTime().getTime(), timeIntervalVo.getEndTime().getTime(), appLoadId, dupAppLoadId);
//                }
                }
            }
        }
    }

    protected TimeIntervalVo getMonthToEnd(Calendar calEnd){
        Calendar startCal = Calendar.getInstance();
        startCal.setTimeInMillis(calEnd.getTimeInMillis());
        startCal.set(Calendar.DAY_OF_MONTH, 1);

        Calendar endCal = Calendar.getInstance();
        endCal.set(Calendar.YEAR, calEnd.get(Calendar.YEAR));
        endCal.set(Calendar.MONTH, calEnd.get(Calendar.MONTH));
        endCal.set(Calendar.DAY_OF_MONTH, calEnd.get(Calendar.DAY_OF_MONTH));
        endCal.add(Calendar.DAY_OF_MONTH,  -1);

        return TimeIntervalVo.builder().startTime(DateUtils.getMidnight(startCal.getTimeInMillis())).endTime(DateUtils.getLastQuarter(endCal.getTimeInMillis())).build();
    }

    private boolean calculateDailyDirectSupplyAndPowerWheeling(TimeIntervalVo timeIntervalVo
            , Long settlementId
            , Long selfAppId
            , List<Long> relationAppId
            , Map<Long, BigDecimal> appLoadIdNiMap) {

        if(directService.calculateDaily(timeIntervalVo
                , settlementId
                , selfAppId
                , relationAppId
                , appLoadIdNiMap)){

            //直供若有餘電，再轉供
            if(directService.hasTimelyUnmatchedRmByDateIntervalAndSettlementId(timeIntervalVo.getStartTime().getTime()
                    , timeIntervalVo.getEndTime().getTime()
                    , settlementId
                    , selfAppId)){
                return powerWheelingService.calculateDaily(timeIntervalVo
                        , settlementId
                        , selfAppId
                        , relationAppId
                        , appLoadIdNiMap);
            }
            return true;
        }
        return false;
    }

}
