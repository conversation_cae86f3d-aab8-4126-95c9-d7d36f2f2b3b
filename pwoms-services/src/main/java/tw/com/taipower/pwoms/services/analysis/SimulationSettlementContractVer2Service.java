package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.ApplicationLoad;
import tw.com.taipower.data.entity.pwoms.ApplicationRelation;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum;
import tw.com.taipower.pwoms.services.vo.settlement.LoadAppMeterVo;
import tw.com.taipower.pwoms.services.vo.utils.TimeIntervalVo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static tw.com.taipower.pwoms.services.constant.Constants.BIGDECIMAL_KW_PRECISION;

@Log4j2
@Service
public class SimulationSettlementContractVer2Service {

    @Autowired
    private ApplicationRepository appRepository;

    @Autowired
    private SimulationApplicationGeneratorRepository appGeneratorRepository;

    @Autowired
    private SimulationApplicationLoadRepository appLoadRepository;

    @Autowired
    private ApplicationMonthlyDirectLoadRecordRepository monthlyDirectLoadRecordRepository;

    @Autowired
    private ApplicationMonthlyLoadRecordRepository monthlyLoadRecordRepository;

    @Autowired
    private SimulationApplicationRelationRepository relationRepository;

    public List<Long> getApplicationIdByContractNo(List<String> contractNoList){
        return appRepository.findByContractNoAndVersion(contractNoList);
    }

    public Map<Long, Long> getApplicationGeneratorIdByAppIdInAndPmiIsNull(List<Long> appIdList){

        return convertApplicationIdAndApplicationGeneratorId(appGeneratorRepository.findIdAndApplicationIdByAppIdInAndPmiIsNull(appIdList));
    }

    private Map<Long, Long> convertApplicationIdAndApplicationGeneratorId(List<Map<String, Object>> appGenIdList){

        Map<Long, Long> appGenIdMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(appGenIdList)){
            for(Map<String, Object> appRecordId : appGenIdList){
                appGenIdMap.put((Long)appRecordId.get("ID"), (Long)appRecordId.get("APPLICATION_ID"));
            }
        }
        return appGenIdMap;
    }

    public Map<Long, Long> getApplicationGeneratorIdByAppIdInAndCapacityIsNull(List<Long> appIdList){

        return convertApplicationIdAndApplicationGeneratorId(appGeneratorRepository.findIdAndApplicationIdByAppIdInAndCapacityIsNull(appIdList));

    }

    public List<ApplicationLoad> getApplicationLoadByAppIdInAndContractCapIsNull(List<Long> appIdList){
        return appLoadRepository.findByAppIdInAndContractCapIsNull(appIdList);
    }

    public List<Long> getResetAnnualNiApplicationId(TimeIntervalVo timeIntervalVo){
        List<Map<String, Object>> preIdValueMapList = appRepository.findByContractEndAndOnlinAtAndVersionGreaterThan(timeIntervalVo.getStartTime().getTime(), timeIntervalVo.getEndTime().getTime());
        List<Long> resetIdList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(preIdValueMapList)) {
            List<String> contractNoList = new ArrayList<>();
            Map<String, Date> preContractEndMap = new HashMap<>();
            //CONTRACT_NO, VERSION, CONTRACTED_END
            for(Map<String, Object> preIdValueMap : preIdValueMapList){
                contractNoList.add((String) preIdValueMap.get("CONTRACT_NO") + "-" + String.format("%02d", Integer.parseInt((String) preIdValueMap.get("VERSION")) + 1));
                preContractEndMap.put((String) preIdValueMap.get("CONTRACT_NO"), (Date)preIdValueMap.get("CONTRACTED_END"));
            }
            List<Map<String, Object>> idValueMapList = appRepository.findContractNoAndOnlineAtByContractNoAndVersion(contractNoList);
            if(CollectionUtils.isNotEmpty(idValueMapList)) {
                //ID, CONTRACT_NO, ONLINE_AT
                for(Map<String, Object> idValueMap : idValueMapList) {
                    Date contractEnd = preContractEndMap.get((String) idValueMap.get("CONTRACT_NO"));
                    Date onlineAt = (Date) idValueMap.get("ONLINE_AT");

                    long diff = onlineAt.getTime() - contractEnd.getTime();
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(contractEnd);

                    if(TimeUnit.MILLISECONDS.toDays(diff) >= calendar.getActualMaximum(Calendar.DAY_OF_MONTH)){
                        resetIdList.add((Long) idValueMap.get("ID"));
                    }
                }
            }
        }
        return resetIdList;
    }

    public Map<Long, BigDecimal> getDirectAnnualYni(Date startDate, Date endDate, Map<Long, LoadAppMeterVo> idLoadAppVoMap, List<Long> resetAppIdList){
        Map<Long, BigDecimal> idYniMap = new HashMap<>();

        Map<Long, LoadAppMeterVo> notIdResetMap = idLoadAppVoMap;
        if(CollectionUtils.isNotEmpty(resetAppIdList)){
            Map<Long, LoadAppMeterVo> idResetMap = idLoadAppVoMap.entrySet().stream().filter(entry->resetAppIdList.contains(entry.getValue().getApplicationId())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            notIdResetMap =  idLoadAppVoMap.entrySet().stream().filter(entry->!resetAppIdList.contains(entry.getValue().getApplicationId())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            Map<Long, BigDecimal> resetIdYniMap = convertAnnualContractToIdResidualCapacity(monthlyDirectLoadRecordRepository.findAnnualContractResidualCapacityForSimulation(startDate, endDate, new ArrayList<>(idResetMap.keySet())));
            for(Map.Entry<Long, LoadAppMeterVo> idResetVo : idResetMap.entrySet()) {
                if (resetIdYniMap.containsKey(idResetVo.getKey())) { //表示沒有monthly record, 所以剩餘量就是annual capacity
                    idYniMap.put(idResetVo.getKey(), resetIdYniMap.get(idResetVo.getKey()));
                } else {
                    idYniMap.put(idResetVo.getKey(), idResetVo.getValue().getAnnualContractCap());
                }
            }
        }

        for(Map.Entry<Long, LoadAppMeterVo> notIdReset : notIdResetMap.entrySet()) {
            LoadAppMeterVo loadAppMeterVo = notIdReset.getValue();
            BigDecimal annualYni = monthlyDirectLoadRecordRepository.findAnnualContractResidualCapacityForSimulation(startDate, endDate
                    , loadAppMeterVo.getAppLoadId()
                    , loadAppMeterVo.getAnnualContractCap().setScale(BIGDECIMAL_KW_PRECISION, RoundingMode.DOWN)); //若不加小數點，jpa就會自動取整
            if(null == annualYni){ //表示沒有monthly record, 所以剩餘量就是annual capacity
                idYniMap.put(notIdReset.getKey(), loadAppMeterVo.getAnnualContractCap());
            }else {
                idYniMap.put(notIdReset.getKey(), annualYni);
            }
        }
        return idYniMap;
    }

    private Map<Long, BigDecimal> convertAnnualContractToIdResidualCapacity(List<Map<String, Object>> idValueMapList){
        Map<Long, BigDecimal> idYniMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(idValueMapList)){
            //APPLICATION_LOAD_ID, RESIDUAL_CN
            for(Map<String, Object> idValueMap : idValueMapList){
                idYniMap.put((Long)idValueMap.get("APPLICATION_LOAD_ID"), (BigDecimal) idValueMap.get("RESIDUAL_CN"));
            }
        }
        return idYniMap;
    }

    public Map<Long, BigDecimal> getPWAnnualYni(Date startDate, Date endDate, Map<Long, LoadAppMeterVo> idLoadAppVoMap, List<Long> resetAppIdList){
        Map<Long, BigDecimal> idYniMap = new HashMap<>();

        Map<Long, LoadAppMeterVo> notIdResetMap = idLoadAppVoMap;
        if(CollectionUtils.isNotEmpty(resetAppIdList)){
            Map<Long, LoadAppMeterVo> idResetMap = idLoadAppVoMap.entrySet().stream().filter(entry->resetAppIdList.contains(entry.getValue().getApplicationId())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            notIdResetMap =  idLoadAppVoMap.entrySet().stream().filter(entry->!resetAppIdList.contains(entry.getValue().getApplicationId())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            Map<Long, BigDecimal> resetIdYniMap = convertAnnualContractToIdResidualCapacity(monthlyLoadRecordRepository.findAnnualContractResidualCapacityForSimulation(startDate, endDate, new ArrayList<>(idResetMap.keySet())));
            for(Map.Entry<Long, LoadAppMeterVo> idResetVo : idResetMap.entrySet()) {
                if (resetIdYniMap.containsKey(idResetVo.getKey())) { //表示沒有monthly record, 所以剩餘量就是annual capacity
                    idYniMap.put(idResetVo.getKey(), resetIdYniMap.get(idResetVo.getKey()));
                } else {
                    idYniMap.put(idResetVo.getKey(), idResetVo.getValue().getAnnualContractCap());
                }
            }
        }

        for(Map.Entry<Long, LoadAppMeterVo> notIdReset : notIdResetMap.entrySet()) {
            LoadAppMeterVo loadAppMeterVo = notIdReset.getValue();
            BigDecimal annualYni = monthlyLoadRecordRepository.findAnnualContractResidualCapacityForSimulation(startDate, endDate
                    , loadAppMeterVo.getAppLoadId()
                    , loadAppMeterVo.getAnnualContractCap().setScale(BIGDECIMAL_KW_PRECISION, RoundingMode.DOWN)); //若不加小數點，jpa就會自動取整
            if(null == annualYni){ //表示沒有monthly record, 所以剩餘量就是annual capacity
                idYniMap.put(notIdReset.getKey(), loadAppMeterVo.getAnnualContractCap());
            }else {
                idYniMap.put(notIdReset.getKey(), annualYni);
            }
        }
        return idYniMap;
    }

    public List<Long> getSelfAndRelationApplicationId(Date startDate, Date endDate, List<Long> appIdList) {
        return relationRepository.findRelationIdByDateIntervalAndApplicationIdIn(startDate, endDate, appIdList);
    }

    public Map<Long, List<Long>> getRelationApplicationId(Date date, Long settlementId) {
        return relationRepository.findApplicationRelationByAppDateComputable(date, settlementId)
                .stream().filter(relation->!relation.getAppRelationId().equals(relation.getApplicationId()))
                .collect(Collectors.groupingBy(ApplicationRelation::getApplicationId
                        , Collectors.mapping(ApplicationRelation::getAppRelationId, Collectors.toList())));
    }

    public ApplicationTypeEnum getApplicationTypeById(Long appId){
        return ApplicationTypeEnum.get(appRepository.findTypeById(appId));
    }

    public List<Long> getDirectApplicationLoad(List<Long> appIdList, boolean isDirect){
        return appLoadRepository.findIdByIdInAndIsDirect(appIdList, isDirect);
    }

    public List<Long> findApplicationGeneratorIdByIdIn(List<Long> appLoadIdList){
        return appLoadRepository.findApplicationGeneratorIdByIdIn(appLoadIdList);
    }

    public Map<Long, List<Long>> getRelationApplicationId(Date startDate, Date endDate, Long settlementId, List<Long> appIdList) {
        Map<Long, List<Long>> selfIdRelIdMap = new HashMap<>();
        List<?> appRelationList = relationRepository.findByComputableDateIntervalAndSettlementId(startDate, endDate, settlementId);

        //APPLICATION_ID, RELATION_ID
        if(CollectionUtils.isNotEmpty(appRelationList)){
            for (Object appRelationObject : appRelationList) {
                Map<?, ?> appRelationMap = (Map<?, ?>) appRelationObject;
                Long selfId = (Long) appRelationMap.get("APPLICATION_ID");
                Long relationId = (Long) appRelationMap.get("RELATION_ID");

                if(appIdList.contains(selfId)) {
                    if(!selfIdRelIdMap.containsKey(selfId)){
                        selfIdRelIdMap.put(selfId, new ArrayList<>());
                    }
                    if (!Objects.equals(selfId, relationId)) {
                        selfIdRelIdMap.get(selfId).add(relationId);
                    }
                }
            }
        }
        return selfIdRelIdMap;
    }

}
