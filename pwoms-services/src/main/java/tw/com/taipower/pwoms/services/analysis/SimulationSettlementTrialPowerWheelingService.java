package tw.com.taipower.pwoms.services.analysis;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum;
import tw.com.taipower.pwoms.services.settlement.SettlementTrialService;
import tw.com.taipower.pwoms.services.vo.settlement.*;
import tw.com.taipower.pwoms.services.vo.utils.TimeIntervalVo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;
import static tw.com.taipower.data.constant.Constants.FIELD_NAME_APPLICATION_GENERATOR_ID;
import static tw.com.taipower.data.constant.Constants.FIELD_NAME_APPLICATION_LOAD_ID;
import static tw.com.taipower.pwoms.services.constant.Constants.BIGDECIMAL_KW_PRECISION;
import static tw.com.taipower.pwoms.services.constant.Constants.BIGDECIMAL_PRECISION;
import static tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum.*;
import static tw.com.taipower.pwoms.services.enumclass.ApplicationTypeEnum.APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION;

@Log4j2
@Service
public class SimulationSettlementTrialPowerWheelingService extends SimulationSettlementTrialService {

    @Autowired
    protected SimulationTempApplicationMonthlyRematchGeneratorLoadRecordRepository rematchGenLoadRecordRepository;

    @Autowired
    protected SimulationTempApplicationMonthlyGeneratorRecordRepository monthlyGenRecordRepository;

    @Autowired
    protected SimulationTempApplicationMonthlyGeneratorLoadRecordRepository monthlyGenLoadRecordRepository;

    @Autowired
    protected SimulationTempApplicationMonthlyLoadRecordRepository monthlyLoadRecordRepository;

    @Autowired
    protected SimulationTempApplicationDailyGeneratorLoadRecordRepository dailyGenLoadRecordRepository;

    @Autowired
    protected SimulationTempApplicationDailyLoadRecordRepository dailyLoadRecordRepository;

    @Autowired
    protected SimulationTempApplicationDailyGeneratorRecordRepository dailyGenRecordRepository;

    @Autowired
    protected SimulationTempApplicationTimelyGeneratorLoadRecordRepository timelyGenLoadRecordRepository;

    @Autowired
    protected SimulationTempApplicationTimelyLoadRecordRepository timelyLoadRecordRepository;

    @Autowired
    protected SimulationTempApplicationTimelyGeneratorRecordRepository timelyGenRecordRepository;

    @Autowired
    protected SimulationTempApplicationMonthlyRematchLoadRecordRepository rematchLoadRecordRepository;

    @Autowired
    protected SimulationTempApplicationMonthlyRematchGeneratorRecordRepository rematchGenRecordRepository;


    @Override
    public void deleteAllRecordBySettlementId(Long settlementId) throws Exception{
        try {
            rematchGenLoadRecordRepository.deleteAllRecordBySettlementId(settlementId);
            monthlyGenLoadRecordRepository.deleteAllRecordBySettlementId(settlementId);
            dailyGenLoadRecordRepository.deleteAllRecordBySettlementId(settlementId);
            timelyGenLoadRecordRepository.deleteAllRecordBySettlementId(settlementId);
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new Exception(exception.getMessage());
        }
    }

    @Override
    protected Map<ApplicationTypeEnum, Map<Long, BigDecimal>> getAnnualYni(TimeIntervalVo timeIntervalVo, TimeIntervalVo annualTimeIntervalVo, Map<Long, LoadAppMeterVo> appLoadVoMap, List<Long> resetAppIdList) {
        Map<ApplicationTypeEnum, Map<Long, BigDecimal>> pwAnnualYni = new HashMap<>();
        pwAnnualYni.put(APPLICATION_TYPE_POWER_WHEELING, new HashMap<>());
        pwAnnualYni.put(APPLICATION_TYPE_POWER_WHEELING_OWN_USE, new HashMap<>());
        pwAnnualYni.put(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING, new HashMap<>());
        pwAnnualYni.put(APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION, new HashMap<>());

        if(MapUtils.isNotEmpty(appLoadVoMap)){
            Date startDate = annualTimeIntervalVo.getStartTime().getTime();
            Date endDate = annualTimeIntervalVo.getEndTime().getTime();

            Map<Long, LoadAppMeterVo> pwLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_POWER_WHEELING.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(pwLoadMap)) {
                pwAnnualYni.get(APPLICATION_TYPE_POWER_WHEELING).putAll(contractServiceVer2.getPWAnnualYni(startDate, endDate, pwLoadMap, resetAppIdList));
            }

            Map<Long, LoadAppMeterVo> pwOwnLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_POWER_WHEELING_OWN_USE.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(pwOwnLoadMap)) {
                pwAnnualYni.get(APPLICATION_TYPE_POWER_WHEELING_OWN_USE).putAll(contractServiceVer2.getPWAnnualYni(startDate, endDate, pwOwnLoadMap, resetAppIdList));
            }

            Map<Long, LoadAppMeterVo> directPwOwnLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(directPwOwnLoadMap)) {
                pwAnnualYni.get(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING).putAll(contractServiceVer2.getPWAnnualYni(startDate, endDate, directPwOwnLoadMap, resetAppIdList));
            }

            Map<Long, LoadAppMeterVo> greenPwLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(greenPwLoadMap)) {
                pwAnnualYni.get(APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION).putAll(contractServiceVer2.getPWAnnualYni(startDate, endDate, greenPwLoadMap, resetAppIdList));
            }
        }
        return pwAnnualYni;
    }

    @Override
    public void saveDailyRecordByDateIntervalAndSettlementId(Date startDate, Date endDate, Long settlementId) throws Exception{
        try{
            dailyLoadRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId);
            dailyGenRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId);
            dailyGenLoadRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId);
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new Exception(exception.getMessage());
        }
    }

    @Override
    protected Map<ApplicationTypeEnum, Map<Long, BigDecimal>> getMonthlyNi(TimeIntervalVo timeIntervalVo, Long settlementId, Map<Long, LoadAppMeterVo> appLoadVoMap) {
        Map<ApplicationTypeEnum, Map<Long, BigDecimal>> pwMonthlyNi = new HashMap<>();
        pwMonthlyNi.put(APPLICATION_TYPE_POWER_WHEELING, new HashMap<>());
        pwMonthlyNi.put(APPLICATION_TYPE_POWER_WHEELING_OWN_USE, new HashMap<>());
        pwMonthlyNi.put(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING, new HashMap<>());
        pwMonthlyNi.put(APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION, new HashMap<>());

        if(MapUtils.isNotEmpty(appLoadVoMap)){
//            TimeIntervalVo monthlyTimeIntervalVo = getMonthToEnd(timeIntervalVo.getEndTime());
            Date startTime = timeIntervalVo.getStartTime().getTime();
            Date endTime = timeIntervalVo.getEndTime().getTime();

            Map<Long, LoadAppMeterVo> pwLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_POWER_WHEELING.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(pwLoadMap)) {
                pwMonthlyNi.get(APPLICATION_TYPE_POWER_WHEELING).putAll(tempService.getPWMonthlyNi(startTime, endTime, settlementId, pwLoadMap));
            }

            Map<Long, LoadAppMeterVo> pwOwnLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_POWER_WHEELING_OWN_USE.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(pwOwnLoadMap)) {
                pwMonthlyNi.get(APPLICATION_TYPE_POWER_WHEELING_OWN_USE).putAll(tempService.getPWMonthlyNi(startTime, endTime, settlementId, pwOwnLoadMap));
            }

            Map<Long, LoadAppMeterVo> directPwOwnLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(directPwOwnLoadMap)) {
                pwMonthlyNi.get(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING).putAll(tempService.getPWMonthlyNi(startTime, endTime, settlementId, directPwOwnLoadMap));
            }

            Map<Long, LoadAppMeterVo> greenPwLoadMap = appLoadVoMap.entrySet().stream().filter(
                            entry -> APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION.getId().equals(entry.getValue().getType()))
                    .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(MapUtils.isNotEmpty(greenPwLoadMap)) {
                pwMonthlyNi.get(APPLICATION_TYPE_POWER_WHEELING_GREEN_AUCTION).putAll(tempService.getPWMonthlyNi(startTime, endTime, settlementId, greenPwLoadMap));
            }
        }
        return pwMonthlyNi;
    }

    @Override
    protected Boolean calculateDaily(TimeIntervalVo timeIntervalVo
            , Long settlementId
            , Long selfAppId
            , List<Long> relationAppIdList
            , Map<Long, BigDecimal> currentNiMap) {

        try {
            List<Long> allAppIdList = new ArrayList<>();
            allAppIdList.add(selfAppId);
            if(CollectionUtils.isNotEmpty(relationAppIdList)){
                allAppIdList.addAll(relationAppIdList);
            }

            Date startTime = timeIntervalVo.getStartTime().getTime();
            Date endTime = timeIntervalVo.getEndTime().getTime();

            ApplicationTypeEnum appTypeEnum = contractServiceVer2.getApplicationTypeById(selfAppId);

            Map<Long, Map<String, List<Long>>> appIdGenLoadMap = tempService.getApplicationGenLoadIdList(startTime, settlementId, allAppIdList);
            List<Long> selfLoadIdList = filterLoadByIsDirectAndApplicationType(appTypeEnum, selfAppId, appIdGenLoadMap.get(selfAppId).get(FIELD_NAME_APPLICATION_LOAD_ID));
            List<Long> selfGenIdList = appIdGenLoadMap.get(selfAppId).get(FIELD_NAME_APPLICATION_GENERATOR_ID);
            List<Long> allGenIdList = appIdGenLoadMap.values().stream().map(entry->entry.get(FIELD_NAME_APPLICATION_GENERATOR_ID)).toList().stream().flatMap(Collection::stream).toList();

            if ((appTypeEnum.equals(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING))) {
                //因為先直後轉表示，Generator有剩餘量，所以，若契約發電端皆為零不會發生
                //這裏要update Generator GMI = unmatchedRM剩餘量
                tempService.updateDirectGmiEqualToTimelyUnmatchedRm(startTime, endTime, settlementId, selfGenIdList);
            } else {
                if (CollectionUtils.isNotEmpty(relationAppIdList)) {
//                    當跨契約時，需要算出關聯契約用電端可媒合量比例
//                    若契約發電端皆為零，更新GMI=Capacity*PMI*0.01
//                    這個處理的原因是，雖然發電量為zero但還是要算出用電端可媒合量Uni
//                    而算出Uni需要發電端比例來計算，但因發電端皆為0，便無法算出比例
//                    因此用發電端比例使用Capacity來計算
//                    之後改回去避免rematch發生錯誤
//                    tempService.updateZeroGmiByDatetimeAndApplicationGeneratorIdIn(startTime, endTime, selfGenIdList);
                    tempService.updateZeroGmiByDatetimeAndApplicationGeneratorIdIn(settlementId, startTime, endTime, allGenIdList);
                }
            }

            Map<Long, SumGmiKwVo> selfSumGmiKwMap = tempService.getSumGmiAndKwByDatetimeIntervalAndApplicationGeneratorIdIn(startTime, endTime, selfGenIdList);
            Map<Long, TempLoadUniVo> appLoadUniVoMap = assembleTempLoadUniVo(startTime, endTime, settlementId, currentNiMap, selfAppId, selfLoadIdList, relationAppIdList, appIdGenLoadMap);
            Map<Long, TempGenGmiVo> appGenGmiVoMap = assembleTempGeneratorGmiVo(startTime, endTime, selfGenIdList);

            //電價區間時間帶媒合用用電端
            List<SimulationTempApplicationTimelyGeneratorLoadRecord> genLoadRecordList = new ArrayList<>();
            List<SimulationTempApplicationTimelyLoadRecord> loadRecordList = new ArrayList<>();

            long quarterInMillis = TimeUnit.MINUTES.toMillis(15);
            for (long datetime = startTime.getTime(); datetime <= endTime.getTime(); datetime += quarterInMillis) {
                //calculate qni 媒合量
                List<SimulationTempApplicationTimelyLoadRecord> dateLoadRecordList = calculateLoadMatchedCn(datetime, settlementId, appLoadUniVoMap, selfSumGmiKwMap.get(datetime));
                loadRecordList.addAll(dateLoadRecordList);
                BigDecimal sumMatchedCn = dateLoadRecordList.stream().map(SimulationTempApplicationTimelyLoadRecord::getMatchedCn).reduce(BigDecimal.ZERO, BigDecimal::add);
                genLoadRecordList.addAll(calculateGeneratorMatchedRm(datetime, settlementId, appGenGmiVoMap, appLoadUniVoMap, selfSumGmiKwMap.get(datetime), sumMatchedCn));
            }
            //用電端轉直供度數/未媒合度數分成1,3,9,11時，系統由用電端之「用電契約別」的三段式時間電價 判斷使用哪一個電價時間帶
            saveTempApplicationTimelyLoadRecord(loadRecordList);
            //電價區間時間帶媒合用用電端
            saveTempApplicationTimelyGeneratorLoadRecord(genLoadRecordList);
            //若契約發電端皆為零，更新GMI=Capacity*PMI*0.01
            //改回GMI=0 改回去避免rematch和timely generator record發生錯誤
            if (CollectionUtils.isNotEmpty(relationAppIdList)) {
                tempService.updateGmiToZeroByDatetimeAndApplicationGeneratorIdIn(startTime, endTime, allGenIdList);
            }
            //發電端轉直供度數/餘電度數分成1,3,9,11時，固定使用「高壓及特高壓三段式時間電價」的時間帶
            saveTempApplicationTimelyGeneratorRecord(startTime, endTime, settlementId, selfGenIdList);
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    private List<Long> filterLoadByIsDirectAndApplicationType(ApplicationTypeEnum typeEnum,  Long selfAppId, List<Long> selfLoadIdList){
        //若是先直後轉移除isDirect = false
        if(typeEnum.equals(APPLICATION_TYPE_DIRECT_SUPPLY_AND_POWER_WHEELING)){
            return contractServiceVer2.getDirectApplicationLoad(selfLoadIdList, false);
        }
        return selfLoadIdList;
    }

    protected Map<Long, TempLoadUniVo> assembleTempLoadUniVo(Date startTime
            , Date endTime
            , Long settlementId
            , Map<Long, BigDecimal> currNiMap
            , Long selfAppId
            , List<Long> selfLoadIdList
            , List<Long> relationAppIdList
            , Map<Long, Map<String, List<Long>>> appIdGenLoadMap){

        Map<Long, Map<Long, TempTempApplicationLoadKw>> appLoadKwMap = tempService.getApplicationLoadKw(startTime, endTime, selfLoadIdList);

        Map<Long, Map<Long, BigDecimal>> appLoadIdRelSumGmiMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(relationAppIdList)) {
            Map<Long, List<Long>> appLoadIdRelAppGenIdMap = tempService.getRelationApplicationGeneratorIdByApplicationLoadId(startTime
                    , settlementId
                    , selfAppId
                    , appIdGenLoadMap);

            //若是關聯契約Load已達上限（年月上限）就不需考慮關聯GMI
            List<Long> zeroNiLoadIdList = currNiMap.entrySet().stream().filter(entry->entry.getValue().compareTo(BigDecimal.ZERO) == 0).map(Map.Entry::getKey).toList();
            if(CollectionUtils.isNotEmpty(zeroNiLoadIdList)){
                List<Long> zeroGenIdList = contractServiceVer2.findApplicationGeneratorIdByIdIn(zeroNiLoadIdList);
                Map<Long, List<Long>> ridOffZeroNiMap = new HashMap<>();
                for(Map.Entry<Long, List<Long>> appLoadIdRelAppGenId : appLoadIdRelAppGenIdMap.entrySet()){
                    ridOffZeroNiMap.put(appLoadIdRelAppGenId.getKey(), appLoadIdRelAppGenId.getValue().stream().filter(value->!zeroGenIdList.contains(value)).collect(Collectors.toList()));
                }
                appLoadIdRelSumGmiMap = getRelationSumGmi(startTime, endTime, ridOffZeroNiMap);
            }else {
                appLoadIdRelSumGmiMap = getRelationSumGmi(startTime, endTime, appLoadIdRelAppGenIdMap);
            }
        }

        Map<Long, TempLoadUniVo> appLoadUnVoMap = new HashMap<>();

        for(Long appLoadId : selfLoadIdList){
            appLoadUnVoMap.put(appLoadId, TempLoadUniVo.builder()
                    .cni(BigDecimal.ZERO)
                    .currentNi(currNiMap.get(appLoadId))
                    .timeLoadKwMap(appLoadKwMap.get(appLoadId))
                    .relSumGmiMap(appLoadIdRelSumGmiMap.get(appLoadId))
                    .appLoadId(appLoadId)
                    .matchedCn(new HashMap<>())
                    .build());
        }
        return appLoadUnVoMap;
    }

    protected Map<Long, Map<Long, BigDecimal>> getRelationSumGmi(Date startTime, Date endTime
            , Map<Long, List<Long>> appLoadIdRelAppGenIdMap){

        Map<Long, Map<Long, BigDecimal>> appLoadRelSumGmiMap = new HashMap<>();

        for(Map.Entry<Long, List<Long>> appLoadIdRelAppGenId : appLoadIdRelAppGenIdMap.entrySet()){
            if(CollectionUtils.isNotEmpty(appLoadIdRelAppGenId.getValue())){
                appLoadRelSumGmiMap.put(appLoadIdRelAppGenId.getKey(), tempService.getSumGmiByDatetimeIntervalAndApplicationGeneratorIdIn(startTime
                        , endTime
                        , appLoadIdRelAppGenId.getValue()));
            }else{
                appLoadRelSumGmiMap.put(appLoadIdRelAppGenId.getKey(), new HashMap<>());
            }
        }
        return appLoadRelSumGmiMap;
    }

    protected Map<Long, TempGenGmiVo> assembleTempGeneratorGmiVo(Date startTime
            , Date endTime
            , List<Long> selfGenIdList){

        Map<Long, TempGenGmiVo> appGenIdGmVoMap = new HashMap<>();

        Map<Long, Map<Long, TempTempApplicationGeneratorKw>> appGenKwMap = tempService.getApplicationGeneratorKw(startTime, endTime, selfGenIdList);
        for(Long appGenId : selfGenIdList){
            appGenIdGmVoMap.put(appGenId, TempGenGmiVo.builder()
                    .appGenId(appGenId)
                    .timeGenKwMap(appGenKwMap.get(appGenId))
                    .matchedRm(new HashMap<>())
                    .build());
        }
        return appGenIdGmVoMap;
    }

    protected List<SimulationTempApplicationTimelyLoadRecord> calculateLoadMatchedCn(long datetime, Long settlementId, Map<Long, TempLoadUniVo> appLoadUniVoMap, SumGmiKwVo selfSumGmiKwVo){

        List<SimulationTempApplicationTimelyLoadRecord> timelyLoadRecordList = new ArrayList<>();

        BigDecimal sumUni = BigDecimal.ZERO;
        //calculate uni
        for(TempLoadUniVo tempLoadUniVo : appLoadUniVoMap.values()){

            BigDecimal uni = BigDecimal.ZERO;
            //檢查上限與用電量取最小值
//            BigDecimal cn = tempLoadUniVo.getCurrentNi().min(tempLoadUniVo.getTimeLoadKwMap().get(datetime).getKwRatio());
            BigDecimal cn = tempLoadUniVo.getTimeLoadKwMap().get(datetime).getKwRatio();
            if(0 < cn.compareTo(BigDecimal.ZERO)) {
                //selfSumGmiKwVo.getGmi().compareTo(BigDecimal.ZERO) 避免除以０
                if (MapUtils.isNotEmpty(tempLoadUniVo.getRelSumGmiMap())) {
                    if(0 < selfSumGmiKwVo.getGmi().compareTo(BigDecimal.ZERO)) {
                        //有關聯契約
                        BigDecimal relSumGmi = tempLoadUniVo.getRelSumGmiMap().get(datetime);

                        //契約用電端比率 selfSumGmi / (selfSumGmi + relSumGmi)
                        uni = cn.multiply(selfSumGmiKwVo.getGmi().divide(relSumGmi.add(selfSumGmiKwVo.getGmi()), BIGDECIMAL_KW_PRECISION, RoundingMode.DOWN));
                    }
                } else {
                    uni = cn;
                }
                //Cni未考慮上限前的數據
                tempLoadUniVo.setCni(uni);
                //檢查上限與用電量取最小值
                uni = uni.min(tempLoadUniVo.getCurrentNi());
                sumUni = sumUni.add(uni);
            }
            tempLoadUniVo.setUni(uni);
        }
        //可媒合之轉供量 qi = MIN(SumUni, SumGmi)
        BigDecimal qi = sumUni.min(selfSumGmiKwVo.getGmi());

        BigDecimal sumMatchedCn = BigDecimal.ZERO;
        Date date = new Date(datetime);

        //(qi*uni)/sumUni
        for(TempLoadUniVo tempLoadUniVo : appLoadUniVoMap.values()){
            BigDecimal matchedCn = BigDecimal.ZERO;
            //若是generator kwRatio為0, 就不用計算matchedCn媒合量
            if((0 < selfSumGmiKwVo.getKwRatio().compareTo(BigDecimal.ZERO))
                    && (0 < tempLoadUniVo.getUni().compareTo(BigDecimal.ZERO))) {
                matchedCn = qi.multiply(tempLoadUniVo.getUni()).divide(sumUni, BIGDECIMAL_KW_PRECISION, RoundingMode.DOWN);
                sumMatchedCn = sumMatchedCn.add(matchedCn);
            }
            tempLoadUniVo.getMatchedCn().put(datetime, matchedCn);
            tempLoadUniVo.setCurrentNi(tempLoadUniVo.getCurrentNi().subtract(matchedCn));

            TempTempApplicationLoadKw appLoadKw = tempLoadUniVo.getTimeLoadKwMap().get(datetime);
            timelyLoadRecordList.add(SimulationTempApplicationTimelyLoadRecord.builder()
                    .loadId(tempLoadUniVo.getAppLoadId())
                    .datetime(date)
                    .energyChargeSectionId(appLoadKw.getEnergyChargeSectionId())
//                    .unmatchedCn(tempLoadUniVo.getUni().subtract(matchedCn))
                    .unmatchedCn(tempLoadUniVo.getCni().subtract(matchedCn))
                    .matchedCn(matchedCn)
                    .actualLoad(appLoadKw.getKwRatio())
                    .uni(tempLoadUniVo.getUni())
                    .kwUpdateTime(appLoadKw.getKwUpdateTime())
                    .settlementId(settlementId)
                    .build());
        }

        return timelyLoadRecordList;
    }

    protected List<SimulationTempApplicationTimelyGeneratorLoadRecord> calculateGeneratorMatchedRm(long datetime
            , Long settlementId
            , Map<Long, TempGenGmiVo> appGenGmiVoMap
            , Map<Long, TempLoadUniVo> appLoadUnVoMap
            , SumGmiKwVo selfSumGmiKwVo
            , BigDecimal sumMatchedCn){

        List<SimulationTempApplicationTimelyGeneratorLoadRecord> genLoadRecordList = new ArrayList<>();

        Date date = new Date(datetime);
        if(0 < sumMatchedCn.compareTo(BigDecimal.ZERO)) {
            for (TempGenGmiVo tempGenGmiVo : appGenGmiVoMap.values()) {
                for(TempLoadUniVo tempLoadUniVo : appLoadUnVoMap.values()){
                    BigDecimal gmi = tempGenGmiVo.getTimeGenKwMap().get(datetime).getGmi();
                    //每個發電量媒合用電端量  (matchedCn * gmi)/(selfSumGmi)
                    BigDecimal matchedRm = BigDecimal.ZERO;
                    if(0 < gmi.compareTo(BigDecimal.ZERO)) {
                        matchedRm = tempLoadUniVo.getMatchedCn().get(datetime).multiply(gmi).divide(selfSumGmiKwVo.getGmi(), BIGDECIMAL_KW_PRECISION, RoundingMode.DOWN);
                    }
                    genLoadRecordList.add(SimulationTempApplicationTimelyGeneratorLoadRecord.builder()
                            .generatorId(tempGenGmiVo.getAppGenId())
                            .loadId(tempLoadUniVo.getAppLoadId())
                            .datetime(date)
//                            .energyChargeSectionId(tempLoadUniVo.getTimeLoadKwMap().get(datetime).getEnergyChargeSectionId())
                            .energyChargeSectionId(tempGenGmiVo.getTimeGenKwMap().get(datetime).getEnergyChargeSectionId())
                            .matchedRm(matchedRm)
                            .settlementId(settlementId)
                            .build());
                }
            }
        }else{
            for (TempGenGmiVo tempGenGmiVo : appGenGmiVoMap.values()) {
                for(TempLoadUniVo tempLoadUniVo : appLoadUnVoMap.values()){
                    genLoadRecordList.add(SimulationTempApplicationTimelyGeneratorLoadRecord.builder()
                            .generatorId(tempGenGmiVo.getAppGenId())
                            .loadId(tempLoadUniVo.getAppLoadId())
                            .datetime(date)
                            //                            .energyChargeSectionId(tempLoadUniVo.getTimeLoadKwMap().get(datetime).getEnergyChargeSectionId())
                            .energyChargeSectionId(tempGenGmiVo.getTimeGenKwMap().get(datetime).getEnergyChargeSectionId())
                            .matchedRm(BigDecimal.ZERO)
                            .settlementId(settlementId)
                            .build());
                }
            }
        }
        return genLoadRecordList;
    }

    public boolean saveTempApplicationTimelyLoadRecord(List<SimulationTempApplicationTimelyLoadRecord> loadRecordList ) {
        try {
            List<SimulationTempApplicationTimelyLoadRecord> storedRecordList = timelyLoadRecordRepository.saveAll(loadRecordList);
            return storedRecordList.size() == loadRecordList.size();
        } catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
    }

    public boolean saveTempApplicationTimelyGeneratorLoadRecord(List<SimulationTempApplicationTimelyGeneratorLoadRecord> genLoadRecordList ) {
        try {
            List<SimulationTempApplicationTimelyGeneratorLoadRecord> storedRecordList = timelyGenLoadRecordRepository.saveAll(genLoadRecordList);
            return storedRecordList.size() == genLoadRecordList.size();
        } catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
    }

    public boolean saveTempApplicationTimelyGeneratorRecord(Date startTime, Date endTime, Long settlementId, List<Long> appGenIdList) {
        try {
            timelyGenRecordRepository.saveAllByDateIntervalApplicationGeneratorIdIn(startTime, endTime, settlementId, appGenIdList);
        } catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    @Override
    protected Map<Long, Boolean> calculateMonthly(TimeIntervalVo timeIntervalVo
            , Long settlementId
            , List<EnergyChargeSection> chargeSectionList
            , Map.Entry<ApplicationTypeEnum, List<Long>> typeEnumIdEntry
            , Map<Long, BigDecimal> currentNiMap){

        return rematch(timeIntervalVo, settlementId, chargeSectionList, typeEnumIdEntry, currentNiMap);
    }

    private Map<Long, Boolean> rematch(TimeIntervalVo timeIntervalVo
            , Long settlementId
            , List<EnergyChargeSection> chargeSectionList
            , Map.Entry<ApplicationTypeEnum, List<Long>> typeEnumIdEntry
            , Map<Long, BigDecimal> currentNiMap){

        Map<Long, Boolean> appIdResultMap = new HashMap<>();
        Map<Long, List<Long>> selfRelIdMap = contractServiceVer2.getRelationApplicationId(timeIntervalVo.getStartTime().getTime()
                , timeIntervalVo.getEndTime().getTime(), settlementId, typeEnumIdEntry.getValue());

        Date startDate = timeIntervalVo.getStartTime().getTime();
        Date endDate = timeIntervalVo.getEndTime().getTime();
        List<Long> noRematchGenIdList = new ArrayList<>();
        List<Long> noRematchLoadIdList = new ArrayList<>();

        List<Long> rematchGenIdList = new ArrayList<>();
        List<Long> rematchLoadIdList = new ArrayList<>();

        for(Map.Entry<Long, List<Long>> selfRelId : selfRelIdMap.entrySet()){
            log.info("start with application Id = {}", selfRelId.getKey());
            Long selfAppId = selfRelId.getKey();
            List<Long> relationIdList = selfRelId.getValue();
            List<Long> allAppIdList = new ArrayList<>(relationIdList);
            allAppIdList.add(selfAppId);

            Map<Long, Map<String, List<Long>>> appIdGenLoadMap = tempService.getApplicationGenLoadIdList(startDate
                    , endDate
                    , settlementId, allAppIdList);

            Map<Long, List<Long>> appLoadIdRelAppGenIdMap = tempService.getRelationApplicationGeneratorIdByApplicationLoadId(startDate
                    , endDate
                    , settlementId
                    , selfAppId
                    , appIdGenLoadMap);

            Map<Long, RematchContractSettlementVo> appContractSettlementMap = new HashMap<>();
            for (Map.Entry<Long, Map<String, List<Long>>> appIdGenLoad : appIdGenLoadMap.entrySet()) {
                Long applicationId = appIdGenLoad.getKey();

                if (selfAppId.equals(applicationId)) {
                    //本合約
                    appContractSettlementMap.put(appIdGenLoad.getKey(), RematchContractSettlementVo.builder()
                            .id(appIdGenLoad.getKey())
                            .idLoadMap(getRematchContractLoadSni(startDate, endDate, settlementId
                                    , applicationId
                                    , filterLoadByIsDirectAndApplicationType(typeEnumIdEntry.getKey(), selfAppId, appIdGenLoadMap.get(selfAppId).get(FIELD_NAME_APPLICATION_LOAD_ID))
                                    , appLoadIdRelAppGenIdMap
                                    , currentNiMap))
                            .idGeneratorMap(getRematchContractGeneratorBmi(startDate, endDate, settlementId
                                    , applicationId
                                    , appIdGenLoadMap.get(applicationId).get(FIELD_NAME_APPLICATION_GENERATOR_ID)))
                            .build());
                }
                else {
                    //為關聯契約只需儲存發電端Bmi，用電端不用
                    appContractSettlementMap.put(appIdGenLoad.getKey(), RematchContractSettlementVo.builder()
                            .id(appIdGenLoad.getKey())
                            .idGeneratorMap(getRematchContractGeneratorBmi(startDate, endDate, settlementId
                                    , applicationId
                                    , appIdGenLoadMap.get(applicationId).get(FIELD_NAME_APPLICATION_GENERATOR_ID)))
                            .build());
                }
            }
            if (MapUtils.isNotEmpty(appContractSettlementMap)) {
                List<Integer> sectionIdList = chargeSectionList.stream().map(EnergyChargeSection::getId).toList();

                RematchContractSettlementVo contractSettlementVo = appContractSettlementMap.get(selfAppId);
                if (needToRematch(contractSettlementVo)) {
                    appIdResultMap.put(selfAppId
                            , rematch(startDate
                                    , settlementId
                                    , sectionIdList
                                    , contractSettlementVo));
//                                        , appContractSettlementMap));
                    rematchGenIdList.addAll(contractSettlementVo.getIdGeneratorMap().keySet());
                    rematchLoadIdList.addAll(contractSettlementVo.getIdLoadMap().keySet());
                } else {
                    noRematchGenIdList.addAll(contractSettlementVo.getIdGeneratorMap().keySet());
                    noRematchLoadIdList.addAll(contractSettlementVo.getIdLoadMap().keySet());
                    appIdResultMap.put(selfAppId, true);
                }
            }
            log.info("end with application Id = {}", selfRelId.getKey());
        }
        //TempApplicationMonthlyXXXX為 TempApplicationDailyXXXX + TempApplicationMonthlyRematchXXXX
        saveMonthlyRecord(timeIntervalVo, settlementId, rematchGenIdList, rematchLoadIdList
                , noRematchGenIdList, noRematchLoadIdList);
        return appIdResultMap;
    }

    private Map<Long, RematchContractLoadSniVo> getRematchContractLoadSni(Date startDate
            , Date endDate
            , Long settlementId
            , Long applicationId
            , List<Long> selfLoadIdList
            , Map<Long, List<Long>> appLoadIdRelAppGenIdMap
            , Map<Long, BigDecimal> currentNiMap) {

        Map<Long, RematchContractLoadSniVo> rematchLoadCnMap = new HashMap<>();

        List<ContractSectionCnRmVo> contractSectionCnRmVoList = getDailySectionSumMatchedCn(startDate, endDate, settlementId, selfLoadIdList);
        Map<Long, List<ContractSectionCnRmVo>> loadIdSectionCnRmVoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(contractSectionCnRmVoList)) {
            loadIdSectionCnRmVoMap = contractSectionCnRmVoList.stream().collect(groupingBy(ContractSectionCnRmVo::getId, Collectors.toList()));
        }
//        Set<Long> appIdList = filteredViewDateAppGenLoadList.stream().map(ViewDateApplicationGeneratorLoad::getApplicationId).collect(Collectors.toSet());
        for (Long appLoadId : selfLoadIdList) {
            if (!rematchLoadCnMap.containsKey(appLoadId)) {

                List<ContractSectionCnRmVo> sectionSniBmiVoList = loadIdSectionCnRmVoMap.getOrDefault(appLoadId, new ArrayList<>());
                Map<Integer, BigDecimal> sectionIdSumSniMap = sectionSniBmiVoList.stream().collect(toMap(ContractSectionCnRmVo::getChargeSectionId, ContractSectionCnRmVo::getSumUnmatchedCnRm));

                rematchLoadCnMap.put(appLoadId, RematchContractLoadSniVo.builder()
                        .applicationId(applicationId)
                        .appLoadId(appLoadId)
                        .monthlyNi(currentNiMap.get(appLoadId)) //目前剩餘量
                        .relationAppGenIdList(appLoadIdRelAppGenIdMap.get(appLoadId))
                        .sectionIdSumSniMap(recalculateUnmatchedSniByMonthlyNi(sectionIdSumSniMap, currentNiMap.get(appLoadId)))  //因為有月年上限月量，若是四段未媒合量超過上限，就須重新計算
                        .pureSectionIdSumSniMap(sectionIdSumSniMap)
                        .build());
            }
        }
        return rematchLoadCnMap;
    }

    public List<ContractSectionCnRmVo> getDailySectionSumMatchedCn(Date startDate
            , Date endDate
            , Long settlementId
            , List<Long> appLoadIdList) {
        List<Map<String, Object>> dailySectionSumList = dailyLoadRecordRepository.sumByDateGroupByApplicationLoadIdAndEnergyChargeSectionId(startDate, endDate, settlementId, appLoadIdList);

        return convertContractSectionSniBmi(dailySectionSumList);
    }

    private List<ContractSectionCnRmVo> convertContractSectionSniBmi(List<Map<String, Object>> dailySectionSumList) {
        List<ContractSectionCnRmVo> contractSectionCnRmList = new ArrayList<>();
        //ID, ENERGY_CHARGE_SECTION_ID, UNMATCHED_CAPACITY, MATCHED_CAPACITY

        if (CollectionUtils.isNotEmpty(dailySectionSumList)) {
            for(Map<String, Object> dailySectionSumMap : dailySectionSumList){
                Byte energyId = (Byte) dailySectionSumMap.get("ENERGY_CHARGE_SECTION_ID");
                Integer energyInt = energyId.intValue();
                contractSectionCnRmList.add(ContractSectionCnRmVo.builder()
                        .Id((Long) dailySectionSumMap.get("ID"))
                        .chargeSectionId(energyInt)
                        .sumMatchedCnRm(((BigDecimal) dailySectionSumMap.get("MATCHED_CAPACITY")).stripTrailingZeros())
                        .sumUnmatchedCnRm(((BigDecimal) dailySectionSumMap.get("UNMATCHED_CAPACITY")).stripTrailingZeros())
                        .build());
            }
        }
        return contractSectionCnRmList;
    }

    private Map<Long, RematchContractGeneratorBmiVo> getRematchContractGeneratorBmi(Date startDate
            , Date endDate
            , Long settlementId
            , Long applicationId
            , List<Long> appGenIdList) {

        Map<Long, RematchContractGeneratorBmiVo> rematchGenMap = new HashMap<>();

        List<ContractSectionCnRmVo> contractSectionCnRmVoList = getDailySectionSumMatchedRm(startDate, endDate, settlementId, appGenIdList);
        Map<Long, List<ContractSectionCnRmVo>> genIdSectionCnRmVoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(contractSectionCnRmVoList)) {
            genIdSectionCnRmVoMap = contractSectionCnRmVoList.stream().collect(groupingBy(ContractSectionCnRmVo::getId, Collectors.toList()));
        }

        for (Long appGenId : appGenIdList) {
            if (!rematchGenMap.containsKey(appGenId)) {
                List<ContractSectionCnRmVo> sectionCnRmVoList = genIdSectionCnRmVoMap.getOrDefault(appGenId, new ArrayList<>());
                Map<Integer, BigDecimal> sectionIdSumCnMap = sectionCnRmVoList.stream().collect(toMap(ContractSectionCnRmVo::getChargeSectionId, ContractSectionCnRmVo::getSumUnmatchedCnRm));

                rematchGenMap.put(appGenId, RematchContractGeneratorBmiVo.builder()
                        .applicationId(applicationId)
                        .appGeneratorId(appGenId)
                        .sectionIdSumBmiMap(sectionIdSumCnMap)
                        .pureSectionIdSumBmiMap(sectionIdSumCnMap)
                        .build());
            }
        }
        return rematchGenMap;
    }

    public List<ContractSectionCnRmVo> getDailySectionSumMatchedRm(Date startDate
            , Date endDate
            , Long settlementId
            , List<Long> appGenIdList) {
        List<Map<String, Object>> dailySectionSumList = dailyGenRecordRepository.sumByDateGroupByApplicationLoadIdAndEnergyChargeSectionId(startDate, endDate, settlementId, appGenIdList);

        return convertContractSectionSniBmi(dailySectionSumList);
    }

    private boolean needToRematch(RematchContractSettlementVo contractSettlementVo){
        if(contractSettlementVo.getIdLoadMap().entrySet().stream().filter(Objects::nonNull).anyMatch(idLoadMap->idLoadMap.getValue().getMonthlyNi().compareTo(BigDecimal.ZERO) > 0)){
            if(contractSettlementVo.getIdLoadMap().entrySet().stream().filter(Objects::nonNull).anyMatch(idLoadMap->idLoadMap.getValue().getSectionIdSumSniMap().values().stream().anyMatch(cn->cn.compareTo(BigDecimal.ZERO) > 0))){
                return (contractSettlementVo.getIdGeneratorMap().entrySet().stream().anyMatch(IdGeneratorMap -> IdGeneratorMap.getValue().getSectionIdSumBmiMap().values().stream().anyMatch(rm -> rm.compareTo(BigDecimal.ZERO) > 0)));
            }
        }
        return false;
    }

    private boolean rematch(Date startDate
            , Long settlementId
            , List<Integer> sectionIdList
            , RematchContractSettlementVo contractSettlementVo){
//            , Map<Long, RematchContractSettlementVo> appContractSettlementMap){
//
        //本合約未媒合[總]發電量(Generator)
        Map<Integer, BigDecimal> sumSectionIdSumBmiMap = sumApplicationGeneratorBmi(sectionIdList, new ArrayList<>(contractSettlementVo.getIdGeneratorMap().values()));

        //若是有關聯合約，必須重計算未媒合用電量 (不用重新計算，因為，在第一階段[每15分鐘]時，已經分配好每個契約轉供用電（Uni）
//        for(Map.Entry<Long, RematchContractLoadSniVo> rematchContractLoadSni : contractSettlementVo.getIdLoadMap().entrySet()) {
//            if(CollectionUtils.isNotEmpty(rematchContractLoadSni.getValue().getRelationAppGenIdList())) {
//                rematchContractLoadSni.getValue().setSectionIdSumSniMap(recalculateUnmatchedSniByBmi(sectionIdList
//                        , rematchContractLoadSni.getValue()
//                        , sumSectionIdSumBmiMap
//                        , appContractSettlementMap));
//            }
//        }

        //本合約未媒合[總]用電量(Load)
        Map<Integer, BigDecimal> sumSectionIdSumSniMap = sumApplicationLoadSni(sectionIdList, new ArrayList<>(contractSettlementVo.getIdLoadMap().values()));
        //正式計算
        //可再媒合發電總量(Generator)
        Map<Integer, BigDecimal> sumUnmatchedBmiMap = rematchedSumCapacity(sectionIdList, sumSectionIdSumBmiMap, sumSectionIdSumSniMap);

        //分配個時段轉供量(Load)
        List<SimulationTempApplicationMonthlyRematchLoadRecord> rematchedLoadRecordList = dispatchSectionLoadSni(startDate, settlementId, contractSettlementVo, sumUnmatchedBmiMap, sumSectionIdSumSniMap);

        //分配個時段轉供量(Generator)
        return dispatchSectionGeneratorBmi(startDate, settlementId, contractSettlementVo, sumSectionIdSumBmiMap, rematchedLoadRecordList);
    }

    private Map<Integer, BigDecimal> sumApplicationGeneratorBmi(List<Integer> sectionIdList, List<RematchContractGeneratorBmiVo> genBmiList){
        Map<Integer, BigDecimal> sectionRmMap = new HashMap<>();

        for(Integer sectionId : sectionIdList){
            sectionRmMap.put(sectionId, BigDecimal.ZERO);
        }

        for(RematchContractGeneratorBmiVo contractGeneratorRmVo : genBmiList){
            for(Map.Entry<Integer, BigDecimal> sectionRm :  sectionRmMap.entrySet()){
                sectionRm.setValue(sectionRm.getValue().add(contractGeneratorRmVo.getSectionIdSumBmiMap().getOrDefault(sectionRm.getKey(), BigDecimal.ZERO)));
            }
        }
        return sectionRmMap;
    }

    private Map<Integer, BigDecimal> sumApplicationLoadSni(List<Integer> sectionIdList, List<RematchContractLoadSniVo> loadSniVoList){
        Map<Integer, BigDecimal> sectionCnMap = new HashMap<>();

        for(Integer sectionId : sectionIdList){
            sectionCnMap.put(sectionId, BigDecimal.ZERO);
        }

        for(RematchContractLoadSniVo loadSniVo : loadSniVoList){
            for(Map.Entry<Integer, BigDecimal> sectionCn :  sectionCnMap.entrySet()){
                sectionCn.setValue(sectionCn.getValue().add(loadSniVo.getSectionIdSumSniMap().getOrDefault(sectionCn.getKey(), BigDecimal.ZERO)));
            }
        }
        return sectionCnMap;
    }

    private Map<Integer, BigDecimal> rematchedSumCapacity(List<Integer> sectionIdList
            , Map<Integer, BigDecimal> sumSectionIdSumBmiMap
            , Map<Integer, BigDecimal> sumSectionIdSumSniMap){
        Map<Integer, BigDecimal> sumCapacityMap = new HashMap<>();
        for(Integer sectionId : sectionIdList){
            sumCapacityMap.put(sectionId, sumSectionIdSumBmiMap.get(sectionId).min(sumSectionIdSumSniMap.get(sectionId)));
        }
        return sumCapacityMap;
    }

    private List<SimulationTempApplicationMonthlyRematchLoadRecord> dispatchSectionLoadSni(Date startDate
            , Long settlementId
            , RematchContractSettlementVo contractSettlementVo
            , Map<Integer, BigDecimal> sumUnmatchedBmiMap
            , Map<Integer, BigDecimal> sumSectionIdSumSniMap){
        List<SimulationTempApplicationMonthlyRematchLoadRecord> rematchedLoadRecordList = new ArrayList<>();

        for(RematchContractLoadSniVo rematchContractLoadSni : contractSettlementVo.getIdLoadMap().values()) {
            Map<Integer, BigDecimal> sectionIdSumSniMap = rematchContractLoadSni.getSectionIdSumSniMap();
            Map<Integer, BigDecimal> pureSectionIdSumSniMap = rematchContractLoadSni.getPureSectionIdSumSniMap();
            //處理個別Load
            for(Map.Entry<Integer, BigDecimal> sectionIdSumSni : sectionIdSumSniMap.entrySet()) {
                //個別Load sni
                BigDecimal sni = sectionIdSumSni.getValue();
                //未經重新計算的個別Load sni
                BigDecimal pureSni = pureSectionIdSumSniMap.get(sectionIdSumSni.getKey());
                //Load總和sni
                BigDecimal sumSni = sumSectionIdSumSniMap.getOrDefault(sectionIdSumSni.getKey(), BigDecimal.ZERO);
                BigDecimal rematchedSni = BigDecimal.ZERO;
                if(0 < sni.compareTo(BigDecimal.ZERO)
                        && 0 < sumSni.compareTo(BigDecimal.ZERO)) {
                    //可媒合Generator發電量
                    BigDecimal sumBmi = sumUnmatchedBmiMap.get(sectionIdSumSni.getKey());
                    rematchedSni = (sumBmi.multiply(sni)).divide(sumSni, BIGDECIMAL_PRECISION, RoundingMode.DOWN);
                }
                rematchedLoadRecordList.add(SimulationTempApplicationMonthlyRematchLoadRecord.builder().loadId(rematchContractLoadSni.getAppLoadId())
                        .energyChargeSectionId(sectionIdSumSni.getKey())
                        .date(startDate)
                        .settlementId(settlementId)
                        .matchedCn(rematchedSni.stripTrailingZeros())
                        .unmatchedCn(pureSni.subtract(rematchedSni).stripTrailingZeros())
                        .build());
            }
        }
        if(CollectionUtils.isNotEmpty(rematchedLoadRecordList)){
            List<SimulationTempApplicationMonthlyRematchLoadRecord> storedRematchedLoadRecordList = rematchLoadRecordRepository.saveAll(rematchedLoadRecordList);
            if(storedRematchedLoadRecordList.size() == rematchedLoadRecordList.size()){
                return storedRematchedLoadRecordList;
            }
            log.error("Fail : Rematch application load record");
        }
        return rematchedLoadRecordList;
    }

    private boolean dispatchSectionGeneratorBmi(Date startDate
            , Long settlementId
            , RematchContractSettlementVo contractSettlementVo
            , Map<Integer, BigDecimal> sumSectionIdSumBmiMap
            , List<SimulationTempApplicationMonthlyRematchLoadRecord> rematchedLoadRecordList){

        List<SimulationTempApplicationMonthlyRematchGeneratorRecord>  rematchGeneratorRecordList = new ArrayList<>();
        List<SimulationTempApplicationMonthlyRematchGeneratorLoadRecord> rematchGeneratorLoadRecordList = new ArrayList<>();

        Map<Long, Map<Integer, BigDecimal>> appLoadIdSectionSni = rematchedLoadRecordList.stream().collect(groupingBy(SimulationTempApplicationMonthlyRematchLoadRecord::getLoadId
                , Collectors.toMap(SimulationTempApplicationMonthlyRematchLoadRecord::getEnergyChargeSectionId, SimulationTempApplicationMonthlyRematchLoadRecord::getMatchedCn)));

        List<Long> loadIdList = contractSettlementVo.getIdLoadMap().keySet().stream().toList();
        for(RematchContractGeneratorBmiVo rematchContractGeneratorBmi : contractSettlementVo.getIdGeneratorMap().values()) {
            Map<Integer, BigDecimal> sectionIdSumBmiMap = rematchContractGeneratorBmi.getSectionIdSumBmiMap();
            Map<Integer, BigDecimal> pureSectionIdSumBmiMap = rematchContractGeneratorBmi.getPureSectionIdSumBmiMap();

            //處理個別Generator
            for(Map.Entry<Integer, BigDecimal> sectionIdSumBmi : sectionIdSumBmiMap.entrySet()){
                Integer sectionId = sectionIdSumBmi.getKey();
                //個別Generator Bmi
                BigDecimal bmi = sectionIdSumBmi.getValue();
                //個別未經經重新計算 Generator Bmi
                BigDecimal pureBmi = pureSectionIdSumBmiMap.get(sectionId);
                //Generator總和Bmi
                BigDecimal sumBmi = sumSectionIdSumBmiMap.get(sectionId);
                BigDecimal rematchedSumBmi = BigDecimal.ZERO;
                for(Long loadId : loadIdList) {
                    BigDecimal rematchedBmi = BigDecimal.ZERO;
                    BigDecimal sni = appLoadIdSectionSni.get(loadId).get(sectionId);
                    if(null != sni
                            && 0 < sni.compareTo(BigDecimal.ZERO)
                            && 0 < sumBmi.compareTo(BigDecimal.ZERO)
                            && 0 < bmi.compareTo(BigDecimal.ZERO)) {
                        rematchedBmi = sni.multiply(bmi).divide(sumBmi, BIGDECIMAL_PRECISION, RoundingMode.DOWN);
                    }
                    rematchGeneratorLoadRecordList.add(SimulationTempApplicationMonthlyRematchGeneratorLoadRecord.builder()
                            .date(startDate)
                            .settlementId(settlementId)
                            .loadId(loadId)
                            .generatorId(rematchContractGeneratorBmi.getAppGeneratorId())
                            .energyChargeSectionId(sectionId)
                            .matchedRm(rematchedBmi)
                            .build());
                    rematchedSumBmi = rematchedSumBmi.add(rematchedBmi);
                }
                rematchGeneratorRecordList.add(SimulationTempApplicationMonthlyRematchGeneratorRecord.builder()
                        .date(startDate)
                        .settlementId(settlementId)
                        .generatorId(rematchContractGeneratorBmi.getAppGeneratorId())
                        .energyChargeSectionId(sectionId)
                        .matchedRm(rematchedSumBmi.stripTrailingZeros())
                        .unmatchedRm(pureBmi.subtract(rematchedSumBmi).stripTrailingZeros())
                        .build());
            }
        }

        if(CollectionUtils.isNotEmpty(rematchGeneratorLoadRecordList)){
            List<SimulationTempApplicationMonthlyRematchGeneratorLoadRecord> storedRematchedGeneratorLoadRecordList = rematchGenLoadRecordRepository.saveAll(rematchGeneratorLoadRecordList);
            if(storedRematchedGeneratorLoadRecordList.size() != rematchGeneratorLoadRecordList.size()){
                log.error("Fail : Rematch application generator load record");
                return false;
            }
        }

        if(CollectionUtils.isNotEmpty(rematchGeneratorRecordList)){
            List<SimulationTempApplicationMonthlyRematchGeneratorRecord> storedRematchedGeneratorRecordList = rematchGenRecordRepository.saveAll(rematchGeneratorRecordList);
            if(storedRematchedGeneratorRecordList.size() != rematchGeneratorRecordList.size()) {
                log.error("Fail : Rematch application generator record");
                return false;
            }
        }
        return true;
    }

    private void saveMonthlyRecord(TimeIntervalVo timeIntervalVo
            , Long settlementId
            , List<Long> rematchGenIdList
            , List<Long> rematchLoadIdList
            , List<Long> noRematchGenIdList
            , List<Long> noRematchLoadIdList){

        if(CollectionUtils.isNotEmpty(rematchGenIdList)) {
            saveMonthlyRematchRecordByDateIntervalAndSettlementId(timeIntervalVo.getStartTime().getTime()
                    , timeIntervalVo.getEndTime().getTime()
                    , settlementId, rematchGenIdList, rematchLoadIdList);
        }
        if(CollectionUtils.isNotEmpty(noRematchGenIdList)) {
            saveMonthlyRecordByDateIntervalAndSettlementId(timeIntervalVo.getStartTime().getTime()
                    , timeIntervalVo.getEndTime().getTime()
                    , settlementId, noRematchGenIdList, noRematchLoadIdList);
        }
    }

    private boolean saveMonthlyRematchRecordByDateIntervalAndSettlementId(Date startDate, Date endDate, Long settlementId, List<Long> appGenIdList, List<Long> appLoadList){
        try{
            monthlyLoadRecordRepository.saveByRematchRecordAndDateIntervalAndSettlementId(startDate, endDate, settlementId, appLoadList);
            monthlyGenRecordRepository.saveByRematchRecordAndDateIntervalAndSettlementId(startDate, endDate, settlementId, appGenIdList);
            monthlyGenLoadRecordRepository.saveByRematchRecordAndDateIntervalAndSettlementId(startDate, endDate, settlementId, appGenIdList, appLoadList);
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    private boolean saveMonthlyRecordByDateIntervalAndSettlementId(Date startDate, Date endDate, Long settlementId, List<Long> appGenIdList, List<Long> appLoadList){
        try{
            monthlyLoadRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId, appLoadList);
            monthlyGenRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId, appGenIdList);
            monthlyGenLoadRecordRepository.saveByDateIntervalAndSettlementId(startDate, endDate, settlementId, appGenIdList, appLoadList);
        }catch (Exception exception){
            log.error(exception.getMessage());
            return false;
        }
        return true;
    }

    private Map<Integer, BigDecimal> recalculateUnmatchedSniByMonthlyNi(Map<Integer, BigDecimal> sectionIdSumSniMap, BigDecimal monthlyNi) {
        if (0 < monthlyNi.compareTo(BigDecimal.ZERO)) {
            BigDecimal sumSni = sectionIdSumSniMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            //用電量大於目前月上限量
            if (0 < sumSni.compareTo(monthlyNi)) {
                Map<Integer, BigDecimal> sectionAvailSni = new HashMap<>();
                BigDecimal ratio = monthlyNi.divide(sumSni, BIGDECIMAL_PRECISION, RoundingMode.DOWN);
                for (Map.Entry<Integer, BigDecimal> sectionIdSumSni : sectionIdSumSniMap.entrySet()) {
                    sectionAvailSni.put(sectionIdSumSni.getKey(), (sectionIdSumSni.getValue().multiply(ratio)).stripTrailingZeros());
                }
                return sectionAvailSni;
            }
            return sectionIdSumSniMap;
        }else {
            //monthlyNi = 0
            Map<Integer, BigDecimal> sectionSniToZero = new HashMap<>();
            for (Map.Entry<Integer, BigDecimal> sectionIdSumSni : sectionIdSumSniMap.entrySet()) {
                sectionSniToZero.put(sectionIdSumSni.getKey(), BigDecimal.ZERO);
            }
            return sectionSniToZero;
        }
    }

}
