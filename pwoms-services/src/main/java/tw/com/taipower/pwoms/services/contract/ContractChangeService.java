package tw.com.taipower.pwoms.services.contract;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.enums.ALGKeepEnum;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.logger.CustomLog;
import tw.com.taipower.pwoms.services.BaseService;
import tw.com.taipower.pwoms.services.entitymanage.GeneratorService;
import tw.com.taipower.pwoms.services.entitymanage.LoadService;
import tw.com.taipower.pwoms.services.enumclass.*;
import tw.com.taipower.pwoms.services.flowcontrol.WorkflowStage;
import tw.com.taipower.pwoms.services.flowcontrol.*;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.utils.VoUtils;
import tw.com.taipower.pwoms.services.vo.contract.ContractChangeRecordOfGenCapacityVO;
import tw.com.taipower.pwoms.services.vo.contract.ContractChangeRecordOfGenVO;
import tw.com.taipower.pwoms.services.vo.contract.ContractChangeRecordOfLoadVO;
import tw.com.taipower.pwoms.services.vo.contract.ContractChangeRecordVO;
import tw.com.taipower.pwoms.services.vo.generated.ApplicationChangeRecordVO;
import tw.com.taipower.pwoms.services.vo.generated.ApplicationVO;
import tw.com.taipower.pwoms.services.vo.generated.GeneratorEntityVO;
import tw.com.taipower.pwoms.services.vo.generated.LoadEntityVO;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static tw.com.taipower.pwoms.services.flowcontrol.WorkflowGroup.*;

/**
 * 案件契約管理服務
 *
 * @class: ApplicantEntityService
 * @author: linyu-sheng
 * @version: 0.0.0
 * @since: 2024/5/3
 * @see:
 */
@Service
@CustomLog
public class ContractChangeService extends BaseService {

    @Autowired
    private WorkflowStepMappingRepository workflowStepMappingRepository;

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private ApplicantEntityRepository applicantEntityRepository;

    @Autowired
    private ApplicantEntityContactRepository applicantEntityContactRepository;

    @Autowired
    private ApplicationGeneratorRepository applicationGeneratorRepository;

    @Autowired
    private ApplicationLoadRepository applicationLoadRepository;

    @Autowired
    private GeneratorEntityRepository generatorEntityRepository;

    @Autowired
    private LoadEntityRepository loadEntityRepository;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private ApplicationDocumentRepository applicationDocumentRepository;

    @Autowired
    private GeneratorService generatorService;

    @Autowired
    private GeneratorEntityDocumentRepository generatorEntityDocumentRepository;

    @Autowired
    private ContractDocumentService contractDocumentService;

    @Autowired
    private ContractTxService contractTxService;

    @Autowired
    private FuelTypeRepository fuelTypeRepository;

    @Autowired
    ApplicationGeneratorLoadTypeRepository applicationGeneratorLoadTypeRepository;

    @Autowired
    PwFuelTypeRepository pwFuelTypeRepository;

    @Autowired
    PriceTypeRepository priceTypeRepository;

    @Autowired
    PwFuelRateRepository pwFuelRateRepository;

    @Autowired
    private ContractService contractService;

    @Autowired
    private ApplicationStage2TxService applicationStage2TxService;

    @Autowired
    private ApplicationWorkflowRepository applicationWorkflowRepository;

    @Autowired
    private WorkflowService workflowService;


    @Autowired
    private LoadService loadService;

    @Autowired
    private ContractTerminateService contractTerminateService;

    @Autowired
    private ApplicationTxService applicationTxService;

    /**
     * 終止契約更新，變更為跑流程，無法直接更新
     *
     * @param applicationVO
     * @param userId
     */
    @Transactional
    public void updateTerminateDate(ApplicationVO applicationVO, Long userId) {
        var application = applicationRepository.findById(applicationVO.getId()).orElseThrow();
        application.setPreContractedEnd(applicationVO.getPreContractedEnd());
        application.setApplyContractedEnd(new Date());
        saveApplication(application);
        contractTerminateService.startTerminateWorkflow(application, userId);

    }

    /**
     * 修約時，應該要重置的契約/計畫書狀態
     *
     * @param applicationVO
     */
    public void resetContractInfo(ApplicationVO applicationVO) {
        var optApplicant = applicantEntityRepository.findById(applicationVO.getApplicantId());
        if (optApplicant.isPresent()) {
            var applicant = optApplicant.get();
            var yearMonth = DateUtils.getYearMonth();
            applicationVO.setNo(applicationTxService.applicationNo(applicant.getTaxId(), yearMonth));
        }
        applicationVO.setOnlineAt(null);
        applicationVO.setContractedStart(null);
        applicationVO.setContractedEnd(null);

    }

    /**
     * 檢查轉供pmi範圍是否合理
     *
     * @param applicationVO
     */
    public void checkApplicationPmiRange(ApplicationVO applicationVO) {
        List<String> errors = new ArrayList<>();
        applicationVO.getApplicationGeneratorList().forEach(ag -> {
            if (ag.getPmi() == null) {
                errors.add(String.format("%s %s 未輸入轉供比例", ag.getGeneratorEntity().getNbsCustomerNumber(),
                        ag.getGeneratorEntity().getName()));
            } else {
                /**
                 * ag.getPmi() 只能介於0~100之間
                 */

                if (ag.getPmi().compareTo(BigDecimal.ZERO) < 0) {
                    errors.add(String.format("%s %s 轉供比例不得為負數", ag.getGeneratorEntity().getNbsCustomerNumber(),
                            ag.getGeneratorEntity().getName()));
                } else if (ag.getPmi().compareTo(BigDecimal.valueOf(100)) > 0) {
                    errors.add(
                            String.format("%s %s 轉供比例不得超過100", ag.getGeneratorEntity().getNbsCustomerNumber(),
                                    ag.getGeneratorEntity().getName()));
                }
            }
        });
        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join("\n", errors));
        }
    }

    /**
     * 無審查的修約
     *
     * @param applicationVO
     * @param userId
     */
    @Transactional
    public void updateContractWithoutReview(ApplicationVO applicationVO, Long userId) throws IOException {
        keepALGRelations(applicationVO);
        checkApplicationPmiRange(applicationVO);

        /**
         * 更新版本號，複製整張契約
         */
        var currentVersion =
                applicationRepository.findMaxUseVersionWithContractNo(applicationVO.getContractNo()).orElse(0);
        applicationVO.setVersion(String.format("%03d", currentVersion + 1));
        applicationVO.setStage3Status(ApplicationStage3Status.SUBMITTED.getValue());
        applicationVO.setParentId(applicationVO.getId());
        applicationVO.setId(null);
        applicationVO.setModifiedAt(null);
        applicationVO.setContractRequestAt(new Date());
        applicationVO.setSubmittedAt(new Date());
        applicationVO.setOnlineAt(null);
        resetContractInfo(applicationVO);

        var application = VoUtils.toEntity(applicationVO, Application.class);
        try {
            ObjectMapper mapper = new ObjectMapper();
            var changeRecordJson = mapper.writeValueAsString(applicationVO.getChangeRecord());
            application.setChangeRecordJson(changeRecordJson);
        } catch (Throwable e) {

        }
        applicationVO.setStage3Status(ApplicationStage3Status.NOT_YET.getValue());
//        application = applicationRepository.save(application);
        application = saveApplication(application);
        Application finalApplication = application;
        applicationVO.getApplicationGeneratorList().forEach(a -> {
            a.setId(null);
            a.setApplicationId(finalApplication.getId());
            if (a.getPmi() == null) {
                a.setPmi(BigDecimal.ZERO);
            }
        });
        applicationVO.getApplicationLoadList().forEach(a -> {
            a.setId(null);
            a.setApplicationId(finalApplication.getId());
        });
        var ags = applicationVO.getApplicationGeneratorList().stream().map(a -> VoUtils.toEntity(a,
                ApplicationGenerator.class)).toList();
        var als = applicationVO.getApplicationLoadList().stream().map(a -> VoUtils.toEntity(a,
                ApplicationLoad.class)).toList();
        applicationGeneratorRepository.saveAll(ags);
        applicationLoadRepository.saveAll(als);

        /**
         * 重新計算輸配電狀態，決定契約單位
         */
        applicationStage2TxService.assignApplicationContractDept(application.getId());

        /**
         * 產生未用印契約檔案
         */
        contractService.createContractNotSignDocument(application.getId(), userId);
        /**
         * 檢查，並開始簽約流程
         */
        contractService.submitCheck(application.getId(),
                application.getParentId() == null ? 0L : application.getParentId()
                , userId);

    }

//    /**
//     * VoUtils.toEntity不能處理太複雜的結構，所以需要這個來做手動處理
//     * @param vo
//     * @return
//     */
//    public ApplicationChangeRecord convert(ApplicationChangeRecordVO vo) {
//        var changeRecord = ApplicationChangeRecord.builder().build();
//        if (vo.getAddGen() != null) {
//            changeRecord.setAddGen(vo.getAddGen());
//        } else {
//            changeRecord.setAddGen(new ArrayList<>());
//        }
//        if (vo.getAddLoad() != null) {
//            changeRecord.setAddLoad(vo.getAddLoad());
//        } else {
//            changeRecord.setAddLoad(new ArrayList<>());
//        }
//        if (vo.getRemoveGen() != null) {
//            changeRecord.setRemoveGen(vo.getRemoveGen());
//        } else {
//            changeRecord.setRemoveGen(new ArrayList<>());
//        }
//        if (vo.getRemoveLoad() != null) {
//            changeRecord.setRemoveLoad(vo.getRemoveLoad());
//        } else {
//            changeRecord.setRemoveLoad(new ArrayList<>());
//        }
//        if (vo.getGenPmi() != null) {
//            var data =
//                    vo.getGenPmi().stream().map(a -> {
//                                ApplicationChangeRecordOfGen gen = new ApplicationChangeRecordOfGen();
//                                gen.setId(a.getId());
//                                var before = VoUtils.toEntity(a.getBefore(), ApplicationChangeRecordOfGenPmi.class);
//                                var after = VoUtils.toEntity(a.getAfter(), ApplicationChangeRecordOfGenPmi.class);
//                                gen.setBefore(before);
//                                gen.setAfter(after);
//                                return gen;
//                            }
//                    ).toList();
//            changeRecord.setGenPmi(data);
//        } else {
//            changeRecord.setGenPmi(new ArrayList<>());
//        }
//        if (vo.getLoadCap() != null) {
//            var data =
//                    vo.getLoadCap().stream().map(a -> {
//                                ApplicationChangeRecordOfLoad load = new ApplicationChangeRecordOfLoad();
//                                load.setId(a.getId());
//                                var before = VoUtils.toEntity(a.getBefore(), ApplicationChangeRecordOfLoadCap.class);
//                                var after = VoUtils.toEntity(a.getAfter(), ApplicationChangeRecordOfLoadCap.class);
//                                load.setBefore(before);
//                                load.setAfter(after);
//                                return load;
//                            }
//                    ).toList();
//            changeRecord.setLoadCap(data);
//        } else {
//            changeRecord.setLoadCap(new ArrayList<>());
//        }
//
//        if (vo.getGenCapacity() != null) {
//            var data = vo.getGenCapacity().stream().map(a -> {
//                ApplicationChangeRecordOfGenCapacity gen = new ApplicationChangeRecordOfGenCapacity();
//                gen.setId(a.getId());
//                var before = VoUtils.toEntity(a.getBefore(), ApplicationChangeRecordOfGenCapacityRecord.class);
//                var after = VoUtils.toEntity(a.getAfter(), ApplicationChangeRecordOfGenCapacityRecord.class);
//                gen.setBefore(before);
//                gen.setAfter(after);
//                return gen;
//            }).toList();
//            changeRecord.setGenCapacity(data);
//        } else {
//            changeRecord.setGenCapacity(new ArrayList<>());
//        }
//        return changeRecord;
//    }

    public void keepALGRelations(ApplicationVO applicationVO) {
        var newAls = applicationVO.getApplicationLoadList().stream().filter(
                a -> a.getKeep() == null || a.getKeep().equals(ALGKeepEnum.KEEP_USING)).toList();
        var newAgs = applicationVO.getApplicationGeneratorList().stream().filter(
                a -> a.getKeep() == null || a.getKeep().equals(ALGKeepEnum.KEEP_USING)).toList();
        applicationVO.setApplicationGeneratorList(newAgs);
        applicationVO.setApplicationLoadList(newAls);
    }

    /**
     * 修約流程，包含審核項目，但具體包含哪些不知道
     *
     * @param applicationVO
     * @param userId
     */
    @Transactional
    public void updateContractWithReview(ApplicationVO applicationVO, Long userId) {
        checkApplicationPmiRange(applicationVO);
        keepALGRelations(applicationVO);
        /**
         * 更新版本號，複製整張契約
         */
        List<Long> reviewGeneratorIds = new ArrayList<>();
        List<Long> reviewLoadIds = new ArrayList<>();
        var currentVersion =
                applicationRepository.findMaxUseVersionWithContractNo(applicationVO.getContractNo()).orElse(0);
        applicationVO.setVersion(String.format("%03d", currentVersion + 1));
        applicationVO.setSubmittedAt(new Date());
        applicationVO.setStage2Status(ApplicationStage2Status.ENTITY_IN_WORKFLOW.getValue());
        applicationVO.setStage3Status(ApplicationStage3Status.NOT_YET.getValue());
        applicationVO.setParentId(applicationVO.getId());
        applicationVO.setModifiedAt(null);
        applicationVO.setParentId(applicationVO.getId());
        applicationVO.setId(null);
        // reset 新建契約目前步驟位置
        applicationVO.setContractStep(null);
        resetContractInfo(applicationVO);

        var application = VoUtils.toEntity(applicationVO, Application.class);
        try {
            ObjectMapper mapper = new ObjectMapper();
            var changeRecordJson = mapper.writeValueAsString(applicationVO.getChangeRecord());
            application.setChangeRecordJson(changeRecordJson);
        } catch (Throwable e) {

        }
        application = saveApplication(application);
//        application = applicationRepository.save(application);
        ApplicationChangeRecord changeRecord = new ApplicationChangeRecord();
        try {
            ObjectMapper mapper = new ObjectMapper();
            changeRecord = mapper.readValue(application.getChangeRecordJson(), ApplicationChangeRecord.class);
        } catch (Throwable ex) {

        }
        var capacityChangedGenertorIds = changeRecord.getGenCapacity().stream().map(a -> a.getId()).toList();
        var generatorIds = application.getApplicationGeneratorList().stream().map(a -> a.getGeneratorId()).toList();
        var loadIds = application.getApplicationLoadList().stream().map(a -> a.getLoadId()).toList();
        var passedAgs =
                applicationGeneratorRepository.findGeneatrorIsInContractAndNotChange(generatorIds, new Date());
        var passedAls = applicationLoadRepository.findLoadIsInContractAndNotChange(loadIds, new Date());

        Application finalApplication = application;
        applicationVO.getApplicationGeneratorList().forEach(a -> {
            if (a.getId() == null) {
                // 新增加的，所以需要走審核流程
//                a.setIsInContractChange(true);
            }
            // 查詢變更紀錄是否有變更裝置容量
            if (capacityChangedGenertorIds.contains(a.getGeneratorId())) {
                reviewGeneratorIds.add(a.getGeneratorId());
                a.setIsInContractChange(true);
                try {
                    generatorService.setTempGeneratorEntity(a.getGeneratorEntity(), userId);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            } else {
                var optPassAg =
                        passedAgs.stream().filter(
                                passedAg -> passedAg.getGeneratorId().equals(a.getGeneratorId())).findFirst();
                if (!optPassAg.isPresent()) {
                    reviewGeneratorIds.add(a.getGeneratorId());
                    a.setIsInContractChange(true);
                }
            }
            a.setId(null);
            a.setApplicationId(finalApplication.getId());
            if (a.getPmi() == null) {
                a.setPmi(BigDecimal.ZERO);
            }
        });


        applicationVO.getApplicationLoadList().forEach(a -> {
            if (a.getId() == null) {
                // 新增加的，所以需要走審核流程
//                reviewLoadIds.add(a.getLoadId());
//                a.setIsInContractChange(true);
            }
            a.setId(null);
            // 沒有電壓層級變更的話，用電端很多狀況都不需要審查，除了新增，但如果新增卻有已經在轉直供中的契約，走快速通關
            if (false) {
                reviewLoadIds.add(a.getLoadId());
                a.setIsInContractChange(true);
                try {
                    loadService.setTempLoadEntity(a.getLoadEntity(), userId);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            } else {
                var optPassAg =
                        passedAls.stream().filter(
                                passedAl -> passedAl.getLoadId().equals(a.getLoadId())).findFirst();
                if (!optPassAg.isPresent()) {
                    reviewLoadIds.add(a.getLoadId());
                    a.setIsInContractChange(true);
                }
            }

            a.setApplicationId(finalApplication.getId());
        });

        var ags = applicationVO.getApplicationGeneratorList().stream().map(a -> VoUtils.toEntity(a,
                ApplicationGenerator.class)).toList();
        for (int index = 0; index < ags.size(); index++) {
            ags.get(index).setOrder(index + 1);
        }
        var als = applicationVO.getApplicationLoadList().stream().map(a -> VoUtils.toEntity(a,
                ApplicationLoad.class)).toList();
        for (int index = 0; index < als.size(); index++) {
            als.get(index).setOrder(index + 1);
        }
        applicationGeneratorRepository.saveAll(ags);
        applicationLoadRepository.saveAll(als);

        /**
         * 複製原本契約的審核流程，但是要排除掉Ｘ以前的步驟
         */
        List<WorkflowStepMapping> totalSteps = new ArrayList<>();
        totalSteps.addAll(workflowStepMappingRepository.findByCustom1(PLAN, application.getSubmittedAt(), 2));
        totalSteps.addAll(workflowStepMappingRepository.findByCustom1(GENERATOR, application.getSubmittedAt(), 2));
        totalSteps.addAll(workflowStepMappingRepository.findByCustom1(LOAD, application.getSubmittedAt(), 2));

        totalSteps = totalSteps.stream().filter(a -> a.getStepSeq() <= 2).toList();
        Application finalApplication1 = application;
        List<ApplicationWorkflow> allApplicationWorkflows = new ArrayList();
        List<ApplicationWorkflow> saveDatas = new ArrayList<>();
        {
            /**
             * 不用複製原本審查結果的原因是，有可能單據是從舊平台來的，他不會有紀錄可以複製
             */
            /**
             * 先處理計畫書層級
             */
            {
                var planWorkflows = totalSteps.stream().filter(a -> PLAN.equals(a.getStepGroup())).map(step -> {
                    var mainUnitVO =
                            workflowService.getMainUnitAndAccounts(step, finalApplication1);
                    var useUnit =
                            mainUnitVO.getReceivers().stream().map(
                                    a -> a.getCompanyUnit().getId()).findFirst().orElseThrow();
                    return ApplicationWorkflow.builder()
                            .stepId(step.getId())
                            .applicationId(finalApplication1.getId())
                            .status(ApplicationWorkflowStatus.COMPLETE.getValue()) // 已經完成
                            .tpcDeptId(useUnit) // 暫時使用，之後再變更
                            .createdAt(new Date())
                            .stepMapping(step)
                            .approvedFrom(null)
                            .modifiedBy(userId)
                            .build();
                }).toList();
                saveDatas.addAll(planWorkflows);
            }
            {
                var genSteps = totalSteps.stream().filter(a -> GENERATOR.equals(a.getStepGroup())).toList();
                // 只處理不用審核的
                ags.stream().filter(a -> !reviewGeneratorIds.contains(
                        a.getGeneratorId())).forEach(ag -> {

                    var genEntity = generatorEntityRepository.findById(ag.getGeneratorId()).orElseThrow();
                    var genAws =
                            genSteps.stream().filter(step -> step.getSubSeq() == null || step.getSubSeq() > 0).map(
                                    step -> {
                                        var useUnit = getStepRefferDeptId(step, finalApplication1, genEntity, null);
                                        return ApplicationWorkflow.builder()
                                                .stepId(step.getId())
                                                .applicationId(finalApplication1.getId())
                                                .status(ApplicationWorkflowStatus.COMPLETE.getValue()) // 已經完成
                                                .tpcDeptId(useUnit) // 暫時使用，之後再變更
                                                .createdAt(new Date())
                                                .stepMapping(step)
                                                .entityType(GENERATOR)
                                                .entityId(ag.getGeneratorId())
                                                .approvedFrom(null)
                                                .modifiedBy(userId)
                                                .build();
                                    }).toList();
                    saveDatas.addAll(genAws);


                });
            }

            {
                var loadSteps = totalSteps.stream().filter(a -> LOAD.equals(a.getStepGroup())).toList();
                // 只處理不用審核的
                als.stream().filter(a -> !reviewLoadIds.contains(
                        a.getLoadId())).forEach(al -> {
                    var loadEntity = loadEntityRepository.findById(al.getLoadId()).orElseThrow();
                    var loadAws =
                            loadSteps.stream().filter(step -> step.getSubSeq() == null || step.getSubSeq() > 0).map(
                                    step -> {
                                        var useUnit = getStepRefferDeptId(step, finalApplication1, null, loadEntity);
                                        return ApplicationWorkflow.builder()
                                                .stepId(step.getId())
                                                .applicationId(finalApplication1.getId())
                                                .status(ApplicationWorkflowStatus.COMPLETE.getValue()) // 已經完成
                                                .tpcDeptId(useUnit) // 暫時使用，之後再變更
                                                .createdAt(new Date())
                                                .stepMapping(step)
                                                .entityType(LOAD)
                                                .entityId(al.getLoadId())
                                                .approvedFrom(null)
                                                .modifiedBy(userId)
                                                .build();
                                    }).toList();
                    saveDatas.addAll(loadAws);
                });
            }
        }

        {
            var gen1stStep =
                    totalSteps.stream().filter(
                            a -> GENERATOR.equals(a.getStepGroup()) && a.getSubSeq() == 1).findFirst().orElseThrow();
            var genReviewWorkflows = reviewGeneratorIds.stream().map(generatorId -> {
                GeneratorEntityVO gen = null;
                try {
                    gen = generatorService.getTempGeneratorEntity(generatorId);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                return ApplicationWorkflow.builder()
                        .stepId(gen1stStep.getId())
                        .applicationId(finalApplication1.getId())
                        .status(ApplicationWorkflowStatus.WAITING.getValue()) // 待審核
                        .tpcDeptId(gen.getTpcDeptId()) // 暫時使用，之後再變更
                        .entityType("G")
                        .entityId(generatorId)
                        .createdAt(new Date())
                        .stepMapping(gen1stStep)
                        .approvedFrom(null)
                        .message(String.format("%s %s", gen.getNbsCustomerNumber(), gen.getName()))
                        .modifiedBy(userId)
                        .build();
            }).toList();
            var allDepts = genReviewWorkflows.stream().map(a -> a.getTpcDeptId()).distinct().toList();
            allDepts.forEach(deptId -> {
                var receivers =
                        workflowService.findAccountsByUpperLevelTpcDeptIdAndRoleId(deptId, gen1stStep.getRoleId());
                var useUnit = receivers.stream().map(a -> a.getCompanyUnit().getId()).findFirst().orElseThrow();
                genReviewWorkflows.stream().filter(a -> a.getTpcDeptId().equals(deptId)).forEach(wf -> {
                    wf.setTpcDeptId(useUnit); // 審核人員真正的所屬單位
                    wf.setReceivers(receivers);
                });
            });
            allApplicationWorkflows.addAll(genReviewWorkflows);
        }
        {
            var load1step = totalSteps.stream().filter(
                    a -> LOAD.equals(a.getStepGroup()) && a.getSubSeq() == 1).findFirst().orElseThrow();
            var loadReviewWorkflows = reviewLoadIds.stream().map(loadId -> {
                LoadEntityVO load = null;
                try {
                    load = loadService.getTempLoadEntity(loadId);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                return ApplicationWorkflow.builder()
                        .stepId(load1step.getId())
                        .applicationId(finalApplication1.getId())
                        .status(ApplicationWorkflowStatus.WAITING.getValue()) // 待審核
                        .tpcDeptId(load.getTpcDeptId()) // 暫時使用，之後再變更
                        .entityType("L")
                        .entityId(loadId)
                        .createdAt(new Date())
                        .stepMapping(load1step)
                        .approvedFrom(null)
                        .message(String.format("%s %s", load.getNbsCustomerNumber(), load.getName()))
                        .modifiedBy(userId)
                        .build();
            }).toList();
            var allDepts = loadReviewWorkflows.stream().map(a -> a.getTpcDeptId()).distinct().toList();
            allDepts.forEach(deptId -> {
                var receivers =
                        workflowService.findAccountsByUpperLevelTpcDeptIdAndRoleId(deptId, load1step.getRoleId());
                var useUnit = receivers.stream().map(a -> a.getCompanyUnit().getId()).findFirst().orElseThrow();
                loadReviewWorkflows.stream().filter(a -> a.getTpcDeptId().equals(deptId)).forEach(wf -> {
                    wf.setTpcDeptId(useUnit); // 審核人員真正的所屬單位
                    wf.setReceivers(receivers);
                });
            });
            allApplicationWorkflows.addAll(loadReviewWorkflows);
        }
        // 儲存不需要審查的審查步驟
        applicationWorkflowRepository.saveAll(saveDatas);

        if (!allApplicationWorkflows.isEmpty()) {
            // 儲存需要審查的審查步驟
            applicationWorkflowRepository.saveAll(allApplicationWorkflows);

            allApplicationWorkflows.forEach(wf -> {
                sendNotificationWithAccount(userId, wf.getReceivers(), wf.getStepMapping().getStepDesc(),
                        String.format("契約編號：%s %s 審查作業待確認", contractTxService.contractNo(finalApplication1),
                                wf.getMessage()), wf.getId(), WorkflowStage.REVIEW);
            });
        } else {
            var lastAw = saveDatas.getLast();
            var step = workflowStepMappingRepository.findById(lastAw.getStepId()).orElseThrow();
            workflowService.processMergePlan(application, step.getStepSeq() + 1, lastAw, lastAw.getModifiedBy());
        }
    }

    /**
     * 比對發電端資訊是否有異動
     *
     * @param vo
     * @return
     */
    public boolean generatorChange(GeneratorEntityVO vo) {
        var formal = generatorService.findOneById(vo.getId()).orElseThrow();
        // 併網方式比對
        if (!formal.getCombineMethod().equals(vo.getCombineMethod())) {
            return true;
        }
        //併網電壓層級
        if (!formal.getVoltage().equals(vo.getVoltage())) {
            return true;
        }
        //責任分界點電壓層級
        if (!formal.getResponsibilityVoltage().equals(vo.getResponsibilityVoltage())) {
            return true;
        }
        generatorService.renewCapacity(vo);
        // 電業執照裝置容量
        if (!formal.getCapacityApplied().equals(vo.getCapacityApplied())) {
            return true;
        }

        // 裝置容量 - 併聯，
        if (!formal.getCapacityUndergo().equals(vo.getCapacityUndergo())) {
            return true;
        }

        return false;
    }

    /**
     * 比對發電端資訊是否有異動
     *
     * @param vo
     * @return
     */
    public boolean loadChange(LoadEntityVO vo) {
        var formal = loadService.findOneById(vo.getId()).orElseThrow();

        //責任分界點電壓層級
        if (!formal.getResponsibilityVoltage().equals(vo.getResponsibilityVoltage())) {
            return true;
        }


        return false;
    }

    /**
     * 取得審查步驟對應部門
     *
     * @param nextStep
     * @param application
     * @param generatorEntity
     * @param loadEntity
     * @return
     * @throws JsonProcessingException
     */
    private Integer getStepRefferDeptId(
            WorkflowStepMapping nextStep,
            Application application,
            GeneratorEntity generatorEntity,
            LoadEntity loadEntity) {
        String message = "";
        Integer useUnit = null;

        var processUnit =
                WorkflowStepMappingProcessUnit.fromValue(nextStep.getProcessUnit());
        if (WorkflowStepMappingProcessUnit.D.equals(processUnit)) {
            if (GENERATOR.equals(nextStep.getStepGroup())) {
                // 發電端
                var gen = generatorEntity;
                message = String.format("%s %s", gen.getNbsCustomerNumber(), gen.getName());
                useUnit = gen.getTpcDeptId();
                if (useUnit == null) {
                    throw new RuntimeException(
                            String.format("%s 負責單位異常", gen.getNbsCustomerNumber() + gen.getName()));
                }
            } else {
                var load = loadEntity;
                message = String.format("%s %s", load.getNbsCustomerNumber(), load.getName());
                useUnit = load.getTpcDeptId();
                if (useUnit == null) {
                    throw new RuntimeException(
                            String.format("%s 負責單位異常", load.getNbsCustomerNumber() + load.getName()));
                }
            }
        } else if (WorkflowStepMappingProcessUnit.S.equals(processUnit)) {
            // 使用步驟規定指定單位
            useUnit = nextStep.getUnitId();
        } else if (WorkflowStepMappingProcessUnit.M.equals(processUnit)) {
            // 使用計畫書主辦區處
            useUnit = application.getTpcDeptId();
        } else {
            throwException(ErrorCode.APPLICATION_WORKFLOW_PROCESS_UNIT_NOT_FOUND);
        }
        var receivers = workflowService.findAccountsByUpperLevelTpcDeptIdAndRoleId(useUnit, nextStep.getRoleId());
        var accountDeptId = receivers.stream().map(a -> a.getCompanyUnit().getId()).findFirst().orElseThrow();
        return accountDeptId;
    }

    public ContractChangeRecordVO searchViewOfChangeRecordByApplicationId(
            Long applicationid) throws JsonProcessingException {
        var application = applicationRepository.findById(applicationid).orElseThrow();
        var cr = application.getChangeRecordJson();
        ObjectMapper mapper = new ObjectMapper();
        try {
            var crVO = mapper.readValue(cr, ApplicationChangeRecordVO.class);
            return searchViewOfChangeRecord(crVO);
        } catch (Throwable ex) {
            log.error(ex, ex);
            return new ContractChangeRecordVO();
        }

    }

    /**
     * 以契約變更紀錄查詢對應發用電端並補齊資訊
     *
     * @param req
     * @return
     */
    public ContractChangeRecordVO searchViewOfChangeRecord(ApplicationChangeRecordVO req) {
        ContractChangeRecordVO recordVO = new ContractChangeRecordVO();
        if (req.getAddGen() != null) {
            var gens =
                    generatorEntityRepository.findAllById(req.getAddGen()).stream().map(
                            gen -> GeneratorEntityVO.builder().id(gen.getId()).name(gen.getName()).nbsCustomerNumber(
                                    gen.getNbsCustomerNumber()).build()).toList();
            recordVO.setAddGen(gens);
        }
        if (req.getAddLoad() != null) {
            var loads =
                    loadEntityRepository.findAllById(req.getAddLoad()).stream().map(
                            load -> LoadEntityVO.builder().id(load.getId()).name(load.getName()).nbsCustomerNumber(
                                    load.getNbsCustomerNumber()).build()).toList();
            recordVO.setAddLoad(loads);
        }
        if (req.getRemoveGen() != null) {
            var gens =
                    generatorEntityRepository.findAllById(req.getRemoveGen()).stream().map(
                            gen -> GeneratorEntityVO.builder().id(gen.getId()).name(gen.getName()).nbsCustomerNumber(
                                    gen.getNbsCustomerNumber()).build()).toList();
            recordVO.setRemoveGen(gens);
        }
        if (req.getRemoveLoad() != null) {
            var loads =
                    loadEntityRepository.findAllById(req.getRemoveLoad()).stream().map(
                            load -> LoadEntityVO.builder().id(load.getId()).name(load.getName()).nbsCustomerNumber(
                                    load.getNbsCustomerNumber()).build()).toList();
            recordVO.setRemoveLoad(loads);
        }
        if (req.getGenPmi() != null) {
            var genIds = req.getGenPmi().stream().map(a -> a.getId()).toList();
            var gens =
                    generatorEntityRepository.findAllById(genIds).stream().map(
                            gen -> {
                                ContractChangeRecordOfGenVO contractChangeRecordOfGenVO =
                                        new ContractChangeRecordOfGenVO();
                                contractChangeRecordOfGenVO.setId(gen.getId());
                                contractChangeRecordOfGenVO.setName(gen.getName());
                                contractChangeRecordOfGenVO.setNbsCustomerNumber(gen.getNbsCustomerNumber());
                                var reqGen =
                                        req.getGenPmi().stream().filter(
                                                a -> a.getId().equals(gen.getId())).findFirst().orElseThrow();
                                contractChangeRecordOfGenVO.setBefore(reqGen.getBefore());
                                contractChangeRecordOfGenVO.setAfter(reqGen.getAfter());
                                return contractChangeRecordOfGenVO;

                            }).toList();
            recordVO.setGenPmi(gens);
        }
        if (req.getLoadCap() != null) {
            var loadIds = req.getLoadCap().stream().map(a -> a.getId()).toList();
            var loads = loadEntityRepository.findAllById(loadIds).stream().map(load -> {
                ContractChangeRecordOfLoadVO contractChangeRecordOfLoadVO = new ContractChangeRecordOfLoadVO();
                contractChangeRecordOfLoadVO.setId(load.getId());
                contractChangeRecordOfLoadVO.setName(load.getName());
                contractChangeRecordOfLoadVO.setNbsCustomerNumber(load.getNbsCustomerNumber());
                var reqLoad =
                        req.getLoadCap().stream().filter(a -> a.getId().equals(load.getId())).findFirst().orElseThrow();
                contractChangeRecordOfLoadVO.setBefore(reqLoad.getBefore());
                contractChangeRecordOfLoadVO.setAfter(reqLoad.getAfter());
                return contractChangeRecordOfLoadVO;
            }).toList();
            recordVO.setLoadCap(loads);
        }
        if (req.getGenCapacity() != null) {
            var genIds = req.getGenCapacity().stream().map(a -> a.getId()).toList();
            var gens =
                    generatorEntityRepository.findAllById(genIds).stream().map(
                            gen -> {
                                ContractChangeRecordOfGenCapacityVO contractChangeRecordOfGenVO =
                                        new ContractChangeRecordOfGenCapacityVO();
                                contractChangeRecordOfGenVO.setId(gen.getId());
                                contractChangeRecordOfGenVO.setName(gen.getName());
                                contractChangeRecordOfGenVO.setNbsCustomerNumber(gen.getNbsCustomerNumber());
                                var reqGen =
                                        req.getGenCapacity().stream().filter(
                                                a -> a.getId().equals(gen.getId())).findFirst().orElseThrow();
                                contractChangeRecordOfGenVO.setBefore(reqGen.getBefore());
                                contractChangeRecordOfGenVO.setAfter(reqGen.getAfter());
                                return contractChangeRecordOfGenVO;

                            }).toList();
            recordVO.setGenCapacity(gens);
        }
        return recordVO;
    }

    /**
     * 確認修約以後的發電端轉供比例是否超過100
     *
     * @param applicationId
     */
    public void checkCurrentApplicationPmi(Long applicationId, Long parentId) {
        var usages =
                applicationGeneratorRepository.findAllGeneratorPmiUsageByApplicationId(
                        applicationId, new Date(), List.of(parentId), List.of());
        List<String> errors = new ArrayList<>();
        usages.stream().map(a -> a.getGeneratorId()).distinct().forEach(genId -> {
            var totalPmi = usages.stream().filter(a -> a.getGeneratorId().equals(genId)).map(a -> a.getPmi()).reduce(
                    BigDecimal.ZERO, BigDecimal::add);
            if (totalPmi.compareTo(BigDecimal.valueOf(100)) > 0) {
                try {
                    var gen = generatorService.getTempGeneratorEntity(genId);
                    errors.add(String.format("%s %s 轉供比例超過上限", gen.getNbsCustomerNumber(), gen.getName()));
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
        });

        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join("\n", errors));
        }
    }
}
