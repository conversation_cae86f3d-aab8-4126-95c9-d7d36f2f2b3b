package tw.com.taipower.pwoms.services.vo.analysis;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoadsVo {

    private Long id;
    private Long applicationId;
    private Long loadId;
    private Boolean isDirect;
    private BigDecimal monthlyContractCap;
    private BigDecimal annualContractCap;
    private String responsibilityVoltage;
}
