package tw.com.taipower.pwoms.services.re;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.services.utils.DataException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Log4j2
@Service
public class ReDistributionService {

    public static final int WATER_ENERGY_TYPE_ID = ReFuelTypeEnum.WATER.getCode();

    @Autowired
    private ReParameterRepository reParameterRepository;
    @Autowired
    private ReLoadRepository loadRepository;
    @Autowired
    private ReSettlementRepository settlementRepository;
    @Autowired
    private ReSettlementLoadPwRepository loadPwRepository;
    @Autowired
    private ReSettlementGeneratorPwRepository generatorPwRepository;
    @Autowired
    private ReAllocationRepository allocationRepository;
    @Autowired
    private ReSettlementLoadSummaryRepository loadSummaryRepository;
    @Autowired
    private ReSettlementDistPctRepository distPctRepository;
    @Autowired
    private ReProgressService progressService;
    @Autowired
    private ReSettlementStepService reSettlementStepService;
    @Autowired
    private AmiStagingService amiStagingService;



    @Transactional
    public void distribute(LocalDate date, ReProgressListener listener) {
        if (date == null) throw new DataException("沒有資料!");

        log.info("== oout ReDistributionService.distribute. =" + date);

        ReSettlement reSettlement = getOrCreateSettlement(date);

        log.info("== oout ReDistributionService.distribute.reSettlement =" + reSettlement);

        listener.onProgress(progressService.buildReProgress(reSettlement.getId(), 1, ReSettlementStepStatusEnum.IN_PROGRESS, 0, false));
        listener.onProgress(progressService.buildReProgress(reSettlement.getId(), 1, ReSettlementStepStatusEnum.IN_PROGRESS, 5, false));

        performDistribution(reSettlement, listener);
    }

    public ReSettlement getOrCreateSettlement(LocalDate date) {
//        Long settlementId = settlementRepository.getSettlementId(date).orElseThrow(() -> new DataException("沒有 結算資料！"));
//        log.info("== oout getOrCreateSettlement.settlementId =" + settlementId);

        LocalDate firstDayOfMonth = date.withDayOfMonth(1);
        return settlementRepository.findByYymmBetween(firstDayOfMonth, firstDayOfMonth)
                .stream().findFirst().orElseGet(() -> {
                    ReSettlement s = new ReSettlement();
                    s.setYymm(firstDayOfMonth);
                    s.setSettlementId(9999L);
                    return settlementRepository.save(s);
                });
    }


    public void performDistribution(ReSettlement reSettlement, ReProgressListener listener) {
        log.info("== oout performDistribution. =" + reSettlement);
        if (reSettlement == null) throw new DataException("沒有 RE結算資料！");
        ReParameter reParameter = reParameterRepository.findLatest().orElseThrow(() -> new DataException("沒有 RE參數資料！"));
        listener.onProgress(progressService.buildReProgress(reSettlement.getId(), 1, ReSettlementStepStatusEnum.IN_PROGRESS, 50, false));
        log.info("== oout performDistribution. start .......... =" );
//        reSettlementStepService.insertReAmiGenerator(reSettlement.getId());
//        reSettlementStepService.insertReAmiLoad(reSettlement.getId());

//        current use, take lots of time
//        reSettlementStepService.insertReAmiGeneratorDetail(reSettlement.getId());
//        reSettlementStepService.insertReAmiLoadDetail(reSettlement.getId());

        amiStagingService.insertReAmiGeneratorDetail(reSettlement.getId());
        amiStagingService.insertReAmiLoadDetail(reSettlement.getId());

        reSettlementStepService.insertLoadPWdataDetail(reSettlement.getId());
        reSettlementStepService.insertGeneratorPWdata(reSettlement.getId());
        reSettlementStepService.insertLoadPWdata(reSettlement.getId());
        distPctRepository.deleteByReSettlementId(reSettlement.getId());
        loadSummaryRepository.deleteByReSettlementId(reSettlement.getId());
        allocationRepository.deleteByIdReSettlementId(reSettlement.getId());

        List<ReSettlementLoadPw> loadPws = loadPwRepository.findByReSettlement(reSettlement);
        Map<String, BigDecimal> generatorRemainingMap = getGeneratorRemainingMap(reSettlement);
        Map<Integer, Map<Integer, BigDecimal>> distPctMap = calculateAndLoadDistPctMap(reSettlement);
        Map<Integer, Map<Integer, List<ReSettlementGeneratorPw>>> generatorMap = getGeneratorMap(reSettlement);
        Map<Long, List<ReSettlementLoadPw>> loadPwsByLoadId = groupLoadPwByLoadId(loadPws);

        List<ReAllocation> allocationBuffer = new ArrayList<>();
        List<ReSettlementLoadSummary> summaryBuffer = new ArrayList<>();

        // 所有用戶各時段 allocation ===
        Map<Integer, Map<Long, BigDecimal>> sectionToLoadAlloc = new HashMap<>();
        Map<Long, Map<Integer, BigDecimal>> loadToSectionAlloc = new HashMap<>();
        Map<Long, BigDecimal> loadAvailableQuota = new HashMap<>();

        for (Map.Entry<Long, List<ReSettlementLoadPw>> entry : loadPwsByLoadId.entrySet()) {
            Long loadId = entry.getKey();
            ReLoad load = loadRepository.findById(loadId).orElse(null);
            if (load == null) continue;

            BigDecimal availableQuota = Optional.ofNullable(load.getAnnualQuota())
                    .orElse(reParameter.getAnnualQuota())
                    .subtract(getPreSum(reSettlement, load).orElse(BigDecimal.ZERO))
                    .max(BigDecimal.ZERO);
            loadAvailableQuota.put(loadId, availableQuota);

            Map<Integer, BigDecimal> consumption = extractConsumption(entry.getValue());
            Map<Integer, BigDecimal> matched = extractMatched(entry.getValue());
            Map<Integer, BigDecimal> sectionAllocMap = calculateAllocationPerSection(consumption, matched, availableQuota, reParameter.getPercentage());

            loadToSectionAlloc.put(loadId, sectionAllocMap);

            for (Map.Entry<Integer, BigDecimal> alloc : sectionAllocMap.entrySet()) {
                Integer sectionId = alloc.getKey();
                BigDecimal allocKwh = alloc.getValue();
                sectionToLoadAlloc.computeIfAbsent(sectionId, k -> new HashMap<>()).put(loadId, allocKwh);
            }
        }

        scaleSectionAllocation(sectionToLoadAlloc, generatorMap, loadToSectionAlloc);

        AtomicInteger progress = new AtomicInteger(0);
        int start = 10;
        double stepRatio = (100.0 - start) / loadPwsByLoadId.entrySet().size();

        // 分配細分 by load ===
        for (Map.Entry<Long, List<ReSettlementLoadPw>> entry : loadPwsByLoadId.entrySet()) {
            int current = progress.incrementAndGet();
            int percentage = start + (int) (current * stepRatio);
            listener.onProgress(progressService.buildReProgress(reSettlement.getId(), 1, ReSettlementStepStatusEnum.IN_PROGRESS, percentage, false));

            Long loadId = entry.getKey();
            ReLoad load = loadRepository.findById(loadId).orElse(null);
            if (load == null) continue;

            Map<Integer, BigDecimal> sectionAllocMap = loadToSectionAlloc.getOrDefault(loadId, new HashMap<>());
            distributeToSections(load, reSettlement, sectionAllocMap, distPctMap, generatorMap, generatorRemainingMap, allocationBuffer);

//            summaryBuffer.add(buildLoadSummary(load, reSettlement, sectionAllocMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add)));
        }

        Map<Long, BigDecimal> loadIdToActualAllocated = allocationBuffer.stream()
                .collect(Collectors.groupingBy(
                        alloc -> alloc.getId().getLoadId(),
                        Collectors.mapping(ReAllocation::getDistKwh, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))
                ));

        for (Map.Entry<Long, BigDecimal> entry : loadIdToActualAllocated.entrySet()) {
            ReLoad load = loadRepository.findById(entry.getKey()).orElse(null);
            if (load == null) continue;
            summaryBuffer.add(buildLoadSummary(load, reSettlement, entry.getValue(),reParameter));
        }

        allocationRepository.saveAll(allocationBuffer);
        loadSummaryRepository.saveAll(summaryBuffer);

        for (int step = 1; step <= 4; step++) {
            listener.onProgress(progressService.buildReProgress(reSettlement.getId(), step, ReSettlementStepStatusEnum.COMPLETED, 100, true));
        }
    }

    // if all-kwh of secton is not enough , then scale down allocation
    public void scaleSectionAllocation(
            Map<Integer, Map<Long, BigDecimal>> sectionToLoadAlloc,
            Map<Integer, Map<Integer, List<ReSettlementGeneratorPw>>> generatorMap,
            Map<Long, Map<Integer, BigDecimal>> loadToSectionAlloc) {

        for (Integer sectionId : sectionToLoadAlloc.keySet()) {
            Map<Long, BigDecimal> loadAllocMap = sectionToLoadAlloc.get(sectionId);

            BigDecimal totalGeneration = getTotalGenerationForSection(sectionId, generatorMap);

            BigDecimal totalUserAlloc = loadAllocMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalUserAlloc.compareTo(BigDecimal.ZERO) == 0) continue; // no allocation

            // do scale
            if (totalUserAlloc.compareTo(totalGeneration) > 0) {
                BigDecimal ratio = totalGeneration.divide(totalUserAlloc, 10, RoundingMode.HALF_UP);
                loadAllocMap.replaceAll((k, v) -> v.multiply(ratio).setScale(0, RoundingMode.HALF_UP));
            }

            // write back
            for (Map.Entry<Long, BigDecimal> entry : loadAllocMap.entrySet()) {
                Long loadId = entry.getKey();
                loadToSectionAlloc.computeIfAbsent(loadId, k -> new HashMap<>()).put(sectionId, entry.getValue());
            }
        }
    }

    // 時段總發電量
    private BigDecimal getTotalGenerationForSection(Integer sectionId, Map<Integer, Map<Integer, List<ReSettlementGeneratorPw>>> generatorMap) {
        return generatorMap.getOrDefault(sectionId, Collections.emptyMap())
                .values().stream()
                .flatMap(List::stream)
                .map(ReSettlementGeneratorPw::getKwh)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private Map<String, BigDecimal> getGeneratorRemainingMap(ReSettlement settlement) {
        List<ReSettlementGeneratorPw>  alist = generatorPwRepository.findByReSettlement(settlement);
        alist.forEach(s->log.info("== oout.list.getGeneratorRemainingMap  =" + s));

        return generatorPwRepository.findByReSettlement(settlement).stream()
                .filter(g -> g != null && g.getId() != null)
                .collect(Collectors.toMap(
                        g -> g.getId().getGeneratorId() + "-" + g.getId().getEnergyChargeSectionId(),
//                        ReSettlementGeneratorPw::getKwh
                         g -> Optional.ofNullable(g.getKwh()).orElse(BigDecimal.ZERO)
                ));
    }

    private Map<Integer, Map<Integer, List<ReSettlementGeneratorPw>>> getGeneratorMap(ReSettlement settlement) {
        return generatorPwRepository.findByReSettlement(settlement).stream()
                .collect(Collectors.groupingBy(
                        g -> g.getId().getEnergyChargeSectionId(),
                        Collectors.groupingBy(g -> g.getId().getReFuelTypeId())
                ));
    }

    private Map<Integer, Map<Integer, BigDecimal>> calculateAndLoadDistPctMap(ReSettlement settlement) {
        calculateAndSaveDistPctPerSection(settlement);
        List<ReSettlementDistPct> distPctList = distPctRepository.findByIdReSettlementId(settlement.getId());
        return distPctList.stream()
                .collect(Collectors.groupingBy(
                        d -> d.getId().getEnergyChargeSectionId(),
                        Collectors.toMap(
                                d -> d.getId().getReFuelTypeId(),
                                ReSettlementDistPct::getPct
                        )
                ));
    }

    private Map<Long, List<ReSettlementLoadPw>> groupLoadPwByLoadId(List<ReSettlementLoadPw> loadPws) {
        return loadPws.stream().collect(Collectors.groupingBy(pw -> pw.getId().getLoadId()));
    }

    private Map<Integer, BigDecimal> extractConsumption(List<ReSettlementLoadPw> pws) {
        return pws.stream().collect(Collectors.toMap(
                pw -> pw.getId().getEnergyChargeSectionId(),
                pw -> Optional.ofNullable(pw.getConsumedKwh()).orElse(BigDecimal.ZERO)
        ));
    }

    private Map<Integer, BigDecimal> extractMatched(List<ReSettlementLoadPw> pws) {
        return pws.stream().collect(Collectors.toMap(
                pw -> pw.getId().getEnergyChargeSectionId(),
                pw -> Optional.ofNullable(pw.getMatchedCn()).orElse(BigDecimal.ZERO)
        ));
    }

    public Map<Integer, BigDecimal> calculateAllocationPerSection(
            Map<Integer, BigDecimal> consumptionBySection,
            Map<Integer, BigDecimal> matchedBySection,
            BigDecimal quotaAvailable,
            BigDecimal rePct
    ) {
        BigDecimal totalConsumption = consumptionBySection.values().stream()
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal re30Target = totalConsumption.multiply(rePct.divide(new BigDecimal(100)));

        BigDecimal alreadyMatched = matchedBySection.values().stream()
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal availablePlan = quotaAvailable.subtract(alreadyMatched).max(BigDecimal.ZERO);
        BigDecimal totalPlan = re30Target.subtract(alreadyMatched).max(BigDecimal.ZERO);
        BigDecimal planToAllocate = totalPlan.min(availablePlan);

        // remaining per sect
        Map<Integer, BigDecimal> remainingBySection = consumptionBySection.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> Optional.ofNullable(e.getValue()).orElse(BigDecimal.ZERO)
                                .subtract(Optional.ofNullable(matchedBySection.get(e.getKey())).orElse(BigDecimal.ZERO))
                                .max(BigDecimal.ZERO)
                ));

        BigDecimal totalRemaining = remainingBySection.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        // dist-pct  per sect
        Map<Integer, BigDecimal> ratioBySection = remainingBySection.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> totalRemaining.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO
                                : e.getValue().divide(totalRemaining, 10, RoundingMode.HALF_UP)
                ));

        log.info("\n== oout calculateAllocationPerSection.totalConsumption =" + totalConsumption + "/ re30Target= " + re30Target + "/ alreadyMatched= " + alreadyMatched + "/ totalPlan= " + totalPlan + "/ availablePlan= " + availablePlan + "/ planToAllocate= " + planToAllocate + "/ totalRemaining= " + totalRemaining + "/ annualQuotaAvailable= " + quotaAvailable);

        Map<Integer, BigDecimal> allocation = ratioBySection.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> planToAllocate.multiply(e.getValue())
                ));

        // rounded
        allocation.replaceAll((k, v) -> v.setScale(0, RoundingMode.HALF_UP));

        // 加總與補差
        BigDecimal roundedTotal = allocation.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal diff = planToAllocate.setScale(0, RoundingMode.HALF_UP).subtract(roundedTotal);
        allocation.compute(1, (k, v) -> (v == null ? BigDecimal.ZERO : v).add(diff));

        BigDecimal totalFinal = allocation.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        // log.info("== oout calculateAllocationPerSection.totalFinal =" + totalFinal);

        return allocation;
    }

    public void distributeToSections(ReLoad load, ReSettlement settlement,
                                     Map<Integer, BigDecimal> sectionAllocMap,
                                     Map<Integer, Map<Integer, BigDecimal>> distPctMap,
                                     Map<Integer, Map<Integer, List<ReSettlementGeneratorPw>>> generatorMap,
                                     Map<String, BigDecimal> generatorRemainingMap,
                                     List<ReAllocation> allocationBuffer) {

        for (Map.Entry<Integer, BigDecimal> entry : sectionAllocMap.entrySet()) {
            Integer sectionId = entry.getKey();
            BigDecimal sectionTarget = entry.getValue();

            Map<Integer, BigDecimal> pctMap = distPctMap.getOrDefault(sectionId, Collections.emptyMap());
            Map<Integer, List<ReSettlementGeneratorPw>> generatorsByType = generatorMap.getOrDefault(sectionId, Collections.emptyMap());

            BigDecimal remaining = sectionTarget;
            log.info("== oout distributeToSections.sectionTarget =" + sectionTarget);

            for (Map.Entry<Integer, List<ReSettlementGeneratorPw>> e : generatorsByType.entrySet()) {
                Integer fuelTypeId = e.getKey();
                BigDecimal pct = pctMap.getOrDefault(fuelTypeId, BigDecimal.ZERO);
                BigDecimal amount = sectionTarget.multiply(pct).setScale(0, RoundingMode.HALF_UP);
                log.info("\n== oout .fuelTypeId =" + fuelTypeId + "/ pct= " + pct + "/ amount= " + amount);

                //  發電量最大的能源型態，誤差留到後面補，skip here
                // 分配比例有可能不是1.0
                if (fuelTypeId.equals(findMaxGenFuelTypeId(generatorsByType, generatorRemainingMap))) continue;

                distributeWithinEnergyType(load, settlement, e.getValue(), amount,  generatorRemainingMap, allocationBuffer);
                remaining = remaining.subtract(amount);
            }

            // 找出發電量最大的能源型態補誤差
            Integer maxGenFuelTypeId = findMaxGenFuelTypeId(generatorsByType, generatorRemainingMap);
            if (maxGenFuelTypeId != null && remaining.compareTo(BigDecimal.ZERO) > 0) {
                List<ReSettlementGeneratorPw> maxGenGenerators = generatorsByType.get(maxGenFuelTypeId);
                distributeWithinEnergyType(load, settlement, maxGenGenerators, remaining,  generatorRemainingMap, allocationBuffer);
            }
        }
    }

    public Integer findMaxGenFuelTypeId(
            Map<Integer, List<ReSettlementGeneratorPw>> generatorsByType,
            Map<String, BigDecimal> generatorRemainingMap) {
        Integer maxGenFuelTypeId = null;
        BigDecimal maxGen = BigDecimal.ZERO;


        for (Map.Entry<Integer, List<ReSettlementGeneratorPw>> e : generatorsByType.entrySet()) {
            // e.getKey()  = fuelTypeId
            // e.getValue() = List<ReSettlementGeneratorPw>，given fuel-type, all generator

            // for fuel-type，all generator available-total
            BigDecimal totalGen = e.getValue().stream()
                    .map(gen -> {
                        String key = gen.getId().getGeneratorId() + "-" + gen.getId().getEnergyChargeSectionId();
                        // get the generator 剩餘可用量 from generatorRemainingMap,
                        // if not found，use generator's kwh
                        return generatorRemainingMap.getOrDefault(key, gen.getKwh());
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);


            if (totalGen.compareTo(maxGen) > 0) {
                maxGen = totalGen;
                maxGenFuelTypeId = e.getKey();
            }
        }

        return maxGenFuelTypeId;
    }


//    public void distributeWithinEnergyType(ReLoad load, ReSettlement settlement,
//                                           List<ReSettlementGeneratorPw> generators,
//                                           BigDecimal allocation, boolean allowOverAllocate,
//                                           Map<String, BigDecimal> generatorRemainingMap,
//                                           List<ReAllocation> allocationBuffer) {
//
//        if (generators.isEmpty() || allocation.compareTo(BigDecimal.ZERO) <= 0) return;
//
//        generators.sort((a, b) -> b.getKwh().compareTo(a.getKwh()));
//        BigDecimal remaining = allocation;
//
//        for (ReSettlementGeneratorPw generator : generators) {
//            String key = generator.getId().getGeneratorId() + "-" + generator.getId().getEnergyChargeSectionId();
//            BigDecimal available = generatorRemainingMap.getOrDefault(key, generator.getKwh());
//
//            BigDecimal assign = remaining.min(available);
//
//            log.info("\n== oout fuel. =" + key + "/ remaining= " + remaining + "/ available= " + available + "/ assign= " + assign);
//
//            if (assign.compareTo(BigDecimal.ZERO) > 0) {
//                allocationBuffer.add(createAllocation(settlement, load, generator, assign));
//                remaining = remaining.subtract(assign);
//                generatorRemainingMap.put(key, available.subtract(assign));
//            }
//
//            if (remaining.compareTo(BigDecimal.ZERO) <= 0) break;
//        }
//
//        if (allowOverAllocate && remaining.compareTo(BigDecimal.ZERO) > 0 && !generators.isEmpty()) {
//            allocationBuffer.add(createAllocation(settlement, load, generators.get(0), remaining));
//        }
//    }

    // all generator get ratio distrubution
    public void distributeWithinEnergyTypeToAllGenerator(
            ReLoad load, ReSettlement settlement,
            List<ReSettlementGeneratorPw> generators,
            BigDecimal allocation, boolean allowOverAllocate,
            Map<String, BigDecimal> generatorRemainingMap,
            List<ReAllocation> allocationBuffer) {

        if (generators.isEmpty() || allocation.compareTo(BigDecimal.ZERO) <= 0) return;

        generators.sort((a, b) -> b.getKwh().compareTo(a.getKwh()));

        generators.forEach(s->log.info("== oout.list.s  =" + s));

        BigDecimal totalAvailable = generators.stream()
                .map(g -> generatorRemainingMap.getOrDefault(
                        g.getId().getGeneratorId() + "-" + g.getId().getEnergyChargeSectionId(), g.getKwh()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // calc ratio，& not 超過該台可用量，先四捨五入
        List<BigDecimal> assignedList = new ArrayList<>();
        BigDecimal roundedSum = BigDecimal.ZERO;
        int maxIdx = 0;
        BigDecimal maxAvail = BigDecimal.ZERO;

        for (int i = 0; i < generators.size(); i++) {
            ReSettlementGeneratorPw g = generators.get(i);
            String key = g.getId().getGeneratorId() + "-" + g.getId().getEnergyChargeSectionId();
            BigDecimal available = generatorRemainingMap.getOrDefault(key, g.getKwh());

            // 比例分配  available/totalAvailable
            BigDecimal ratio = totalAvailable.compareTo(BigDecimal.ZERO) == 0 ?
                    BigDecimal.ONE.divide(BigDecimal.valueOf(generators.size()), 10, RoundingMode.HALF_UP) :
                    available.divide(totalAvailable, 10, RoundingMode.HALF_UP);

            BigDecimal rawAssign = allocation.multiply(ratio).min(available);
            BigDecimal rounded = rawAssign.setScale(0, RoundingMode.HALF_UP);
            assignedList.add(rounded);
            roundedSum = roundedSum.add(rounded);

            if (available.compareTo(maxAvail) > 0) {
                maxAvail = available;
                maxIdx = i;
            }
        }

        // 補誤差給最大 available -> generator
        BigDecimal diff = allocation.setScale(0, RoundingMode.HALF_UP).subtract(roundedSum);
        if (!assignedList.isEmpty() && diff.compareTo(BigDecimal.ZERO) != 0) {
            assignedList.set(maxIdx, assignedList.get(maxIdx).add(diff));
        }

        // write allocationBuffer
        for (int i = 0; i < generators.size(); i++) {
            if (assignedList.get(i).compareTo(BigDecimal.ZERO) > 0) {
                ReSettlementGeneratorPw g = generators.get(i);
                allocationBuffer.add(createAllocation(settlement, load, g, assignedList.get(i)));
                String key = g.getId().getGeneratorId() + "-" + g.getId().getEnergyChargeSectionId();
                BigDecimal available = generatorRemainingMap.getOrDefault(key, g.getKwh());
                generatorRemainingMap.put(key, available.subtract(assignedList.get(i)));
            }
        }
    }

    public void distributeWithinEnergyType(
            ReLoad load, ReSettlement settlement,
            List<ReSettlementGeneratorPw> generators,
            BigDecimal allocation,
            Map<String, BigDecimal> generatorRemainingMap,
            List<ReAllocation> allocationBuffer) {

        if (generators.isEmpty() || allocation.compareTo(BigDecimal.ZERO) <= 0) return;

        // Sort by available descending
        generators.sort((a, b) -> {
            BigDecimal aAvailable = generatorRemainingMap.getOrDefault(
                    a.getId().getGeneratorId() + "-" + a.getId().getEnergyChargeSectionId(), a.getKwh());
            BigDecimal bAvailable = generatorRemainingMap.getOrDefault(
                    b.getId().getGeneratorId() + "-" + b.getId().getEnergyChargeSectionId(), b.getKwh());
            return bAvailable.compareTo(aAvailable);
        });

        // Check if any single generator can handle full allocation
        for (ReSettlementGeneratorPw g : generators) {
            String key = g.getId().getGeneratorId() + "-" + g.getId().getEnergyChargeSectionId();
            BigDecimal available = generatorRemainingMap.getOrDefault(key, g.getKwh());

            if (available.compareTo(allocation) >= 0) {
                allocationBuffer.add(createAllocation(settlement, load, g, allocation));
                generatorRemainingMap.put(key, available.subtract(allocation));
                return;
            }
        }

        // allocation with rounding
        BigDecimal totalAvailable = generators.stream()
                .map(g -> generatorRemainingMap.getOrDefault(
                        g.getId().getGeneratorId() + "-" + g.getId().getEnergyChargeSectionId(), g.getKwh()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<BigDecimal> assignedList = new ArrayList<>();
        BigDecimal roundedSum = BigDecimal.ZERO;
        int maxIdx = -1;
        BigDecimal maxAvailable = BigDecimal.ZERO;

        for (int i = 0; i < generators.size(); i++) {
            ReSettlementGeneratorPw g = generators.get(i);
            String key = g.getId().getGeneratorId() + "-" + g.getId().getEnergyChargeSectionId();
            BigDecimal available = generatorRemainingMap.getOrDefault(key, g.getKwh());

            BigDecimal ratio = totalAvailable.compareTo(BigDecimal.ZERO) == 0
                    ? BigDecimal.ONE.divide(BigDecimal.valueOf(generators.size()), 10, RoundingMode.HALF_UP)
                    : available.divide(totalAvailable, 10, RoundingMode.HALF_UP);

            BigDecimal rawAssign = allocation.multiply(ratio).min(available);
            BigDecimal rounded = rawAssign.setScale(0, RoundingMode.HALF_UP);
            assignedList.add(rounded);
            roundedSum = roundedSum.add(rounded);

            if (available.compareTo(maxAvailable) > 0) {
                maxAvailable = available;
                maxIdx = i;
            }
        }

        // Rounding adjustment — correct rounding diff
        BigDecimal diff = allocation.setScale(0, RoundingMode.HALF_UP).subtract(roundedSum);
        if (diff.compareTo(BigDecimal.ZERO) != 0 && maxIdx >= 0) {
            ReSettlementGeneratorPw g = generators.get(maxIdx);
            String key = g.getId().getGeneratorId() + "-" + g.getId().getEnergyChargeSectionId();
            BigDecimal available = generatorRemainingMap.getOrDefault(key, g.getKwh());

            BigDecimal newValue = assignedList.get(maxIdx).add(diff);
            assignedList.set(maxIdx, newValue.min(available)); // ❗確保不超配
        }

        // Write allocation buffer
        for (int i = 0; i < generators.size(); i++) {
            BigDecimal assigned = assignedList.get(i);
            if (assigned.compareTo(BigDecimal.ZERO) > 0) {
                ReSettlementGeneratorPw g = generators.get(i);
                String key = g.getId().getGeneratorId() + "-" + g.getId().getEnergyChargeSectionId();
                BigDecimal available = generatorRemainingMap.getOrDefault(key, g.getKwh());

                allocationBuffer.add(createAllocation(settlement, load, g, assigned));
                generatorRemainingMap.put(key, available.subtract(assigned));
            }
        }
    }




    private ReAllocation createAllocation(ReSettlement settlement, ReLoad load,
                                          ReSettlementGeneratorPw generatorPw, BigDecimal allocation) {
        ReAllocationId id = ReAllocationId.builder()
                .reSettlementId(settlement.getId())
                .generatorId(generatorPw.getId().getGeneratorId())
                .loadId(load.getLoadId())
                .energyChargeSectionId(generatorPw.getId().getEnergyChargeSectionId())
                .reFuelTypeId(generatorPw.getId().getReFuelTypeId())
                .build();

        ReAllocation reAllocation = new ReAllocation();
        reAllocation.setId(id);
        reAllocation.setDistKwh(allocation);
        return reAllocation;
    }

    private ReSettlementLoadSummary buildLoadSummary(ReLoad load, ReSettlement settlement, BigDecimal allocatedKwh,ReParameter reParameter) {
        BigDecimal preSumKwh = isNewYear(settlement, load) ? BigDecimal.ZERO : calculatePreSumKwh(settlement, load);
        BigDecimal totalUsed = preSumKwh.add(allocatedKwh);
        BigDecimal annualuata = load.getAnnualQuota()!=null ?  load.getAnnualQuota() : reParameter.getAnnualQuota()  ;
        BigDecimal remainingKwh = annualuata.subtract(totalUsed);

        ReSettlementLoadSummary summary = loadSummaryRepository
                .findByReSettlementAndReLoad(settlement, load)
                .orElse(new ReSettlementLoadSummary());

        summary.setId(new ReSettlementLoadSummaryId(settlement.getId(), load.getLoadId()));
        summary.setReSettlement(settlement);
        summary.setReLoad(load);
        summary.setCurrKwh(allocatedKwh);
        summary.setPreSumKwh(preSumKwh);
        summary.setRemainingKwh(remainingKwh);
        return summary;
    }

    public boolean isNewYear(ReSettlement settlement, ReLoad load) {
        return !settlement.getYymm().isBefore(load.getActivatedAt().plusMonths(12));
    }


    @Transactional
    public void calculateAndSaveDistPctPerSection(ReSettlement settlement) {
        List<ReSettlementGeneratorPw> allGenerators = generatorPwRepository.findByReSettlement(settlement);
        allGenerators.forEach(s->log.info("== oout.list.allGenerators  =" + s));
        Map<Integer, List<ReSettlementGeneratorPw>> bySection = allGenerators.stream()
                .collect(Collectors.groupingBy(g -> g.getId().getEnergyChargeSectionId()));

        List<ReSettlementDistPct> pctList = new ArrayList<>();

        for (Map.Entry<Integer, List<ReSettlementGeneratorPw>> entry : bySection.entrySet()) {
            Integer sectionId = entry.getKey();
            List<ReSettlementGeneratorPw> sectionGenerators = entry.getValue();
            log.info("== oout calculateAndSaveDistPctPerSection.sectionId =" + sectionId);
            sectionGenerators.forEach(s->log.info("== oout.list.sectionGenerators  =" + s));

            Map<Integer, BigDecimal> byFuel = new HashMap<>();
            BigDecimal total = BigDecimal.ZERO;
            for (ReSettlementGeneratorPw g : sectionGenerators) {
                BigDecimal kwh = g.getKwh()==null ? BigDecimal.ZERO : g.getKwh() ;
                int fuelTypeId = g.getId().getReFuelTypeId();
                byFuel.merge(fuelTypeId, kwh, BigDecimal::add);
                total = total.add(kwh);
            }

            // Calculate ratios and prepare for batch save
            for (Map.Entry<Integer, BigDecimal> fuelEntry : byFuel.entrySet()) {
                Integer fuelTypeId = fuelEntry.getKey();
                BigDecimal fuelTotal = fuelEntry.getValue();
                BigDecimal ratio = total.compareTo(BigDecimal.ZERO) == 0
                        ? BigDecimal.ZERO
                        : fuelTotal.divide(total, 6, RoundingMode.HALF_UP);

                ReSettlementDistPct dist = new ReSettlementDistPct();
                dist.setId(new ReSettlementDistPctId(settlement.getId(), sectionId, fuelTypeId));
                dist.setKwh(fuelTotal);
                dist.setPct(ratio);
                pctList.add(dist);
            }
        }
        pctList.forEach(s->log.info("== oout.list.s  =" + s));
        if (!pctList.isEmpty()) {
            distPctRepository.saveAll(pctList);
        }
    }

    private BigDecimal calculatePreSumKwh(ReSettlement settlement, ReLoad load) {
        return loadSummaryRepository.findAllByReLoadAndReSettlement_YymmLessThan(load, settlement.getYymm()).stream()
                .map(ReSettlementLoadSummary::getCurrKwh)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    private Optional<BigDecimal> getPreSum(ReSettlement settlement, ReLoad load) {
        return loadSummaryRepository.findAllByReLoadAndReSettlement_YymmLessThan(load, settlement.getYymm())
                .stream()
                .findFirst()
                .map(ReSettlementLoadSummary::getCurrKwh);
    }
}

