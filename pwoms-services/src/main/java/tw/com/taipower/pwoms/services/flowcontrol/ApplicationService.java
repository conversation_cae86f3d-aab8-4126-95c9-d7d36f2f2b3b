package tw.com.taipower.pwoms.services.flowcontrol;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.common.util.StringUtils;
import jakarta.persistence.EntityManagerFactory;
import jakarta.transaction.Transactional;
import org.dhatim.fastexcel.reader.ExcelReaderException;
import org.dhatim.fastexcel.reader.ReadableWorkbook;
import org.dhatim.fastexcel.reader.Row;
import org.dhatim.fastexcel.reader.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tw.com.taipower.data.entity.pwoms.*;
import tw.com.taipower.data.entity.pwoms.enums.ApplicationStatusEnum;
import tw.com.taipower.data.enums.ALGKeepEnum;
import tw.com.taipower.data.repository.pwoms.*;
import tw.com.taipower.pwoms.logger.CustomLog;
import tw.com.taipower.pwoms.services.BaseService;
import tw.com.taipower.pwoms.services.account.AccountService;
import tw.com.taipower.pwoms.services.entitymanage.GeneratorService;
import tw.com.taipower.pwoms.services.entitymanage.LoadService;
import tw.com.taipower.pwoms.services.enumclass.ApplicationStage2Status;
import tw.com.taipower.pwoms.services.enumclass.ApplicationStatus;
import tw.com.taipower.pwoms.services.enumclass.ApplicationWorkflowStatus;
import tw.com.taipower.pwoms.services.enumclass.ErrorCode;
import tw.com.taipower.pwoms.services.filter.ApplicationFilter;
import tw.com.taipower.pwoms.services.filter.ViewApplicationWithEntityDetailFilter;
import tw.com.taipower.pwoms.services.notification.NotificationService;
import tw.com.taipower.pwoms.services.tpc.RemsService;
import tw.com.taipower.pwoms.services.utils.DateUtils;
import tw.com.taipower.pwoms.services.utils.FileTypeValidator;
import tw.com.taipower.pwoms.services.utils.VoUtils;
import tw.com.taipower.pwoms.services.vo.flowcontrol.ApplicationGeneratorOrderVo;
import tw.com.taipower.pwoms.services.vo.flowcontrol.ApplicationLoadOrderVo;
import tw.com.taipower.pwoms.services.vo.generated.*;
import tw.com.taipower.pwoms.services.vo.utils.OptionVo;
import tw.com.taipower.pwoms.services.vo.utils.PageVo;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 案件契約管理服務
 *
 * @class: ApplicantEntityService
 * @author: linyu-sheng
 * @version: 0.0.0
 * @since: 2024/5/3
 * @see:
 */
@Service
@CustomLog
public class ApplicationService extends BaseService {
    @Autowired
    ApplicationRepository applicationRepository;

    @Autowired
    private ApplicantEntityRepository applicantEntityRepository;

    @Autowired
    private ApplicantEntityContactRepository applicantEntityContactRepository;

    @Autowired
    private ApplicationGeneratorRepository applicationGeneratorRepository;

    @Autowired
    private ApplicationLoadRepository applicationLoadRepository;

    @Autowired
    private ApplicationDocumentRepository applicationDocumentRepository;

    @Autowired
    private GeneratorEntityRepository generatorEntityRepository;

    @Autowired
    private LoadEntityRepository loadEntityRepository;

    @Autowired
    private StoredProcedureRepository storedProcedureRepository;

    @Autowired
    private TaipowerCompanyUnitRepository taipowerCompanyUnitRepository;

    @Autowired
    private ViewApplicationGeneratorGroupRepository viewApplicationGeneratorGroupRepository;

    @Autowired
    private ViewApplicationLoadGroupRepository viewApplicationLoadGroupRepository;

    @Autowired
    private EntityDocumentRequiredRepository entityDocumentRequiredRepository;

    @Autowired
    private WorkflowDocumentRepository workflowDocumentRepository;

    @Autowired
    private GeneratorService generatorService;

    @Autowired
    private LoadService loadService;

    @Autowired
    private ApplicationEntityDocumentRequiredRepository applicationEntityDocumentRequiredRepository;

    @Autowired
    private GeneratorEntityDocumentRepository generatorEntityDocumentRepository;

    @Autowired
    private ViewApplicationEntityDocumentRequiredRepository viewApplicationEntityDocumentRequiredRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ViewApplicationWithEntityDetailRepository viewApplicationWithEntityDetailRepository;

    @Autowired
    private VoltageLevelRepository voltageLevelRepository;

    @Autowired
    private ApplicationGeneratorLoadTypeRepository applicationGeneratorLoadTypeRepository;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private ApplicationWorkflowRepository applicationWorkflowRepository;

    @Autowired
    @Qualifier("pwomsEntityManagerFactory")
    EntityManagerFactory entityManagerFactory;

    @Autowired
    private ApplicationTxService applicationTxService;

    @Autowired
    private RemsService remsService;

    @Autowired
    private AccountService accountService;

    /**
     * 案件契約狀態對應資料
     *
     * @return
     */
    public List<OptionVo> getApplicationStatus() {
        return Arrays.stream(ApplicationStatusEnum.values()).map(
                a -> OptionVo.builder().value(Long.valueOf(a.getValue())).desc(a.getDesc()).build()).collect(
                Collectors.toList());
    }

    /***
     * 根據查詢條件取得案件契約清單
     *
     * @param filter
     * @return
     */
    public PageVo<ApplicationVO> findAllByPage(ApplicationFilter filter) {
        var page = applicationRepository.findAll(filter.createCriteria(), filter.toPageable());
        var pageVO = PageVo.toVO(page, ApplicationVO.class);

        for (int i = 0; i < pageVO.getContents().size(); i++) {

            ApplicantEntity applicantEntity =
                    applicantEntityRepository.findById(pageVO.getContents().get(i).getApplicantId()).orElse(null);

            pageVO.getContents().get(i).setApplicantEntity(VoUtils.toVO(applicantEntity, ApplicantEntityVO.class));
        }


        return pageVO;

    }


    public Integer findMaxUseVersionWithContractNo(String contractNo) {

        return applicationRepository.findMaxUseVersionWithContractNo(contractNo).orElse(null);

    }


    public List<Long> findAllByContractNoAndToday(String contractNo, Date today) {

        return applicationRepository.findAllByContractNo(contractNo, today);

    }

    public List<ApplicationVO> findByContractNo(String contractNo) {

        List<ApplicationVO> result = new ArrayList<>();

        List<Application> applications = applicationRepository.findByContractNo(contractNo);

        if (!applications.isEmpty()) {
            for (Application application : applications) {
                result.add(VoUtils.toVO(application, ApplicationVO.class));
            }
        }

        return result;

    }

    /***
     * 根據查詢條件取得案件契約清單
     *
     * @param filter
     * @return
     */
    public PageVo<ApplicationVO> findAllUseView(ViewApplicationWithEntityDetailFilter filter) {
        filter.init();
        PageVo<ApplicationVO> pageVO = new PageVo<>();
        try (var entityManager = entityManagerFactory.createEntityManager()) {
            StringBuilder selectBuilder = new StringBuilder();
            selectBuilder.append(" SELECT a ");
            StringBuilder countBuilder = new StringBuilder();
            countBuilder.append(" SELECT count(*) ");
            StringBuilder whereBuilder = new StringBuilder();
            whereBuilder.append(" FROM Application a ");

            if ((filter.getGenNoLike() != null && !filter.getGenNoLike().isEmpty())
                    || (filter.getGenNameLike() != null && !filter.getGenNameLike().isEmpty())) {
                whereBuilder.append(" JOIN ApplicationGenerator ag on a.id = ag.applicationId ");
                whereBuilder.append(" JOIN GeneratorEntity ge on ge.id = ag.generatorId ");
            }
            if ((filter.getLoadNoLike() != null && !filter.getLoadNoLike().isEmpty())
                    || (filter.getLoadNameLike() != null && !filter.getLoadNameLike().isEmpty())) {
                whereBuilder.append(" JOIN ApplicationLoad al on a.id = al.applicationId ");
                whereBuilder.append(" JOIN LoadEntity le on le.id = al.loadId ");
            }
            whereBuilder.append(" WHERE 1 = 1 ");
            if (StringUtils.isNotEmpty(filter.getContractNoLike())) {
                whereBuilder.append(" AND CONCAT( CONCAT( a.contractNo, '-' ) , a.version ) LIKE :contractNoLike ");
            }
            if (filter.getType() != null) {
                whereBuilder.append(" AND a.type = :type ");
            }
            if (StringUtils.isNotEmpty(filter.getNoLike())) {
                whereBuilder.append(" AND a.no like :noLike ");
            }
            if (filter.getApplicantId() != null) {
                whereBuilder.append(" AND a.applicantId = :applicantId ");
            }
            if (filter.getTpcDeptId() != null) {
                whereBuilder.append(" AND a.tpcDeptId = :tpcDeptId ");
            }
            if (filter.getContractDeptId() != null) {
                whereBuilder.append(" AND a.contractDeptId = :contractDeptId ");
            }
            if (filter.getUpdateTimeFrom() != null) {
                whereBuilder.append(" AND a.modifiedAt >= :updateTimeFrom ");
            }
            if (filter.getUpdateTimeEnd() != null) {
                whereBuilder.append(" AND a.modifiedAt <= :updateTimeEnd ");
            }
            if (filter.getSubmitTimeFrom() != null) {
                whereBuilder.append(" AND a.submittedAt >= :submitTimeFrom ");
            }
            if (filter.getSubmitTimeEnd() != null) {
                whereBuilder.append(" AND a.submittedAt <= :submitTimeEnd ");
            }
            if (filter.getContractStartFrom() != null) {
                whereBuilder.append(" AND a.contractedStart >= :contractStartFrom ");
            }
            if (filter.getContractStartEnd() != null) {
                whereBuilder.append(" AND a.contractedStart <= :contractStartEnd ");
            }
            if (filter.getContractEndFrom() != null) {
                whereBuilder.append(" AND a.contractedEnd >= :contractEndFrom ");
            }
            if (filter.getContractEndEnd() != null) {
                whereBuilder.append(" AND a.contractedEnd <= :contractEndEnd ");
            }
            if (filter.getContractOnlineFrom() != null) {
                whereBuilder.append(" AND a.onlineAt >= :contractOnlineFrom ");
            }
            if (filter.getContractOnlineEnd() != null) {
                whereBuilder.append(" AND a.onlineAt <= :contractOnlineEnd ");
            }
            if (filter.getStatus() != null) {
                whereBuilder.append(" AND a.status = :status ");
            }
            if (filter.getStage2Status() != null) {
                whereBuilder.append(" AND a.stage2Status = :stage2Status ");
            }
            if (StringUtils.isNotEmpty(filter.getGenNoLike())) {
                whereBuilder.append(" AND ge.nbsCustomerNumber like :genNoLike ");
            }
            if (StringUtils.isNotEmpty(filter.getLoadNoLike())) {
                whereBuilder.append(" AND le.nbsCustomerNumber like :loadNoLike ");
            }
            if (StringUtils.isNotEmpty(filter.getGenNameLike())) {
                whereBuilder.append(" AND ge.name like :genNameLike ");
            }
            if (StringUtils.isNotEmpty(filter.getLoadNameLike())) {
                whereBuilder.append(" AND le.name like :loadNameLike ");
            }
            if (filter.getDepositType() != null) {
                whereBuilder.append(" AND a.depositType = :depositType ");
            }
            if (filter.getContractStatus() != null) {
                whereBuilder.append(" AND a.contractStatus = :contractStatus ");
            }
            if (filter.getStage3Status() != null) {
                whereBuilder.append(" AND a.stage3Status = :stage3Status ");
            }
            if (filter.getStage9Status() != null) {
                whereBuilder.append(" AND a.stage9Status = :stage9Status ");
            }
            if (filter.getIsContract() != null) {
                if (filter.getIsContract()) {
                    whereBuilder.append(" AND a.contractNo IS NOT NULL ");
                } else {
                    whereBuilder.append(" AND a.contractNo IS NULL ");
                }
            }
            if (filter.getIsUseForOutterDeposit() != null) {
                whereBuilder.append(" AND a.contractStatus >= 4 ");
            }

            // 保證金狀態條件判斷
            if (filter.getDepositStatus() != null) {
                java.util.Date paidFrom = new java.util.Date(System.currentTimeMillis() - (59L * 24 * 60 * 60 * 1000));


                switch (filter.getDepositStatus()) {
                    case PENDING_PAYMENT:
                        // 無入帳日期 AND TODAY'S DATE <= 繳費期限(簽約日+59天)
                        whereBuilder.append(" AND a.depositPaidAt IS NULL AND :paidFrom <= a.contractedStart ");
                        break;

                    case OVERDUE:
                        // 無入帳日期 AND TODAY'S DATE > 繳費期限(簽約日+59天)
                        whereBuilder.append(
                                " AND a.depositPaidAt IS NULL AND a.contractedEnd IS NULL  AND :paidFrom > a.contractedStart ");
                        break;
                    case PAID:
                        // 有保證金入賬日期
                        whereBuilder.append(" AND a.depositPaidAt IS NOT NULL AND a.contractedEnd IS NULL ");
                        break;

                    case PENDING_REFUND:
                        // 有終止日 AND 退回單號 = null
                        whereBuilder.append(" AND a.contractedEnd IS NOT NULL AND a.depositReturnNo IS NULL ");
                        break;

                    case REFUNDED:
                        // 有終止日 AND 退回單號 <> null
                        whereBuilder.append(" AND a.contractedEnd IS NOT NULL AND a.depositReturnNo IS NOT NULL ");
                        break;
                }
            }

            StringBuilder sortBuilder = new StringBuilder();
            if (filter.getSort() != null && !filter.getSort().isEmpty()) {
                sortBuilder.append(" Order BY ");
                for (int index = 0; index < filter.getSort().size(); index++) {
                    var sort = filter.getSort().get(index);
                    var params = sort.split(",");
                    var fieldName = params[0];
                    var isAsc = params[1].equals("asc");
                    if (index > 0) {
                        sortBuilder.append(" , ");
                    }
                    sortBuilder.append(String.format("a.%s %s", fieldName, isAsc ? "asc" : "desc"));
                }
            }

            var query = entityManager.createQuery(selectBuilder.toString() + whereBuilder + sortBuilder,
                    Application.class);
            var countQuery = entityManager.createQuery(countBuilder.toString() + whereBuilder);

            if (StringUtils.isNotBlank(filter.getContractNoLike())) {
                countQuery.setParameter("contractNoLike", filter.getContractNoLike() + "%");
                query.setParameter("contractNoLike", filter.getContractNoLike() + "%");
            }
            if (filter.getType() != null) {
                countQuery.setParameter("type", filter.getType());
                query.setParameter("type", filter.getType());

            }
            if (filter.getNoLike() != null && !filter.getNoLike().isEmpty()) {
                countQuery.setParameter("noLike", filter.getNoLike() + "%");
                query.setParameter("noLike", filter.getNoLike() + "%");
            }
            if (filter.getApplicantId() != null) {
                countQuery.setParameter("applicantId", filter.getApplicantId());
                query.setParameter("applicantId", filter.getApplicantId());

            }
            if (filter.getTpcDeptId() != null) {
                countQuery.setParameter("tpcDeptId", filter.getTpcDeptId());
                query.setParameter("tpcDeptId", filter.getTpcDeptId());
            }
            if (filter.getContractDeptId() != null) {
                countQuery.setParameter("contractDeptId", filter.getContractDeptId());
                query.setParameter("contractDeptId", filter.getContractDeptId());
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            if (filter.getUpdateTimeFrom() != null) {
                countQuery.setParameter("updateTimeFrom", (filter.getUpdateTimeFrom()));
                query.setParameter("updateTimeFrom", (filter.getUpdateTimeFrom()));
            }
            if (filter.getUpdateTimeEnd() != null) {
                countQuery.setParameter("updateTimeEnd", (filter.getUpdateTimeEnd()));
                query.setParameter("updateTimeEnd", (filter.getUpdateTimeEnd()));
            }
            if (filter.getSubmitTimeFrom() != null) {
                countQuery.setParameter("submitTimeFrom", (filter.getSubmitTimeFrom()));
                query.setParameter("submitTimeFrom", (filter.getSubmitTimeFrom()));
            }
            if (filter.getSubmitTimeEnd() != null) {
                countQuery.setParameter("submitTimeEnd", (filter.getSubmitTimeEnd()));
                query.setParameter("submitTimeEnd", (filter.getSubmitTimeEnd()));
            }
            if (filter.getContractStartFrom() != null) {
                // whereBuilder.append(" AND a.contractedStart >= :contractStartFrom ");
                countQuery.setParameter("contractStartFrom", (filter.getContractStartFrom()));
                query.setParameter("contractStartFrom", (filter.getContractStartFrom()));
            }
            if (filter.getContractStartEnd() != null) {
                // whereBuilder.append(" AND a.contractedStart <= :contractStartEnd ");
                countQuery.setParameter("contractStartEnd", (filter.getContractStartEnd()));
                query.setParameter("contractStartEnd", (filter.getContractStartEnd()));
            }
            if (filter.getContractEndFrom() != null) {
                // whereBuilder.append(" AND a.contractedEnd >= :contractEndFrom ");
                countQuery.setParameter("contractEndFrom", (filter.getContractEndFrom()));
                query.setParameter("contractEndFrom", (filter.getContractEndFrom()));
            }
            if (filter.getContractEndEnd() != null) {
                // whereBuilder.append(" AND a.contractedEnd <= :contractEndEnd ");
                countQuery.setParameter("contractEndEnd", (filter.getContractEndEnd()));
                query.setParameter("contractEndEnd", (filter.getContractEndEnd()));
            }
            if (filter.getContractOnlineFrom() != null) {
                // whereBuilder.append(" AND a.onlineAt >= :contractOnlineFrom ");
                countQuery.setParameter("contractOnlineFrom", (filter.getContractOnlineFrom()));
                query.setParameter("contractOnlineFrom", (filter.getContractOnlineFrom()));
            }
            if (filter.getContractOnlineEnd() != null) {
                // whereBuilder.append(" AND a.onlineAt <= :contractOnlineEnd ");
                countQuery.setParameter("contractOnlineEnd", (filter.getContractOnlineEnd()));
                query.setParameter("contractOnlineEnd", (filter.getContractOnlineEnd()));
            }
            if (filter.getGenNoLike() != null && !filter.getGenNoLike().isEmpty()) {
                countQuery.setParameter("genNoLike", filter.getGenNoLike() + "%");
                query.setParameter("genNoLike", filter.getGenNoLike() + "%");
            }
            if (filter.getLoadNoLike() != null && !filter.getLoadNoLike().isEmpty()) {
                countQuery.setParameter("loadNoLike", filter.getLoadNoLike() + "%");
                query.setParameter("loadNoLike", filter.getLoadNoLike() + "%");
            }
            if (filter.getGenNameLike() != null && !filter.getGenNameLike().isEmpty()) {
                countQuery.setParameter("genNameLike", filter.getGenNameLike() + "%");
                query.setParameter("genNameLike", filter.getGenNameLike() + "%");
            }
            if (filter.getLoadNameLike() != null && !filter.getLoadNameLike().isEmpty()) {
                countQuery.setParameter("loadNameLike", filter.getLoadNameLike() + "%");
                query.setParameter("loadNameLike", filter.getLoadNameLike() + "%");
            }
            if (filter.getStatus() != null) {
                countQuery.setParameter("status", filter.getStatus());
                query.setParameter("status", filter.getStatus());
            }
            if (filter.getStage2Status() != null) {
                countQuery.setParameter("stage2Status", filter.getStage2Status());
                query.setParameter("stage2Status", filter.getStage2Status());
            }
            if (filter.getDepositType() != null) {
                countQuery.setParameter("depositType", filter.getDepositType());
                query.setParameter("depositType", filter.getDepositType());
            }
            if (filter.getContractStatus() != null) {
                countQuery.setParameter("contractStatus", filter.getContractStatus());
                query.setParameter("contractStatus", filter.getContractStatus());
            }
            if (filter.getStage3Status() != null) {
                countQuery.setParameter("stage3Status", filter.getStage3Status());
                query.setParameter("stage3Status", filter.getStage3Status());
            }
            if (filter.getStage9Status() != null) {
                countQuery.setParameter("stage9Status", filter.getStage9Status());
                query.setParameter("stage9Status", filter.getStage9Status());
            }

            // 保證金狀態條件判斷
            if (filter.getDepositStatus() != null) {
                java.util.Date paidFrom = new java.util.Date(System.currentTimeMillis() - (59L * 24 * 60 * 60 * 1000));


                switch (filter.getDepositStatus()) {
                    case PENDING_PAYMENT:
                    case OVERDUE:
                        countQuery.setParameter("paidFrom", paidFrom);
                        query.setParameter("paidFrom", paidFrom);
                        break;
                    default:
                        break;
                }
            }
            var pageRequest = filter.toPageable();
            query.setFirstResult((pageRequest.getPageNumber()) * pageRequest.getPageSize());
            query.setMaxResults(pageRequest.getPageSize());

            var list = query.getResultList();

            Long total = (Long) countQuery.getSingleResult();

            pageVO.setTotal(total);
            pageVO.setContents(
                    list.stream().map(a -> VoUtils.toVO(a, ApplicationVO.class)).collect(Collectors.toList()));
            pageVO.setPage(pageRequest.getPageNumber());
            pageVO.setPageSize(pageRequest.getPageSize());
        }

        return pageVO;
    }

    /**
     * 查詢案件主體（但不包含用發電端關聯）
     *
     * @param applicationId
     * @return
     */
    public ApplicationVO findById(Long applicationId) {
        var optApplication = applicationRepository.findById(applicationId);
        if (optApplication.isPresent()) {
            var applicationVo = VoUtils.toVO(optApplication.get(), ApplicationVO.class);
            var applicationGenerators = findAllApplicationGeneratorsByApplicationId(applicationId).stream().map(
                    a -> VoUtils.toVO(a, ApplicationGeneratorVO.class)).toList();
            var applicationLoads = findAllApplicationLoadsByApplicationId(applicationId).stream().map(
                    a -> VoUtils.toVO(a, ApplicationLoadVO.class)).toList();
            applicationVo.setApplicationGeneratorList(applicationGenerators);
            applicationVo.setApplicationLoadList(applicationLoads);
            /**
             * 補上暫存VO查詢
             */
            var loadIds = applicationVo.getApplicationLoadList().stream().map(a -> a.getLoadId()).collect(
                    Collectors.toList());
            var loadEntities = loadService.getTempLoadEntitys(loadIds);

            applicationVo.getApplicationLoadList().stream().forEach(al -> {
                var opt = loadEntities.stream().filter(a -> a.getId().equals(al.getLoadId())).findFirst();
                if (opt.isPresent()) {
                    al.setLoadEntity(opt.get());
                }
            });

            var generatorIds = applicationVo.getApplicationGeneratorList().stream().map(a -> a.getGeneratorId())
                    .collect(
                            Collectors.toList());
            var genEntities = generatorService.getTempGeneratorEntitys(generatorIds);

            applicationVo.getApplicationGeneratorList().stream().forEach(ag -> {
                var opt = genEntities.stream().filter(a -> a.getId().equals(ag.getGeneratorId())).findFirst();
                if (opt.isPresent()) {
                    ag.setGeneratorEntity(opt.get());
                }
            });

            return applicationVo;
        }
        throw new RuntimeException("Application not found");
    }

    public PageVo<ApplicationVO> findFullAllByPage(ViewApplicationWithEntityDetailFilter filter) {
        var vo = this.findAllUseView(filter);
        var list = vo.getContents();
        var applicationIds = list.stream().map(ApplicationVO::getId).collect(Collectors.toList());
        var applicantIds = list.stream().map(ApplicationVO::getApplicantId).filter(Objects::nonNull)
                .collect(Collectors.toList());
        var applicantContactsIds = list.stream().filter(a -> a.getApplicantId() != null)
                .map(ApplicationVO::getApplicantContactId).collect(
                        Collectors.toList());
        var applicantEntitys = applicantEntityRepository.findAllById(applicantIds);
        var applicantContacts = applicantEntityContactRepository.findAllById(applicantContactsIds);
        var algs = viewApplicationGeneratorGroupRepository.findAllById(applicationIds);
        var alls = viewApplicationLoadGroupRepository.findAllById(applicationIds);
        list.forEach(application -> {
            var applicant = applicantEntitys.stream().filter(a -> a.getId().equals(application.getApplicantId()))
                    .findFirst();
            applicant.ifPresent(applicantEntity -> application.setApplicantEntity(
                    VoUtils.toVO(applicantEntity, ApplicantEntityVO.class)));
            var applicatnContact = applicantContacts.stream().filter(
                    a -> a.getId().equals(application.getApplicantContactId())).findFirst();
            applicatnContact.ifPresent(applicantEntity -> application.setApplicantEntityContact(
                    VoUtils.toVO(applicantEntity, ApplicantEntityContactVO.class)));
            var alg = algs.stream().filter(a -> a.getApplicationId().equals(application.getId())).findFirst();
            if (alg.isPresent()) {
                var algvo = VoUtils.toVO(alg.get(), ViewApplicationGeneratorGroupVO.class);
                application.setViewApplicationGeneratorGroup(algvo);
            }
            var all = alls.stream().filter(a -> a.getApplicationId().equals(application.getId())).findFirst();
            if (all.isPresent()) {
                var allvo = VoUtils.toVO(all.get(), ViewApplicationLoadGroupVO.class);
                application.setViewApplicationLoadGroup(allvo);
            }
        });

        return vo;
    }

    /**
     * 案件選擇申請人及聯絡人以後，產生案件編號，並且儲存。
     *
     * @param applicationVO
     * @param userId
     * @return
     */
    @Transactional
    public Long saveDraft(ApplicationVO applicationVO, Long userId) {

        /**
         * init applicationLoad & applicationGenerator
         */
        applicationVO.getApplicationGeneratorList().forEach(ag -> {
            if (ag.getKeep() == null) {
                ag.setKeep(ALGKeepEnum.KEEP_USING);
            }
        });
        applicationVO.getApplicationLoadList().forEach(al -> {

            if (al.getKeep() == null) {
                al.setKeep(ALGKeepEnum.KEEP_USING);
            }
        });
        // checkDraftNeededColumn(applicationVO);

        applicationVO.setModifiedAt(null);

        /**
         * 1. 轉換VO成model
         */
        var model = VoUtils.toEntity(applicationVO, Application.class);
        /**
         * 2. 設定編輯人員
         */
        model.setModifiedBy(userId);
        /**
         * 3.
         * 查詢申請人，取得統編
         */
        if (model.getApplicantId() == null) {
            throwException(ErrorCode.APPLICATION_APPLICANT_IS_NEED);
        }
        if (model.getApplicantContactId() == null) {
            throwException(ErrorCode.APPLICATION_APPLICANT_CONTACT_IS_NEED);
        }
        var optApplicant = applicantEntityRepository.findById(model.getApplicantId());
        if (!optApplicant.isPresent()) {
            throw new RuntimeException("ApplicantEntity not found");
        }
        var optApplicantContact = applicantEntityContactRepository.findById(model.getApplicantContactId());
        if (!optApplicantContact.isPresent()) {
            throw new RuntimeException("ApplicantEntityContact not found");
        }

        var applicant = optApplicant.get();
        // if (applicant.getType().equals(2L)) {
        // // 如果申請者為售電業，需要檢查發電端不得有光儲
        // checkApplicationGeneratorPvStorage(applicationVO);
        // }
        // /**
        // *
        // */
        // checkApplicationGeneratorValid(applicationVO);
        /**
         * 將所有發用電端暫存步驟往後移動
         */

        // saveTempGeneratorAndLoads(applicationVO);

        /**
         * 儲存時，檢查發電端
         */

        if (model.getId() == null) {

            // 如果計畫書沒有主辦區處，以登入者的身份，看其所屬區處來決定主辦區處
            if (model.getTpcDeptId() == null) {
                var mainDeptId = accountService.getMainDeptByAccountId(userId);
                model.setTpcDeptId(mainDeptId);
            }
            var taxId = applicant.getTaxId();
            var yearMonth = DateUtils.getYearMonth();
            //
            // 如果沒有遞件編號，產生遞件編號
            model.setNo(applicationTxService.applicationNo(taxId, yearMonth));
            /**
             * 存入資料庫
             */
            var dbModel = applicationRepository.save(model);
            /**
             * 回傳案件ID
             */
            saveRelations(model, dbModel.getId(), true);
            return dbModel.getId();
        } else {
            var optdbModel = applicationRepository.findById(model.getId());
            if (!optdbModel.isPresent()) {
                throw new RuntimeException("Application not found");
            }
            model.setModifiedAt(null);
            var dbModel = optdbModel.get();
            VoUtils.mergeObjectWithNullIgnore(model, dbModel);

            applicationRepository.save(dbModel);
            saveRelations(model, model.getId(), false);
            return dbModel.getId();
        }
    }

    /**
     * 檢查發電端必填欄位
     *
     * @param applicationVO
     */
    void checkDraftGeneratorNeededColumn(ApplicationVO applicationVO) {
        StringBuilder message = new StringBuilder();
        applicationVO.getApplicationGeneratorList().stream().map(ApplicationGeneratorVO::getGeneratorEntity).forEach(
                entity -> {
                    if (entity.getResponsiblePerson() == null || entity.getResponsiblePerson().isEmpty()) {
                        message.append(String.format("%s %s 負責人名稱欄位為空\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    }
                    if (entity.getResponsiblePersonPhone() == null || entity.getResponsiblePersonPhone().isEmpty()) {
                        message.append(String.format("%s %s 負責人電話欄位未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    }
                    if (entity.getResponsiblePersonAddressArea() == null) {
                        message.append(String.format("%s %s 負責人地區欄位未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    }
                    if (entity.getResponsiblePersonAddressOther() == null
                            || entity.getResponsiblePersonAddressOther().isEmpty()) {
                        message.append(String.format("%s %s 負責人地址欄位未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    }
                    if (entity.getGenerationUnitType() == null) {
                        message.append(String.format("%s %s 發電設備型別欄位未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    }
                    if (entity.getIsNewDevice() == null) {
                        message.append(
                                String.format("%s %s 是否為新增設發電設備欄位未設置\n", entity.getNbsCustomerNumber(),
                                        entity.getName()));
                    } else if (entity.getIsNewDevice() && entity.getNewDeviceReason() == null) {
                        message.append(
                                String.format("%s %s 新增設發電設備狀態欄位未設置\n", entity.getNbsCustomerNumber(),
                                        entity.getName()));
                    }
                    if (entity.getIsOldDevice() == null) {
                        message.append(
                                String.format("%s %s 是否為既有發電設備欄位未設置\n", entity.getNbsCustomerNumber(),
                                        entity.getName()));
                    }
                    if (entity.getIsWholesale() == null) {
                        message.append(String.format("%s %s 是否躉售欄位未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    } else if (entity.getIsWholesale() && entity.getWholesaleReason() == null) {
                        message.append(String.format("%s %s 躉售狀態欄位未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    }
                    // else if (entity.getIsWholesale() && entity.getWholesaleType() == null) {
                    // message.append(String.format("%s %s 躉售狀態欄位未設置\n",
                    // entity.getNbsCustomerNumber(),
                    // entity.getName()));
                    // }
                    if (entity.getIsMultiStage() == null) {
                        message.append(String.format("%s %s 是否採分期併網欄位未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    }
                    if (entity.getIsSaleInTrialRun() == null) {
                        message.append(
                                String.format("%s %s 併聯試運轉期間是否轉供欄位未設置\n", entity.getNbsCustomerNumber(),
                                        entity.getName()));
                    } else if (entity.getIsSaleInTrialRun() && entity.getSaleInTrialRunReason() == null) {
                        message.append(
                                String.format("%s %s 併聯試運轉期間轉供狀態欄位未設置\n", entity.getNbsCustomerNumber(),
                                        entity.getName()));
                    }
                    if (entity.getIsPvStorage() == null) {
                        message.append(String.format("%s %s 是否為光儲欄位未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    } else if (entity.getIsPvStorage() && entity.getIsPvStorageReason() == null) {
                        message.append(String.format("%s %s 光儲案場狀態欄位未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    }

                    if (entity.getCombineMethod() == null) {
                        message.append(String.format("%s %s 併聯方式未設置\n", entity.getNbsCustomerNumber(),
                                entity.getName()));
                    }

                });

        if (!message.isEmpty())
            throw new RuntimeException(message.toString());
    }

    /**
     * 檢查發電端資訊， 設備類型 = 第二、三型時：如果選是，儲存基本資料頁籤時警告「發電端設備類型為第二、三型時，不可有光儲」
     *
     * @param applicationVO
     */
    private void checkApplicationGeneratorValid(ApplicationVO applicationVO) {
        List<Long> gu23 = List.of(2L, 3L); // 設備二三型
        List<String> errors = new ArrayList<>();
        var algs = applicationVO.getApplicationGeneratorList();
        algs.forEach(alg -> {
            var entity = alg.getGeneratorEntity();
            try {
                if (entity.getGenerationUnitType() == null) {
                    errors.add(String.format("%s(%s) 請確認發電端設備類型是否選擇", entity.getName(),
                            entity.getNbsCustomerNumber()));
                } else if (gu23.contains(entity.getGenerationUnitType())) {
                    if (entity.getIsPvStorage() == null) {
                        errors.add(String.format("%s(%s) 請確認發電端光儲是否存在", entity.getName(),
                                entity.getNbsCustomerNumber()));
                    } else if (entity.getIsPvStorage()) {
                        errors.add(String.format("%s(%s)設備類型為第二、三型時，不可有光儲", entity.getName(),
                                entity.getNbsCustomerNumber()));
                    }
                }
            } catch (Throwable ex) {
                log.error("{} {}", entity.getName(), ex);
                errors.add(String.format("%s(%s) 資料異常", entity.getName(), entity.getNbsCustomerNumber()));
            }
        });
        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join(",", errors));
        }
    }

    /**
     * 如果申請者為售電業，「申請者為售電業時，發電端不可有光儲」
     *
     * @param applicationVO
     */
    private void checkApplicationGeneratorPvStorage(ApplicationVO applicationVO) {
        List<String> errors = new ArrayList<>();
        var algs = applicationVO.getApplicationGeneratorList();
        algs.forEach(alg -> {
            var entity = alg.getGeneratorEntity();
            if (entity.getIsPvStorage()) {
                errors.add(String.format("申請者為售電業時，%s(%s)不可有光儲", entity.getName(),
                        entity.getNbsCustomerNumber()));
            }
        });
        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join(",", errors));
        }
    }

    void saveTempGeneratorAndLoads(ApplicationVO model, Long userId) {
        var genRefs = model.getApplicationGeneratorList();
        genRefs.stream().forEach(ref -> {
            var entityVO = ref.getGeneratorEntity();
            /**
             * 發電端資料非readOnly才儲存到資料庫，
             * 審查流程的修改資料走其他方法，計畫書草稿需要阻擋
             */
            if (entityVO != null && !Boolean.TRUE.equals(entityVO.getReadOnly())) {
                try {
                    generatorService.setTempGeneratorEntity(entityVO, userId);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
        });
        // 用電端不修改，所以暫時不儲存暫存資料
        // var loadRefs = model.getApplicationLoadList();
        // loadRefs.stream().forEach(ref -> {
        // var entityVO = ref.getLoadEntity();
        // if (entityVO != null) {
        // try {
        // loadService.setTempLoadEntity(entityVO);
        // } catch (JsonProcessingException e) {
        // throw new RuntimeException(e);
        // }
        // }
        // });
    }

    void saveRelations(Application model, Long applicationId, boolean isNew) {
        var genRefs = model.getApplicationGeneratorList();
        var loadRefs = model.getApplicationLoadList();
        /**
         * 新建草稿，不用比對，直接新建
         */
        if (isNew) {
            if (!genRefs.isEmpty()) {
                genRefs.stream().forEach(a -> {
                    a.setApplicationId(applicationId);
                    if (a.getPmi() == null) {
                        a.setPmi(BigDecimal.ZERO);
                    }
                });
                applicationGeneratorRepository.saveAll(genRefs);
            }
            if (!loadRefs.isEmpty()) {
                loadRefs.stream().forEach(a -> {
                    a.setApplicationId(applicationId);
                    if (a.getAnnualContractCap() == null) {
                        a.setAnnualContractCap(0L);
                    }
                    if (a.getMonthlyContractCap() == null) {
                        a.setMonthlyContractCap(0L);
                    }
                });
                applicationLoadRepository.saveAll(loadRefs);
            }
        } else {
            var queryGen = ApplicationGenerator.builder().applicationId(model.getId()).build();
            var allDbGens = applicationGeneratorRepository.findAll(Example.of(queryGen));
            var queryLoad = ApplicationLoad.builder().applicationId(model.getId()).build();
            var allDbLoads = applicationLoadRepository.findAll(Example.of(queryLoad));

            var saveGenRefs = genRefs.stream().map(genRef -> {
                var optFrist = allDbGens.stream().filter(a -> a.getGeneratorId().equals(genRef.getGeneratorId()))
                        .findFirst();
                if (optFrist.isPresent()) {
                    VoUtils.mergeObjectWithNullIgnore(genRef, optFrist.get());
                    return optFrist.get();
                } else {
                    genRef.setApplicationId(applicationId);
                    if (genRef.getPmi() == null) {
                        genRef.setPmi(BigDecimal.ZERO);
                    }
                    return genRef;
                }
            }).collect(Collectors.toList());
            var saveLoadRefs = loadRefs.stream().map(loadRef -> {
                var optFrist = allDbLoads.stream().filter(a -> a.getLoadId().equals(loadRef.getLoadId())).findFirst();
                if (optFrist.isPresent()) {
                    VoUtils.mergeObjectWithNullIgnore(loadRef, optFrist.get());
                    return optFrist.get();
                } else {
                    loadRef.setApplicationId(applicationId);
                    if (loadRef.getAnnualContractCap() == null) {
                        loadRef.setAnnualContractCap(0L);
                    }
                    if (loadRef.getMonthlyContractCap() == null) {
                        loadRef.setMonthlyContractCap(0L);
                    }
                    return loadRef;
                }
            }).collect(Collectors.toList());

            /**
             * 這邊是要儲存的
             */
            saveGenRefs = applicationGeneratorRepository.saveAll(saveGenRefs);
            saveLoadRefs = applicationLoadRepository.saveAll(saveLoadRefs);

            /**
             * 要刪除的
             */
            var saveGenIds = saveGenRefs.stream().map(a -> a.getId()).collect(Collectors.toList());
            var saveLoadIds = saveLoadRefs.stream().map(a -> a.getId()).collect(Collectors.toList());
            var deleteGens = allDbGens.stream().filter(a -> !saveGenIds.contains(a.getId()))
                    .collect(Collectors.toList());
            var deleteLoads = allDbLoads.stream().filter(a -> !saveLoadIds.contains(a.getId()))
                    .collect(Collectors.toList());

            applicationGeneratorRepository.deleteAll(deleteGens);
            applicationLoadRepository.deleteAll(deleteLoads);
        }
    }

    /**
     * 確認計畫書發電端是否階段1的檔案都已經上傳
     *
     * @param application
     */
    public void checkGeneratorStage1FilesReadyFromDatabase(Application application) {
        var ags = applicationGeneratorRepository.findAllByApplicationId(application.getId());
        List<String> errorList = new ArrayList<>();
        ags.forEach(ag -> {
            try {
                // 統一在下列方法檢查第一階段檔案是否備齊，如果沒有齊備就拋出Exception
                var errors = getGeneratorEntityDocumentStage1FromDatabase(application.getType(), ag.getGeneratorId(),
                        ag);
                errorList.addAll(errors);
            } catch (Throwable ex) {
                log.error(ex);
            }
        });
        if (!errorList.isEmpty()) {
            throw new RuntimeException(String.join("\n", errorList));
        }
    }

    /**
     * 確認計畫書發電端是否階段1的檔案都已經上傳
     *
     * @param applicationVO
     */
    public void checkGeneratorStage1FilesReadyFromVO(ApplicationVO applicationVO) {
        var ags = applicationVO.getApplicationGeneratorList();
        List<String> errorList = new ArrayList<>();
        ags.forEach(ag -> {
            try {
                // 統一在下列方法檢查第一階段檔案是否備齊，如果沒有齊備就拋出Exception
                var errors = getGeneratorEntityDocumentStage1FromVO(applicationVO.getType(), ag.getGeneratorEntity(),
                        ag);
                errorList.addAll(errors);
            } catch (Throwable ex) {
                log.error(ex);
            }
        });
        if (!errorList.isEmpty()) {
            throw new RuntimeException(String.join("\n", errorList));
        }
    }

    /**
     * 計畫書/案件送出審查申請
     *
     * @param applicationid
     * @param userId
     */
    public void submitApplication(Long applicationid, ApplicationVO applicationVO,
                                  Long userId) throws JsonProcessingException {
        saveDraft(applicationVO, userId);
        // validApplication(applicationVO);
        /**
         *
         * M362調整
         * 原本完整檢查，現在只針對計畫書本身文件檢核
         */
        checkApplicationDocumentStage1(applicationVO.getId());
        applicationTxService.submitToWorkflow(applicationid, userId);
    }

    /**
     * 送出以前才檢查計畫書內容是否正確無誤
     *
     * @param applicationVO
     */
    public void validApplication(ApplicationVO applicationVO) {
        fullyCheckApplicationGeneratorValid(applicationVO, true);
        fullyCheckApplicationLoadValid(applicationVO);
        checkApplicationDocumentStage1(applicationVO.getId());
    }

    /**
     * 檢查用電端資訊，之後提供API給前端呼叫可以部分檢查
     *
     * @param applicationVO
     */
    public void fullyCheckApplicationGeneratorValid(ApplicationVO applicationVO, boolean checkFromDatabase) {
        checkDraftGeneratorNeededColumn(applicationVO);

        var optApplicant = applicantEntityRepository.findById(applicationVO.getApplicantId());
        if (!optApplicant.isPresent()) {
            throw new RuntimeException("ApplicantEntity not found");
        }
        var optApplicantContact = applicantEntityContactRepository.findById(applicationVO.getApplicantContactId());
        if (!optApplicantContact.isPresent()) {
            throw new RuntimeException("ApplicantEntityContact not found");
        }

        var applicant = optApplicant.get();
        if (applicant.getType().equals(2L)) {
            // 如果申請者為售電業，需要檢查發電端不得有光儲
            checkApplicationGeneratorPvStorage(applicationVO);
        }
        /**
         *
         */
        checkApplicationGeneratorValid(applicationVO);

        if (checkFromDatabase) {
            var application = VoUtils.toEntity(applicationVO, Application.class);
            /**
             * 確認計畫書發電端是否階段1的檔案都已經上傳
             */
            checkGeneratorStage1FilesReadyFromDatabase(application);
        } else {
            checkGeneratorStage1FilesReadyFromVO(applicationVO);
        }
    }

    /**
     * 提供API讓前端在步驟進行時可以提早檢查
     * 檢查是否所有用電端都有符合契約類型
     *
     * @param applicationVO
     */
    public void fullyCheckApplicationLoadValid(ApplicationVO applicationVO) {
        /**
         * 用電端是否符合資格（是/否）
         * 7/10業務處提供不合資格判斷邏輯：
         * 用電契約別為0、B、1、2、3、4、7不可參加轉直供，系統自動選否
         */
        List illegalContracts = List.of("0", "1", "2", "3", "4", "7", "B");
        List<String> errors = new ArrayList<>();
        var als = applicationVO.getApplicationLoadList();
        als.forEach(al -> {
            var entity = al.getLoadEntity();
            try {
                if (illegalContracts.contains(entity.getContractStg())) {
                    errors.add(String.format("%s(%s) 請確認用電端契約類型是否可以參與", entity.getName(),
                            entity.getNbsCustomerNumber()));
                }
            } catch (Throwable ex) {
                log.error("{} {}", entity.getName(), ex);
                errors.add(String.format("%s(%s) 資料異常", entity.getName(), entity.getNbsCustomerNumber()));
            }
        });
        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join(",", errors));
        }
    }

    /**
     * 透過上傳檔案(內含ORDER以及電號)，查詢現有發電端暫存以及發電端
     * 或者由前端讀取檔案，將檔案內容整理成Vo樣式傳送到後端會更好些？
     * 發電端之後會有另外一張暫存表，發電端處於進行中契約時，要複製到暫存區，所有契約結束以後，從暫存區移除
     *
     * @param vos
     */
    public List<ApplicationGeneratorVO> searchGeneratorByUploadFile(List<ApplicationGeneratorOrderVo> vos) {
        // 整理出所有order & 電號
        // 先到暫存區(GeneratorEntityTemp)查詢再到正式區(GeneratorEntity)查詢
        // 還是加上flag用同一張Table控管暫存或非暫存
        var customerNos = vos.stream().map(a -> a.getNbsCustomerNumber()).collect(Collectors.toList());
        List<GeneratorEntity> gens = generatorEntityRepository.findAllByNbsCustomerNumberIn(customerNos);
        var results = vos.parallelStream().map(vo -> {
            ApplicationGeneratorVO avo = new ApplicationGeneratorVO();
            avo.setOrder(vo.getOrder());
            var optGen = gens.stream().filter(a -> a.getNbsCustomerNumber().equals(vo.getNbsCustomerNumber()))
                    .findFirst();
            if (optGen.isPresent()) {
                avo.setGeneratorEntity(VoUtils.toVO(optGen.get(), GeneratorEntityVO.class));
            }
            return avo;
        }).filter(a -> a != null).sorted(Comparator.comparing(ApplicationGeneratorVO::getOrder)).collect(
                Collectors.toList());
        return results;
    }

    /**
     * 將選擇的發電端與案件相關聯
     * 發電端需要檢查不得重複，或者於資料庫加上unique條件
     *
     * @param applicationId
     * @param vos
     * @param userId
     */
    public void addGeneratorToApplication(Long applicationId, List<ApplicationGeneratorOrderVo> vos, Long userId) {
        var gens = vos.stream().map(a -> ApplicationGenerator.builder().applicationId(applicationId).generatorId(
                a.getId()).build()).collect(Collectors.toList());
        applicationGeneratorRepository.saveAll(gens);
        /**
         * 將使用到的發電端存到暫存區去 TODO
         */
    }

    /**
     * 尋找案件對應所有發電端
     *
     * @param applicationId
     * @return
     */
    public List<ApplicationGeneratorVO> findAllApplicationGeneratorsByApplicationId(Long applicationId) {
        var example = ApplicationGenerator.builder().applicationId(applicationId).build();
        var list = applicationGeneratorRepository.findAll(Example.of(example));
        return list.stream().map(a -> {
            var agVO = VoUtils.toVO(a, ApplicationGeneratorVO.class);
            if (!org.apache.axis.utils.StringUtils.isEmpty(agVO.getTempMeter())) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    GeneratorEntityMeterVO specialMeter = null;
                    specialMeter = mapper.readValue(agVO.getTempMeter(), GeneratorEntityMeterVO.class);
                    agVO.setSpecialMeter(specialMeter);
                    agVO.setTempMeter(null); // 不需要原始的JSON檔案
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
            return agVO;
        }).collect(Collectors.toList());
    }

    /**
     * 尋找案件對應所有用電端
     *
     * @param applicationId
     * @return
     */
    public List<ApplicationLoadVO> findAllApplicationLoadsByApplicationId(Long applicationId) {
        var example = ApplicationLoad.builder().applicationId(applicationId).build();
        var list = applicationLoadRepository.findAll(Example.of(example));
        return list.stream().map(a -> {
            var alVO = VoUtils.toVO(a, ApplicationLoadVO.class);
            if (!org.apache.axis.utils.StringUtils.isEmpty(alVO.getTempMeter())) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    LoadEntityMeterVO specialMeter = null;
                    specialMeter = mapper.readValue(alVO.getTempMeter(), LoadEntityMeterVO.class);
                    alVO.setSpecialMeter(specialMeter);
                    alVO.setTempMeter(null); // 不需要原始的JSON檔案
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
            return alVO;
        }).collect(Collectors.toList());
    }

    /**
     * 尋找案件關聯所有發電端及資訊
     *
     * @param applicationId
     * @return
     */
    public List<ApplicationGeneratorVO> searchGeneratorByApplicationId(Long applicationId) {
        var list = findAllApplicationGeneratorsByApplicationId(applicationId);
        var generatorIds = list.stream().map(a -> a.getGeneratorId()).collect(Collectors.toList());
        var gens = generatorEntityRepository.findAllById(generatorIds);
        var vos = list.stream().map(a -> VoUtils.toVO(a, ApplicationGeneratorVO.class)).collect(Collectors.toList());
        vos.parallelStream().forEach(a -> {
            var gen = gens.stream().filter(b -> a.getGeneratorId().equals(b.getId())).findFirst();
            if (gen.isPresent()) {
                a.setGeneratorEntity(VoUtils.toVO(gen, GeneratorEntityVO.class));
            }
        });
        return vos;
    }

    /**
     * 透過上傳檔案(內含ORDER以及電號)，查詢現有發電端暫存以及發電端
     * 或者由前端讀取檔案，將檔案內容整理成Vo樣式傳送到後端會更好些？
     * 發電端之後會有另外一張暫存表，發電端處於進行中契約時，要複製到暫存區，所有契約結束以後，從暫存區移除
     *
     * @param vos
     */
    public List<ApplicationLoadVO> searchLoadByUploadFile(List<ApplicationLoadOrderVo> vos) {
        // 整理出所有order & 電號
        // 先到暫存區(GeneratorEntityTemp)查詢再到正式區(GeneratorEntity)查詢
        // 還是加上flag用同一張Table控管暫存或非暫存
        var customerNos = vos.stream().map(a -> a.getNbsCustomerNumber()).collect(Collectors.toList());
        List<LoadEntity> loads = loadEntityRepository.findAllByNbsCustomerNumberIn(customerNos);
        var results = vos.parallelStream().map(vo -> {
            ApplicationLoadVO avo = new ApplicationLoadVO();
            avo.setOrder(vo.getOrder());
            var optLoad = loads.stream().filter(a -> a.getNbsCustomerNumber().equals(vo.getNbsCustomerNumber()))
                    .findFirst();
            if (optLoad.isPresent()) {
                avo.setLoadEntity(VoUtils.toVO(optLoad.get(), LoadEntityVO.class));
            }
            return avo;
        }).sorted(Comparator.comparing(ApplicationLoadVO::getOrder)).collect(Collectors.toList());
        return results;
    }

    /**
     * 將選擇的發電端與案件相關聯
     * 發電端需要檢查不得重複，或者於資料庫加上unique條件
     *
     * @param applicationId
     * @param vos
     * @param userId
     */
    public void addLoadToApplication(Long applicationId, List<ApplicationLoadOrderVo> vos, Long userId) {
        var gens = vos.stream().map(a -> {
            return ApplicationGenerator.builder().applicationId(applicationId).generatorId(a.getId()).build();
        }).collect(Collectors.toList());
        applicationGeneratorRepository.saveAll(gens);
        /**
         * 將使用到的發電端存到暫存區去 TODO
         */
    }

    /**
     * 尋找案件關聯所有發電端及資訊
     *
     * @param applicationId
     * @return
     */
    public List<ApplicationLoadVO> searchLoadByApplicationId(Long applicationId) {
        var list = findAllApplicationLoadsByApplicationId(applicationId);
        var loadIds = list.stream().map(a -> a.getLoadId()).collect(Collectors.toList());
        var loads = loadEntityRepository.findAllById(loadIds);
        var vos = list.stream().map(a -> VoUtils.toVO(a, ApplicationLoadVO.class)).collect(Collectors.toList());
        vos.parallelStream().forEach(a -> {
            var gen = loads.stream().filter(b -> a.getLoadId().equals(b.getId())).findFirst();
            if (gen.isPresent()) {
                a.setLoadEntity(VoUtils.toVO(gen, LoadEntityVO.class));
            }
        });
        return vos;
    }

    /**
     * 查詢案件發電端資訊（暫存區優先、如果沒有就取正式區）
     *
     * @param vo
     * @return
     */
    public ApplicationGeneratorVO searchTempGeneratorByApplicationGeneratorVo(ApplicationGeneratorVO vo) {
        var gen = generatorEntityRepository.findById(vo.getGeneratorId());
        if (gen.isPresent()) {
            var genVo = VoUtils.toVO(gen.get(), GeneratorEntityVO.class);
            vo.setGeneratorEntity(genVo);
            return vo;
        }
        throw new RuntimeException("generator not found");
    }

    /**
     * 查詢案件用電端資訊（暫存區優先、如果沒有就取正式區）
     *
     * @param vo
     * @return
     */
    public ApplicationLoadVO searchTempLoadByApplicationLoadVo(ApplicationLoadVO vo) {
        var optLoad = loadEntityRepository.findById(vo.getLoadId());
        if (optLoad.isPresent()) {
            var genVo = VoUtils.toVO(optLoad.get(), LoadEntityVO.class);
            vo.setLoadEntity(genVo);
            return vo;
        }
        throw new RuntimeException("load not found");
    }

    /**
     * 儲存案件用電端暫存資訊
     *
     * @param vo
     * @param userId
     */
    public void saveTempLoadByApplicationLoadVo(ApplicationLoadVO vo, Long userId) {
        // TODO
        throw new RuntimeException("Not implemented yet");
    }

    /**
     * 儲存案件發電端暫存資訊
     *
     * @param vo
     * @param userId
     */
    public void saveTempGeneratorByApplicationGeneratorVo(ApplicationGeneratorVO vo, Long userId) {
        // TODO
        throw new RuntimeException("Not implemented yet");
    }

    /**
     * 儲存案件草稿
     *
     * @param vo
     * @param userId
     */
    public void saveApplicationDraft(ApplicationVO vo, Long userId) {
        // TODO
        throw new RuntimeException("Not implemented yet");
    }

    /**
     * 儲存案件相關檔案，可能不是針對案件而是針對申請者，之後有可能需要調整
     *
     * @param applicationId
     * @param fileId
     * @param userId
     * @param file
     */
    public void saveApplicationFile(Long applicationId, Long fileId, Long userId, MultipartFile file) {
        // TODO
        throw new RuntimeException("Not implemented yet");
    }

    /**
     * 儲存檔案至用電端暫存區，之後再覆蓋
     *
     * @param applicationId
     * @param loadId
     * @param fileId
     * @param userId
     * @param file
     */
    public void saveFileWithTempLoad(Long applicationId, Long loadId, Long fileId, Long userId, MultipartFile file) {
        // TODO
        throw new RuntimeException("Not implemented yet");
    }

    /**
     * 儲存檔案至用電端暫存區，之後再覆蓋
     *
     * @param applicationId
     * @param generatorId
     * @param fileId
     * @param userId
     * @param file
     */
    public void saveFileWithTempGenerator(Long applicationId, Long generatorId, Long fileId, Long userId,
                                          MultipartFile file) {
        // TODO
        throw new RuntimeException("Not implemented yet");
    }

    /**
     * 利用檔案匯入查詢大量用電端
     *
     * @param fileInputStream excel file
     * @return List<LoadEntityVO>
     */
    public List<ApplicationGeneratorVO> searchGeneratorFromExcel(InputStream fileInputStream,
                                                                 Long userId) throws IOException {
        List<ApplicationGeneratorVO> vos = new ArrayList<>();
        List<String> nbsList = new ArrayList<>();
        try {
            try (ReadableWorkbook wb = new ReadableWorkbook(fileInputStream)) {
                Sheet sheet = wb.getFirstSheet();
                try (Stream<Row> rows = sheet.openStream()) {
                    rows.forEach(r -> {
                        String nbsNo = null;
                        Integer order = null;
                        if (r.getCellCount() < 2)
                            return;
                        if (r.getCell(1) != null && r.getCell(1).getRawValue() != null) {
                            try {
                                nbsNo = r.getCell(1).getRawValue().replaceAll("-", "").trim();
                                Long.valueOf(nbsNo); // 無法轉換代表非有效資料
                                nbsList.add(nbsNo);
                            } catch (ExcelReaderException e) {
                                // skip this value
                                log.error(e.getMessage());
                            } catch (Throwable e) {
                                // skip this value
                                log.error(e.getMessage());
                            }
                        }
                        // 要加上處理order給前端判斷
                        if (r.getCell(0) != null && r.getCell(0).getRawValue() != null) {
                            try {
                                order = Integer.valueOf(r.getCell(0).getRawValue());
                            } catch (ExcelReaderException e) {
                                // skip this value
                                log.error(e.getMessage());
                            } catch (Throwable e) {
                                // skip this value
                                log.error(e.getMessage());
                            }
                        }
                        if (nbsNo != null && order != null) {
                            var tempLoad = GeneratorEntityVO.builder().nbsCustomerNumber(nbsNo).build();
                            var vo = ApplicationGeneratorVO.builder().order(order).generatorEntity(tempLoad).build();
                            vos.add(vo);
                        }
                    });
                }
            }
            var entityVos = generatorService.getTempGeneratorEntitysByCustomerNos(nbsList);
            vos.forEach(vo -> {
                var optFirst = entityVos.stream().filter(
                                a -> a.getNbsCustomerNumber().equals(
                                        vo.getGeneratorEntity().getNbsCustomerNumber()))
                        .findFirst();
                if (optFirst.isPresent()) {
                    vo.setGeneratorEntity(optFirst.get());
                } else {
                    vo.getGeneratorEntity().setId(-1L);
                    vo.getGeneratorEntity().setName("Unknown");
                }
            });
            try {
                var newNoList = vos.stream().filter(a -> a.getGeneratorEntity().getId() < 0).map(
                        a -> a.getGeneratorEntity().getNbsCustomerNumber()).collect(Collectors.toList());
                remsService.syncRNISWithCustomNumbers(newNoList, userId);
                var newEntityVos = generatorService.getTempGeneratorEntitysByCustomerNos(newNoList);
                vos.stream().filter(a -> a.getGeneratorEntity().getId() < 0).forEach(vo -> {
                    var optFirst = newEntityVos.stream().filter(
                                    a -> a.getNbsCustomerNumber().equals(
                                            vo.getGeneratorEntity().getNbsCustomerNumber()))
                            .findFirst();
                    optFirst.ifPresent(vo::setGeneratorEntity);
                });
            } catch (Throwable ex) {
                log.error(ex, ex);
            }
            return vos;
        } catch (Throwable ex) {
            throwException(ErrorCode.FILE_IS_INVALID);
        }
        return null;
    }

    /**
     * 利用檔案匯入查詢大量用電端
     *
     * @param fileInputStream excel file
     * @return List<LoadEntityVO>
     */
    public List<ApplicationLoadVO> searchLoadsFromExcel(InputStream fileInputStream, Long userId) throws IOException {
        List<ApplicationLoadVO> vos = new ArrayList<>();
        List<String> nbsList = new ArrayList<>();
        try {
            try (ReadableWorkbook wb = new ReadableWorkbook(fileInputStream)) {
                Sheet sheet = wb.getFirstSheet();
                try (Stream<Row> rows = sheet.openStream()) {
                    rows.forEach(r -> {
                        String nbsNo = null;
                        Integer order = null;
                        if (r.getCellCount() < 2)
                            return;
                        if (r.getCell(1) != null && r.getCell(1).getRawValue() != null) {
                            try {
                                nbsNo = r.getCell(1).getRawValue().replaceAll("-", "").trim();
                                Long.valueOf(nbsNo); // 無法轉換代表非有效資料
                                nbsList.add(nbsNo);
                            } catch (ExcelReaderException e) {
                                // skip this value
                                log.error(e.getMessage());
                            } catch (Throwable e) {
                                // skip this value
                                log.error(e.getMessage());
                            }
                        }
                        // 要加上處理order給前端判斷
                        if (r.getCell(0) != null && r.getCell(0).getRawValue() != null) {
                            try {
                                order = Integer.valueOf(r.getCell(0).getRawValue());
                            } catch (ExcelReaderException e) {
                                // skip this value
                                log.error(e.getMessage());
                            } catch (Throwable e) {
                                // skip this value
                                log.error(e.getMessage());
                            }
                        }
                        if (nbsNo != null && order != null) {
                            var tempLoad = LoadEntityVO.builder().nbsCustomerNumber(nbsNo).build();
                            var vo = ApplicationLoadVO.builder().order(order).loadEntity(tempLoad).build();
                            vos.add(vo);
                        }
                    });
                }
            }
            var entityVos = loadService.getTempLoadEntitysByCustomerNos(nbsList);
            vos.forEach(vo -> {
                var optFirst = entityVos.stream().filter(
                        a -> a.getNbsCustomerNumber().equals(vo.getLoadEntity().getNbsCustomerNumber())).findFirst();
                if (optFirst.isPresent()) {
                    vo.setLoadEntity(optFirst.get());
                } else {

                    vo.getLoadEntity().setId(-1L);
                    vo.getLoadEntity().setName("Unknown");
                }
            });
            try {
                var newNoList = vos.stream().filter(a -> a.getLoadEntity().getId() < 0).map(
                        a -> a.getLoadEntity().getNbsCustomerNumber()).collect(Collectors.toList());
                remsService.syncNBSWithCustomNumbers(newNoList, userId);
                var newEntityVos = loadService.getTempLoadEntitysByCustomerNos(newNoList);
                vos.stream().filter(a -> a.getLoadEntity().getId() < 0).forEach(vo -> {
                    var optFirst = newEntityVos.stream().filter(
                                    a -> a.getNbsCustomerNumber().equals(
                                            vo.getLoadEntity().getNbsCustomerNumber()))
                            .findFirst();
                    optFirst.ifPresent(vo::setLoadEntity);
                });
            } catch (Throwable ex) {
                log.error(ex, ex);
            }
            return vos;
        } catch (Throwable ex) {
            throwException(ErrorCode.FILE_IS_INVALID);
        }
        return null;
    }

    /**
     * 取得計畫書應附文件
     *
     * @return
     */
    public List<EntityDocumentRequiredVO> findPlanAllRequiredDocuments() {
        var vos = entityDocumentRequiredRepository.findAllByType("P").stream().map(
                a -> VoUtils.toVO(a, EntityDocumentRequiredVO.class)).collect(Collectors.toList());
        var documentIds = vos.stream().map(EntityDocumentRequiredVO::getDocumentId).collect(Collectors.toList());
        var documents = workflowDocumentRepository.findAllById(documentIds);
        vos.forEach(vo -> {
            var optFirst = documents.stream().filter(a -> a.getId().equals(vo.getDocumentId())).findFirst();
            if (optFirst.isPresent()) {
                var doc = optFirst.get();
                resetDocRequired(vo, doc);
            }
        });
        return vos;
    }

    /**
     * 取得當前計畫書已上傳之文件
     *
     * @param applicationId
     * @return
     */
    public List<ApplicationDocumentVO> findUploadedDocumentsByApplicationId(Long applicationId) {
        var query = ApplicationDocument.builder().applicationId(applicationId).build();
        var list = applicationDocumentRepository.findAll(Example.of(query)).stream().map(
                a -> VoUtils.toVO(a, ApplicationDocumentVO.class)).collect(Collectors.toList());
        list.forEach(a -> a.setContent(null));
        return list;
    }

    /**
     * 取得當前計畫書對應文件
     *
     * @param id
     * @return
     */
    public ApplicationDocumentVO getUploadedDocumentById(Long id) {
        var model = applicationDocumentRepository.findById(id).get();
        var result = VoUtils.toVO(model, ApplicationDocumentVO.class);
        result.setContent(model.getContent());
        return result;
    }

    /**
     * 儲存檔案並且回報檔案ＩＤ
     *
     * @param applicationId
     * @param documentId
     * @param file
     * @param userId
     * @return
     * @throws IOException
     */
    public Long uploadDocumentByApplicationIdAndDocumentId(Long applicationId, Integer documentId, MultipartFile file,
                                                           String unit, String serialNumber, String operationMode,
                                                           Date validDate, Date issueDate, String comment,
                                                           Long userId) throws Exception {

        FileTypeValidator.checkValid(file);
        checkFileUploadColumnValid(documentId, unit, serialNumber, operationMode, validDate, comment, issueDate);
        var model = ApplicationDocument.builder().applicationId(applicationId).documentId(documentId).build();
        var dbDocument = applicationDocumentRepository.findOne(Example.of(model));
        if (dbDocument.isPresent()) {
            model = dbDocument.get();
            if (file != null) {
                model.setContent(file.getBytes());
                model.setExt(getFileExtension(file));
            }
            model.setModifiedBy(userId);
            model.setUnit(unit);
            model.setSerialNumber(serialNumber);
            model.setOperationMode(operationMode);
            model.setValidDate(validDate);
            model.setIssueDate(issueDate);
            model.setComment(comment);
            var result = applicationDocumentRepository.save(dbDocument.get());
            return result.getId();
        } else {

            if (file != null) {
                model.setContent(file.getBytes());
                model.setExt(getFileExtension(file));
            }
            model.setModifiedBy(userId);
            model.setUnit(unit);
            model.setSerialNumber(serialNumber);
            model.setOperationMode(operationMode);
            model.setValidDate(validDate);
            model.setIssueDate(issueDate);
            model.setComment(comment);
            var result = applicationDocumentRepository.save(model);
            return result.getId();
        }
    }

    /**
     * 根據契約類型取得各需求端需要的文件
     * 改成直接將發電端相關檔案全部給前端去做判斷
     *
     * @param applicationType
     * @return
     */
    public List<EntityDocumentRequiredVO> getRequiredDocumentsByApplicationType(String applicationType) {

        return generatorService.findAllRequiredDocumentsWithoutGeneratorInfo();
    }

    /**
     * 取得發電端特定上傳檔案
     *
     * @param entityId
     * @param documentId
     * @return
     */
    public List<GeneratorEntityDocumentVO> getGeneratorEntityDocument(List<Long> entityId, Long documentId) {
        var docs = generatorEntityDocumentRepository.findByGeneratorEntityIdInAndDocumentId(entityId, documentId);
        var files = entityId.stream().map(generatorId -> {
            var doc = docs.stream().filter(a -> a.getGeneratorEntityId().equals(generatorId)).findFirst();
            if (doc.isPresent()) {
                var vo = VoUtils.toVO(doc.get(), GeneratorEntityDocumentVO.class);
                return vo;
            }
            return null;
        }).filter(a -> a != null).toList();
        return files;
    }

    /**
     * 依據發電端ID取得所有暫存區與正式區檔案，再經由前端過濾要顯示哪些文件
     *
     * @param applicationType
     * @param entityId
     * @return
     */
    public List<GeneratorEntityDocumentVO> getGeneratorEntityDocument(String applicationType,
                                                                      Long entityId) throws JsonProcessingException {

        var genRequired = generatorService.findAllRequiredDocumentsWithoutGeneratorInfo();
        var docs = generatorEntityDocumentRepository.findAllByGeneratorEntityId(entityId);
        var files = genRequired.stream().map(gen -> {
            var documentId = gen.getDocumentId();
            var formal = docs.stream().filter(a -> a.getDocumentId().equals(documentId)).findFirst();
            if (formal.isPresent()) {
                var vo = VoUtils.toVO(formal.get(), GeneratorEntityDocumentVO.class);
                return vo;
            }
            return null;
        }).toList();
        files = files.stream().filter(Objects::nonNull).collect(Collectors.toList());
        return files;
    }

    /**
     * 檢查計畫書檔案是否有遺漏第一階段需附文件
     *
     * @param applicationId
     */
    public void checkApplicationDocumentStage1(Long applicationId) {
        List<String> errors = new ArrayList<>();
        var docs = applicationDocumentRepository.findByApplicationId(applicationId);
        var vos = entityDocumentRequiredRepository.findAllByType("P").stream().map(
                a -> VoUtils.toVO(a, EntityDocumentRequiredVO.class)).toList();
        var applicationRequiredDocumentIds = vos.stream().filter(a -> a.getStage().equals("1"))
                .map(EntityDocumentRequiredVO::getDocumentId).collect(
                        Collectors.toList());
        var documents = workflowDocumentRepository.findAllById(applicationRequiredDocumentIds);
        applicationRequiredDocumentIds.forEach(documentId -> {
            var temp = docs.stream().filter(a -> a.getDocumentId().equals(documentId)).findFirst();
            if (temp.isPresent()) {
                return;
            } else {
                var doc = documents.stream().filter(a -> a.getId().equals(documentId)).findFirst().orElseThrow();
                errors.add(String.format("計畫書 缺少應附文件: %s", doc.getName()));
            }
        });

        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join("\n", errors));
        }
    }

    public void checkApplicationDocumentStage2(Long applicationId) {
        List<String> errors = new ArrayList<>();
        var docs = applicationDocumentRepository.findByApplicationId(applicationId);
        var vos = entityDocumentRequiredRepository.findAllByType("P").stream().map(
                a -> VoUtils.toVO(a, EntityDocumentRequiredVO.class)).toList();
        var applicationRequiredDocumentIds = vos.stream().filter(a -> a.getStage().equals("2"))
                .map(EntityDocumentRequiredVO::getDocumentId).collect(
                        Collectors.toList());
        var documents = workflowDocumentRepository.findAllById(applicationRequiredDocumentIds);
        applicationRequiredDocumentIds.forEach(documentId -> {
            var temp = docs.stream().filter(a -> a.getDocumentId().equals(documentId)).findFirst();
            if (temp.isPresent()) {
                return;
            } else {
                var doc = documents.stream().filter(a -> a.getId().equals(documentId)).findFirst().orElseThrow();
                errors.add(String.format("計畫書 缺少應附文件: %s", doc.getName()));
            }
        });

        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join("\n", errors));
        }
    }

    /**
     * 檢查發電端第一階段文件是否備齊，如果沒有準備好就丟出Exception通知有哪些文件還有欠缺
     *
     * @param applicationType
     * @param entityId
     * @throws JsonProcessingException
     */
    public List<String> getGeneratorEntityDocumentStage1FromDatabase(String applicationType, Long entityId,
                                                                     ApplicationGenerator ag) throws JsonProcessingException {
        GeneratorEntityVO generator = generatorService.getTempGeneratorEntity(entityId);
        var genRequired = generatorService.findGeneratorAllRequiredDocuments(generator, applicationType, ag);
        var docs = generatorEntityDocumentRepository.findAllByGeneratorEntityId(entityId);
        List<String> errors = new ArrayList<>();
        genRequired.stream().filter(a -> a.getStage().equals("1")).forEach(genDoc -> {
            var documentId = genDoc.getDocumentId();
            var formal = docs.stream().filter(a -> a.getDocumentId().equals(documentId)).findFirst();
            if (formal.isPresent()) {
                return;
            }
            errors.add(
                    String.format("電號:%s %s 缺少應附文件:%s", generator.getNbsCustomerNumber(), generator.getName(),
                            genDoc.getName()));
        });
        return errors;

    }

    /**
     * 檢查發電端第一階段文件是否備齊，如果沒有準備好就丟出Exception通知有哪些文件還有欠缺
     *
     * @param applicationType
     * @param generator
     * @throws JsonProcessingException
     */
    public List<String> getGeneratorEntityDocumentStage1FromVO(String applicationType, GeneratorEntityVO generator,
                                                               ApplicationGeneratorVO agVO) throws JsonProcessingException {

        var ag = VoUtils.toEntity(agVO, ApplicationGenerator.class);
        var genRequired = generatorService.findGeneratorAllRequiredDocuments(generator, applicationType, ag);
        var docs = generatorEntityDocumentRepository.findAllByGeneratorEntityId(generator.getId());
        List<String> errors = new ArrayList<>();
        genRequired.stream().filter(a -> a.getStage().equals("1")).forEach(genDoc -> {
            var documentId = genDoc.getDocumentId();
            var formal = docs.stream().filter(a -> a.getDocumentId().equals(documentId)).findFirst();
            if (formal.isPresent()) {
                return;
            }
            errors.add(
                    String.format("電號:%s %s 缺少應附文件:%s", generator.getNbsCustomerNumber(), generator.getName(),
                            genDoc.getName()));
        });
        return errors;

    }

    /**
     * 下載已經上傳檔案
     *
     * @param entityId
     * @param source
     * @param fileId
     * @return
     */
    public GeneratorEntityDocumentVO getGeneratorEntityDocumentFile(Long entityId, String source, Long fileId) {

        var file = generatorEntityDocumentRepository.findById(fileId);
        if (file.isPresent()) {
            var vo = VoUtils.toVO(file.get(), GeneratorEntityDocumentVO.class);
            vo.setContent(file.get().getContent());
            vo.setDocumentId(file.get().getDocumentId());
            return vo;
        }
        return null;
    }

    /**
     * 儲存檔案並且回報檔案ＩＤ
     *
     * @param entityId
     * @param documentId
     * @param file
     * @param userId
     * @return
     * @throws IOException
     */
    public Long uploadDocumentByEntityIdAndDocumentId(Long entityId, Integer documentId, MultipartFile file,
                                                      String unit, String serialNumber, String operationMode,
                                                      Date validDate, String comment, Date issueDate,
                                                      Long userId) throws Exception {

        FileTypeValidator.checkValid(file);
        checkFileUploadColumnValid(documentId, unit, serialNumber, operationMode, validDate, comment, issueDate);
        var model = GeneratorEntityDocument.builder().generatorEntityId(entityId).documentId(documentId).build();
        var dbDocument = generatorEntityDocumentRepository.findOne(Example.of(model));
        if (dbDocument.isPresent()) {
            model = dbDocument.get();
            if (file != null) {
                model.setContent(file.getBytes());
                model.setExt(getFileExtension(file));
            }
            model.setUnit(unit);
            model.setSerialNumber(serialNumber);
            model.setOperationMode(operationMode);
            model.setValidDate(validDate);
            model.setComment(comment);
            model.setIssueDate(issueDate);
            model.setModifiedBy(userId);
            var result = generatorEntityDocumentRepository.save(dbDocument.get());
            return result.getId();
        } else {
            model.setModifiedBy(userId);
            if (file != null) {
                model.setContent(file.getBytes());
                model.setExt(getFileExtension(file));
            }
            model.setUnit(unit);
            model.setSerialNumber(serialNumber);
            model.setOperationMode(operationMode);
            model.setValidDate(validDate);
            model.setComment(comment);
            model.setIssueDate(issueDate);
            var result = generatorEntityDocumentRepository.save(model);
            return result.getId();
        }
    }

    /**
     * create a function can process 輸配電層級判斷
     * 執行後回傳當前組合輸配電層級
     *
     * @param applicationId
     */
    public Integer judgeApplicationGeneratorLoadTransmissionDistribution(Long applicationId) {
        var application = applicationRepository.findById(applicationId).orElse(null);

        var ags = applicationGeneratorRepository.findAllByApplicationId(applicationId);
        var als = applicationLoadRepository.findAllByApplicationId(applicationId);
        return judgeApplicationGeneratorLoadTransmissionDistribution(application, ags, als);
    }

    /**
     * create a function can process 輸配電層級判斷
     * * 執行後回傳當前組合輸配電層級
     *
     * @param application
     * @param ags
     * @param als
     * @return
     */
    @Transactional
    public Integer judgeApplicationGeneratorLoadTransmissionDistribution(Application application,
                                                                         List<ApplicationGenerator> ags,
                                                                         List<ApplicationLoad> als) {
        var genIds = ags.stream().map(a -> a.getGeneratorId()).collect(Collectors.toList());
        var loadIds = als.stream().map(a -> a.getLoadId()).collect(Collectors.toList());
        /**
         * 發用電端要以暫存區的資料為主來判斷
         */
        var gens = generatorService.getTempGeneratorEntitys(genIds);
        var lens = loadService.getTempLoadEntitys(loadIds);
        var voltages = voltageLevelRepository.findAll();

        Integer decideLevel = 1;
        for (ApplicationGenerator ag : ags) {
            var gen = gens.stream().filter(a -> a.getId().equals(ag.getGeneratorId())).findFirst().orElse(null);
            if (gen != null) {
                var algs = als.stream().map(al -> {
                    var len = lens.stream().filter(a -> a.getId().equals(al.getLoadId())).findFirst().orElse(null);
                    if (len != null) {
                        var level = judgeApplicationGeneratorLoadTransmissionDistribution(gen, len, voltages,
                                application.getType());

                        return ApplicationGeneratorLoadType.builder().applicationGeneratorId(
                                ag.getId()).applicationLoadId(al.getId()).type(level).build();
                    }
                    return null;
                }).filter(Objects::nonNull).toList();
                if (!algs.isEmpty()) {
                    var first = algs.stream().map(a -> a.getType()).sorted(Comparator.reverseOrder()).findFirst();
                    if (first.isPresent()) {
                        decideLevel = first.get();

                    }
                    applicationGeneratorLoadTypeRepository.saveAll(algs);
                }
            }

        }
        return decideLevel;
    }

    /**
     * 判斷發電端與用電端的輸配電層級
     *
     * @param ge
     * @param le
     * @param voltages
     * @param applicationType
     * @return Intger 1: 純輸電、2: 純配電、3: 輸配電混合
     */
    Integer judgeApplicationGeneratorLoadTransmissionDistribution(GeneratorEntityVO ge, LoadEntityVO le,
                                                                  List<VoltageLevel> voltages, String applicationType) {

        Integer level = 1;
        boolean useResponsibility = !(applicationType.equals("2") || applicationType.equals("3"));

        /**
         * combineMethod為內線1的時候，取併接點電壓、為外線2的時候取責任分界點
         */
        var geVoltage = voltages.stream().filter(a -> a.getId().equals(
                        useResponsibility ? ge.getResponsibilityVoltage()
                                : ge.getCombineMethod() == 1 ? ge.getVoltage() : ge.getResponsibilityVoltage()))
                .findFirst().orElse(
                        null);
        var leVoltage = voltages.stream().filter(a -> a.getId().equals(le.getResponsibilityVoltage())).findFirst()
                .orElse(null);
        if (geVoltage == null) {
            HashMap<String, String> args = new HashMap<>();
            args.put("entityInfo", ge.getNbsCustomerNumber() + " " + ge.getName());
            throwException(ErrorCode.GENERATOR_VOLTAGE_INFO_LOSS, args);
        }
        if (leVoltage == null) {
            HashMap<String, String> args = new HashMap<>();
            args.put("entityInfo", le.getNbsCustomerNumber() + " " + le.getName());
            throwException(ErrorCode.LOAD_VOLTAGE_INFO_LOSS, args);
        }
        if (voltageLevelCompare(geVoltage, leVoltage)) {
            if (leVoltage.getVoltage().getId().equals("hv") || leVoltage.getVoltage().getId().equals("uhv")) {
                level = 1; // 純輸電、同樣層級的高壓
            } else {
                // 配電情境還需要再加上判斷
                try {
                    // 處理掉可能一方饋線代號為null的情形
                    String geFeeder = ge.getFeeder() == null ? "" : ge.getFeeder();
                    String leFeeder = le.getFeeder() == null ? "" : le.getFeeder();
                    if (geFeeder.equals(leFeeder)) {
                        level = 2; // 饋線相同情境下，為純配電
                    } else {
                        level = 3;// 饋線不同則為輸配電混合
                    }
                } catch (Throwable ex) {
                    // 異常情形，如果沒有饋線代號，或者一者為null，會出錯
                    level = 3;
                }

            }
        } else {
            level = 3; // 如果雙方電壓不同、一定是輸配電
        }
        return level;
    }

    /**
     * 電網層級判斷 uhv跟hv當作同一層級來判斷，將數值區分為uhv跟lv來判斷
     *
     * @param l1
     * @param l2
     * @return
     */
    boolean voltageLevelCompare(VoltageLevel l1, VoltageLevel l2) {
        String v1 = l1.getVoltage().getId().equals("hv") ? "uhv" : l1.getVoltage().getId();
        String v2 = l2.getVoltage().getId().equals("hv") ? "uhv" : l2.getVoltage().getId();
        if (v1.equals(v2)) {
            return true;
        }
        return false;
    }

    /**
     * 變更計劃免稅狀態
     *
     * @param applicationId
     * @param isTaxFree
     * @param accountId
     */
    @Transactional
    public void updateTaxFreeStatus(Long applicationId, Boolean isTaxFree, Long accountId) {

        Timestamp modifiedAt = new Timestamp(new Date().getTime());

        applicationRepository.updateIsTaxFreeStatus(applicationId, isTaxFree, accountId, modifiedAt);

    }

    /**
     * 變更計劃免送ERP狀態
     *
     * @param applicationId
     * @param isSkipERP
     * @param accountId
     */
    @Transactional
    public void updateSkipERPStatus(Long applicationId, Boolean isSkipERP, Long accountId) {

        Timestamp modifiedAt = new Timestamp(new Date().getTime());

        applicationRepository.updateIsSkipERPStatus(applicationId, isSkipERP, accountId, modifiedAt);

    }

    /**
     * 廢棄計畫書
     *
     * @param applicationid
     */
    public void abandon(Long applicationid, Long userId) {
        var application = applicationRepository.findById(applicationid).orElseThrow();
        if (!ApplicationStatus.DRAFT.equals(ApplicationStatus.fromValue(application.getStatus()))) {
            throwException(ErrorCode.APPLICATION_IS_NOT_DRAFT);
        }
        application.setStatus(ApplicationStatus.ABANDON.getValue());
        application.setModifiedBy(userId);
        applicationRepository.save(application);
    }

    /**
     * 反悔，想要將計畫書從作廢回復到審查中的話，可以利用這個方法進行操作。
     *
     * @param applicationId
     * @param userId
     */
    @Transactional
    public void restoreCurrentApplicationWorkflow(Long applicationId, Long userId) {
        var application = applicationRepository.findById(applicationId).orElseThrow();
        application.setStage2Status(ApplicationStage2Status.ENTITY_IN_WORKFLOW_FINISH.getValue());
        this.saveApplication(application);
        var applicationWorkflow = applicationWorkflowRepository.findTopByApplicationIdOrderByIdDesc(applicationId);
        if (applicationWorkflow == null) {
            throwException(ErrorCode.LAST_APPLICATIONWORKFLOW_NOT_FOUND);
        }
        applicationWorkflow.setStatus(ApplicationWorkflowStatus.WAITING.getValue());
        applicationWorkflowRepository.save(applicationWorkflow);
    }

    /**
     * 更新發用電端備註(Notes)
     *
     * @param applicationVO
     * @param userId
     */
    public void updateGeneratorAndLoadEntityNote(ApplicationVO applicationVO, Long userId) {
        var agVOs = applicationVO.getApplicationGeneratorList();
        var alVOs = applicationVO.getApplicationLoadList();
        if (!agVOs.isEmpty() && agVOs.isEmpty()) {
            var generatorIds = agVOs.stream().map(a -> a.getGeneratorId()).toList();
            var generators = generatorEntityRepository.findAllById(generatorIds);
            generators.forEach(g -> {
                var agVO = agVOs.stream().filter(a -> a.getGeneratorId().equals(g.getId())).findFirst().orElse(null);
                if (agVO != null) {
                    var notes = agVO.getGeneratorEntity().getNotes();
                    if (!notes.equals(g.getNotes())) {
                        g.setNotes(notes);
                        g.setModifiedBy(userId);
                    }

                }
            });
            generatorEntityRepository.saveAll(generators);
        }
        if (alVOs.isEmpty() && !alVOs.isEmpty()) {
            var loadIds = alVOs.stream().map(a -> a.getLoadId()).toList();
            var loads = loadEntityRepository.findAllById(loadIds);
            loads.forEach(l -> {
                var alVO = alVOs.stream().filter(a -> a.getLoadId().equals(l.getId())).findFirst().orElse(null);
                if (alVO != null) {
                    var notes = alVO.getLoadEntity().getNotes();
                    if (!notes.equals(l.getNotes())) {
                        l.setNotes(notes);
                        l.setModifiedBy(userId);
                    }

                }
            });
            loadEntityRepository.saveAll(loads);
        }
    }
}
